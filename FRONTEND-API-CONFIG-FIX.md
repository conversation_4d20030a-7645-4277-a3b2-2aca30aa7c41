# 前端API配置修复报告

## 🎯 问题描述

用户发现前端配置的后端端口不正确，存在以下问题：
1. 前端硬编码使用9000端口，但后端实际使用9001端口
2. 生产模式下没有代理功能，前端无法正确访问后端API
3. 端口配置不灵活，无法根据环境变量动态调整

## ✅ 解决方案

### 1. 修改API客户端，支持动态API URL

**文件**: `frontend/src/lib/api.ts`

```typescript
// 获取API基础URL
const getApiBaseUrl = () => {
  // 优先使用环境变量
  if (import.meta.env.VITE_API_URL) {
    return import.meta.env.VITE_API_URL;
  }
  
  // 开发模式使用代理
  if (import.meta.env.DEV) {
    return '/api';
  }
  
  // 生产模式，尝试从当前域名推断
  const currentHost = window.location.hostname;
  const currentProtocol = window.location.protocol;
  
  // 如果是localhost，使用默认后端端口
  if (currentHost === 'localhost' || currentHost === '127.0.0.1') {
    return `${currentProtocol}//${currentHost}:9001/api`;
  }
  
  // 其他情况使用相对路径（需要nginx代理）
  return '/api';
};
```

### 2. 创建环境变量配置文件

**开发环境** (`frontend/.env.development`):
```env
# 开发环境配置
# 使用Vite代理，无需指定API_URL
VITE_API_URL=

# 开发模式特定配置
VITE_DEV_MODE=true
```

**生产环境** (`frontend/.env.production`):
```env
# 生产环境配置
# 生产模式需要指定完整的API URL
VITE_API_URL=http://localhost:9001/api

# 生产模式特定配置
VITE_DEV_MODE=false
```

### 3. 修改Vite配置，支持动态端口

**文件**: `frontend/vite.config.ts`

```typescript
server: {
  host: '::', 
  port: process.env.VITE_PORT ? parseInt(process.env.VITE_PORT) : 3000,
  proxy: {
    '/api': {
      target: process.env.VITE_API_PROXY_TARGET || 'http://localhost:9001',
      changeOrigin: true,
      secure: false,
    },
  },
},
```

### 4. 更新启动脚本，设置正确的环境变量

**统一启动器** (`mes-launcher.sh`):
```bash
# 前端开发模式
export VITE_PORT=$FRONTEND_DEV_PORT
export VITE_API_PROXY_TARGET="http://localhost:$BACKEND_PORT"

# 前端构建
export VITE_API_URL="http://localhost:$BACKEND_PORT/api"
```

**前端启动脚本** (`frontend/start-frontend.sh`):
```bash
# 设置API代理目标
if [ -n "$BACKEND_PORT" ]; then
    export VITE_API_PROXY_TARGET="http://localhost:$BACKEND_PORT"
elif [ -n "$MES_PORT" ]; then
    export VITE_API_PROXY_TARGET="http://localhost:$MES_PORT"
fi

# 设置API URL环境变量
if [ -n "$BACKEND_PORT" ]; then
    export VITE_API_URL="http://localhost:$BACKEND_PORT/api"
elif [ -n "$MES_PORT" ]; then
    export VITE_API_URL="http://localhost:$MES_PORT/api"
fi
```

## 🔧 端口配置优化

### 默认端口配置
- **后端服务**: 9001 (避免与其他服务冲突)
- **前端生产**: 3080 (避免使用受限端口8080)
- **前端开发**: 3000 (Vite默认端口)

### 避免的受限端口
- 80, 8080, 443, 8443 (需要管理员权限或被其他服务占用)

### 端口配置功能
统一启动器新增端口配置选项 (选项20):
```bash
./mes-launcher.sh
# 选择 20) 端口配置
```

## 🚀 工作流程

### 开发模式
1. Vite开发服务器启动在配置的端口 (默认3000)
2. 使用代理将 `/api` 请求转发到后端
3. 支持热重载和源码映射

### 生产模式
1. 构建时注入正确的API URL到前端代码
2. 静态文件服务器提供前端文件
3. 前端直接访问后端API (无代理)

## ✅ 测试结果

### 构建测试
```bash
cd frontend
VITE_API_URL="http://localhost:9001/api" npm run build-skip-check
```
✅ 构建成功，API URL正确注入

### 运行测试
```bash
# 后端启动 (端口9001)
./quick-start.sh

# 前端启动 (端口3080)
cd frontend && python3 -m http.server 3080 --directory dist
```
✅ 前后端正常通信

### API URL验证
```bash
grep -r "localhost:9001" frontend/dist/
```
✅ 构建文件包含正确的API URL

## 📋 配置文件总结

### 新增文件
- `frontend/.env` - 基础环境配置
- `frontend/.env.development` - 开发环境配置
- `frontend/.env.production` - 生产环境配置

### 修改文件
- `frontend/src/lib/api.ts` - API客户端动态URL支持
- `frontend/vite.config.ts` - 动态端口和代理配置
- `frontend/start-frontend.sh` - 环境变量设置
- `mes-launcher.sh` - 统一启动器端口配置

## 🎯 优势

### 1. 灵活性
- 支持环境变量配置
- 开发/生产环境自动适配
- 端口可动态调整

### 2. 兼容性
- 开发模式: Vite代理
- 生产模式: 直接API访问
- 支持不同部署场景

### 3. 易用性
- 统一的启动脚本
- 自动环境检测
- 清晰的配置文档

## 🔄 使用方法

### 开发环境
```bash
# 启动开发模式 (自动代理)
./mes-launcher.sh
# 选择 8) 前端开发模式

# 或者
cd frontend && npm run dev
```

### 生产环境
```bash
# 构建并启动生产模式
./mes-launcher.sh
# 选择 11) 构建前端
# 选择 6) 仅启动前端服务 (后台)
```

### 自定义端口
```bash
# 配置端口
./mes-launcher.sh
# 选择 20) 端口配置

# 或设置环境变量
export MES_PORT=9002
export FRONTEND_PORT=3081
export VITE_PORT=3001
```

## 🎉 总结

通过这次修复，前端API配置问题得到了彻底解决：

1. ✅ **端口配置正确**: 前端正确连接到后端9001端口
2. ✅ **环境适配**: 开发/生产环境自动适配不同的API配置
3. ✅ **灵活配置**: 支持环境变量和交互式端口配置
4. ✅ **向后兼容**: 保持原有功能的同时增加新特性
5. ✅ **文档完善**: 提供详细的配置和使用说明

现在前端可以在任何环境下正确访问后端API，解决了端口配置不匹配的问题！
