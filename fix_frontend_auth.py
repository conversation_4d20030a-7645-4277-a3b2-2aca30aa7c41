#!/usr/bin/env python3
"""
修复前端认证问题的脚本
"""

import requests
import json
import time

BASE_URL = "http://localhost:9001/api"

def main():
    print("🔧 修复前端认证问题...")
    
    # 1. 测试gongyi用户当前状态
    print("1. 测试gongyi用户当前状态...")
    login_response = requests.post(f"{BASE_URL}/auth/login", json={
        "username": "gongyi",
        "password": "gongyi"
    })
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.status_code}")
        return 1
    
    login_data = login_response.json()
    token = login_data["token"]
    user_info = login_data["user"]
    
    print("✅ 登录成功")
    print(f"用户信息: {json.dumps(user_info, ensure_ascii=False, indent=2)}")
    
    # 2. 解析JWT token查看角色信息
    print("\n2. 解析JWT token...")
    try:
        import base64
        token_parts = token.split('.')
        if len(token_parts) >= 2:
            payload_encoded = token_parts[1]
            # 添加padding if needed
            payload_encoded += '=' * (4 - len(payload_encoded) % 4)
            payload_decoded = base64.b64decode(payload_encoded)
            payload_json = json.loads(payload_decoded)
            print(f"JWT Token内容:")
            print(f"  用户ID: {payload_json.get('sub')}")
            print(f"  用户名: {payload_json.get('username')}")
            print(f"  角色: {payload_json.get('roles', [])}")
            print(f"  技能: {payload_json.get('skills', [])}")
            print(f"  过期时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(payload_json.get('exp', 0)))}")
            
            # 检查角色是否正确
            if 'process_engineer' in payload_json.get('roles', []):
                print("✅ JWT Token中包含process_engineer角色")
            else:
                print("❌ JWT Token中不包含process_engineer角色")
                print("这可能是问题的原因！")
    except Exception as e:
        print(f"JWT解析失败: {e}")
    
    # 3. 测试零件创建
    print("\n3. 测试零件创建...")
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    part_data = {
        "part_number": f"FIX-TEST-{int(time.time())}",
        "part_name": "修复测试零件",
        "version": "1.0",
        "specifications": "用于修复前端认证问题的测试零件"
    }
    
    create_response = requests.post(f"{BASE_URL}/parts", 
                                  headers=headers, 
                                  json=part_data)
    
    print(f"零件创建状态: {create_response.status_code}")
    
    if create_response.status_code in [200, 201]:
        result = create_response.json()
        print("✅ 后端零件创建成功!")
        print(json.dumps(result, ensure_ascii=False, indent=2))
    else:
        print("❌ 后端零件创建失败!")
        print(f"错误响应: {create_response.text}")
    
    # 4. 提供解决方案
    print("\n4. 解决方案建议:")
    print("如果后端创建成功但前端失败，问题在于前端token缓存:")
    print("解决方法:")
    print("1. 清除浏览器localStorage和sessionStorage")
    print("2. 重新登录gongyi用户")
    print("3. 或者在前端执行以下JavaScript代码:")
    print("   localStorage.clear();")
    print("   sessionStorage.clear();")
    print("   window.location.reload();")
    
    # 5. 生成新的token供前端使用
    print("\n5. 生成新token供前端使用:")
    print(f"新Token: {token}")
    print("可以在浏览器控制台执行:")
    print(f"localStorage.setItem('token', '{token}');")
    print("localStorage.removeItem('auth-storage');")
    print("window.location.reload();")
    
    return 0

if __name__ == "__main__":
    exit(main())
