#!/usr/bin/env python3
"""
系统状态检查脚本
"""

import requests
import json
import sys

# 配置
BACKEND_URL = "http://localhost:9001"
FRONTEND_URL = "http://localhost:3000"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "admin123"

def test_backend_health():
    """测试后端健康状态"""
    print("🔍 检查后端服务状态...")
    try:
        response = requests.get(f"{BACKEND_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正常运行")
            return True
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端服务连接失败: {e}")
        return False

def test_frontend_health():
    """测试前端服务状态"""
    print("🔍 检查前端服务状态...")
    try:
        response = requests.get(FRONTEND_URL, timeout=5)
        if response.status_code == 200 and "MES" in response.text:
            print("✅ 前端服务正常运行")
            return True
        else:
            print(f"❌ 前端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端服务连接失败: {e}")
        return False

def test_authentication():
    """测试认证功能"""
    print("🔍 检查认证功能...")
    try:
        login_data = {
            "username": ADMIN_USERNAME,
            "password": ADMIN_PASSWORD
        }
        response = requests.post(f"{BACKEND_URL}/api/auth/login", json=login_data, timeout=5)
        if response.status_code == 200:
            data = response.json()
            if "token" in data:
                print("✅ 认证功能正常")
                return data["token"]
            else:
                print("❌ 认证响应格式异常")
                return None
        else:
            print(f"❌ 认证失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 认证请求失败: {e}")
        return None

def test_api_endpoints(token):
    """测试主要API端点"""
    print("🔍 检查主要API端点...")
    headers = {"Authorization": f"Bearer {token}"}
    
    endpoints = [
        ("/api/auth/me", "用户信息"),
        ("/api/parts", "零件管理"),
        ("/api/auth/roles", "角色管理"),
        ("/api/permissions", "权限管理"),
    ]
    
    success_count = 0
    for endpoint, name in endpoints:
        try:
            response = requests.get(f"{BACKEND_URL}{endpoint}", headers=headers, timeout=5)
            if response.status_code == 200:
                print(f"✅ {name} API正常")
                success_count += 1
            else:
                print(f"❌ {name} API异常: {response.status_code}")
        except Exception as e:
            print(f"❌ {name} API请求失败: {e}")
    
    return success_count == len(endpoints)

def test_database_connection():
    """测试数据库连接"""
    print("🔍 检查数据库连接...")
    try:
        # 通过API间接测试数据库连接
        response = requests.get(f"{BACKEND_URL}/api/auth/roles", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if "roles" in data and len(data["roles"]) > 0:
                print(f"✅ 数据库连接正常 (找到 {len(data['roles'])} 个角色)")
                return True
            else:
                print("❌ 数据库数据异常")
                return False
        else:
            print(f"❌ 数据库连接测试失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 数据库连接测试异常: {e}")
        return False

def test_permission_system(token):
    """测试权限系统"""
    print("🔍 检查权限系统...")
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # 获取角色权限
        response = requests.get(f"{BACKEND_URL}/api/roles/4/permissions", headers=headers, timeout=5)
        if response.status_code == 200:
            data = response.json()
            if "permissions" in data:
                granted_count = len([p for p in data["permissions"] if p["granted"]])
                total_count = len(data["permissions"])
                print(f"✅ 权限系统正常 (operator角色: {granted_count}/{total_count} 权限)")
                return True
            else:
                print("❌ 权限数据格式异常")
                return False
        else:
            print(f"❌ 权限系统测试失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 权限系统测试异常: {e}")
        return False

def main():
    print("🚀 开始系统状态检查...\n")
    
    results = []
    
    # 1. 后端健康检查
    results.append(("后端服务", test_backend_health()))
    
    # 2. 前端健康检查
    results.append(("前端服务", test_frontend_health()))
    
    # 3. 认证功能检查
    token = test_authentication()
    results.append(("认证功能", token is not None))
    
    if token:
        # 4. API端点检查
        results.append(("API端点", test_api_endpoints(token)))
        
        # 5. 数据库连接检查
        results.append(("数据库连接", test_database_connection()))
        
        # 6. 权限系统检查
        results.append(("权限系统", test_permission_system(token)))
    
    # 输出总结
    print("\n" + "="*50)
    print("📊 系统状态总结:")
    print("="*50)
    
    success_count = 0
    for name, status in results:
        status_icon = "✅" if status else "❌"
        print(f"{status_icon} {name}: {'正常' if status else '异常'}")
        if status:
            success_count += 1
    
    print(f"\n总体状态: {success_count}/{len(results)} 项检查通过")
    
    if success_count == len(results):
        print("🎉 系统运行完全正常！")
        return 0
    elif success_count >= len(results) * 0.8:
        print("⚠️  系统基本正常，但有部分问题需要关注")
        return 1
    else:
        print("🚨 系统存在严重问题，需要立即处理")
        return 2

if __name__ == "__main__":
    sys.exit(main())
