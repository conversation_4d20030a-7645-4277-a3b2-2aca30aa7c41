#!/bin/bash

# MES系统快速优化脚本集合
# 使用方法: ./quick-optimization-scripts.sh [task_name]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查环境
check_environment() {
    log_info "检查环境依赖..."
    
    # 检查Rust
    if ! command -v cargo &> /dev/null; then
        log_error "Rust/Cargo 未安装"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v npm &> /dev/null; then
        log_error "Node.js/npm 未安装"
        exit 1
    fi
    
    # 检查PostgreSQL
    if ! command -v psql &> /dev/null; then
        log_error "PostgreSQL 客户端未安装"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 1. 修复班次管理系统
fix_shift_management() {
    log_info "开始修复班次管理系统..."
    
    # 备份当前配置
    cp Cargo.toml Cargo.toml.backup
    log_info "已备份 Cargo.toml"
    
    # 更新依赖版本
    log_info "更新BigDecimal依赖版本..."
    sed -i.bak 's/bigdecimal = "0.4.8"/bigdecimal = "0.3.1"/' Cargo.toml
    
    # 重新启用模块
    log_info "重新启用班次管理模块..."
    sed -i.bak 's|// pub mod shift; // Temporarily disabled|pub mod shift;|' src/models/mod.rs
    sed -i.bak 's|// pub mod shift_service; // Temporarily disabled|pub mod shift_service;|' src/services/mod.rs
    sed -i.bak 's|// pub mod shift; // Temporarily disabled|pub mod shift;|' src/handlers/mod.rs
    
    # 重新启用路由
    log_info "重新启用班次管理路由..."
    sed -i.bak 's|// TODO: Shift management routes (temporarily disabled due to type conflicts)|// Shift management routes|' src/main.rs
    sed -i.bak 's|// \.route("/api/shifts/templates", get(handlers::shift::get_shift_templates))|.route("/api/shifts/templates", get(handlers::shift::get_shift_templates))|' src/main.rs
    sed -i.bak 's|// \.route("/api/shifts/templates", post(handlers::shift::create_shift_template))|.route("/api/shifts/templates", post(handlers::shift::create_shift_template))|' src/main.rs
    
    # 重新启用计划任务服务中的班次功能
    log_info "重新启用计划任务服务中的班次功能..."
    sed -i.bak 's|// use crate::services::shift_service::ShiftService; // Temporarily disabled|use crate::services::shift_service::ShiftService;|' src/services/plan_task_service.rs
    sed -i.bak 's|// use crate::models::shift::{ShiftTemplate, PlanGroup}; // Temporarily disabled|use crate::models::shift::{ShiftTemplate, PlanGroup};|' src/services/plan_task_service.rs
    
    # 尝试编译
    log_info "尝试编译项目..."
    if cargo check; then
        log_success "班次管理系统修复成功！"
        
        # 清理备份文件
        find . -name "*.bak" -delete
        rm -f Cargo.toml.backup
        
        return 0
    else
        log_error "编译失败，恢复备份..."
        
        # 恢复备份
        mv Cargo.toml.backup Cargo.toml
        find . -name "*.bak" -exec sh -c 'mv "$1" "${1%.bak}"' _ {} \;
        
        return 1
    fi
}

# 2. 数据库索引优化
optimize_database() {
    log_info "开始数据库索引优化..."
    
    # 检查数据库连接
    if [ -z "$DATABASE_URL" ]; then
        log_error "DATABASE_URL 环境变量未设置"
        return 1
    fi
    
    # 创建索引
    log_info "创建性能优化索引..."
    psql "$DATABASE_URL" << 'EOF'
-- 项目相关索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_plan_tasks_project_id ON plan_tasks(project_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_work_orders_project_id ON work_orders(project_id);

-- 状态查询索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_plan_tasks_status ON plan_tasks(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_work_orders_status ON work_orders(status);

-- 时间范围查询索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_plan_tasks_dates ON plan_tasks(planned_start_date, planned_end_date);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_work_orders_dates ON work_orders(created_at, updated_at);

-- 用户相关索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_plan_tasks_assigned_to ON plan_tasks(assigned_to);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_work_orders_assigned_to ON work_orders(assigned_to);

-- 复合索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_plan_tasks_project_status ON plan_tasks(project_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_work_orders_project_status ON work_orders(project_id, status);

-- 分析表统计信息
ANALYZE plan_tasks;
ANALYZE work_orders;
ANALYZE projects;
ANALYZE users;

\echo '✅ 数据库优化完成'
EOF
    
    if [ $? -eq 0 ]; then
        log_success "数据库索引优化完成！"
        return 0
    else
        log_error "数据库索引优化失败"
        return 1
    fi
}

# 3. 前端性能优化
optimize_frontend() {
    log_info "开始前端性能优化..."
    
    cd frontend || {
        log_error "frontend 目录不存在"
        return 1
    }
    
    # 安装分析工具
    log_info "安装构建分析工具..."
    npm install --save-dev webpack-bundle-analyzer
    
    # 分析当前构建产物
    log_info "分析构建产物..."
    npm run build:prod
    
    # 检查构建产物大小
    BUNDLE_SIZE=$(du -sh dist/ | cut -f1)
    log_info "当前构建产物大小: $BUNDLE_SIZE"
    
    # 生成分析报告
    log_info "生成构建分析报告..."
    npx webpack-bundle-analyzer dist/assets/*.js --mode json --report bundle-analysis.json
    
    log_success "前端性能分析完成！"
    log_info "查看详细报告: npx webpack-bundle-analyzer dist/assets/*.js"
    
    cd ..
    return 0
}

# 4. 系统健康检查
health_check() {
    log_info "执行系统健康检查..."
    
    # 检查后端服务
    log_info "检查后端服务..."
    if curl -s http://localhost:9001/api/health > /dev/null; then
        log_success "后端服务运行正常"
    else
        log_warning "后端服务未运行"
    fi
    
    # 检查前端服务
    log_info "检查前端服务..."
    if curl -s http://localhost:3000 > /dev/null; then
        log_success "前端开发服务运行正常"
    else
        log_warning "前端开发服务未运行"
    fi
    
    if curl -s http://localhost:3080 > /dev/null; then
        log_success "前端生产服务运行正常"
    else
        log_warning "前端生产服务未运行"
    fi
    
    # 检查数据库连接
    log_info "检查数据库连接..."
    if [ -n "$DATABASE_URL" ] && psql "$DATABASE_URL" -c "SELECT 1;" > /dev/null 2>&1; then
        log_success "数据库连接正常"
    else
        log_warning "数据库连接失败"
    fi
    
    log_success "系统健康检查完成"
}

# 5. 完整优化流程
full_optimization() {
    log_info "开始完整优化流程..."
    
    check_environment
    
    log_info "步骤 1/4: 修复班次管理系统"
    if fix_shift_management; then
        log_success "班次管理系统修复完成"
    else
        log_error "班次管理系统修复失败，跳过后续步骤"
        return 1
    fi
    
    log_info "步骤 2/4: 数据库索引优化"
    if optimize_database; then
        log_success "数据库优化完成"
    else
        log_warning "数据库优化失败，继续后续步骤"
    fi
    
    log_info "步骤 3/4: 前端性能优化"
    if optimize_frontend; then
        log_success "前端优化完成"
    else
        log_warning "前端优化失败，继续后续步骤"
    fi
    
    log_info "步骤 4/4: 系统健康检查"
    health_check
    
    log_success "完整优化流程完成！"
}

# 显示帮助信息
show_help() {
    echo "MES系统快速优化脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [task_name]"
    echo ""
    echo "可用任务:"
    echo "  shift-fix      - 修复班次管理系统"
    echo "  db-optimize    - 数据库索引优化"
    echo "  frontend-opt   - 前端性能优化"
    echo "  health-check   - 系统健康检查"
    echo "  full           - 执行完整优化流程"
    echo "  help           - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 shift-fix"
    echo "  $0 full"
}

# 主函数
main() {
    case "${1:-help}" in
        "shift-fix")
            check_environment
            fix_shift_management
            ;;
        "db-optimize")
            check_environment
            optimize_database
            ;;
        "frontend-opt")
            check_environment
            optimize_frontend
            ;;
        "health-check")
            health_check
            ;;
        "full")
            full_optimization
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
