<!DOCTYPE html>
<html>
<head>
    <title>甘特图前端测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .error { color: red; }
        .success { color: green; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; max-height: 300px; }
        button { padding: 8px 16px; margin: 5px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #40a9ff; }
        .status { padding: 5px 10px; border-radius: 4px; margin: 5px 0; }
        .status.success { background: #f6ffed; border: 1px solid #b7eb8f; }
        .status.error { background: #fff2f0; border: 1px solid #ffccc7; }
        .status.info { background: #f0f9ff; border: 1px solid #91d5ff; }
    </style>
</head>
<body>
    <h1>MES系统甘特图前端测试</h1>
    
    <div class="test-section">
        <h2>1. 前端服务检查</h2>
        <button onclick="checkFrontend()">检查前端服务</button>
        <div id="frontend-status"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 登录并获取Token</h2>
        <button onclick="loginAndGetToken()">登录获取Token</button>
        <div id="login-status"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 测试甘特图数据API</h2>
        <button onclick="testGanttAPI()">测试甘特图API</button>
        <div id="gantt-api-status"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 模拟前端甘特图数据处理</h2>
        <button onclick="simulateGanttProcessing()">模拟数据处理</button>
        <div id="gantt-processing-status"></div>
    </div>
    
    <div class="test-section">
        <h2>5. 访问计划任务页面</h2>
        <p>请手动访问以下链接查看甘特图：</p>
        <a href="http://localhost:3000/plan-tasks" target="_blank" style="color: #1890ff; text-decoration: none; font-weight: bold;">
            http://localhost:3000/plan-tasks
        </a>
        <p style="color: #666; font-size: 14px;">
            点击链接后，在计划任务页面中找到"甘特图"标签页，查看任务是否正常显示。
        </p>
    </div>

    <script>
        const API_BASE = 'http://localhost:9000/api';
        let authToken = '';
        
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        async function checkFrontend() {
            try {
                const response = await fetch('http://localhost:3000');
                if (response.ok) {
                    showStatus('frontend-status', '✅ 前端服务正常运行', 'success');
                } else {
                    showStatus('frontend-status', `❌ 前端服务响应异常: ${response.status}`, 'error');
                }
            } catch (error) {
                showStatus('frontend-status', `❌ 前端服务连接失败: ${error.message}`, 'error');
            }
        }
        
        async function loginAndGetToken() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'admin', password: 'admin123' })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    authToken = data.token;
                    showStatus('login-status', `✅ 登录成功，Token: ${authToken.substring(0, 20)}...`, 'success');
                } else {
                    showStatus('login-status', `❌ 登录失败: ${response.status}`, 'error');
                }
            } catch (error) {
                showStatus('login-status', `❌ 登录请求失败: ${error.message}`, 'error');
            }
        }
        
        async function testGanttAPI() {
            if (!authToken) {
                showStatus('gantt-api-status', '❌ 请先登录获取Token', 'error');
                return;
            }
            
            try {
                const now = new Date();
                const startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                const endDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
                
                const response = await fetch(`${API_BASE}/planning/gantt?start_date=${startDate.toISOString()}&end_date=${endDate.toISOString()}`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    const taskCount = data.tasks ? data.tasks.length : 0;
                    const skillGroupCount = data.skill_groups ? data.skill_groups.length : 0;
                    
                    showStatus('gantt-api-status', 
                        `✅ 甘特图API调用成功<br>
                        📊 任务数量: ${taskCount}<br>
                        🏭 技能组数量: ${skillGroupCount}<br>
                        📅 时间范围: ${data.time_range?.start} 到 ${data.time_range?.end}<br>
                        <details style="margin-top: 10px;">
                            <summary>查看原始数据 (前3个任务)</summary>
                            <pre>${JSON.stringify(data.tasks?.slice(0, 3), null, 2)}</pre>
                        </details>`, 
                        'success'
                    );
                } else {
                    showStatus('gantt-api-status', `❌ 甘特图API调用失败: ${response.status}`, 'error');
                }
            } catch (error) {
                showStatus('gantt-api-status', `❌ 甘特图API请求失败: ${error.message}`, 'error');
            }
        }
        
        async function simulateGanttProcessing() {
            if (!authToken) {
                showStatus('gantt-processing-status', '❌ 请先登录并测试甘特图API', 'error');
                return;
            }
            
            try {
                // 获取甘特图数据
                const now = new Date();
                const startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                const endDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
                
                const response = await fetch(`${API_BASE}/planning/gantt?start_date=${startDate.toISOString()}&end_date=${endDate.toISOString()}`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                if (!response.ok) {
                    throw new Error(`API调用失败: ${response.status}`);
                }
                
                const data = await response.json();
                const tasks = data.tasks || [];
                
                // 模拟前端数据处理逻辑
                const processedTasks = tasks.map(task => {
                    // 检查是否是甘特图API格式
                    const isApiFormat = task.start && task.end && task.name;
                    
                    if (isApiFormat) {
                        return {
                            id: task.id,
                            name: task.name,
                            startDate: new Date(task.start),
                            endDate: new Date(task.end),
                            status: task.status,
                            progress: task.progress || 0,
                            assignee: task.machine_name ? `设备: ${task.machine_name}` : `技能组: ${task.skill_group_id}`,
                            duration: Math.round((new Date(task.end) - new Date(task.start)) / (1000 * 60 * 60)) + '小时'
                        };
                    } else {
                        return {
                            id: task.id,
                            name: `${task.process_name} - ${task.part_number}`,
                            startDate: new Date(task.planned_start),
                            endDate: new Date(task.planned_end),
                            status: task.status,
                            progress: 0,
                            assignee: task.skill_group_name || `技能组${task.skill_group_id}`,
                            duration: 'N/A'
                        };
                    }
                });
                
                const validTasks = processedTasks.filter(task => 
                    !isNaN(task.startDate.getTime()) && !isNaN(task.endDate.getTime())
                );
                
                showStatus('gantt-processing-status', 
                    `✅ 数据处理成功<br>
                    📊 原始任务数: ${tasks.length}<br>
                    ✅ 有效任务数: ${validTasks.length}<br>
                    📅 时间范围: ${validTasks.length > 0 ? 
                        `${validTasks[0].startDate.toLocaleDateString()} - ${validTasks[validTasks.length-1].endDate.toLocaleDateString()}` : 
                        '无数据'}<br>
                    <details style="margin-top: 10px;">
                        <summary>查看处理后的数据 (前3个任务)</summary>
                        <pre>${JSON.stringify(validTasks.slice(0, 3), null, 2)}</pre>
                    </details>`, 
                    'success'
                );
                
            } catch (error) {
                showStatus('gantt-processing-status', `❌ 数据处理失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动检查前端服务
        window.onload = function() {
            checkFrontend();
        };
    </script>
</body>
</html>
