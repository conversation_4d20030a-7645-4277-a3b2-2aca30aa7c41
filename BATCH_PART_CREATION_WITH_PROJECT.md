# 批量创建零件支持指定项目功能

## 📋 功能概述

现在批量创建零件功能已支持指定项目！您可以在批量导入零件时，同时将零件添加到指定项目的BOM中。

## ✅ 新增功能

### 1. **增强的导入模板**

#### 新的CSV模板格式
```csv
零件编号,零件名称,版本,规格说明,项目名称,BOM数量
P001,齿轮轴,1.0,材质：45钢，硬度：HRC45-50,示例项目1,2
P002,轴承座,1.0,材质：铸铁，表面处理：喷漆,示例项目1,1
P003,连接件,2.0,材质：不锈钢304，厚度：3mm,,
P004,密封圈,1.0,材质：橡胶，规格：Φ50×3,示例项目2,4
```

#### 字段说明
- **零件编号**: 必填，零件的唯一标识
- **零件名称**: 可选，零件的名称
- **版本**: 必填，零件的版本号
- **规格说明**: 可选，零件的详细规格
- **项目名称**: 可选，要关联的项目名称
- **BOM数量**: 可选，在项目BOM中的数量（仅当指定项目时有效）

### 2. **智能处理逻辑**

#### 零件创建逻辑
```rust
// 检查零件是否已存在
if existing_part {
    // 使用现有零件ID
    part_id = existing_part.id;
} else {
    // 创建新零件
    part_id = create_new_part();
}

// 如果指定了项目和BOM数量
if project_name && bom_quantity {
    // 查找项目
    project = find_project_by_name(project_name);
    
    // 添加到项目BOM（如果不存在）
    if !bom_exists(project.id, part_id) {
        add_to_project_bom(project.id, part_id, bom_quantity);
    }
}
```

#### 处理规则
- ✅ **零件去重**: 相同编号+版本的零件不会重复创建
- ✅ **项目验证**: 项目名称必须存在，否则报错
- ✅ **BOM去重**: 相同零件不会重复添加到同一项目BOM
- ✅ **数量验证**: BOM数量必须大于0
- ✅ **可选关联**: 项目名称和BOM数量都是可选的

## 🎮 使用方法

### 1. **下载新模板**
1. 进入"零件管理"页面
2. 点击"导入数据"按钮
3. 点击"下载零件导入模板"
4. 获得包含项目字段的新模板

### 2. **填写导入数据**

#### 场景1：仅创建零件（不关联项目）
```csv
零件编号,零件名称,版本,规格说明,项目名称,BOM数量
P001,齿轮轴,1.0,材质：45钢，硬度：HRC45-50,,
```

#### 场景2：创建零件并关联到项目
```csv
零件编号,零件名称,版本,规格说明,项目名称,BOM数量
P001,齿轮轴,1.0,材质：45钢，硬度：HRC45-50,示例项目1,2
```

#### 场景3：混合模式（部分关联项目）
```csv
零件编号,零件名称,版本,规格说明,项目名称,BOM数量
P001,齿轮轴,1.0,材质：45钢，硬度：HRC45-50,示例项目1,2
P002,轴承座,1.0,材质：铸铁，表面处理：喷漆,,
P003,连接件,2.0,材质：不锈钢304，厚度：3mm,示例项目2,1
```

### 3. **执行导入**
1. 上传填写好的CSV文件
2. 预览数据确认无误
3. 执行导入
4. 查看导入结果

## 🎯 功能优势

### 1. **一站式操作**
- ✅ **一次导入**: 同时完成零件创建和项目关联
- ✅ **批量处理**: 支持大量零件的批量操作
- ✅ **智能去重**: 自动处理重复零件和BOM项

### 2. **灵活性**
- ✅ **可选关联**: 可以选择性地将部分零件关联到项目
- ✅ **多项目支持**: 不同零件可以关联到不同项目
- ✅ **向后兼容**: 现有的零件导入流程完全兼容

### 3. **数据完整性**
- ✅ **项目验证**: 确保项目存在才能关联
- ✅ **数量验证**: 确保BOM数量合理
- ✅ **重复检查**: 避免重复创建和关联

## 🧪 使用示例

### 示例1：为新项目批量创建零件
```csv
零件编号,零件名称,版本,规格说明,项目名称,BOM数量
GEAR001,主齿轮,1.0,模数2.5，齿数40,新产品开发,1
SHAFT001,主轴,1.0,材质45钢，长度200mm,新产品开发,1
BEARING001,轴承,1.0,型号6205，精度P5,新产品开发,2
SEAL001,密封圈,1.0,材质NBR，硬度70,新产品开发,4
```

### 示例2：现有零件关联到新项目
```csv
零件编号,零件名称,版本,规格说明,项目名称,BOM数量
STD001,标准螺栓,1.0,M8×25，材质304,客户定制项目,8
STD002,标准垫片,1.0,Φ8×16×1.5,客户定制项目,8
CUSTOM001,定制件A,1.0,按客户图纸加工,客户定制项目,1
```

## 🔧 技术实现

### 后端逻辑增强
```rust
async fn insert_part_record(&self, row_data: &HashMap<String, String>) -> Result<(), String> {
    // 1. 创建或获取零件ID
    let part_id = create_or_get_part(part_data)?;
    
    // 2. 处理项目关联（如果提供）
    if let (Some(project_name), Some(bom_quantity)) = (project_name, bom_quantity) {
        // 验证项目存在
        let project = find_project_by_name(project_name)?;
        
        // 添加到BOM（如果不存在）
        if !bom_exists(project.id, part_id) {
            add_to_project_bom(project.id, part_id, bom_quantity)?;
        }
    }
    
    Ok(())
}
```

### 数据流程
```
CSV文件 → 解析行数据 → 创建/获取零件 → 验证项目 → 添加BOM关联 → 完成
```

## 📊 导入结果

### 成功消息示例
- "导入成功！共导入 10 条零件记录，其中 6 条已关联到项目BOM"
- "零件 P001 已存在，已添加到项目 '示例项目1' 的BOM中"

### 错误处理
- "项目不存在: 未知项目" - 项目名称不存在
- "BOM数量格式错误: abc" - 数量不是有效数字
- "BOM数量必须大于0" - 数量为0或负数

## 🚀 立即使用

### 验证步骤
1. **访问** http://localhost:3001
2. **进入**"零件管理"页面
3. **点击**"导入数据"按钮
4. **下载**新的零件导入模板
5. **填写**包含项目信息的数据
6. **执行**导入并查看结果

### 注意事项
- 确保项目名称在系统中已存在
- BOM数量必须是正整数
- 项目名称和BOM数量必须同时提供或同时为空
- 相同零件不会重复添加到同一项目BOM

---

*现在您可以通过批量导入同时完成零件创建和项目关联，大大提高工作效率！*
