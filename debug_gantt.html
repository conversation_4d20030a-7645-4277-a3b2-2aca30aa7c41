<!DOCTYPE html>
<html>
<head>
    <title>甘特图调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .error { color: red; }
        .success { color: green; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>MES系统甘特图调试</h1>
    
    <div class="debug-section">
        <h2>1. 检查后端API连接</h2>
        <button onclick="checkBackendHealth()">检查后端健康状态</button>
        <div id="backend-status"></div>
    </div>
    
    <div class="debug-section">
        <h2>2. 检查认证状态</h2>
        <button onclick="checkAuth()">检查认证</button>
        <div id="auth-status"></div>
    </div>
    
    <div class="debug-section">
        <h2>3. 获取计划任务数据</h2>
        <button onclick="fetchPlanTasks()">获取计划任务</button>
        <div id="plan-tasks-data"></div>
    </div>
    
    <div class="debug-section">
        <h2>4. 测试甘特图数据转换</h2>
        <button onclick="testGanttConversion()">测试数据转换</button>
        <div id="gantt-conversion"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:9000/api';
        
        async function checkBackendHealth() {
            const statusDiv = document.getElementById('backend-status');
            try {
                const response = await fetch('http://localhost:9000/health');
                if (response.ok) {
                    const text = await response.text();
                    statusDiv.innerHTML = `<span class="success">✅ 后端健康: ${text}</span>`;
                } else {
                    statusDiv.innerHTML = `<span class="error">❌ 后端响应错误: ${response.status}</span>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<span class="error">❌ 后端连接失败: ${error.message}</span>`;
            }
        }
        
        async function checkAuth() {
            const statusDiv = document.getElementById('auth-status');
            const token = localStorage.getItem('token');
            
            if (!token) {
                statusDiv.innerHTML = '<span class="error">❌ 未找到认证令牌，请先登录</span>';
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/auth/me`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const user = await response.json();
                    statusDiv.innerHTML = `<span class="success">✅ 认证成功: ${user.username} (${user.roles?.join(', ')})</span>`;
                } else {
                    statusDiv.innerHTML = `<span class="error">❌ 认证失败: ${response.status}</span>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<span class="error">❌ 认证检查失败: ${error.message}</span>`;
            }
        }
        
        async function fetchPlanTasks() {
            const dataDiv = document.getElementById('plan-tasks-data');
            const token = localStorage.getItem('token');
            
            if (!token) {
                dataDiv.innerHTML = '<span class="error">❌ 请先检查认证状态</span>';
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/plan-tasks`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    const tasks = data.plan_tasks || data;
                    
                    if (Array.isArray(tasks) && tasks.length > 0) {
                        dataDiv.innerHTML = `
                            <span class="success">✅ 获取到 ${tasks.length} 个计划任务</span>
                            <pre>${JSON.stringify(tasks.slice(0, 2), null, 2)}</pre>
                            ${tasks.length > 2 ? '<p>... (显示前2个任务)</p>' : ''}
                        `;
                    } else {
                        dataDiv.innerHTML = '<span class="info">ℹ️ 暂无计划任务数据</span>';
                    }
                } else {
                    const errorText = await response.text();
                    dataDiv.innerHTML = `<span class="error">❌ API请求失败: ${response.status}<br><pre>${errorText}</pre></span>`;
                }
            } catch (error) {
                dataDiv.innerHTML = `<span class="error">❌ 请求失败: ${error.message}</span>`;
            }
        }
        
        function testGanttConversion() {
            const conversionDiv = document.getElementById('gantt-conversion');
            
            // 模拟一个计划任务数据
            const mockTask = {
                id: 1,
                process_name: '铣削',
                part_number: 'P001',
                planned_start: new Date().toISOString(),
                planned_end: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
                status: 'pending',
                skill_group_name: '机械加工组'
            };
            
            try {
                // 模拟甘特图数据转换
                const ganttTask = {
                    id: mockTask.id,
                    name: `${mockTask.process_name} - ${mockTask.part_number}`,
                    startDate: new Date(mockTask.planned_start),
                    endDate: new Date(mockTask.planned_end),
                    status: mockTask.status,
                    progress: 0,
                    assignee: mockTask.skill_group_name,
                };
                
                conversionDiv.innerHTML = `
                    <span class="success">✅ 数据转换成功</span>
                    <h4>原始数据:</h4>
                    <pre>${JSON.stringify(mockTask, null, 2)}</pre>
                    <h4>转换后的甘特图数据:</h4>
                    <pre>${JSON.stringify(ganttTask, null, 2)}</pre>
                `;
            } catch (error) {
                conversionDiv.innerHTML = `<span class="error">❌ 数据转换失败: ${error.message}</span>`;
            }
        }
        
        // 页面加载时自动检查
        window.onload = function() {
            checkBackendHealth();
        };
    </script>
</body>
</html>
