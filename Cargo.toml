[package]
name = "mes-system"
version = "0.1.0"
edition = "2021"

[dependencies]
# Web framework
axum = { version = "0.7", features = ["multipart"] }
tokio = { version = "1.0", features = ["full"] }
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "fs"] }

# Database
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid", "json", "bigdecimal"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Authentication & Security
jsonwebtoken = "9.2"
bcrypt = "0.15"

# Time handling
chrono = { version = "0.4", features = ["serde"] }

# UUID
uuid = { version = "1.0", features = ["v4", "serde"] }

# Environment variables
dotenvy = "0.15"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Regular expressions
regex = "1.0"

# Static variables
lazy_static = "1.4"

# Validation
validator = { version = "0.18", features = ["derive"] }

# Decimal numbers
rust_decimal = { version = "1.33", features = ["serde"] }
bigdecimal = { version = "0.3.1", features = ["serde"] }

# CSV processing
csv = "1.3"

# Redis caching
redis = { version = "0.24", features = ["tokio-comp", "connection-manager"] }


