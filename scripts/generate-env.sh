#!/bin/bash

# 环境配置生成脚本
# 用于生成安全的环境配置文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 生成随机密码
generate_password() {
    local length=${1:-32}
    openssl rand -base64 $length | tr -d "=+/" | cut -c1-$length
}

# 生成JWT密钥
generate_jwt_secret() {
    openssl rand -base64 64 | tr -d "=+/" | cut -c1-50
}

# 创建.env文件
create_env_file() {
    local env_file=${1:-.env}
    local environment=${2:-development}
    
    log_info "创建环境配置文件: $env_file"
    
    # 生成密码
    local db_password=$(generate_password 25)
    local redis_password=$(generate_password 25)
    local jwt_secret=$(generate_jwt_secret)
    
    cat > $env_file << EOF
# MES系统环境配置 - $environment
# 自动生成于: $(date)

# ===========================================
# 数据库配置
# ===========================================
POSTGRES_DB=mes_db
POSTGRES_USER=mes_user
POSTGRES_PASSWORD=$db_password
DATABASE_URL=postgresql://mes_user:$db_password@localhost:5432/mes_db

# ===========================================
# Redis配置
# ===========================================
REDIS_PASSWORD=$redis_password
REDIS_URL=redis://:$redis_password@localhost:6379

# ===========================================
# 服务器配置
# ===========================================
SERVER_HOST=0.0.0.0
SERVER_PORT=9000
RUST_LOG=info

# ===========================================
# 安全配置
# ===========================================
JWT_SECRET=$jwt_secret
JWT_EXPIRATION=86400

# ===========================================
# 前端配置
# ===========================================
VITE_API_BASE_URL=http://localhost:9000/api
VITE_APP_TITLE=MES制造执行系统

# ===========================================
# 系统配置
# ===========================================
TIMEZONE=Asia/Shanghai
DEFAULT_LANGUAGE=zh-CN
NODE_ENV=$environment
RUST_ENV=$environment

# ===========================================
# IPv6配置
# ===========================================
ENABLE_IPV6=true

# ===========================================
# 移动设备配置
# ===========================================
MOBILE_CACHE_DURATION=3600
MOBILE_MAX_IMAGE_SIZE=2MB
EOF

    log_success "环境配置文件创建完成: $env_file"
    log_warning "请妥善保管密码信息！"
}

# 主函数
main() {
    echo "=================================="
    echo "🔐 MES系统环境配置生成器"
    echo "=================================="
    echo
    
    case ${1:-development} in
        "development"|"dev")
            create_env_file ".env" "development"
            ;;
        "production"|"prod")
            create_env_file ".env.production" "production"
            ;;
        "docker")
            create_env_file ".env.docker" "production"
            # 更新docker-compose.yml中的数据库主机
            sed -i 's/localhost:5432/postgres:5432/g' .env.docker
            sed -i 's/localhost:6379/redis:6379/g' .env.docker
            ;;
        "test")
            create_env_file ".env.test" "test"
            ;;
        *)
            log_error "未知环境: $1"
            echo "用法: $0 [development|production|docker|test]"
            exit 1
            ;;
    esac
}

# 运行主程序
main "$@"
