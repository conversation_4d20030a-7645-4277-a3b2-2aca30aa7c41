@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo MES系统 - Windows自动安装脚本
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo [警告] 建议以管理员身份运行此脚本以确保所有功能正常
    echo.
)

echo 此脚本将自动安装MES系统的所有依赖项
echo 包括: Rust, Node.js, PostgreSQL
echo.
set /p "confirm=是否继续安装? [y/N]: "
if /i not "!confirm!"=="y" (
    echo 安装已取消
    pause
    exit /b 0
)

echo.
echo [1/6] 检查系统环境...

:: 检查Windows版本
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo Windows版本: %VERSION%

:: 检查架构
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    set "ARCH=x64"
) else if "%PROCESSOR_ARCHITECTURE%"=="x86" (
    set "ARCH=x86"
) else (
    set "ARCH=%PROCESSOR_ARCHITECTURE%"
)
echo 系统架构: %ARCH%

echo.
echo [2/6] 检查并安装Rust...

:: 检查Rust是否已安装
where rustc >nul 2>&1
if %errorlevel% equ 0 (
    echo [✓] Rust已安装
    rustc --version
) else (
    echo [安装] 正在下载并安装Rust...
    echo 这可能需要几分钟时间...
    
    :: 下载rustup-init.exe
    powershell -Command "Invoke-WebRequest -Uri 'https://win.rustup.rs/x86_64' -OutFile 'rustup-init.exe'"
    if !errorlevel! neq 0 (
        echo [错误] 下载Rust安装程序失败
        echo 请手动访问 https://rustup.rs/ 下载安装
        pause
        exit /b 1
    )
    
    :: 运行安装程序
    rustup-init.exe -y --default-toolchain stable
    if !errorlevel! neq 0 (
        echo [错误] Rust安装失败
        pause
        exit /b 1
    )
    
    :: 刷新环境变量
    call "%USERPROFILE%\.cargo\env.bat"
    
    :: 清理安装文件
    del rustup-init.exe
    
    echo [✓] Rust安装完成
)

echo.
echo [3/6] 检查并安装Node.js...

:: 检查Node.js是否已安装
where node >nul 2>&1
if %errorlevel% equ 0 (
    echo [✓] Node.js已安装
    node --version
    npm --version
) else (
    echo [安装] 正在下载并安装Node.js...
    
    :: 下载Node.js LTS版本
    set "NODE_VERSION=20.10.0"
    set "NODE_URL=https://nodejs.org/dist/v%NODE_VERSION%/node-v%NODE_VERSION%-x64.msi"
    
    powershell -Command "Invoke-WebRequest -Uri '%NODE_URL%' -OutFile 'nodejs.msi'"
    if !errorlevel! neq 0 (
        echo [错误] 下载Node.js失败
        echo 请手动访问 https://nodejs.org/ 下载安装
        pause
        exit /b 1
    )
    
    :: 静默安装Node.js
    msiexec /i nodejs.msi /quiet /norestart
    if !errorlevel! neq 0 (
        echo [错误] Node.js安装失败
        pause
        exit /b 1
    )
    
    :: 清理安装文件
    del nodejs.msi
    
    :: 刷新PATH
    set "PATH=%PATH%;%ProgramFiles%\nodejs"
    
    echo [✓] Node.js安装完成
)

echo.
echo [4/6] 检查并安装PostgreSQL...

:: 检查PostgreSQL是否已安装
where psql >nul 2>&1
if %errorlevel% equ 0 (
    echo [✓] PostgreSQL已安装
    psql --version
) else (
    echo [安装] PostgreSQL需要手动安装
    echo.
    echo 请按照以下步骤安装PostgreSQL:
    echo 1. 访问 https://www.postgresql.org/download/windows/
    echo 2. 下载PostgreSQL 15或更高版本
    echo 3. 运行安装程序并记住设置的密码
    echo 4. 确保将PostgreSQL添加到系统PATH中
    echo.
    echo 安装完成后请重新运行此脚本
    pause
    exit /b 1
)

echo.
echo [5/6] 安装项目依赖...

:: 安装Rust依赖
echo [构建] 正在编译Rust后端...
cargo build --release
if %errorlevel% neq 0 (
    echo [错误] Rust后端编译失败
    pause
    exit /b 1
)
echo [✓] Rust后端编译完成

:: 安装前端依赖
echo [安装] 正在安装前端依赖...
cd frontend
npm install
if %errorlevel% neq 0 (
    echo [错误] 前端依赖安装失败
    pause
    exit /b 1
)
echo [✓] 前端依赖安装完成

:: 构建前端
echo [构建] 正在构建前端...
npm run build
if %errorlevel% neq 0 (
    echo [错误] 前端构建失败
    pause
    exit /b 1
)
echo [✓] 前端构建完成

cd ..

echo.
echo [6/6] 创建启动脚本...

:: 创建Windows服务脚本
echo [创建] 正在创建Windows启动脚本...

:: 安装sqlx-cli
echo [安装] 正在安装sqlx-cli...
cargo install sqlx-cli --no-default-features --features postgres
if %errorlevel% neq 0 (
    echo [警告] sqlx-cli安装失败，数据库迁移可能需要手动处理
)

echo [✓] 启动脚本创建完成

echo.
echo ========================================
echo MES系统安装完成！
echo ========================================
echo.
echo 下一步:
echo 1. 运行 init-database.bat 初始化数据库
echo 2. 运行 start_all.bat 启动系统
echo.
echo 系统访问地址:
echo   前端: http://localhost:3000
echo   后端API: http://localhost:8080
echo.
echo 默认管理员账户:
echo   用户名: admin
echo   密码: admin123
echo.
pause
