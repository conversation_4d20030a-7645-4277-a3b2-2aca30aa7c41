@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo MES系统 - Windows启动脚本
echo ========================================
echo.

:: 检查环境配置文件
if not exist ".env" (
    echo [错误] 未找到环境配置文件 .env
    echo 请先运行 init-database.bat 初始化数据库
    pause
    exit /b 1
)

:: 读取环境变量
for /f "usebackq tokens=1,2 delims==" %%a in (".env") do (
    if not "%%a"=="" if not "%%a:~0,1%"=="#" (
        set "%%a=%%b"
    )
)

echo 启动模式选择:
echo 1. 快速启动 (推荐)
echo 2. 开发模式启动
echo 3. 生产模式启动
echo 4. 仅启动后端
echo 5. 仅启动前端
echo.
set /p "choice=请选择启动模式 [1-5]: "

if "%choice%"=="1" goto :quick_start
if "%choice%"=="2" goto :dev_start
if "%choice%"=="3" goto :prod_start
if "%choice%"=="4" goto :backend_only
if "%choice%"=="5" goto :frontend_only

echo 无效选择，使用快速启动模式
goto :quick_start

:quick_start
echo.
echo [快速启动] 正在启动MES系统...
echo.

:: 检查PostgreSQL服务
echo [1/4] 检查PostgreSQL服务...
sc query postgresql-x64-15 >nul 2>&1
if %errorlevel% equ 0 (
    sc query postgresql-x64-15 | findstr "RUNNING" >nul
    if !errorlevel! neq 0 (
        echo [启动] 正在启动PostgreSQL服务...
        net start postgresql-x64-15
    )
    echo [✓] PostgreSQL服务运行中
) else (
    echo [警告] 未找到PostgreSQL服务，请确保PostgreSQL已安装并运行
)

:: 测试数据库连接
echo [2/4] 测试数据库连接...
psql "%DATABASE_URL%" -c "SELECT 1;" >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 无法连接到数据库
    echo 请检查数据库服务状态和连接配置
    pause
    exit /b 1
)
echo [✓] 数据库连接正常

:: 启动后端服务
echo [3/4] 启动后端服务...
if not exist "target\release\mes-system.exe" (
    echo [错误] 未找到后端可执行文件
    echo 请先运行 build_production.bat 构建系统
    pause
    exit /b 1
)

echo [启动] 正在启动后端服务 (端口: %SERVER_PORT%)...
start "MES后端服务" cmd /k "title MES后端服务 && target\release\mes-system.exe"

:: 等待后端启动
echo [等待] 等待后端服务启动...
timeout /t 5 /nobreak >nul

:: 检查后端是否启动成功
for /l %%i in (1,1,10) do (
    curl -s http://localhost:%SERVER_PORT%/ >nul 2>&1
    if !errorlevel! equ 0 (
        echo [✓] 后端服务启动成功
        goto :start_frontend
    )
    timeout /t 2 /nobreak >nul
)

echo [警告] 后端服务可能未完全启动，继续启动前端...

:start_frontend
:: 启动前端服务
echo [4/4] 启动前端服务...
if not exist "frontend\node_modules" (
    echo [错误] 前端依赖未安装
    echo 请先运行 install.bat 安装依赖
    pause
    exit /b 1
)

echo [启动] 正在启动前端服务 (端口: 3000)...
cd frontend
start "MES前端服务" cmd /k "title MES前端服务 && npm run dev"
cd ..

echo.
echo ========================================
echo MES系统启动完成！
echo ========================================
echo.
echo 服务状态:
echo   后端API: http://localhost:%SERVER_PORT%
echo   前端界面: http://localhost:3000
echo   网络访问: http://************:3000
echo.
echo 默认管理员账户:
echo   用户名: admin
echo   密码: admin123
echo.
echo 管理说明:
echo   - 关闭此窗口不会停止服务
echo   - 要停止服务请关闭对应的服务窗口
echo   - 或者运行 stop_all.bat 停止所有服务
echo.
goto :end

:dev_start
echo.
echo [开发模式] 正在启动MES系统...
echo.

:: 设置开发环境变量
set "RUST_LOG=debug"

echo [启动] 后端开发模式...
start "MES后端开发" cmd /k "title MES后端开发 && cargo run"

echo [启动] 前端开发模式...
cd frontend
start "MES前端开发" cmd /k "title MES前端开发 && npm run dev"
cd ..

echo [✓] 开发模式启动完成
goto :end

:prod_start
echo.
echo [生产模式] 正在启动MES系统...
echo.

:: 检查生产构建
if not exist "target\release\mes-system.exe" (
    echo [构建] 正在构建生产版本...
    cargo build --release
)

if not exist "frontend\dist" (
    echo [构建] 正在构建前端...
    cd frontend
    npm run build
    cd ..
)

:: 启动生产服务
echo [启动] 生产模式后端...
start "MES生产后端" cmd /k "title MES生产后端 && target\release\mes-system.exe"

echo [启动] 生产模式前端...
cd frontend
start "MES生产前端" cmd /k "title MES生产前端 && npm run preview"
cd ..

echo [✓] 生产模式启动完成
goto :end

:backend_only
echo.
echo [后端] 仅启动后端服务...
start "MES后端服务" cmd /k "title MES后端服务 && target\release\mes-system.exe"
echo [✓] 后端服务已启动
goto :end

:frontend_only
echo.
echo [前端] 仅启动前端服务...
cd frontend
start "MES前端服务" cmd /k "title MES前端服务 && npm run dev"
cd ..
echo [✓] 前端服务已启动
goto :end

:end
echo.
echo 按任意键退出...
pause >nul
