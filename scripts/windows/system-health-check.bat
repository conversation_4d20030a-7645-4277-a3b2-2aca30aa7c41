@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo MES系统 - 健康状态检查 v2.0
echo ========================================
echo.

echo 🏥 正在进行全面系统健康检查...
echo ========================================
echo.

set "health_score=0"
set "max_score=10"
set "warnings=0"
set "errors=0"

:: 1. 系统基础信息
echo [1/10] 📊 系统基础信息
echo ----------------------------------------
echo 🖥️  操作系统: %OS%
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo 📱 Windows版本: %VERSION%
echo 🏗️  系统架构: %PROCESSOR_ARCHITECTURE%
echo 💾 计算机名: %COMPUTERNAME%
echo 👤 当前用户: %USERNAME%
echo ✅ 系统信息收集完成
set /a "health_score+=1"
echo.

:: 2. 硬件资源检查
echo [2/10] 🔧 硬件资源检查
echo ----------------------------------------
:: 检查内存
for /f "skip=1" %%A in ('wmic computersystem get TotalPhysicalMemory /value') do (
    for /f "tokens=2 delims==" %%B in ("%%A") do (
        if not "%%B"=="" (
            set /a "memory_gb=%%B/1024/1024/1024"
            echo 💾 总内存: !memory_gb! GB
            if !memory_gb! geq 4 (
                echo ✅ 内存充足
            ) else (
                echo ⚠️  内存不足，建议至少4GB
                set /a "warnings+=1"
            )
        )
    )
)

:: 检查磁盘空间
for /f "skip=1" %%A in ('wmic logicaldisk where "DeviceID='C:'" get FreeSpace /value') do (
    for /f "tokens=2 delims==" %%B in ("%%A") do (
        if not "%%B"=="" (
            set /a "free_gb=%%B/1024/1024/1024"
            echo 💿 C盘可用空间: !free_gb! GB
            if !free_gb! geq 2 (
                echo ✅ 磁盘空间充足
                set /a "health_score+=1"
            ) else (
                echo ❌ 磁盘空间不足，需要至少2GB
                set /a "errors+=1"
            )
        )
    )
)
echo.

:: 3. 网络连接检查
echo [3/10] 🌐 网络连接检查
echo ----------------------------------------
ping -n 1 ******* >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 互联网连接正常
    set /a "health_score+=1"
) else (
    echo ⚠️  互联网连接异常，可能影响依赖下载
    set /a "warnings+=1"
)

:: 检查本地回环
ping -n 1 127.0.0.1 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 本地回环正常
) else (
    echo ❌ 本地回环异常
    set /a "errors+=1"
)
echo.

:: 4. 开发环境检查
echo [4/10] 🛠️  开发环境检查
echo ----------------------------------------
where rustc >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Rust 已安装
    rustc --version | findstr /C:"rustc"
    cargo --version | findstr /C:"cargo"
    set /a "health_score+=1"
) else (
    echo ❌ Rust 未安装
    echo    💡 运行 deploy_optimized.bat 自动安装
    set /a "errors+=1"
)

where node >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js 已安装
    node --version
    npm --version
    set /a "health_score+=1"
) else (
    echo ❌ Node.js 未安装
    echo    💡 运行 deploy_optimized.bat 自动安装
    set /a "errors+=1"
)
echo.

:: 5. 数据库检查
echo [5/10] 🗄️  数据库检查
echo ----------------------------------------
where psql >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ PostgreSQL 已安装
    psql --version
    
    :: 检查服务状态
    sc query postgresql-x64-15 >nul 2>&1
    if !errorlevel! equ 0 (
        sc query postgresql-x64-15 | findstr "RUNNING" >nul
        if !errorlevel! equ 0 (
            echo ✅ PostgreSQL 服务运行中
            set /a "health_score+=1"
        ) else (
            echo ⚠️  PostgreSQL 服务未运行
            echo    💡 运行: net start postgresql-x64-15
            set /a "warnings+=1"
        )
    ) else (
        echo ⚠️  PostgreSQL 服务未找到
        set /a "warnings+=1"
    )
) else (
    echo ❌ PostgreSQL 未安装
    echo    💡 手动安装: https://www.postgresql.org/download/windows/
    set /a "errors+=1"
)
echo.

:: 6. 项目文件检查
echo [6/10] 📁 项目文件检查
echo ----------------------------------------
if exist "Cargo.toml" (
    echo ✅ 后端项目文件存在
    set /a "health_score+=1"
) else (
    echo ❌ 未找到 Cargo.toml
    set /a "errors+=1"
)

if exist "frontend\package.json" (
    echo ✅ 前端项目文件存在
) else (
    echo ❌ 未找到前端项目
    set /a "errors+=1"
)

if exist ".env" (
    echo ✅ 环境配置文件存在
) else (
    echo ⚠️  环境配置文件不存在
    echo    💡 运行: init-database.bat
    set /a "warnings+=1"
)
echo.

:: 7. 构建状态检查
echo [7/10] 🏗️  构建状态检查
echo ----------------------------------------
if exist "target\release\mes-system.exe" (
    echo ✅ 后端已构建
    for %%A in ("target\release\mes-system.exe") do (
        set "size=%%~zA"
        set /a "size_mb=!size!/1024/1024"
        echo    📦 大小: !size_mb! MB
        echo    📅 修改时间: %%~tA
    )
    set /a "health_score+=1"
) else (
    echo ⚠️  后端未构建
    echo    💡 运行: build_production.bat
    set /a "warnings+=1"
)

if exist "frontend\dist" (
    echo ✅ 前端已构建
    for /f %%A in ('dir /b /s "frontend\dist" 2^>nul ^| find /c /v ""') do set "file_count=%%A"
    echo    📁 文件数量: !file_count!
) else (
    echo ⚠️  前端未构建
    echo    💡 运行: build_production.bat
    set /a "warnings+=1"
)
echo.

:: 8. 端口状态检查
echo [8/10] 🔌 端口状态检查
echo ----------------------------------------
netstat -ano | findstr ":9000" >nul
if %errorlevel% equ 0 (
    echo ⚠️  端口 9000 被占用
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":9000"') do (
        for /f "skip=3 tokens=1" %%b in ('tasklist /fi "PID eq %%a" /fo table /nh 2^>nul') do (
            echo    🔒 进程: %%b (PID: %%a)
        )
    )
    set /a "warnings+=1"
) else (
    echo ✅ 端口 9000 可用
    set /a "health_score+=1"
)

netstat -ano | findstr ":3000" >nul
if %errorlevel% equ 0 (
    echo ⚠️  端口 3000 被占用
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":3000"') do (
        for /f "skip=3 tokens=1" %%b in ('tasklist /fi "PID eq %%a" /fo table /nh 2^>nul') do (
            echo    🔒 进程: %%b (PID: %%a)
        )
    )
    set /a "warnings+=1"
) else (
    echo ✅ 端口 3000 可用
)
echo.

:: 9. 数据库连接测试
echo [9/10] 🔗 数据库连接测试
echo ----------------------------------------
if exist ".env" (
    for /f "usebackq tokens=1,2 delims==" %%a in (".env") do (
        if "%%a"=="DATABASE_URL" set "DATABASE_URL=%%b"
    )
    
    if defined DATABASE_URL (
        psql "!DATABASE_URL!" -c "SELECT version();" >nul 2>&1
        if !errorlevel! equ 0 (
            echo ✅ 数据库连接正常
            
            :: 检查表数量
            for /f %%A in ('psql "!DATABASE_URL!" -tAc "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';" 2^>nul') do set "table_count=%%A"
            if defined table_count (
                echo    📊 数据表数量: !table_count!
                if !table_count! gtr 0 (
                    set /a "health_score+=1"
                ) else (
                    echo    ⚠️  数据库为空，可能需要运行迁移
                    set /a "warnings+=1"
                )
            )
        ) else (
            echo ❌ 数据库连接失败
            echo    💡 检查数据库服务和配置
            set /a "errors+=1"
        )
    ) else (
        echo ⚠️  未找到数据库配置
        set /a "warnings+=1"
    )
) else (
    echo ⚠️  环境配置文件不存在
    set /a "warnings+=1"
)
echo.

:: 10. 服务状态检查
echo [10/10] 🚀 服务状态检查
echo ----------------------------------------
curl -s http://localhost:8080/ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 后端服务运行中
    set /a "health_score+=1"
) else (
    echo ⚠️  后端服务未运行
    echo    💡 运行: start_all.bat
)

curl -s http://localhost:3000/ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 前端服务运行中
) else (
    echo ⚠️  前端服务未运行
    echo    💡 运行: start_all.bat
)
echo.

:: 健康评分计算
echo ========================================
echo 🏥 系统健康报告
echo ========================================
echo.

set /a "health_percentage=health_score*100/max_score"
echo 📊 健康评分: !health_score!/!max_score! (!health_percentage!%%)
echo ⚠️  警告数量: !warnings!
echo ❌ 错误数量: !errors!
echo.

if !health_percentage! geq 80 (
    echo 🎉 系统状态: 优秀
    echo    系统运行良好，可以正常使用
) else if !health_percentage! geq 60 (
    echo 😊 系统状态: 良好  
    echo    系统基本正常，建议处理警告项
) else if !health_percentage! geq 40 (
    echo 😐 系统状态: 一般
    echo    存在一些问题，建议修复后使用
) else (
    echo 😞 系统状态: 需要修复
    echo    存在严重问题，请先修复再使用
)

echo.
echo 💡 建议操作:
if !errors! gtr 0 (
    echo   • 运行 deploy_optimized.bat 修复环境问题
    echo   • 运行 init-database.bat 初始化数据库
)
if !warnings! gtr 0 (
    echo   • 运行 build_production.bat 构建项目
    echo   • 运行 start_all.bat 启动服务
)
if !health_percentage! geq 80 (
    echo   • 系统状态良好，可以开始使用
    echo   • 访问 http://localhost:3000 使用系统
)

echo.
echo 📞 获取帮助:
echo   • 查看 INSTALLATION_GUIDE_OPTIMIZED.md
echo   • 运行 deploy_optimized.bat 重新部署
echo   • 检查项目文档和 README.md
echo.
pause
