@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo MES系统 - Windows Docker安装脚本
echo ========================================
echo.

:: 检查Docker是否安装
where docker >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Docker
    echo 请先安装Docker Desktop for Windows
    echo 下载地址: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

:: 检查Docker是否运行
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Docker服务未运行
    echo 请启动Docker Desktop
    pause
    exit /b 1
)

echo [✓] Docker环境检查通过
echo.

echo Docker部署选项:
echo 1. 完整部署 (数据库 + 后端 + 前端)
echo 2. 仅部署数据库
echo 3. 仅部署应用 (需要外部数据库)
echo 4. 开发模式部署
echo.
set /p "choice=请选择部署选项 [1-4]: "

if "%choice%"=="1" goto :full_deploy
if "%choice%"=="2" goto :db_only
if "%choice%"=="3" goto :app_only
if "%choice%"=="4" goto :dev_deploy

echo 无效选择，使用完整部署
goto :full_deploy

:full_deploy
echo.
echo [完整部署] 正在部署MES系统...

:: 创建Docker Compose文件
echo [创建] 正在创建Docker配置文件...
(
echo version: '3.8'
echo.
echo services:
echo   postgres:
echo     image: postgres:15
echo     container_name: mes-postgres
echo     environment:
echo       POSTGRES_DB: mes_db
echo       POSTGRES_USER: postgres
echo       POSTGRES_PASSWORD: password
echo     ports:
echo       - "5432:5432"
echo     volumes:
echo       - postgres_data:/var/lib/postgresql/data
echo       - ./migrations:/docker-entrypoint-initdb.d
echo     networks:
echo       - mes-network
echo.
echo   backend:
echo     build:
echo       context: .
echo       dockerfile: Dockerfile.backend
echo     container_name: mes-backend
echo     environment:
echo       DATABASE_URL: ********************************************/mes_db
echo       SERVER_HOST: 0.0.0.0
echo       SERVER_PORT: 8080
echo       JWT_SECRET: mes-system-jwt-secret-key-development-only
echo       RUST_LOG: info
echo     ports:
echo       - "8080:8080"
echo     depends_on:
echo       - postgres
echo     networks:
echo       - mes-network
echo.
echo   frontend:
echo     build:
echo       context: ./frontend
echo       dockerfile: Dockerfile
echo     container_name: mes-frontend
echo     ports:
echo       - "3000:3000"
echo     depends_on:
echo       - backend
echo     networks:
echo       - mes-network
echo.
echo volumes:
echo   postgres_data:
echo.
echo networks:
echo   mes-network:
echo     driver: bridge
) > docker-compose.yml

:: 创建后端Dockerfile
echo [创建] 正在创建后端Dockerfile...
(
echo FROM rust:1.70 as builder
echo.
echo WORKDIR /app
echo COPY Cargo.toml Cargo.lock ./
echo COPY src ./src
echo COPY migrations ./migrations
echo.
echo RUN cargo build --release
echo.
echo FROM debian:bookworm-slim
echo.
echo RUN apt-get update ^&^& apt-get install -y \
echo     ca-certificates \
echo     libssl3 \
echo     libpq5 \
echo     ^&^& rm -rf /var/lib/apt/lists/*
echo.
echo WORKDIR /app
echo COPY --from=builder /app/target/release/mes-system .
echo COPY --from=builder /app/migrations ./migrations
echo.
echo EXPOSE 8080
echo.
echo CMD ["./mes-system"]
) > Dockerfile.backend

:: 创建前端Dockerfile
echo [创建] 正在创建前端Dockerfile...
(
echo FROM node:18-alpine as builder
echo.
echo WORKDIR /app
echo COPY package*.json ./
echo RUN npm ci --only=production
echo.
echo COPY . .
echo RUN npm run build
echo.
echo FROM nginx:alpine
echo.
echo COPY --from=builder /app/dist /usr/share/nginx/html
echo COPY nginx.conf /etc/nginx/nginx.conf
echo.
echo EXPOSE 3000
echo.
echo CMD ["nginx", "-g", "daemon off;"]
) > frontend/Dockerfile

:: 创建nginx配置
echo [创建] 正在创建nginx配置...
(
echo events {
echo     worker_connections 1024;
echo }
echo.
echo http {
echo     include       /etc/nginx/mime.types;
echo     default_type  application/octet-stream;
echo.
echo     server {
echo         listen 3000;
echo         server_name localhost;
echo.
echo         location / {
echo             root   /usr/share/nginx/html;
echo             index  index.html index.htm;
echo             try_files $uri $uri/ /index.html;
echo         }
echo.
echo         location /api/ {
echo             proxy_pass http://backend:8080/api/;
echo             proxy_set_header Host $host;
echo             proxy_set_header X-Real-IP $remote_addr;
echo             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
echo             proxy_set_header X-Forwarded-Proto $scheme;
echo         }
echo     }
echo }
) > frontend/nginx.conf

echo [构建] 正在构建和启动容器...
docker-compose up -d --build

if %errorlevel% neq 0 (
    echo [错误] Docker部署失败
    pause
    exit /b 1
)

echo [✓] 完整部署完成
goto :end

:db_only
echo.
echo [数据库] 仅部署PostgreSQL数据库...

docker run -d \
    --name mes-postgres \
    -e POSTGRES_DB=mes_db \
    -e POSTGRES_USER=postgres \
    -e POSTGRES_PASSWORD=password \
    -p 5432:5432 \
    -v postgres_data:/var/lib/postgresql/data \
    postgres:15

if %errorlevel% neq 0 (
    echo [错误] 数据库部署失败
    pause
    exit /b 1
)

echo [✓] 数据库部署完成
goto :end

:app_only
echo.
echo [应用] 仅部署应用服务...

set /p "db_host=数据库主机 [localhost]: "
if "!db_host!"=="" set "db_host=localhost"

set /p "db_port=数据库端口 [5432]: "
if "!db_port!"=="" set "db_port=5432"

set /p "db_name=数据库名称 [mes_db]: "
if "!db_name!"=="" set "db_name=mes_db"

set /p "db_user=数据库用户 [postgres]: "
if "!db_user!"=="" set "db_user=postgres"

set /p "db_password=数据库密码: "

set "DATABASE_URL=postgresql://!db_user!:!db_password!@!db_host!:!db_port!/!db_name!"

:: 构建并运行后端
docker build -t mes-backend -f Dockerfile.backend .
docker run -d \
    --name mes-backend \
    -e DATABASE_URL=!DATABASE_URL! \
    -e SERVER_HOST=0.0.0.0 \
    -e SERVER_PORT=8080 \
    -e JWT_SECRET=mes-system-jwt-secret-key-development-only \
    -e RUST_LOG=info \
    -p 8080:8080 \
    mes-backend

:: 构建并运行前端
cd frontend
docker build -t mes-frontend .
docker run -d \
    --name mes-frontend \
    -p 3000:3000 \
    mes-frontend
cd ..

echo [✓] 应用部署完成
goto :end

:dev_deploy
echo.
echo [开发] 开发模式部署...

:: 使用开发配置
set "RUST_LOG=debug"

:: 启动开发环境
docker-compose -f docker-compose.dev.yml up -d --build

echo [✓] 开发模式部署完成
goto :end

:end
echo.
echo ========================================
echo Docker部署完成！
echo ========================================
echo.
echo 容器状态:
docker ps --filter "name=mes-"

echo.
echo 访问地址:
echo   前端: http://localhost:3000
echo   后端API: http://localhost:8080
echo   数据库: localhost:5432
echo.
echo 管理命令:
echo   docker-compose logs     - 查看日志
echo   docker-compose stop     - 停止服务
echo   docker-compose restart  - 重启服务
echo   docker-compose down     - 删除容器
echo.
pause
