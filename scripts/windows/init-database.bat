@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo MES系统 - Windows数据库初始化脚本
echo ========================================
echo.

:: 检查PostgreSQL是否安装
where psql >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到PostgreSQL命令行工具 psql
    echo 请确保PostgreSQL已安装并添加到系统PATH中
    echo 下载地址: https://www.postgresql.org/download/windows/
    pause
    exit /b 1
)

:: 设置默认值
set "DB_HOST=localhost"
set "DB_PORT=5432"
set "DB_NAME=mes_db"
set "DB_USER=postgres"
set "DB_PASSWORD="

echo 请输入数据库连接信息 (直接回车使用默认值):
echo.

:: 获取数据库主机
set /p "input_host=数据库主机 [%DB_HOST%]: "
if not "!input_host!"=="" set "DB_HOST=!input_host!"

:: 获取数据库端口
set /p "input_port=数据库端口 [%DB_PORT%]: "
if not "!input_port!"=="" set "DB_PORT=!input_port!"

:: 获取数据库名称
set /p "input_name=数据库名称 [%DB_NAME%]: "
if not "!input_name!"=="" set "DB_NAME=!input_name!"

:: 获取数据库用户
set /p "input_user=数据库用户 [%DB_USER%]: "
if not "!input_user!"=="" set "DB_USER=!input_user!"

:: 获取数据库密码
set /p "DB_PASSWORD=数据库密码: "

echo.
echo 配置信息:
echo 主机: %DB_HOST%
echo 端口: %DB_PORT%
echo 数据库: %DB_NAME%
echo 用户: %DB_USER%
echo.

:: 构建连接字符串
set "PGPASSWORD=%DB_PASSWORD%"
set "DB_URL=postgresql://%DB_USER%:%DB_PASSWORD%@%DB_HOST%:%DB_PORT%"

echo [1/4] 测试数据库连接...
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d postgres -c "SELECT version();" >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 无法连接到PostgreSQL服务器
    echo 请检查连接信息和服务器状态
    pause
    exit /b 1
)
echo [✓] 数据库连接成功

echo.
echo [2/4] 检查数据库是否存在...
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d postgres -tAc "SELECT 1 FROM pg_database WHERE datname='%DB_NAME%';" | findstr "1" >nul
if %errorlevel% equ 0 (
    echo [警告] 数据库 '%DB_NAME%' 已存在
    set /p "confirm=是否删除并重新创建? [y/N]: "
    if /i "!confirm!"=="y" (
        echo [删除] 正在删除现有数据库...
        psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d postgres -c "DROP DATABASE IF EXISTS %DB_NAME%;"
        if !errorlevel! neq 0 (
            echo [错误] 删除数据库失败
            pause
            exit /b 1
        )
    ) else (
        echo [跳过] 保留现有数据库
        goto :migrate
    )
)

echo [创建] 正在创建数据库 '%DB_NAME%'...
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d postgres -c "CREATE DATABASE %DB_NAME%;"
if %errorlevel% neq 0 (
    echo [错误] 创建数据库失败
    pause
    exit /b 1
)
echo [✓] 数据库创建成功

:migrate
echo.
echo [3/4] 运行数据库迁移...

:: 检查是否有Rust环境
where cargo >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Rust/Cargo
    echo 请安装Rust: https://rustup.rs/
    pause
    exit /b 1
)

:: 设置环境变量并运行迁移
set "DATABASE_URL=%DB_URL%/%DB_NAME%"
echo 使用连接字符串: %DATABASE_URL%

:: 检查sqlx-cli是否安装
cargo install --list | findstr "sqlx-cli" >nul
if %errorlevel% neq 0 (
    echo [安装] 正在安装sqlx-cli...
    cargo install sqlx-cli --no-default-features --features postgres
    if !errorlevel! neq 0 (
        echo [错误] sqlx-cli安装失败
        pause
        exit /b 1
    )
)

:: 运行迁移
echo [执行] 正在运行数据库迁移...
sqlx migrate run
if %errorlevel% neq 0 (
    echo [错误] 数据库迁移失败
    pause
    exit /b 1
)
echo [✓] 数据库迁移完成

echo.
echo [4/4] 初始化基础数据...

:: 创建管理员用户
echo [创建] 正在创建管理员用户...
set "ADMIN_PASSWORD_HASH=$2b$12$QiakSXKu2vIFNFJAp4uDP.XdOxwPApb/31yLCH6raSTYL9Yh2VIPS"

psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "INSERT INTO users (username, password_hash, full_name, is_active) VALUES ('admin', '%ADMIN_PASSWORD_HASH%', '系统管理员', true) ON CONFLICT (username) DO NOTHING;"

:: 创建管理员角色
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "INSERT INTO roles (role_name, description) VALUES ('admin', '系统管理员') ON CONFLICT (role_name) DO NOTHING;"

:: 分配角色给管理员
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "INSERT INTO user_roles (user_id, role_id) SELECT u.id, r.id FROM users u, roles r WHERE u.username = 'admin' AND r.role_name = 'admin' ON CONFLICT DO NOTHING;"

echo [✓] 基础数据初始化完成

echo.
echo [5/5] 创建环境配置文件...
(
echo # MES系统环境配置
echo DATABASE_URL=%DB_URL%/%DB_NAME%
echo SERVER_HOST=0.0.0.0
echo SERVER_PORT=8080
echo JWT_SECRET=mes-system-jwt-secret-key-development-only
echo RUST_LOG=info
) > .env

echo [✓] 环境配置文件已创建: .env

echo.
echo ========================================
echo 数据库初始化完成！
echo ========================================
echo.
echo 连接信息:
echo   数据库: %DB_NAME%
echo   主机: %DB_HOST%:%DB_PORT%
echo   用户: %DB_USER%
echo.
echo 默认管理员账户:
echo   用户名: admin
echo   密码: admin123
echo.
echo 下一步:
echo   1. 运行 build_production.bat 构建系统
echo   2. 运行 start_all.bat 启动服务
echo.
pause
