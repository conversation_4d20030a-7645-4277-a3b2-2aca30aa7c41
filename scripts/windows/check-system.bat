@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo MES系统 - Windows系统检查脚本
echo ========================================
echo.

set "all_ok=1"

echo [1/8] 检查操作系统...
echo 操作系统: %OS%
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo Windows版本: %VERSION%
echo 架构: %PROCESSOR_ARCHITECTURE%
echo [✓] 操作系统检查完成
echo.

echo [2/8] 检查Rust环境...
where rustc >nul 2>&1
if %errorlevel% equ 0 (
    echo [✓] Rust已安装
    rustc --version
    cargo --version
) else (
    echo [✗] Rust未安装
    echo     请运行 install.bat 安装Rust
    set "all_ok=0"
)
echo.

echo [3/8] 检查Node.js环境...
where node >nul 2>&1
if %errorlevel% equ 0 (
    echo [✓] Node.js已安装
    node --version
    npm --version
) else (
    echo [✗] Node.js未安装
    echo     请运行 install.bat 安装Node.js
    set "all_ok=0"
)
echo.

echo [4/8] 检查PostgreSQL...
where psql >nul 2>&1
if %errorlevel% equ 0 (
    echo [✓] PostgreSQL已安装
    psql --version
    
    :: 检查PostgreSQL服务
    sc query postgresql-x64-15 >nul 2>&1
    if !errorlevel! equ 0 (
        sc query postgresql-x64-15 | findstr "RUNNING" >nul
        if !errorlevel! equ 0 (
            echo [✓] PostgreSQL服务运行中
        ) else (
            echo [!] PostgreSQL服务未运行
            echo     请启动PostgreSQL服务
        )
    ) else (
        echo [!] 未找到PostgreSQL服务
    )
) else (
    echo [✗] PostgreSQL未安装
    echo     请手动安装PostgreSQL
    set "all_ok=0"
)
echo.

echo [5/8] 检查项目文件...
if exist "Cargo.toml" (
    echo [✓] 后端项目文件存在
) else (
    echo [✗] 未找到Cargo.toml
    set "all_ok=0"
)

if exist "frontend\package.json" (
    echo [✓] 前端项目文件存在
) else (
    echo [✗] 未找到前端项目
    set "all_ok=0"
)

if exist ".env" (
    echo [✓] 环境配置文件存在
) else (
    echo [!] 环境配置文件不存在
    echo     请运行 init-database.bat 创建配置
)
echo.

echo [6/8] 检查构建文件...
if exist "target\release\mes-system.exe" (
    echo [✓] 后端可执行文件存在
    for %%A in ("target\release\mes-system.exe") do set "size=%%~zA"
    set /a "size_mb=!size!/1024/1024"
    echo     大小: !size_mb! MB
) else (
    echo [!] 后端未构建
    echo     请运行 build_production.bat 构建
)

if exist "frontend\dist" (
    echo [✓] 前端构建文件存在
    for /f %%A in ('dir /b /s "frontend\dist" ^| find /c /v ""') do set "file_count=%%A"
    echo     文件数量: !file_count!
) else (
    echo [!] 前端未构建
    echo     请运行 build_production.bat 构建
)
echo.

echo [7/8] 检查端口占用...
netstat -ano | findstr ":9000" >nul
if %errorlevel% equ 0 (
    echo [!] 端口9000被占用
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":9000"') do (
        for /f "tokens=1" %%b in ('tasklist /fi "PID eq %%a" /fo csv /nh') do (
            echo     进程: %%b (PID: %%a)
        )
    )
) else (
    echo [✓] 端口9000可用
)

netstat -ano | findstr ":3000" >nul
if %errorlevel% equ 0 (
    echo [!] 端口3000被占用
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":3000"') do (
        for /f "tokens=1" %%b in ('tasklist /fi "PID eq %%a" /fo csv /nh') do (
            echo     进程: %%b (PID: %%a)
        )
    )
) else (
    echo [✓] 端口3000可用
)
echo.

echo [8/8] 检查数据库连接...
if exist ".env" (
    :: 读取数据库URL
    for /f "usebackq tokens=1,2 delims==" %%a in (".env") do (
        if "%%a"=="DATABASE_URL" set "DATABASE_URL=%%b"
    )
    
    if defined DATABASE_URL (
        psql "!DATABASE_URL!" -c "SELECT 1;" >nul 2>&1
        if !errorlevel! equ 0 (
            echo [✓] 数据库连接正常
            
            :: 检查表数量
            for /f %%A in ('psql "!DATABASE_URL!" -tAc "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';"') do set "table_count=%%A"
            echo     数据表数量: !table_count!
        ) else (
            echo [✗] 数据库连接失败
            echo     请检查数据库服务和配置
            set "all_ok=0"
        )
    ) else (
        echo [!] 未找到数据库配置
    )
) else (
    echo [!] 环境配置文件不存在
)
echo.

echo ========================================
echo 系统检查完成
echo ========================================
echo.

if "%all_ok%"=="1" (
    echo [✓] 系统检查通过，所有组件正常
    echo.
    echo 可以运行的操作:
    echo   start_all.bat     - 启动系统
    echo   stop_all.bat      - 停止系统
    echo   build_production.bat - 构建系统
) else (
    echo [✗] 系统检查发现问题，请根据上述提示修复
    echo.
    echo 建议操作:
    echo   install.bat       - 安装依赖
    echo   init-database.bat - 初始化数据库
    echo   build_production.bat - 构建系统
)

echo.
pause
