@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo MES系统 - Windows生产构建脚本
echo ========================================
echo.

:: 检查Rust环境
where cargo >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Rust/Cargo
    echo 请先运行 install.bat 安装Rust环境
    pause
    exit /b 1
)

:: 检查Node.js环境
where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Node.js/npm
    echo 请先运行 install.bat 安装Node.js环境
    pause
    exit /b 1
)

echo 构建选项:
echo 1. 完整构建 (后端 + 前端)
echo 2. 仅构建后端
echo 3. 仅构建前端
echo 4. 清理并重新构建
echo.
set /p "choice=请选择构建选项 [1-4]: "

if "%choice%"=="1" goto :full_build
if "%choice%"=="2" goto :backend_build
if "%choice%"=="3" goto :frontend_build
if "%choice%"=="4" goto :clean_build

echo 无效选择，执行完整构建
goto :full_build

:full_build
echo.
echo [完整构建] 正在构建MES系统...
echo.

:: 构建后端
call :build_backend
if %errorlevel% neq 0 exit /b 1

:: 构建前端
call :build_frontend
if %errorlevel% neq 0 exit /b 1

echo.
echo [✓] 完整构建完成
goto :end

:backend_build
echo.
echo [后端构建] 正在构建Rust后端...
call :build_backend
if %errorlevel% neq 0 exit /b 1
echo [✓] 后端构建完成
goto :end

:frontend_build
echo.
echo [前端构建] 正在构建React前端...
call :build_frontend
if %errorlevel% neq 0 exit /b 1
echo [✓] 前端构建完成
goto :end

:clean_build
echo.
echo [清理构建] 正在清理并重新构建...

:: 清理Rust构建缓存
echo [清理] 清理Rust构建缓存...
cargo clean
if %errorlevel% neq 0 (
    echo [警告] Rust缓存清理失败，继续构建...
)

:: 清理前端构建缓存
echo [清理] 清理前端构建缓存...
if exist "frontend\dist" rmdir /s /q "frontend\dist"
if exist "frontend\node_modules\.cache" rmdir /s /q "frontend\node_modules\.cache"

:: 重新构建
call :build_backend
if %errorlevel% neq 0 exit /b 1

call :build_frontend
if %errorlevel% neq 0 exit /b 1

echo [✓] 清理构建完成
goto :end

:: 构建后端函数
:build_backend
echo [1/3] 检查后端依赖...
if not exist "Cargo.toml" (
    echo [错误] 未找到Cargo.toml文件
    exit /b 1
)

echo [2/3] 编译Rust后端 (Release模式)...
echo 这可能需要几分钟时间，请耐心等待...

:: 设置优化环境变量
set "RUSTFLAGS=-C target-cpu=native"

cargo build --release
if %errorlevel% neq 0 (
    echo [错误] 后端编译失败
    echo 请检查代码错误并修复后重试
    exit /b 1
)

echo [3/3] 验证后端可执行文件...
if not exist "target\release\mes-system.exe" (
    echo [错误] 未找到编译后的可执行文件
    exit /b 1
)

:: 获取文件大小
for %%A in ("target\release\mes-system.exe") do set "size=%%~zA"
set /a "size_mb=!size!/1024/1024"

echo [✓] 后端编译成功
echo     文件: target\release\mes-system.exe
echo     大小: !size_mb! MB
exit /b 0

:: 构建前端函数
:build_frontend
echo [1/4] 检查前端项目...
if not exist "frontend\package.json" (
    echo [错误] 未找到前端项目
    exit /b 1
)

cd frontend

echo [2/4] 检查前端依赖...
if not exist "node_modules" (
    echo [安装] 正在安装前端依赖...
    npm install
    if !errorlevel! neq 0 (
        echo [错误] 前端依赖安装失败
        cd ..
        exit /b 1
    )
) else (
    echo [检查] 检查依赖更新...
    npm ci --only=production
    if !errorlevel! neq 0 (
        echo [警告] 依赖检查失败，继续构建...
    )
)

echo [3/4] 构建前端应用...
npm run build
if %errorlevel% neq 0 (
    echo [错误] 前端构建失败
    cd ..
    exit /b 1
)

echo [4/4] 验证前端构建...
if not exist "dist" (
    echo [错误] 前端构建输出目录不存在
    cd ..
    exit /b 1
)

:: 计算构建文件数量
for /f %%A in ('dir /b /s "dist" ^| find /c /v ""') do set "file_count=%%A"

echo [✓] 前端构建成功
echo     输出目录: frontend\dist
echo     文件数量: !file_count!

cd ..
exit /b 0

:end
echo.
echo ========================================
echo 构建完成！
echo ========================================
echo.
echo 构建输出:
if exist "target\release\mes-system.exe" (
    echo   后端: target\release\mes-system.exe
)
if exist "frontend\dist" (
    echo   前端: frontend\dist\
)
echo.
echo 下一步:
echo   运行 start_all.bat 启动系统
echo.
echo 系统访问地址:
echo   前端: http://localhost:3000
echo   后端API: http://localhost:8080
echo.
pause
