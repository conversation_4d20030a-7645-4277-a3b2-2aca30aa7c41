@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo MES系统 - Windows快速启动脚本
echo ========================================
echo.

echo 欢迎使用MES制造执行系统！
echo.
echo 此脚本将帮助您快速设置和启动MES系统
echo 适用于首次安装或快速部署
echo.

set /p "confirm=是否继续快速启动? [y/N]: "
if /i not "!confirm!"=="y" (
    echo 启动已取消
    pause
    exit /b 0
)

echo.
echo [快速启动] 正在检查系统环境...

:: 步骤1: 系统检查
echo.
echo === 步骤 1/5: 系统环境检查 ===

:: 检查关键组件
set "need_install=0"

where rustc >nul 2>&1
if %errorlevel% neq 0 (
    echo [!] Rust未安装
    set "need_install=1"
)

where node >nul 2>&1
if %errorlevel% neq 0 (
    echo [!] Node.js未安装
    set "need_install=1"
)

where psql >nul 2>&1
if %errorlevel% neq 0 (
    echo [!] PostgreSQL未安装
    set "need_install=1"
)

if "!need_install!"=="1" (
    echo.
    echo [安装] 检测到缺少必要组件，正在自动安装...
    call install.bat
    if !errorlevel! neq 0 (
        echo [错误] 自动安装失败
        pause
        exit /b 1
    )
) else (
    echo [✓] 系统环境检查通过
)

:: 步骤2: 数据库初始化
echo.
echo === 步骤 2/5: 数据库初始化 ===

if not exist ".env" (
    echo [初始化] 正在初始化数据库...
    call init-database.bat
    if !errorlevel! neq 0 (
        echo [错误] 数据库初始化失败
        pause
        exit /b 1
    )
) else (
    echo [检查] 检查数据库连接...
    
    :: 读取数据库URL
    for /f "usebackq tokens=1,2 delims==" %%a in (".env") do (
        if "%%a"=="DATABASE_URL" set "DATABASE_URL=%%b"
    )
    
    if defined DATABASE_URL (
        psql "!DATABASE_URL!" -c "SELECT 1;" >nul 2>&1
        if !errorlevel! equ 0 (
            echo [✓] 数据库连接正常
        ) else (
            echo [重新初始化] 数据库连接失败，重新初始化...
            call init-database.bat
            if !errorlevel! neq 0 (
                echo [错误] 数据库重新初始化失败
                pause
                exit /b 1
            )
        )
    )
)

:: 步骤3: 项目构建
echo.
echo === 步骤 3/5: 项目构建 ===

set "need_build=0"

if not exist "target\release\mes-system.exe" (
    echo [!] 后端未构建
    set "need_build=1"
)

if not exist "frontend\dist" (
    echo [!] 前端未构建
    set "need_build=1"
)

if "!need_build!"=="1" (
    echo [构建] 正在构建项目...
    call build_production.bat
    if !errorlevel! neq 0 (
        echo [错误] 项目构建失败
        pause
        exit /b 1
    )
) else (
    echo [✓] 项目已构建
)

:: 步骤4: 启动服务
echo.
echo === 步骤 4/5: 启动服务 ===

echo [启动] 正在启动MES系统服务...

:: 检查端口占用
netstat -ano | findstr ":8080" >nul
if %errorlevel% equ 0 (
    echo [清理] 清理8080端口占用...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":8080"') do (
        taskkill /f /pid %%a >nul 2>&1
    )
)

netstat -ano | findstr ":3000" >nul
if %errorlevel% equ 0 (
    echo [清理] 清理3000端口占用...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":3000"') do (
        taskkill /f /pid %%a >nul 2>&1
    )
)

:: 启动后端
echo [启动] 启动后端服务...
start "MES后端服务" cmd /k "title MES后端服务 && target\release\mes-system.exe"

:: 等待后端启动
echo [等待] 等待后端服务启动...
for /l %%i in (1,1,15) do (
    timeout /t 2 /nobreak >nul
    curl -s http://localhost:9000/ >nul 2>&1
    if !errorlevel! equ 0 (
        echo [✓] 后端服务启动成功
        goto :start_frontend_quick
    )
    echo [等待] 后端启动中... (%%i/15)
)

echo [警告] 后端服务启动超时，继续启动前端...

:start_frontend_quick
:: 启动前端
echo [启动] 启动前端服务...
cd frontend
start "MES前端服务" cmd /k "title MES前端服务 && npm run dev"
cd ..

:: 等待前端启动
echo [等待] 等待前端服务启动...
for /l %%i in (1,1,10) do (
    timeout /t 3 /nobreak >nul
    curl -s http://localhost:3000/ >nul 2>&1
    if !errorlevel! equ 0 (
        echo [✓] 前端服务启动成功
        goto :verify_services
    )
    echo [等待] 前端启动中... (%%i/10)
)

:verify_services
:: 步骤5: 验证服务
echo.
echo === 步骤 5/5: 服务验证 ===

echo [验证] 检查服务状态...

:: 检查后端
curl -s http://localhost:8080/ >nul 2>&1
if %errorlevel% equ 0 (
    echo [✓] 后端API服务正常
) else (
    echo [!] 后端API服务异常
)

:: 检查前端
curl -s http://localhost:3000/ >nul 2>&1
if %errorlevel% equ 0 (
    echo [✓] 前端Web服务正常
) else (
    echo [!] 前端Web服务异常
)

:: 检查数据库
if defined DATABASE_URL (
    psql "!DATABASE_URL!" -c "SELECT 1;" >nul 2>&1
    if !errorlevel! equ 0 (
        echo [✓] 数据库连接正常
    ) else (
        echo [!] 数据库连接异常
    )
)

echo.
echo ========================================
echo MES系统快速启动完成！
echo ========================================
echo.
echo 🎉 系统已成功启动并运行
echo.
echo 📱 访问地址:
echo   前端界面: http://localhost:3000
echo   后端API:  http://localhost:8080
echo   网络访问: http://************:3000
echo.
echo 🔑 默认管理员账户:
echo   用户名: admin
echo   密码: admin123
echo.
echo 🛠️ 管理命令:
echo   stop_all.bat      - 停止所有服务
echo   check-system.bat  - 检查系统状态
echo   start_all.bat     - 重新启动服务
echo.
echo 💡 提示:
echo   - 关闭此窗口不会停止服务
echo   - 服务在独立窗口中运行
echo   - 可以通过浏览器访问系统界面
echo.

:: 询问是否打开浏览器
set /p "open_browser=是否自动打开浏览器? [y/N]: "
if /i "!open_browser!"=="y" (
    echo [打开] 正在打开浏览器...
    start http://localhost:3000
)

echo.
echo 按任意键退出...
pause >nul
