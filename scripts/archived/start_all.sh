#!/bin/bash

# MES系统一键启动脚本
# 提供交互式菜单选择不同的启动方式

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 全局变量
BACKEND_PID=""
FRONTEND_PID=""

# 打印函数
print_header() {
    clear
    echo ""
    echo -e "${PURPLE}=================================${NC}"
    echo -e "${PURPLE}🏭 MES制造执行系统 - 一键启动${NC}"
    echo -e "${PURPLE}=================================${NC}"
    echo ""
}

print_step() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# 显示主菜单
show_menu() {
    echo -e "${BLUE}请选择启动方式:${NC}"
    echo ""
    echo "1. 🚀 快速启动 (如果已安装)"
    echo "2. 📦 全新安装并启动"
    echo "3. 🐳 Docker方式启动"
    echo "4. 🔍 系统状态检查"
    echo "5. 🛠️  数据库初始化"
    echo "6. 📚 查看帮助信息"
    echo "7. 🚪 退出"
    echo ""
}

# 快速启动
quick_start() {
    print_step "快速启动MES系统..."
    
    # 检查必要文件
    if [ ! -f "target/release/mes-system" ]; then
        print_error "后端程序未找到，请先运行安装"
        echo "运行: ./install.sh 或选择选项2进行全新安装"
        return 1
    fi
    
    if [ ! -d "frontend/node_modules" ]; then
        print_error "前端依赖未安装，请先运行安装"
        echo "运行: cd frontend && npm install"
        return 1
    fi
    
    # 检查环境配置
    if [ ! -f ".env" ]; then
        print_warning "创建默认配置文件..."
        cat > .env << EOF
DATABASE_URL=postgresql://mes_user:mes_password@localhost:5432/mes_db
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
RUST_LOG=info
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
EOF
        print_success "已创建.env文件"
    fi
    
    # 检查数据库连接
    source .env
    if ! psql "$DATABASE_URL" -c "SELECT 1;" > /dev/null 2>&1; then
        print_error "数据库连接失败"
        print_warning "请确保PostgreSQL运行并且数据库已创建"
        print_warning "可以运行: ./init-database.sh 初始化数据库"
        return 1
    fi
    
    print_success "环境检查通过，启动服务..."
    
    # 启动后端
    print_step "启动后端服务..."
    export $(cat .env | xargs)
    ./target/release/mes-system &
    BACKEND_PID=$!
    
    # 等待后端启动
    sleep 5
    if ! curl -f http://localhost:${SERVER_PORT:-9001}/health > /dev/null 2>&1; then
        print_error "后端启动失败"
        kill $BACKEND_PID 2>/dev/null || true
        return 1
    fi
    print_success "后端服务启动成功"
    
    # 启动前端
    print_step "启动前端服务..."
    cd frontend
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    
    sleep 5
    show_success_info
    
    # 等待用户停止
    trap 'stop_services; exit 0' INT
    echo -e "${YELLOW}按 Ctrl+C 停止所有服务${NC}"
    wait
}

# 全新安装
full_install() {
    print_step "开始全新安装..."
    
    if [ -f "install.sh" ]; then
        ./install.sh
        if [ $? -eq 0 ]; then
            print_success "安装完成，启动系统..."
            quick_start
        else
            print_error "安装失败"
            return 1
        fi
    else
        print_error "安装脚本未找到"
        return 1
    fi
}

# Docker启动
docker_start() {
    print_step "使用Docker启动..."
    
    if [ -f "docker-compose.yml" ]; then
        print_step "启动Docker容器..."
        docker-compose up -d
        
        # 等待服务启动
        print_step "等待服务启动..."
        sleep 30
        
        # 检查容器状态
        if docker-compose ps | grep -q "Up"; then
            print_success "Docker容器启动成功"
            show_docker_info
        else
            print_error "Docker容器启动失败"
            docker-compose logs
            return 1
        fi
    elif [ -f "docker-install.sh" ]; then
        print_step "运行Docker安装脚本..."
        ./docker-install.sh
    else
        print_error "Docker配置文件未找到"
        print_warning "请先运行: ./docker-install.sh"
        return 1
    fi
}

# 系统检查
system_check() {
    print_step "检查系统状态..."
    
    if [ -f "check-system.sh" ]; then
        ./check-system.sh
    else
        print_error "系统检查脚本未找到"
        return 1
    fi
}

# 数据库初始化
database_init() {
    print_step "初始化数据库..."
    
    if [ -f "init-database.sh" ]; then
        ./init-database.sh
    else
        print_error "数据库初始化脚本未找到"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}MES系统帮助信息${NC}"
    echo "=================================="
    echo ""
    echo -e "${GREEN}📁 项目结构:${NC}"
    echo "  - install.sh          自动安装脚本"
    echo "  - docker-install.sh   Docker安装脚本"
    echo "  - init-database.sh    数据库初始化脚本"
    echo "  - check-system.sh     系统状态检查脚本"
    echo "  - start_all.sh        一键启动脚本（当前）"
    echo ""
    echo -e "${GREEN}🚀 启动方式:${NC}"
    echo "  1. 自动安装: ./install.sh"
    echo "  2. Docker部署: ./docker-install.sh"
    echo "  3. 手动启动: ./start_all.sh"
    echo ""
    echo -e "${GREEN}📚 文档:${NC}"
    echo "  - INSTALLATION_GUIDE.md  完整安装指南"
    echo "  - README.md              项目说明"
    echo "  - API_DOCUMENTATION.md   API文档"
    echo ""
    echo -e "${GREEN}🔧 常用命令:${NC}"
    echo "  - 检查系统: ./check-system.sh"
    echo "  - 初始化数据库: ./init-database.sh"
    echo "  - 启动后端: cargo run"
    echo "  - 启动前端: cd frontend && npm run dev"
    echo ""
    echo -e "${GREEN}🆘 获取帮助:${NC}"
    echo "  - 查看日志: tail -f /var/log/mes/mes.log"
    echo "  - 提交Issue: GitHub仓库"
    echo "  - 邮件支持: <EMAIL>"
    echo ""
}

# 显示成功信息
show_success_info() {
    echo ""
    echo -e "${GREEN}🎉 MES系统启动成功！${NC}"
    echo "=================================="
    echo -e "🌐 前端界面: ${GREEN}http://localhost:3000${NC}"
    echo -e "🔌 后端API: ${GREEN}http://localhost:${SERVER_PORT:-9001}${NC}"
    echo -e "📚 API文档: ${GREEN}http://localhost:${SERVER_PORT:-9001}/api/docs${NC}"
    echo ""
    echo -e "${BLUE}🔑 默认登录信息:${NC}"
    echo -e "👤 用户名: ${GREEN}admin${NC}"
    echo -e "🔒 密码: ${GREEN}admin123${NC}"
    echo ""
}

# 显示Docker信息
show_docker_info() {
    echo ""
    echo -e "${GREEN}🐳 Docker容器启动成功！${NC}"
    echo "=================================="
    echo -e "🌐 前端界面: ${GREEN}http://localhost:3000${NC}"
    echo -e "🔌 后端API: ${GREEN}http://localhost:${SERVER_PORT:-9001}${NC}"
    echo ""
    echo -e "${BLUE}🐳 Docker管理命令:${NC}"
    echo "  - 查看状态: docker-compose ps"
    echo "  - 查看日志: docker-compose logs -f"
    echo "  - 停止服务: docker-compose down"
    echo "  - 重启服务: docker-compose restart"
    echo ""
}

# 停止服务
stop_services() {
    print_step "正在停止服务..."
    
    # 停止进程
    if [ -n "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        print_success "后端服务已停止"
    fi
    if [ -n "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        print_success "前端服务已停止"
    fi
    
    # 停止Docker容器（如果存在）
    if [ -f "docker-compose.yml" ] && docker-compose ps | grep -q "Up"; then
        docker-compose down
        print_success "Docker容器已停止"
    fi
}

# 主程序
main() {
    # 检查是否在项目根目录
    if [ ! -f "Cargo.toml" ]; then
        print_error "请在MES项目根目录运行此脚本"
        exit 1
    fi
    
    while true; do
        print_header
        show_menu
        read -p "请选择 (1-7): " choice
        
        case $choice in
            1)
                quick_start
                break
                ;;
            2)
                full_install
                break
                ;;
            3)
                docker_start
                break
                ;;
            4)
                system_check
                echo
                read -p "按Enter键继续..."
                ;;
            5)
                database_init
                echo
                read -p "按Enter键继续..."
                ;;
            6)
                show_help
                echo
                read -p "按Enter键继续..."
                ;;
            7)
                print_success "再见！"
                exit 0
                ;;
            *)
                print_error "无效选择，请重新输入"
                sleep 2
                ;;
        esac
    done
}

# 运行主程序
main "$@"
