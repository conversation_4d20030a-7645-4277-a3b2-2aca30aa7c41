#!/bin/bash

# MES系统预览版停止脚本

echo "🛑 停止MES系统预览版..."

# 从PID文件读取进程ID
if [ -f ".backend.pid" ]; then
    BACKEND_PID=$(cat .backend.pid)
    echo "🔧 停止后端服务 (PID: $BACKEND_PID)..."
    kill $BACKEND_PID 2>/dev/null || true
    rm -f .backend.pid
fi

if [ -f ".frontend.pid" ]; then
    FRONTEND_PID=$(cat .frontend.pid)
    echo "🌐 停止前端服务 (PID: $FRONTEND_PID)..."
    kill $FRONTEND_PID 2>/dev/null || true
    rm -f .frontend.pid
fi

# 强制杀死所有相关进程
echo "🔄 清理所有相关进程..."
pkill -f "mes_system" 2>/dev/null || true
pkill -f "vite preview" 2>/dev/null || true
pkill -f "cargo run" 2>/dev/null || true

# 等待进程完全停止
sleep 2

echo "✅ MES系统预览版已停止"
