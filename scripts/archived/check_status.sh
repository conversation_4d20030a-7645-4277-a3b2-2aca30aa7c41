#!/bin/bash

# MES系统状态检查脚本 - Deep分支版本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

echo -e "${PURPLE}🏭 MES制造执行系统 - Deep分支状态检查${NC}"
echo -e "${PURPLE}===========================================${NC}"
echo ""

# 检查后端服务
echo -e "${BLUE}📋 后端服务状态${NC}"
if curl -s http://localhost:8080/health > /dev/null 2>&1; then
    echo -e "  ✅ 后端API服务: ${GREEN}运行中${NC} (http://localhost:8080)"
    
    # 测试登录API
    LOGIN_RESULT=$(curl -s -X POST http://localhost:8080/api/auth/login \
        -H "Content-Type: application/json" \
        -d '{"username":"admin","password":"admin123"}' | jq -r '.token // "error"')
    
    if [ "$LOGIN_RESULT" != "error" ] && [ "$LOGIN_RESULT" != "null" ]; then
        echo -e "  ✅ 登录认证: ${GREEN}正常${NC}"
    else
        echo -e "  ❌ 登录认证: ${RED}失败${NC}"
    fi
else
    echo -e "  ❌ 后端API服务: ${RED}未运行${NC}"
fi

echo ""

# 检查前端服务
echo -e "${BLUE}📋 前端服务状态${NC}"
if curl -s http://localhost:3000 > /dev/null 2>&1; then
    echo -e "  ✅ 前端开发服务: ${GREEN}运行中${NC} (http://localhost:3000)"
else
    echo -e "  ❌ 前端开发服务: ${RED}未运行${NC}"
fi

if curl -s http://localhost:4173 > /dev/null 2>&1; then
    echo -e "  ✅ 前端预览服务: ${GREEN}运行中${NC} (http://localhost:4173)"
else
    echo -e "  ❌ 前端预览服务: ${RED}未运行${NC}"
fi

echo ""

# 检查数据库
echo -e "${BLUE}📋 数据库状态${NC}"
if [ -f ".env" ]; then
    source .env
    if PGPASSWORD=mcpqMlT7hhCy6naS2kSQ1sTDf psql -h localhost -U mes_user -d mes_db -c "SELECT 1;" > /dev/null 2>&1; then
        echo -e "  ✅ 数据库连接: ${GREEN}正常${NC}"
        
        # 检查用户表
        USER_COUNT=$(PGPASSWORD=mcpqMlT7hhCy6naS2kSQ1sTDf psql -h localhost -U mes_user -d mes_db -tAc "SELECT COUNT(*) FROM users;" 2>/dev/null || echo "0")
        echo -e "  📊 用户数量: ${USER_COUNT}"
        
        # 检查表数量
        TABLE_COUNT=$(PGPASSWORD=mcpqMlT7hhCy6naS2kSQ1sTDf psql -h localhost -U mes_user -d mes_db -tAc "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null || echo "0")
        echo -e "  📊 数据库表数量: ${TABLE_COUNT}"
    else
        echo -e "  ❌ 数据库连接: ${RED}失败${NC}"
    fi
else
    echo -e "  ❌ 环境配置: ${RED}.env文件不存在${NC}"
fi

echo ""

# 检查进程
echo -e "${BLUE}📋 系统进程${NC}"
BACKEND_PID=$(pgrep -f "mes-system" | head -1)
if [ -n "$BACKEND_PID" ]; then
    echo -e "  ✅ 后端进程: ${GREEN}PID $BACKEND_PID${NC}"
else
    echo -e "  ❌ 后端进程: ${RED}未找到${NC}"
fi

FRONTEND_DEV_PID=$(pgrep -f "vite.*3000" | head -1)
if [ -n "$FRONTEND_DEV_PID" ]; then
    echo -e "  ✅ 前端开发进程: ${GREEN}PID $FRONTEND_DEV_PID${NC}"
else
    echo -e "  ❌ 前端开发进程: ${RED}未找到${NC}"
fi

FRONTEND_PREVIEW_PID=$(pgrep -f "vite preview" | head -1)
if [ -n "$FRONTEND_PREVIEW_PID" ]; then
    echo -e "  ✅ 前端预览进程: ${GREEN}PID $FRONTEND_PREVIEW_PID${NC}"
else
    echo -e "  ❌ 前端预览进程: ${RED}未找到${NC}"
fi

echo ""

# 显示访问信息
echo -e "${PURPLE}🌐 访问信息${NC}"
echo -e "  前端开发服务: ${BLUE}http://localhost:3000${NC}"
echo -e "  前端预览服务: ${BLUE}http://localhost:4173${NC}"
echo -e "  后端API服务: ${BLUE}http://localhost:8080${NC}"
echo -e "  API文档: ${BLUE}http://localhost:8080/api${NC}"

echo ""

# 显示登录信息
echo -e "${PURPLE}👤 默认登录账户${NC}"
echo -e "  用户名: ${GREEN}admin${NC}"
echo -e "  密码: ${GREEN}admin123${NC}"

echo ""

# 显示Git分支信息
echo -e "${PURPLE}📂 项目信息${NC}"
CURRENT_BRANCH=$(git branch --show-current 2>/dev/null || echo "unknown")
echo -e "  当前分支: ${GREEN}$CURRENT_BRANCH${NC}"

COMMIT_HASH=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
echo -e "  提交哈希: ${GREEN}$COMMIT_HASH${NC}"

echo ""
echo -e "${GREEN}✨ MES系统状态检查完成！${NC}"
