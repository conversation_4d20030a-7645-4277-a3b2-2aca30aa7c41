#!/bin/bash

# MES 系统服务状态检查脚本

echo "🔍 MES 系统服务状态检查"
echo "=========================="

# 检查端口占用
echo "📡 端口占用情况:"
netstat -tlnp | grep -E ':(3000|9000)' || echo "   未发现服务端口占用"

echo ""

# 检查进程
echo "🔄 运行中的进程:"
ps aux | grep -E '(cargo|node|vite)' | grep -v grep || echo "   未发现相关进程"

echo ""

# 检查服务响应
echo "🌐 服务连通性测试:"

# 测试后端服务
echo -n "   后端服务 (端口 9000): "
if curl -s -f http://localhost:9000/api/health >/dev/null 2>&1; then
    echo "✅ 正常"
else
    echo "❌ 无响应"
fi

# 测试前端服务
echo -n "   前端服务 (端口 3000): "
if curl -s -f -I http://localhost:3000 >/dev/null 2>&1; then
    echo "✅ 正常"
else
    echo "❌ 无响应"
fi

echo ""

# 检查日志文件
echo "📋 日志文件状态:"
if [ -f "/root/mes-1/backend.log" ]; then
    echo "   后端日志: ✅ 存在 ($(wc -l < /root/mes-1/backend.log) 行)"
    echo "   最新后端日志:"
    tail -n 3 /root/mes-1/backend.log | sed 's/^/     /'
else
    echo "   后端日志: ❌ 不存在"
fi

if [ -f "/root/mes-1/frontend.log" ]; then
    echo "   前端日志: ✅ 存在 ($(wc -l < /root/mes-1/frontend.log) 行)"
    echo "   最新前端日志:"
    tail -n 3 /root/mes-1/frontend.log | sed 's/^/     /'
else
    echo "   前端日志: ❌ 不存在"
fi

echo ""

# 访问地址
echo "🌍 访问地址:"
echo "   前端: http://localhost:3000"
echo "   前端: http://ql.hjch.cf:3000"
echo "   后端: http://localhost:9000"
echo "   后端: http://ql.hjch.cf:9000"

echo ""
echo "✨ 检查完成！"
