#!/bin/bash

# MES Frontend 启动脚本
# 自动安装依赖并启动开发服务器

echo "🚀 MES Frontend 启动脚本"
echo "========================"

# 检查 Node.js 版本
check_node() {
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装，请先安装 Node.js 18 或更高版本"
        echo "下载地址: https://nodejs.org/"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        echo "❌ Node.js 版本过低 (当前: $(node -v))，需要 18 或更高版本"
        exit 1
    fi
    
    echo "✅ Node.js 版本检查通过: $(node -v)"
}

# 检查包管理器
check_package_manager() {
    if command -v yarn &> /dev/null; then
        PACKAGE_MANAGER="yarn"
        echo "📦 使用 Yarn 作为包管理器"
    elif command -v npm &> /dev/null; then
        PACKAGE_MANAGER="npm"
        echo "📦 使用 npm 作为包管理器"
    else
        echo "❌ 未找到包管理器 (npm/yarn)"
        exit 1
    fi
}

# 进入前端目录
cd frontend || {
    echo "❌ frontend 目录不存在"
    exit 1
}

echo "📁 当前目录: $(pwd)"

# 检查环境
check_node
check_package_manager

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📥 安装依赖包..."
    if [ "$PACKAGE_MANAGER" = "yarn" ]; then
        yarn install
    else
        npm install
    fi
    
    if [ $? -eq 0 ]; then
        echo "✅ 依赖安装完成"
    else
        echo "❌ 依赖安装失败"
        exit 1
    fi
else
    echo "✅ 依赖已安装"
fi

# 检查后端服务
echo "🔍 检查后端服务..."
if curl -s http://localhost:8080/health > /dev/null; then
    echo "✅ 后端服务运行正常"
else
    echo "⚠️  后端服务未运行，请先启动后端服务"
    echo "   在项目根目录运行: cargo run"
    echo ""
    echo "是否继续启动前端? (y/n)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 启动开发服务器
echo "🚀 启动前端开发服务器..."
echo "前端地址: http://localhost:3000"
echo "API 代理: http://localhost:8080/api"
echo ""
echo "默认登录账号:"
echo "  用户名: admin"
echo "  密码: admin123"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

if [ "$PACKAGE_MANAGER" = "yarn" ]; then
    yarn dev
else
    npm run dev
fi
