#!/bin/bash

# MES系统状态检查脚本

# 读取环境变量
if [ -f ".env" ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# 设置默认端口
SERVER_PORT=${SERVER_PORT:-9001}
# 检查后端和前端服务状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查后端状态
check_backend() {
    echo "=== 后端服务状态 ==="
    
    if [ -f "backend.pid" ]; then
        BACKEND_PID=$(cat backend.pid)
        if kill -0 $BACKEND_PID 2>/dev/null; then
            log_success "后端服务运行中 (PID: $BACKEND_PID)"
            
            # 检查端口是否监听
            if netstat -tuln 2>/dev/null | grep -q ":$SERVER_PORT "; then
                log_success "后端API端口 $SERVER_PORT 正在监听"
            else
                log_warning "后端API端口 $SERVER_PORT 未监听"
            fi

            # 尝试健康检查
            if curl -s http://localhost:$SERVER_PORT/ > /dev/null 2>&1; then
                log_success "后端健康检查通过"
            else
                log_warning "后端健康检查失败"
            fi
        else
            log_error "后端服务进程不存在"
        fi
    else
        log_error "未找到后端PID文件"
    fi
    echo ""
}

# 检查前端状态
check_frontend() {
    echo "=== 前端服务状态 ==="
    
    if [ -f "frontend.pid" ]; then
        FRONTEND_PID=$(cat frontend.pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            log_success "前端服务运行中 (PID: $FRONTEND_PID)"
            
            # 检查端口是否监听
            if netstat -tuln 2>/dev/null | grep -q ":3000 "; then
                log_success "前端服务端口 3000 正在监听"
            else
                log_warning "前端服务端口 3000 未监听"
            fi
        else
            log_error "前端服务进程不存在"
        fi
    else
        log_error "未找到前端PID文件"
    fi
    echo ""
}

# 检查数据库状态
check_database() {
    echo "=== 数据库状态 ==="
    
    if [ -f ".env" ]; then
        source .env
        export PGPASSWORD=$(echo $DATABASE_URL | sed -n 's/.*:\([^@]*\)@.*/\1/p')
        
        if psql "$DATABASE_URL" -c "SELECT 1;" > /dev/null 2>&1; then
            log_success "数据库连接正常"
            
            # 检查表数量
            TABLE_COUNT=$(psql "$DATABASE_URL" -tAc "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';")
            log_info "数据库表数量: $TABLE_COUNT"
        else
            log_error "数据库连接失败"
        fi
    else
        log_error ".env 文件不存在"
    fi
    echo ""
}

# 显示系统信息
show_system_info() {
    echo "=== 系统信息 ==="
    log_info "服务地址:"
    log_info "  前端: http://localhost:3000"
    log_info "  后端API: http://localhost:$SERVER_PORT"
    echo ""
    log_info "日志文件:"
    if [ -f "backend.log" ]; then
        BACKEND_LOG_SIZE=$(du -h backend.log | cut -f1)
        log_info "  后端日志: backend.log ($BACKEND_LOG_SIZE)"
    else
        log_info "  后端日志: backend.log (不存在)"
    fi
    
    if [ -f "frontend.log" ]; then
        FRONTEND_LOG_SIZE=$(du -h frontend.log | cut -f1)
        log_info "  前端日志: frontend.log ($FRONTEND_LOG_SIZE)"
    else
        log_info "  前端日志: frontend.log (不存在)"
    fi
    echo ""
}

# 主函数
main() {
    log_info "检查MES系统状态..."
    echo ""
    
    check_backend
    check_frontend
    check_database
    show_system_info
}

# 执行主函数
main "$@"
