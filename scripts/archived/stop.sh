#!/bin/bash

# MES系统停止脚本
# 停止后端和前端服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止后端服务
stop_backend() {
    log_info "停止后端服务..."
    
    if [ -f "backend.pid" ]; then
        BACKEND_PID=$(cat backend.pid)
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill $BACKEND_PID
            log_success "后端服务已停止 (PID: $BACKEND_PID)"
        else
            log_warning "后端服务进程不存在"
        fi
        rm -f backend.pid
    else
        log_warning "未找到后端PID文件"
    fi
}

# 停止前端服务
stop_frontend() {
    log_info "停止前端服务..."
    
    if [ -f "frontend.pid" ]; then
        FRONTEND_PID=$(cat frontend.pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            kill $FRONTEND_PID
            log_success "前端服务已停止 (PID: $FRONTEND_PID)"
        else
            log_warning "前端服务进程不存在"
        fi
        rm -f frontend.pid
    else
        log_warning "未找到前端PID文件"
    fi
}

# 清理其他相关进程
cleanup_processes() {
    log_info "清理相关进程..."
    
    # 查找并停止可能的MES相关进程
    pkill -f "mes-system" 2>/dev/null || true
    pkill -f "npm run preview" 2>/dev/null || true
    
    log_success "进程清理完成"
}

# 主函数
main() {
    log_info "停止MES系统..."
    
    stop_backend
    stop_frontend
    cleanup_processes
    
    log_success "MES系统已停止"
}

# 执行主函数
main "$@"
