#!/bin/bash

# MES系统预览版启动脚本
# 用于启动生产优化版本的前端和后端服务

echo "🚀 启动MES系统预览版..."

# 检查是否在正确的目录
if [ ! -f "Cargo.toml" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 检查前端构建文件是否存在
if [ ! -d "frontend/dist" ]; then
    echo "❌ 错误: 前端构建文件不存在，请先运行构建"
    echo "   cd frontend && npm run build"
    exit 1
fi

# 检查后端可执行文件是否存在
if [ ! -f "target/release/deps/mes_system-f3bc069022f7bcbd" ]; then
    echo "❌ 错误: 后端可执行文件不存在，请先运行构建"
    echo "   cargo build --release"
    exit 1
fi

# 杀死现有进程
echo "🔄 停止现有服务..."
pkill -f "mes_system" 2>/dev/null || true
pkill -f "vite" 2>/dev/null || true
pkill -f "cargo run" 2>/dev/null || true

# 等待进程完全停止
sleep 2

# 启动后端服务 (Release版本)
echo "🔧 启动后端服务 (Release版本)..."
cd /root/mes-1
RUST_LOG=info ./target/release/deps/mes_system-f3bc069022f7bcbd &
BACKEND_PID=$!

# 等待后端启动
echo "⏳ 等待后端服务启动..."
sleep 3

# 检查后端是否启动成功
if ! curl -s http://localhost:9001/health > /dev/null 2>&1; then
    echo "❌ 后端服务启动失败"
    kill $BACKEND_PID 2>/dev/null || true
    exit 1
fi

echo "✅ 后端服务启动成功 (PID: $BACKEND_PID)"

# 启动前端预览服务
echo "🌐 启动前端预览服务..."
cd frontend
npx vite preview --host 0.0.0.0 --port 3001 &
FRONTEND_PID=$!

# 等待前端启动
echo "⏳ 等待前端服务启动..."
sleep 3

# 检查前端是否启动成功
if ! curl -s http://localhost:3001 > /dev/null 2>&1; then
    echo "❌ 前端服务启动失败"
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null || true
    exit 1
fi

echo "✅ 前端服务启动成功 (PID: $FRONTEND_PID)"

# 显示服务信息
echo ""
echo "🎉 MES系统预览版启动成功!"
echo ""
echo "📊 服务信息:"
echo "  🔧 后端API:    http://localhost:9001"
echo "  🌐 前端界面:   http://localhost:3001"
echo "  📱 移动端:     http://***********:3001"
echo ""
echo "🔧 进程信息:"
echo "  后端PID: $BACKEND_PID"
echo "  前端PID: $FRONTEND_PID"
echo ""
echo "📝 特性:"
echo "  ✅ 生产优化构建"
echo "  ✅ 代码压缩和混淆"
echo "  ✅ 静态资源优化"
echo "  ✅ 双栈网络支持 (IPv4+IPv6)"
echo "  ✅ 批量创建零件支持指定项目"
echo ""
echo "🛑 停止服务:"
echo "  kill $BACKEND_PID $FRONTEND_PID"
echo "  或运行: pkill -f 'mes_system|vite'"
echo ""
echo "📋 日志查看:"
echo "  后端日志: tail -f /var/log/mes-system.log"
echo "  前端访问: 浏览器开发者工具"
echo ""

# 保存PID到文件，方便后续管理
echo "$BACKEND_PID" > .backend.pid
echo "$FRONTEND_PID" > .frontend.pid

echo "💡 提示: 按 Ctrl+C 停止所有服务"

# 等待用户中断
trap 'echo ""; echo "🛑 正在停止服务..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null || true; rm -f .backend.pid .frontend.pid; echo "✅ 服务已停止"; exit 0' INT

# 保持脚本运行
wait
