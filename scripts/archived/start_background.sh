#!/bin/bash

# MES系统后台启动脚本
# 启动后端和前端服务并在后台运行

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境变量
check_environment() {
    log_info "检查环境变量..."
    
    if [ ! -f ".env" ]; then
        log_error ".env 文件不存在"
        exit 1
    fi
    
    source .env
    
    if [ -z "$DATABASE_URL" ]; then
        log_error "DATABASE_URL 环境变量未设置"
        exit 1
    fi
    
    log_success "环境变量检查完成"
}

# 检查数据库连接
check_database() {
    log_info "检查数据库连接..."
    
    # 从DATABASE_URL提取连接信息
    export PGPASSWORD=$(echo $DATABASE_URL | sed -n 's/.*:\([^@]*\)@.*/\1/p')
    
    if psql "$DATABASE_URL" -c "SELECT 1;" > /dev/null 2>&1; then
        log_success "数据库连接正常"
    else
        log_error "数据库连接失败，请检查PostgreSQL服务状态"
        exit 1
    fi
}

# 启动后端服务
start_backend() {
    log_info "启动后端服务..."
    
    if [ ! -f "target/release/mes-system" ]; then
        log_error "后端可执行文件不存在，请先运行 ./install.sh"
        exit 1
    fi
    
    # 在后台启动后端，使用nohup确保进程不会因终端关闭而终止
    nohup ./target/release/mes-system > backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > backend.pid
    
    # 等待后端启动
    sleep 5
    
    # 检查后端是否启动成功
    if kill -0 $BACKEND_PID 2>/dev/null; then
        log_success "后端服务启动成功 (PID: $BACKEND_PID)"
        log_info "后端服务运行在:"
        log_info "  IPv4: http://localhost:9000"
        log_info "  IPv6: http://[::1]:9000"
    else
        log_error "后端服务启动失败"
        exit 1
    fi
}

# 启动前端服务
start_frontend() {
    log_info "启动前端服务..."
    
    cd frontend
    
    # 检查是否有构建文件，如果没有则使用开发模式
    if [ -d "dist" ]; then
        log_info "使用预览模式启动前端..."
        # 在后台启动前端预览服务器，使用nohup确保进程不会因终端关闭而终止
        nohup npm run preview > ../frontend.log 2>&1 &
    else
        log_info "使用开发模式启动前端..."
        # 在后台启动前端开发服务器，使用nohup确保进程不会因终端关闭而终止
        nohup npm run dev > ../frontend.log 2>&1 &
    fi
    
    FRONTEND_PID=$!
    echo $FRONTEND_PID > ../frontend.pid
    
    cd ..
    
    # 等待前端启动
    sleep 8
    
    # 检查前端是否启动成功
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        log_success "前端服务启动成功 (PID: $FRONTEND_PID)"
        
        # 从日志中获取实际端口
        ACTUAL_PORT=$(tail -5 frontend.log | grep -o "localhost:[0-9]*" | head -1 | cut -d: -f2)
        if [ -n "$ACTUAL_PORT" ]; then
            log_info "前端服务运行在:"
            log_info "  IPv4: http://localhost:$ACTUAL_PORT"
            log_info "  IPv6: http://[::1]:$ACTUAL_PORT"
        else
            log_info "前端服务运行在:"
            log_info "  IPv4: http://localhost:3000"
            log_info "  IPv6: http://[::1]:3000"
        fi
    else
        log_error "前端服务启动失败"
        exit 1
    fi
}

# 显示服务状态
show_status() {
    echo ""
    log_success "=== MES系统后台启动完成 ==="
    echo ""
    
    # 获取实际端口
    ACTUAL_PORT=$(tail -5 frontend.log | grep -o "localhost:[0-9]*" | head -1 | cut -d: -f2)
    if [ -z "$ACTUAL_PORT" ]; then
        ACTUAL_PORT="3000"
    fi
    
    log_info "服务地址:"
    log_info "  前端 (IPv4): http://localhost:$ACTUAL_PORT"
    log_info "  前端 (IPv6): http://[::1]:$ACTUAL_PORT"
    log_info "  后端API (IPv4): http://localhost:9000"
    log_info "  后端API (IPv6): http://[::1]:9000"
    echo ""
    log_info "进程信息:"
    log_info "  后端PID: $(cat backend.pid 2>/dev/null || echo '未知')"
    log_info "  前端PID: $(cat frontend.pid 2>/dev/null || echo '未知')"
    echo ""
    log_info "日志文件:"
    log_info "  后端日志: backend.log"
    log_info "  前端日志: frontend.log"
    echo ""
    log_info "管理命令:"
    log_info "  停止服务: ./stop.sh"
    log_info "  查看状态: ./status.sh"
    log_info "  查看后端日志: tail -f backend.log"
    log_info "  查看前端日志: tail -f frontend.log"
    echo ""
    log_warning "服务已在后台运行，关闭终端不会影响服务运行"
}

# 主函数
main() {
    log_info "启动MES系统 (后台模式)..."
    
    check_environment
    check_database
    start_backend
    start_frontend
    show_status
}

# 执行主函数
main "$@"
