#!/bin/bash

# 网关配置检查脚本
# 创建时间: $(date)
# 用途: 检查当前网关配置状态

echo "=================================="
echo "🌐 MES系统网关配置检查"
echo "=================================="
echo

# 显示当前路由表
echo "📋 当前路由表:"
echo "----------------------------------------"
ip route show
echo

# 显示默认网关
echo "🚪 默认网关:"
echo "----------------------------------------"
ip route | grep default
echo

# 测试网关连通性
echo "🔍 网关连通性测试:"
echo "----------------------------------------"
GATEWAY=$(ip route | grep "default.*proto static" | awk '{print $3}' | head -1)
if [ -n "$GATEWAY" ]; then
    echo "正在测试网关 $GATEWAY 的连通性..."
    if ping -c 3 -W 3 "$GATEWAY" > /dev/null 2>&1; then
        echo "✅ 网关 $GATEWAY 连通正常"
    else
        echo "❌ 网关 $GATEWAY 连通失败"
    fi
else
    echo "⚠️  未找到静态默认网关"
fi
echo

# 测试外网连通性
echo "🌍 外网连通性测试:"
echo "----------------------------------------"
echo "正在测试外网连接 (*******)..."
if ping -c 3 -W 5 ******* > /dev/null 2>&1; then
    echo "✅ 外网连接正常"
else
    echo "❌ 外网连接失败"
fi
echo

# 显示网络接口信息
echo "🔌 网络接口信息:"
echo "----------------------------------------"
ip addr show ens18 | grep -E "(inet |link/)"
echo

# 显示DNS配置
echo "🔍 DNS配置:"
echo "----------------------------------------"
cat /etc/resolv.conf | grep nameserver
echo

echo "=================================="
echo "检查完成"
echo "=================================="
