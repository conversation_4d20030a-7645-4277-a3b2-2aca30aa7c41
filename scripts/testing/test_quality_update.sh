#!/bin/bash

# 测试质量检验更新功能

echo "========================================"
echo "质量检验更新功能测试"
echo "========================================"

# 登录获取token
echo "登录获取token..."
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:9000/api/auth/login \
    -H "Content-Type: application/json" \
    -d '{"username": "admin", "password": "admin123"}')

TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.token')

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
    echo "❌ 登录失败"
    exit 1
fi

echo "✓ 登录成功，获取到token"

# 测试更新质量检验状态
echo ""
echo "测试: 更新质量检验状态为完成"
UPDATE_RESPONSE=$(curl -s -X PUT http://localhost:9000/api/quality/inspections/10 \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{
        "status": "completed",
        "result": "pass",
        "notes": "检验完成，所有项目合格"
    }')

echo "响应: $UPDATE_RESPONSE"

if echo "$UPDATE_RESPONSE" | jq -e '.inspection' > /dev/null; then
    echo "✓ 更新质量检验成功"
else
    echo "❌ 更新质量检验失败"
fi

# 测试更新不存在的检验
echo ""
echo "测试: 更新不存在的质量检验"
UPDATE_RESPONSE_404=$(curl -s -X PUT http://localhost:9000/api/quality/inspections/999 \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{
        "status": "completed",
        "result": "pass"
    }')

echo "响应: $UPDATE_RESPONSE_404"

if echo "$UPDATE_RESPONSE_404" | jq -e '.error' > /dev/null; then
    echo "✓ 正确处理不存在的检验"
else
    echo "❌ 未正确处理不存在的检验"
fi

# 验证更新后的数据
echo ""
echo "测试: 验证更新后的质量指标"
METRICS_RESPONSE=$(curl -s -X GET "http://localhost:9000/api/quality/metrics" \
    -H "Authorization: Bearer $TOKEN")

echo "响应: $METRICS_RESPONSE"

PASS_RATE=$(echo "$METRICS_RESPONSE" | jq -r '.overall_metrics.pass_rate')
echo "✓ 更新后合格率: $PASS_RATE%"

echo ""
echo "========================================"
echo "质量检验更新功能测试完成"
echo "========================================"
