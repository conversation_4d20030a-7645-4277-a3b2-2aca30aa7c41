#!/bin/bash

# 项目状态功能部署和测试脚本

set -e  # 遇到错误立即退出

echo "🚀 开始部署项目状态功能..."

# 1. 编译后端
echo "📦 编译后端代码..."
cargo build --release

# 2. 启动服务
echo "🔧 启动MES系统服务..."
cargo run &
SERVER_PID=$!

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务是否启动成功
if ! curl -s http://localhost:8080/health > /dev/null; then
    echo "❌ 服务启动失败"
    kill $SERVER_PID 2>/dev/null || true
    exit 1
fi

echo "✅ 服务启动成功"

# 3. 运行测试
echo "🧪 运行项目状态功能测试..."
python3 test_project_status.py

# 4. 清理
echo "🧹 清理资源..."
kill $SERVER_PID 2>/dev/null || true

echo "🎉 项目状态功能部署和测试完成！"
