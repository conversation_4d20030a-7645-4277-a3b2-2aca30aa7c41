#!/bin/bash

# 测试MES系统API的脚本

API_BASE="http://localhost:9000/api"
TOKEN=""

echo "=== MES系统API测试 ==="

# 1. 测试健康检查
echo "1. 测试后端健康检查..."
curl -s http://localhost:9000/health
echo -e "\n"

# 2. 登录获取token
echo "2. 登录获取认证token..."
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}')

echo "登录响应: $LOGIN_RESPONSE"

# 提取token
TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
    echo "❌ 登录失败，无法获取token"
    exit 1
fi

echo "✅ 登录成功，token: ${TOKEN:0:20}..."
echo ""

# 3. 测试获取计划任务
echo "3. 测试获取计划任务..."
PLAN_TASKS_RESPONSE=$(curl -s "$API_BASE/plan-tasks" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json")

echo "计划任务响应:"
echo "$PLAN_TASKS_RESPONSE" | head -20
echo ""

# 检查是否有数据
TASK_COUNT=$(echo "$PLAN_TASKS_RESPONSE" | grep -o '"plan_tasks":\[' | wc -l)
if [ "$TASK_COUNT" -gt 0 ]; then
    echo "✅ 成功获取计划任务数据"
    # 尝试提取任务数量
    echo "$PLAN_TASKS_RESPONSE" | grep -o '"id":[0-9]*' | wc -l | xargs echo "任务数量:"
else
    echo "❌ 未获取到计划任务数据"
fi

echo ""

# 4. 测试获取甘特图数据
echo "4. 测试获取甘特图数据..."
START_DATE=$(date -d "yesterday" +%Y-%m-%d)
END_DATE=$(date -d "+7 days" +%Y-%m-%d)

GANTT_RESPONSE=$(curl -s "$API_BASE/planning/gantt?start_date=${START_DATE}T00:00:00Z&end_date=${END_DATE}T23:59:59Z" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json")

echo "甘特图数据响应:"
echo "$GANTT_RESPONSE" | head -20
echo ""

# 5. 直接查询数据库中的计划任务数量
echo "5. 直接查询数据库..."
source .env
export PGPASSWORD=$(echo $DATABASE_URL | sed -n 's/.*:\([^@]*\)@.*/\1/p')
DB_COUNT=$(psql -h localhost -U mes_user -d mes_db -t -c "SELECT COUNT(*) FROM plan_tasks;" 2>/dev/null | tr -d ' ')

if [ ! -z "$DB_COUNT" ]; then
    echo "✅ 数据库中的计划任务数量: $DB_COUNT"
else
    echo "❌ 无法查询数据库"
fi

echo ""
echo "=== 测试完成 ==="
