#!/bin/bash

# 质量模块测试脚本
# 测试质量管理相关的API端点

BASE_URL="http://localhost:9000"
TOKEN=""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

print_test() {
    echo -e "${YELLOW}测试: $1${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# 登录获取token
login() {
    print_header "登录测试"
    
    response=$(curl -s -X POST "$BASE_URL/api/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"username": "admin", "password": "admin123"}')
    
    if echo "$response" | grep -q "token"; then
        TOKEN=$(echo "$response" | jq -r '.token')
        print_success "登录成功，获取到token"
        return 0
    else
        print_error "登录失败: $response"
        return 1
    fi
}

# 测试获取质量检验列表
test_get_inspections() {
    print_test "获取质量检验列表"
    
    response=$(curl -s -X GET "$BASE_URL/api/quality/inspections" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json")
    
    if echo "$response" | grep -q "inspections"; then
        count=$(echo "$response" | jq '.total_count')
        print_success "获取质量检验列表成功，共 $count 条记录"
        echo "响应: $response"
    else
        print_error "获取质量检验列表失败: $response"
    fi
}

# 测试创建质量检验
test_create_inspection() {
    print_test "创建质量检验"
    
    response=$(curl -s -X POST "$BASE_URL/api/quality/inspections" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "plan_task_id": 1,
            "inspection_type": "in_process",
            "notes": "测试质量检验 - 过程检测"
        }')
    
    if echo "$response" | grep -q "inspection_creation_failed"; then
        print_error "创建质量检验失败（预期）: 功能未实现"
        echo "响应: $response"
    else
        print_success "创建质量检验成功"
        echo "响应: $response"
    fi
}

# 测试获取待检验项目
test_get_pending_inspections() {
    print_test "获取待检验项目"
    
    response=$(curl -s -X GET "$BASE_URL/api/quality/pending" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json")
    
    if echo "$response" | grep -q "pending_inspections"; then
        print_success "获取待检验项目成功"
        echo "响应: $response"
    else
        print_error "获取待检验项目失败: $response"
    fi
}

# 测试质量指标
test_get_quality_metrics() {
    print_test "获取质量指标"
    
    response=$(curl -s -X GET "$BASE_URL/api/quality/metrics" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json")
    
    if echo "$response" | grep -q "overall_metrics"; then
        pass_rate=$(echo "$response" | jq '.overall_metrics.pass_rate')
        total_inspections=$(echo "$response" | jq '.overall_metrics.total_inspections')
        print_success "获取质量指标成功 - 合格率: $pass_rate%, 总检验数: $total_inspections"
        echo "响应: $response"
    else
        print_error "获取质量指标失败: $response"
    fi
}

# 测试质量报告
test_get_quality_report() {
    print_test "获取质量报告"
    
    response=$(curl -s -X POST "$BASE_URL/api/quality/reports" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "start_date": "2025-07-01T00:00:00Z",
            "end_date": "2025-07-17T23:59:59Z",
            "period_type": "daily"
        }')
    
    if echo "$response" | grep -q "overall_metrics"; then
        print_success "获取质量报告成功"
        echo "响应: $response"
    else
        print_error "获取质量报告失败: $response"
    fi
}

# 测试创建质量检查点
test_create_checkpoint() {
    print_test "创建质量检查点"
    
    response=$(curl -s -X POST "$BASE_URL/api/quality/checkpoints" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "routing_step_id": 1,
            "checkpoint_name": "尺寸检查",
            "description": "检查零件尺寸是否符合要求",
            "required": true,
            "measurement_type": "dimensional",
            "tolerance_min": "9.8",
            "tolerance_max": "10.2",
            "unit_of_measure": "mm"
        }')
    
    if echo "$response" | grep -q "checkpoint_creation_failed"; then
        print_error "创建质量检查点失败（预期）: 功能未实现"
        echo "响应: $response"
    else
        print_success "创建质量检查点成功"
        echo "响应: $response"
    fi
}

# 测试权限控制
test_permission_control() {
    print_test "测试权限控制 - 使用无效token"
    
    response=$(curl -s -X GET "$BASE_URL/api/quality/inspections" \
        -H "Authorization: Bearer invalid-token" \
        -H "Content-Type: application/json")
    
    if [ -z "$response" ]; then
        print_success "权限控制正常 - 无效token被拒绝"
    else
        print_error "权限控制异常: $response"
    fi
}

# 主测试流程
main() {
    print_header "质量模块API测试"
    
    # 检查依赖
    if ! command -v curl &> /dev/null; then
        print_error "curl 未安装"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        print_error "jq 未安装"
        exit 1
    fi
    
    # 执行测试
    if login; then
        echo
        test_get_inspections
        echo
        test_create_inspection
        echo
        test_get_pending_inspections
        echo
        test_get_quality_metrics
        echo
        test_get_quality_report
        echo
        test_create_checkpoint
        echo
        test_permission_control
        
        echo
        print_header "测试完成"
        print_success "质量模块API基本功能测试完成"
        echo -e "${YELLOW}注意: 部分功能显示'未实现'是正常的，因为这些功能在服务层还未完全实现${NC}"
    else
        print_error "无法获取认证token，测试终止"
        exit 1
    fi
}

# 运行测试
main "$@"
