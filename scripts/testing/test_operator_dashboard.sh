#!/bin/bash

# 测试操作员仪表盘功能
echo "=== 测试操作员仪表盘功能 ==="

# 设置基础URL
BASE_URL="http://localhost:9000"

# 1. 登录获取token
echo "1. 登录获取token..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}')

TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.token')
echo "Token: $TOKEN"

if [ "$TOKEN" = "null" ]; then
    echo "❌ 登录失败"
    exit 1
fi

echo "✅ 登录成功"

# 2. 测试操作员仪表盘API
echo -e "\n2. 测试操作员仪表盘API..."
DASHBOARD_RESPONSE=$(curl -s -X GET "$BASE_URL/api/dashboard/operator/1" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json")

echo "仪表盘响应:"
echo $DASHBOARD_RESPONSE | jq '.'

# 检查响应结构
CURRENT_TASK=$(echo $DASHBOARD_RESPONSE | jq '.current_task')
DAILY_STATS=$(echo $DASHBOARD_RESPONSE | jq '.daily_stats')
MY_MACHINES=$(echo $DASHBOARD_RESPONSE | jq '.my_machines')
UPCOMING_TASKS=$(echo $DASHBOARD_RESPONSE | jq '.upcoming_tasks')

if [ "$CURRENT_TASK" != "null" ] || [ "$DAILY_STATS" != "null" ] || [ "$MY_MACHINES" != "null" ] || [ "$UPCOMING_TASKS" != "null" ]; then
    echo "✅ 操作员仪表盘API响应正常"
else
    echo "❌ 操作员仪表盘API响应异常"
fi

# 3. 检查每日统计数据结构
echo -e "\n3. 检查每日统计数据..."
COMPLETED_TASKS=$(echo $DASHBOARD_RESPONSE | jq '.daily_stats.completed_tasks')
EFFICIENCY=$(echo $DASHBOARD_RESPONSE | jq '.daily_stats.efficiency')
QUALITY_RATE=$(echo $DASHBOARD_RESPONSE | jq '.daily_stats.quality_rate')
WORKING_HOURS=$(echo $DASHBOARD_RESPONSE | jq '.daily_stats.working_hours')

echo "完成任务数: $COMPLETED_TASKS"
echo "效率: $EFFICIENCY%"
echo "质量率: $QUALITY_RATE%"
echo "工作时长: $WORKING_HOURS 小时"

if [ "$COMPLETED_TASKS" != "null" ] && [ "$EFFICIENCY" != "null" ] && [ "$QUALITY_RATE" != "null" ] && [ "$WORKING_HOURS" != "null" ]; then
    echo "✅ 每日统计数据结构正确"
else
    echo "❌ 每日统计数据结构异常"
fi

# 4. 测试前端页面可访问性
echo -e "\n4. 测试前端页面可访问性..."
FRONTEND_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:10000)

if [ "$FRONTEND_RESPONSE" = "200" ]; then
    echo "✅ 前端页面可访问 (HTTP $FRONTEND_RESPONSE)"
else
    echo "❌ 前端页面不可访问 (HTTP $FRONTEND_RESPONSE)"
fi

echo -e "\n=== 测试完成 ==="
echo "操作员仪表盘功能已实现并可正常工作！"
echo "前端地址: http://localhost:10000"
echo "后端API地址: http://localhost:9000"
