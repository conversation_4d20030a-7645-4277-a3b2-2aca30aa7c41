#!/usr/bin/env python3
"""
直接在数据库中重置工艺员密码
"""

import bcrypt
import psycopg2
import sys

def hash_password(password: str) -> str:
    """使用bcrypt哈希密码"""
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')

def main():
    print("🔧 直接在数据库中重置工艺员密码...")
    
    # 数据库连接参数
    db_config = {
        'host': 'localhost',
        'database': 'mes_db',
        'user': 'mes_user',
        'password': 'mcpqMlT7hhCy6naS2kSQ1sTDf'
    }
    
    try:
        # 连接数据库
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()
        
        # 新密码
        new_password = "gongyi"
        hashed_password = hash_password(new_password)
        
        print(f"新密码哈希: {hashed_password[:50]}...")
        
        # 更新engineer1用户的密码
        cursor.execute(
            "UPDATE users SET password_hash = %s WHERE username = %s",
            (hashed_password, "engineer1")
        )
        
        if cursor.rowcount > 0:
            conn.commit()
            print("✅ 工艺员密码重置成功")
            
            # 验证更新
            cursor.execute(
                "SELECT id, username, password_hash FROM users WHERE username = %s",
                ("engineer1",)
            )
            result = cursor.fetchone()
            if result:
                print(f"用户ID: {result[0]}, 用户名: {result[1]}")
                print(f"新密码哈希: {result[2][:50]}...")
        else:
            print("❌ 未找到engineer1用户")
            
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
