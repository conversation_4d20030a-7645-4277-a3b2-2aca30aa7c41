#!/usr/bin/env python3
"""
设置自动工单配置
"""

import requests
import json
import sys

BASE_URL = "http://localhost:9001/api"

def get_admin_token():
    """获取管理员token"""
    response = requests.post(f"{BASE_URL}/auth/login", json={
        "username": "admin",
        "password": "admin123"
    })
    
    if response.status_code == 200:
        return response.json()["token"]
    else:
        print(f"❌ 管理员登录失败: {response.status_code}")
        return None

def create_auto_work_order_config(token, config_data):
    """创建自动工单配置"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.post(f"{BASE_URL}/auto-work-orders/configs", 
                           headers=headers, 
                           json=config_data)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 创建配置成功: {config_data['trigger_type']}")
        return result["config"]
    else:
        print(f"❌ 创建配置失败: {config_data['trigger_type']}")
        print(f"错误: {response.text}")
        return None

def main():
    print("🔧 设置自动工单配置...")
    
    # 获取管理员token
    token = get_admin_token()
    if not token:
        return 1
    
    # 定义自动工单配置
    configs = [
        {
            "trigger_type": "part_created",
            "project_id": None,  # 全局配置
            "is_enabled": True,
            "default_quantity": 1,
            "default_due_days": 30,
            "auto_create_plan_tasks": True
        },
        {
            "trigger_type": "routing_created", 
            "project_id": None,  # 全局配置
            "is_enabled": True,
            "default_quantity": 1,
            "default_due_days": 30,
            "auto_create_plan_tasks": True
        },
        {
            "trigger_type": "bom_added",
            "project_id": None,  # 全局配置
            "is_enabled": True,
            "default_quantity": None,  # 使用BOM中的数量
            "default_due_days": 30,
            "auto_create_plan_tasks": True
        }
    ]
    
    print(f"准备创建 {len(configs)} 个自动工单配置...")
    
    created_configs = []
    for config in configs:
        print(f"\n创建配置: {config['trigger_type']}")
        print(f"  触发类型: {config['trigger_type']}")
        print(f"  项目范围: {'全局' if config['project_id'] is None else f'项目ID {config['project_id']}'}")
        print(f"  启用状态: {'是' if config['is_enabled'] else '否'}")
        print(f"  默认数量: {config['default_quantity'] or '使用BOM数量'}")
        print(f"  默认交期: {config['default_due_days']} 天")
        print(f"  自动创建计划: {'是' if config['auto_create_plan_tasks'] else '否'}")
        
        result = create_auto_work_order_config(token, config)
        if result:
            created_configs.append(result)
    
    print(f"\n📊 配置创建结果:")
    print(f"成功创建: {len(created_configs)} 个配置")
    print(f"失败: {len(configs) - len(created_configs)} 个配置")
    
    # 验证配置
    print(f"\n🔍 验证配置...")
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/auto-work-orders/configs", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 当前配置总数: {data['total_count']}")
        
        for config in data['configs']:
            status = "启用" if config['is_enabled'] else "禁用"
            print(f"  - {config['trigger_type']}: {status}")
    else:
        print(f"❌ 验证配置失败: {response.status_code}")
    
    # 测试自动工单创建
    print(f"\n🧪 测试自动工单创建功能...")
    print("现在您可以:")
    print("1. 创建新零件 - 应该自动创建工单")
    print("2. 添加项目BOM - 应该自动创建工单")
    print("3. 创建工艺路线 - 应该自动创建工单")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
