#!/usr/bin/env python3
"""
重置工艺员密码
"""

import requests
import json

BASE_URL = "http://localhost:9001/api"

def main():
    print("🔧 重置工艺员密码...")
    
    # 1. 管理员登录
    admin_login = requests.post(f"{BASE_URL}/auth/login", json={
        "username": "admin",
        "password": "admin123"
    })
    
    if admin_login.status_code != 200:
        print("❌ 管理员登录失败")
        return
    
    admin_token = admin_login.json()["token"]
    print("✅ 管理员登录成功")
    
    # 2. 重置工艺员密码
    headers = {
        "Authorization": f"Bearer {admin_token}",
        "Content-Type": "application/json"
    }
    
    # 假设用户ID是2（从之前的查询结果看到）
    user_id = 2
    new_password = "gongyi"
    
    update_data = {
        "password": new_password
    }
    
    try:
        response = requests.put(f"{BASE_URL}/users/{user_id}", 
                              headers=headers, 
                              json=update_data)
        
        print(f"密码重置响应状态: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print(f"✅ 工艺员密码重置成功，新密码: {new_password}")
            
            # 3. 测试新密码登录
            test_login = requests.post(f"{BASE_URL}/auth/login", json={
                "username": "engineer1",
                "password": new_password
            })
            
            if test_login.status_code == 200:
                print("✅ 新密码登录测试成功")
                token = test_login.json()["token"]
                
                # 4. 测试零件创建权限
                part_data = {
                    "part_number": "TEST-RESET-001",
                    "part_name": "重置测试零件",
                    "version": "1.0",
                    "specifications": "测试规格"
                }
                
                part_response = requests.post(f"{BASE_URL}/parts",
                                            headers={"Authorization": f"Bearer {token}",
                                                   "Content-Type": "application/json"},
                                            json=part_data)
                
                print(f"零件创建测试状态: {part_response.status_code}")
                if part_response.status_code in [200, 201]:
                    print("✅ 工艺员可以成功创建零件")
                    print(f"创建结果: {part_response.json()}")
                else:
                    print("❌ 工艺员创建零件失败")
                    print(f"错误信息: {part_response.text}")
            else:
                print(f"❌ 新密码登录测试失败: {test_login.status_code}")
        else:
            print("❌ 密码重置失败")
            
    except Exception as e:
        print(f"❌ 重置过程异常: {e}")

if __name__ == "__main__":
    main()
