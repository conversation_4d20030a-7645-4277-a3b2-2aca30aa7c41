#!/usr/bin/env python3
"""
简单的数据清理脚本 - 使用TRUNCATE CASCADE
"""

import psycopg2
import sys

# 数据库连接参数
DB_CONFIG = {
    'host': 'localhost',
    'database': 'mes_db',
    'user': 'mes_user',
    'password': 'mcpqMlT7hhCy6naS2kSQ1sTDf'
}

def show_current_data_count(cursor):
    """显示当前数据统计"""
    print("📊 当前数据统计:")
    
    tables = [
        ('projects', '项目'),
        ('parts', '零件'),
        ('routings', '工艺路线'),
        ('plan_tasks', '计划任务'),
        ('work_orders', '工单'),
        ('project_boms', '项目BOM'),
        ('auto_work_order_logs', '自动工单日志'),
        ('execution_logs', '执行日志'),
        ('quality_inspections', '质量检验'),
        ('quality_measurements', '质量测量'),
        ('quality_alerts', '质量警报')
    ]
    
    total_count = 0
    for table, name in tables:
        try:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"  {name}: {count} 条")
            total_count += count
        except Exception as e:
            print(f"  {name}: 查询失败 - {e}")
    
    print(f"\n总计: {total_count} 条记录")
    return total_count

def truncate_all_data(cursor, conn):
    """使用TRUNCATE CASCADE清空所有相关表"""
    print("\n🗑️  开始清空数据...")
    
    # 主要的业务数据表
    main_tables = [
        'projects',
        'parts', 
        'routings',
        'plan_tasks',
        'work_orders',
        'project_boms',
        'auto_work_order_logs',
        'execution_logs',
        'quality_inspections',
        'quality_measurements',
        'quality_alerts'
    ]
    
    try:
        # 使用TRUNCATE CASCADE一次性清空所有相关表
        for table in main_tables:
            try:
                cursor.execute(f"TRUNCATE TABLE {table} RESTART IDENTITY CASCADE")
                print(f"  ✅ 清空表: {table}")
            except Exception as e:
                print(f"  ⚠️  清空表 {table} 失败: {e}")
        
        # 提交事务
        conn.commit()
        print("\n✅ 所有数据清空完成！")
        
    except Exception as e:
        print(f"\n❌ 清空过程中出错: {e}")
        conn.rollback()
        raise

def main():
    print("🚨 MES系统数据快速清理工具")
    print("=" * 50)
    print("⚠️  警告：此操作将清空以下所有数据：")
    print("   - 项目、零件、工艺路线")
    print("   - 计划任务、工单")
    print("   - 项目BOM、执行日志")
    print("   - 质量相关数据")
    print("   - 自动工单日志")
    print("=" * 50)
    print("💡 使用TRUNCATE CASCADE，会自动处理外键约束")
    print("=" * 50)
    
    # 确认操作
    confirm = input("确定要继续吗？输入 'YES' 确认: ")
    if confirm != 'YES':
        print("❌ 操作已取消")
        return 1
    
    try:
        # 连接数据库
        print("\n🔌 连接数据库...")
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        print("✅ 数据库连接成功")
        
        # 显示当前数据统计
        total_before = show_current_data_count(cursor)
        
        if total_before == 0:
            print("\n💡 数据库已经是空的，无需清理")
            return 0
        
        # 最后确认
        final_confirm = input(f"\n确定要清空 {total_before} 条记录吗？输入 'DELETE' 确认: ")
        if final_confirm != 'DELETE':
            print("❌ 清空操作已取消")
            return 1
        
        # 清空数据
        truncate_all_data(cursor, conn)
        
        # 验证清空结果
        print("\n📊 清空后数据统计:")
        total_after = show_current_data_count(cursor)
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 数据清理完成！")
        print(f"📊 清理前: {total_before} 条记录")
        print(f"📊 清理后: {total_after} 条记录")
        print("💡 所有ID序列已重置为1")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ 操作失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
