#!/usr/bin/env python3
"""
清理所有项目、零件、工艺、计划数据
"""

import psycopg2
import json
import sys
from datetime import datetime, date
from decimal import Decimal

# 数据库连接参数
DB_CONFIG = {
    'host': 'localhost',
    'database': 'mes_db',
    'user': 'mes_user',
    'password': 'mcpqMlT7hhCy6naS2kSQ1sTDf'
}

def backup_data(cursor):
    """备份数据到JSON文件"""
    print("📦 备份数据...")
    
    backup_data = {
        'backup_time': datetime.now().isoformat(),
        'tables': {}
    }
    
    # 要备份的表（只包含实际存在的表）
    tables_to_backup = [
        'projects',
        'parts',
        'routings',
        'plan_tasks',
        'work_orders',
        'project_boms',
        'auto_work_order_logs'
    ]
    
    for table in tables_to_backup:
        try:
            # 检查表是否存在
            cursor.execute(f"SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = '{table}')")
            table_exists = cursor.fetchone()[0]

            if not table_exists:
                print(f"  ⏭️  跳过 {table}: 表不存在")
                continue

            cursor.execute(f"SELECT * FROM {table}")
            rows = cursor.fetchall()

            # 获取列名
            cursor.execute(f"SELECT column_name FROM information_schema.columns WHERE table_name = '{table}' ORDER BY ordinal_position")
            columns = [row[0] for row in cursor.fetchall()]
            
            # 转换为字典列表
            table_data = []
            for row in rows:
                row_dict = {}
                for i, value in enumerate(row):
                    if isinstance(value, (datetime, date)):
                        row_dict[columns[i]] = value.isoformat()
                    elif isinstance(value, Decimal):
                        row_dict[columns[i]] = float(value)
                    else:
                        row_dict[columns[i]] = value
                table_data.append(row_dict)
            
            backup_data['tables'][table] = {
                'count': len(table_data),
                'data': table_data
            }
            
            print(f"  ✅ 备份 {table}: {len(table_data)} 条记录")
            
        except Exception as e:
            print(f"  ❌ 备份 {table} 失败: {e}")
    
    # 保存备份文件
    backup_filename = f"mes_data_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(backup_filename, 'w', encoding='utf-8') as f:
        json.dump(backup_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 数据备份完成: {backup_filename}")
    return backup_filename

def show_current_data_count(cursor):
    """显示当前数据统计"""
    print("📊 当前数据统计:")
    
    tables = [
        ('projects', '项目'),
        ('parts', '零件'),
        ('routings', '工艺路线'),
        ('plan_tasks', '计划任务'),
        ('work_orders', '工单'),
        ('project_boms', '项目BOM'),
        ('auto_work_order_logs', '自动工单日志')
    ]
    
    for table, name in tables:
        try:
            # 检查表是否存在
            cursor.execute(f"SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = '{table}')")
            table_exists = cursor.fetchone()[0]

            if not table_exists:
                print(f"  {name}: 表不存在")
                continue

            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"  {name}: {count} 条")
        except Exception as e:
            print(f"  {name}: 查询失败 - {e}")

def delete_all_data(cursor, conn):
    """删除所有相关数据"""
    print("\n🗑️  开始删除数据...")
    
    # 删除顺序很重要，要先删除有外键依赖的表
    delete_order = [
        ('auto_work_order_logs', '自动工单日志'),
        ('plan_tasks', '计划任务'),
        ('work_orders', '工单'),
        ('project_boms', '项目BOM'),
        ('routings', '工艺路线'),
        ('parts', '零件'),
        ('projects', '项目')
    ]
    
    try:
        for table, name in delete_order:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count_before = cursor.fetchone()[0]
            
            if count_before > 0:
                cursor.execute(f"DELETE FROM {table}")
                print(f"  ✅ 删除 {name}: {count_before} 条记录")
            else:
                print(f"  ⏭️  跳过 {name}: 无数据")
        
        # 重置序列
        print("\n🔄 重置ID序列...")
        sequences = [
            'projects_id_seq',
            'parts_id_seq',
            'routings_id_seq',
            'plan_tasks_id_seq',
            'work_orders_id_seq',
            'project_boms_id_seq',
            'auto_work_order_logs_id_seq'
        ]
        
        for seq in sequences:
            try:
                cursor.execute(f"ALTER SEQUENCE {seq} RESTART WITH 1")
                print(f"  ✅ 重置序列: {seq}")
            except Exception as e:
                print(f"  ⚠️  重置序列 {seq} 失败: {e}")
        
        # 提交事务
        conn.commit()
        print("\n✅ 所有数据删除完成！")
        
    except Exception as e:
        print(f"\n❌ 删除过程中出错: {e}")
        conn.rollback()
        raise

def main():
    print("🚨 MES系统数据清理工具")
    print("=" * 50)
    print("⚠️  警告：此操作将删除以下所有数据：")
    print("   - 项目 (projects)")
    print("   - 零件 (parts)")
    print("   - 工艺路线 (routings)")
    print("   - 计划任务 (plan_tasks)")
    print("   - 工单 (work_orders)")
    print("   - 项目BOM (project_boms)")

    print("   - 自动工单日志 (auto_work_order_logs)")
    print("=" * 50)
    
    # 确认操作
    confirm = input("确定要继续吗？输入 'YES' 确认: ")
    if confirm != 'YES':
        print("❌ 操作已取消")
        return 1
    
    try:
        # 连接数据库
        print("\n🔌 连接数据库...")
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        print("✅ 数据库连接成功")
        
        # 显示当前数据统计
        show_current_data_count(cursor)
        
        # 备份数据
        backup_filename = backup_data(cursor)
        
        # 最后确认
        print(f"\n📋 数据已备份到: {backup_filename}")
        final_confirm = input("确定要删除所有数据吗？输入 'DELETE' 确认: ")
        if final_confirm != 'DELETE':
            print("❌ 删除操作已取消")
            return 1
        
        # 删除数据
        delete_all_data(cursor, conn)
        
        # 验证删除结果
        print("\n📊 删除后数据统计:")
        show_current_data_count(cursor)
        
        cursor.close()
        conn.close()
        
        print("\n🎉 数据清理完成！")
        print(f"📦 备份文件: {backup_filename}")
        print("💡 如需恢复数据，请联系管理员")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ 操作失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
