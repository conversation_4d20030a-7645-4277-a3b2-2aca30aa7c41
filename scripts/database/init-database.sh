#!/bin/bash

# MES系统数据库初始化脚本
# 用于初始化PostgreSQL数据库和运行迁移

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 默认配置
DEFAULT_DB_HOST="localhost"
DEFAULT_DB_PORT="5432"
DEFAULT_DB_NAME="mes_db"
DEFAULT_DB_USER="mes_user"
DEFAULT_ADMIN_USER="admin"
DEFAULT_ADMIN_PASSWORD="admin123"

# 读取配置
read_config() {
    # 从.env文件读取配置
    if [ -f ".env" ]; then
        source .env
        log_info "从.env文件读取配置"
    fi
    
    # 从环境变量或用户输入获取配置
    read -p "数据库主机 [$DEFAULT_DB_HOST]: " DB_HOST
    DB_HOST=${DB_HOST:-$DEFAULT_DB_HOST}
    
    read -p "数据库端口 [$DEFAULT_DB_PORT]: " DB_PORT
    DB_PORT=${DB_PORT:-$DEFAULT_DB_PORT}
    
    read -p "数据库名称 [$DEFAULT_DB_NAME]: " DB_NAME
    DB_NAME=${DB_NAME:-$DEFAULT_DB_NAME}
    
    read -p "数据库用户 [$DEFAULT_DB_USER]: " DB_USER
    DB_USER=${DB_USER:-$DEFAULT_DB_USER}
    
    read -s -p "数据库密码: " DB_PASSWORD
    echo
    
    if [ -z "$DB_PASSWORD" ]; then
        log_error "数据库密码不能为空"
        exit 1
    fi
    
    # 构建数据库URL
    DATABASE_URL="postgresql://$DB_USER:$DB_PASSWORD@$DB_HOST:$DB_PORT/$DB_NAME"
}

# 检查PostgreSQL连接
check_postgres_connection() {
    log_info "检查PostgreSQL连接..."
    
    if psql "$DATABASE_URL" -c "SELECT 1;" > /dev/null 2>&1; then
        log_success "数据库连接成功"
        return 0
    else
        log_error "数据库连接失败"
        return 1
    fi
}

# 创建数据库和用户（如果不存在）
create_database_if_not_exists() {
    log_info "检查并创建数据库..."
    
    # 使用postgres用户连接
    POSTGRES_URL="postgresql://postgres@$DB_HOST:$DB_PORT/postgres"
    
    # 检查数据库是否存在
    DB_EXISTS=$(psql "$POSTGRES_URL" -tAc "SELECT 1 FROM pg_database WHERE datname='$DB_NAME';" 2>/dev/null || echo "")
    
    if [ -z "$DB_EXISTS" ]; then
        log_info "创建数据库 $DB_NAME..."
        psql "$POSTGRES_URL" -c "CREATE DATABASE $DB_NAME;"
    else
        log_info "数据库 $DB_NAME 已存在"
    fi
    
    # 检查用户是否存在
    USER_EXISTS=$(psql "$POSTGRES_URL" -tAc "SELECT 1 FROM pg_user WHERE usename='$DB_USER';" 2>/dev/null || echo "")
    
    if [ -z "$USER_EXISTS" ]; then
        log_info "创建用户 $DB_USER..."
        psql "$POSTGRES_URL" -c "CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';"
        psql "$POSTGRES_URL" -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;"
    else
        log_info "用户 $DB_USER 已存在"
    fi
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 检查迁移文件
    if [ ! -d "migrations" ]; then
        log_error "未找到migrations目录"
        exit 1
    fi
    
    # 创建迁移历史表
    psql "$DATABASE_URL" -c "
        CREATE TABLE IF NOT EXISTS schema_migrations (
            version VARCHAR(255) PRIMARY KEY,
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
    " > /dev/null
    
    # 按顺序执行迁移文件
    for migration_file in migrations/*.sql; do
        if [ -f "$migration_file" ]; then
            migration_name=$(basename "$migration_file" .sql)
            
            # 检查迁移是否已执行
            MIGRATION_EXISTS=$(psql "$DATABASE_URL" -tAc "SELECT 1 FROM schema_migrations WHERE version='$migration_name';" 2>/dev/null || echo "")
            
            if [ -z "$MIGRATION_EXISTS" ]; then
                log_info "执行迁移: $migration_name"
                
                # 执行迁移
                if psql "$DATABASE_URL" -f "$migration_file" > /dev/null 2>&1; then
                    # 记录迁移历史
                    psql "$DATABASE_URL" -c "INSERT INTO schema_migrations (version) VALUES ('$migration_name');" > /dev/null
                    log_success "迁移 $migration_name 执行成功"
                else
                    log_error "迁移 $migration_name 执行失败"
                    exit 1
                fi
            else
                log_info "迁移 $migration_name 已执行，跳过"
            fi
        fi
    done
    
    log_success "所有迁移执行完成"
}

# 初始化基础数据
init_base_data() {
    log_info "初始化基础数据..."
    
    # 检查是否已有管理员用户
    ADMIN_EXISTS=$(psql "$DATABASE_URL" -tAc "SELECT 1 FROM users WHERE username='$DEFAULT_ADMIN_USER';" 2>/dev/null || echo "")
    
    if [ -z "$ADMIN_EXISTS" ]; then
        log_info "创建默认管理员用户..."
        
        # 生成密码哈希（使用bcrypt）
        HASHED_PASSWORD=$(python3 -c "
import bcrypt
password = '$DEFAULT_ADMIN_PASSWORD'.encode('utf-8')
hashed = bcrypt.hashpw(password, bcrypt.gensalt())
print(hashed.decode('utf-8'))
" 2>/dev/null || echo "")
        
        if [ -z "$HASHED_PASSWORD" ]; then
            # 如果Python不可用，使用简单的哈希（仅用于开发）
            log_warning "无法生成bcrypt哈希，使用简单哈希（仅用于开发）"
            HASHED_PASSWORD="$DEFAULT_ADMIN_PASSWORD"
        fi
        
        # 插入管理员用户
        psql "$DATABASE_URL" -c "
            INSERT INTO users (id, username, password_hash, email, role, created_at, updated_at)
            VALUES (
                gen_random_uuid(),
                '$DEFAULT_ADMIN_USER',
                '$HASHED_PASSWORD',
                '<EMAIL>',
                'Admin',
                CURRENT_TIMESTAMP,
                CURRENT_TIMESTAMP
            );
        " > /dev/null
        
        log_success "默认管理员用户创建成功"
        log_info "用户名: $DEFAULT_ADMIN_USER"
        log_info "密码: $DEFAULT_ADMIN_PASSWORD"
    else
        log_info "管理员用户已存在，跳过创建"
    fi
    
    # 初始化系统配置
    log_info "初始化系统配置..."
    psql "$DATABASE_URL" -c "
        INSERT INTO system_configs (key, value, description, created_at, updated_at)
        VALUES 
            ('system.name', 'MES制造执行系统', '系统名称', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            ('system.version', '1.0.0', '系统版本', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            ('system.timezone', 'Asia/Shanghai', '系统时区', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            ('plan.assignment_mode', 'skill_group', '计划分配模式', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            ('work_order.auto_create', 'false', '自动创建工单', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        ON CONFLICT (key) DO NOTHING;
    " > /dev/null 2>&1 || log_warning "系统配置初始化可能失败（表可能不存在）"
    
    log_success "基础数据初始化完成"
}

# 验证数据库状态
verify_database() {
    log_info "验证数据库状态..."
    
    # 检查表是否存在
    TABLES=$(psql "$DATABASE_URL" -tAc "
        SELECT COUNT(*) FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
    " 2>/dev/null || echo "0")
    
    if [ "$TABLES" -gt 0 ]; then
        log_success "数据库表创建成功，共 $TABLES 个表"
    else
        log_error "数据库表创建失败"
        return 1
    fi
    
    # 检查管理员用户
    ADMIN_COUNT=$(psql "$DATABASE_URL" -tAc "SELECT COUNT(*) FROM users WHERE role = 'Admin';" 2>/dev/null || echo "0")
    
    if [ "$ADMIN_COUNT" -gt 0 ]; then
        log_success "管理员用户创建成功"
    else
        log_warning "未找到管理员用户"
    fi
    
    # 显示数据库统计
    log_info "数据库统计信息:"
    psql "$DATABASE_URL" -c "
        SELECT 
            schemaname,
            tablename,
            n_tup_ins as inserts,
            n_tup_upd as updates,
            n_tup_del as deletes
        FROM pg_stat_user_tables 
        ORDER BY tablename;
    " 2>/dev/null || log_warning "无法获取统计信息"
    
    log_success "数据库验证完成"
}

# 创建备份
create_backup() {
    log_info "创建数据库备份..."
    
    BACKUP_DIR="./backups"
    mkdir -p "$BACKUP_DIR"
    
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_FILE="$BACKUP_DIR/mes_init_backup_$TIMESTAMP.sql"
    
    if pg_dump "$DATABASE_URL" > "$BACKUP_FILE"; then
        log_success "备份创建成功: $BACKUP_FILE"
    else
        log_warning "备份创建失败"
    fi
}

# 显示初始化结果
show_results() {
    echo
    echo "=================================="
    echo "🗄️  数据库初始化完成！"
    echo "=================================="
    echo
    echo "📋 数据库信息:"
    echo "  - 主机: $DB_HOST:$DB_PORT"
    echo "  - 数据库: $DB_NAME"
    echo "  - 用户: $DB_USER"
    echo "  - 连接URL: postgresql://$DB_USER:***@$DB_HOST:$DB_PORT/$DB_NAME"
    echo
    echo "👤 默认管理员:"
    echo "  - 用户名: $DEFAULT_ADMIN_USER"
    echo "  - 密码: $DEFAULT_ADMIN_PASSWORD"
    echo
    echo "📊 数据库状态:"
    TABLES=$(psql "$DATABASE_URL" -tAc "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null || echo "未知")
    USERS=$(psql "$DATABASE_URL" -tAc "SELECT COUNT(*) FROM users;" 2>/dev/null || echo "未知")
    echo "  - 数据表数量: $TABLES"
    echo "  - 用户数量: $USERS"
    echo
    echo "🔧 管理命令:"
    echo "  - 连接数据库: psql \"$DATABASE_URL\""
    echo "  - 备份数据库: pg_dump \"$DATABASE_URL\" > backup.sql"
    echo "  - 恢复数据库: psql \"$DATABASE_URL\" < backup.sql"
    echo
    echo "⚠️  重要提醒:"
    echo "  - 请妥善保管数据库密码"
    echo "  - 生产环境请修改默认管理员密码"
    echo "  - 定期备份数据库数据"
    echo
}

# 主程序
main() {
    echo "=================================="
    echo "🗄️  MES系统数据库初始化程序"
    echo "=================================="
    echo
    
    # 检查依赖
    if ! command -v psql &> /dev/null; then
        log_error "未找到psql命令，请先安装PostgreSQL客户端"
        exit 1
    fi
    
    # 读取配置
    read_config
    
    # 检查连接或创建数据库
    if ! check_postgres_connection; then
        log_info "尝试创建数据库和用户..."
        create_database_if_not_exists
        
        if ! check_postgres_connection; then
            log_error "数据库连接失败，请检查配置"
            exit 1
        fi
    fi
    
    # 运行迁移
    run_migrations
    
    # 初始化基础数据
    init_base_data
    
    # 验证数据库
    if verify_database; then
        # 创建备份
        create_backup
        
        # 显示结果
        show_results
    else
        log_error "数据库验证失败"
        exit 1
    fi
}

# 运行主程序
main "$@"
