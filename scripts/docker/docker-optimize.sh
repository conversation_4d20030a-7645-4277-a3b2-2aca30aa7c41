#!/bin/bash

# Docker镜像优化脚本
# 用于优化Docker镜像大小和构建速度

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 清理Docker缓存
clean_docker_cache() {
    log_info "清理Docker构建缓存..."
    
    # 清理未使用的镜像
    docker image prune -f
    
    # 清理构建缓存
    docker builder prune -f
    
    # 清理未使用的容器
    docker container prune -f
    
    # 清理未使用的网络
    docker network prune -f
    
    # 清理未使用的卷
    docker volume prune -f
    
    log_success "Docker缓存清理完成"
}

# 构建优化的镜像
build_optimized_images() {
    log_info "构建优化的Docker镜像..."
    
    # 构建后端镜像
    log_info "构建后端镜像..."
    docker build \
        --target builder \
        --cache-from mes-backend:builder \
        -t mes-backend:builder \
        .
    
    docker build \
        --cache-from mes-backend:builder \
        --cache-from mes-backend:latest \
        -t mes-backend:latest \
        .
    
    # 构建前端镜像
    log_info "构建前端镜像..."
    docker build \
        --target builder \
        --cache-from mes-frontend:builder \
        -t mes-frontend:builder \
        ./frontend
    
    docker build \
        --cache-from mes-frontend:builder \
        --cache-from mes-frontend:latest \
        -t mes-frontend:latest \
        ./frontend
    
    log_success "镜像构建完成"
}

# 分析镜像大小
analyze_image_sizes() {
    log_info "分析镜像大小..."
    
    echo "=================================="
    echo "📊 镜像大小分析"
    echo "=================================="
    
    # 显示镜像大小
    docker images | grep -E "(mes-|postgres|redis|nginx)" | sort -k7 -h
    
    echo ""
    echo "=================================="
    echo "📈 镜像层分析"
    echo "=================================="
    
    # 分析后端镜像
    if docker images | grep -q "mes-backend"; then
        echo "后端镜像层:"
        docker history mes-backend:latest --format "table {{.CreatedBy}}\t{{.Size}}" | head -10
    fi
    
    echo ""
    
    # 分析前端镜像
    if docker images | grep -q "mes-frontend"; then
        echo "前端镜像层:"
        docker history mes-frontend:latest --format "table {{.CreatedBy}}\t{{.Size}}" | head -10
    fi
}

# 优化建议
show_optimization_tips() {
    echo ""
    echo "=================================="
    echo "💡 优化建议"
    echo "=================================="
    echo
    echo "1. 使用多阶段构建减少镜像大小"
    echo "2. 合并RUN指令减少镜像层数"
    echo "3. 使用.dockerignore排除不必要文件"
    echo "4. 使用Alpine Linux基础镜像"
    echo "5. 清理包管理器缓存"
    echo "6. 使用构建缓存加速构建"
    echo "7. 定期清理未使用的镜像和容器"
    echo
}

# 主函数
main() {
    echo "=================================="
    echo "🐳 Docker镜像优化工具"
    echo "=================================="
    echo
    
    case ${1:-build} in
        "clean")
            clean_docker_cache
            ;;
        "build")
            build_optimized_images
            ;;
        "analyze")
            analyze_image_sizes
            ;;
        "all")
            clean_docker_cache
            build_optimized_images
            analyze_image_sizes
            show_optimization_tips
            ;;
        *)
            log_error "未知操作: $1"
            echo "用法: $0 [clean|build|analyze|all]"
            echo "  clean   - 清理Docker缓存"
            echo "  build   - 构建优化镜像"
            echo "  analyze - 分析镜像大小"
            echo "  all     - 执行所有操作"
            exit 1
            ;;
    esac
}

# 运行主程序
main "$@"
