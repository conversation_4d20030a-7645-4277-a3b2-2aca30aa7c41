#!/bin/bash

# MES系统Docker安装脚本
# 支持Docker和Docker Compose部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检测操作系统
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if [ -f /etc/debian_version ]; then
            OS="debian"
        elif [ -f /etc/redhat-release ]; then
            OS="redhat"
        else
            OS="linux"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    else
        log_error "不支持的操作系统: $OSTYPE"
        exit 1
    fi
    log_info "检测到操作系统: $OS"
}

# 检查Docker是否安装
check_docker() {
    if command -v docker &> /dev/null; then
        log_info "Docker已安装，版本: $(docker --version)"
        return 0
    else
        return 1
    fi
}

# 检查Docker Compose是否安装
check_docker_compose() {
    if command -v docker-compose &> /dev/null; then
        log_info "Docker Compose已安装，版本: $(docker-compose --version)"
        return 0
    elif docker compose version &> /dev/null; then
        log_info "Docker Compose (plugin)已安装，版本: $(docker compose version)"
        return 0
    else
        return 1
    fi
}

# 安装Docker
install_docker() {
    log_info "安装Docker..."
    
    case $OS in
        "debian")
            # 更新包索引
            sudo apt-get update
            
            # 安装必要的包
            sudo apt-get install -y \
                apt-transport-https \
                ca-certificates \
                curl \
                gnupg \
                lsb-release
            
            # 添加Docker官方GPG密钥
            curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
            
            # 设置稳定版仓库
            echo \
                "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu \
                $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
            
            # 安装Docker Engine
            sudo apt-get update
            sudo apt-get install -y docker-ce docker-ce-cli containerd.io
            ;;
        "redhat")
            # 安装yum-utils
            sudo yum install -y yum-utils
            
            # 添加Docker仓库
            sudo yum-config-manager \
                --add-repo \
                https://download.docker.com/linux/centos/docker-ce.repo
            
            # 安装Docker Engine
            sudo yum install -y docker-ce docker-ce-cli containerd.io
            ;;
        "macos")
            log_info "请手动下载并安装Docker Desktop for Mac"
            log_info "下载地址: https://www.docker.com/products/docker-desktop"
            read -p "安装完成后按Enter继续..."
            ;;
    esac
    
    # 启动Docker服务
    if [[ "$OS" != "macos" ]]; then
        sudo systemctl start docker
        sudo systemctl enable docker
        
        # 将当前用户添加到docker组
        sudo usermod -aG docker $USER
        log_warning "请重新登录以使docker组权限生效，或运行: newgrp docker"
    fi
    
    log_success "Docker安装完成"
}

# 安装Docker Compose
install_docker_compose() {
    log_info "安装Docker Compose..."
    
    # 获取最新版本号
    COMPOSE_VERSION=$(curl -s https://api.github.com/repos/docker/compose/releases/latest | grep 'tag_name' | cut -d\" -f4)
    
    case $OS in
        "linux"|"debian"|"redhat")
            # 下载Docker Compose
            sudo curl -L "https://github.com/docker/compose/releases/download/${COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
            
            # 添加执行权限
            sudo chmod +x /usr/local/bin/docker-compose
            ;;
        "macos")
            # macOS的Docker Desktop已包含Docker Compose
            log_info "Docker Desktop for Mac已包含Docker Compose"
            ;;
    esac
    
    log_success "Docker Compose安装完成"
}

# 创建Dockerfile
create_dockerfile() {
    log_info "创建Dockerfile..."
    
    cat > Dockerfile << 'EOF'
# 多阶段构建 - 构建阶段
FROM rust:1.75 as builder

WORKDIR /app

# 复制Cargo文件
COPY Cargo.toml Cargo.lock ./

# 创建虚拟main.rs以缓存依赖
RUN mkdir src && echo "fn main() {}" > src/main.rs
RUN cargo build --release
RUN rm src/main.rs

# 复制源代码
COPY src ./src
COPY migrations ./migrations

# 构建应用
RUN cargo build --release

# 运行阶段
FROM debian:bookworm-slim

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    libpq5 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/target/release/mes-system /app/mes-system
COPY --from=builder /app/migrations /app/migrations

# 创建非root用户
RUN useradd -r -s /bin/false mes
RUN chown -R mes:mes /app
USER mes

EXPOSE 8080

CMD ["./mes-system"]
EOF
    
    log_success "Dockerfile创建完成"
}

# 创建前端Dockerfile
create_frontend_dockerfile() {
    log_info "创建前端Dockerfile..."
    
    cat > frontend/Dockerfile << 'EOF'
# 构建阶段
FROM node:18-alpine as builder

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 运行阶段
FROM nginx:alpine

# 复制构建结果
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
EOF
    
    # 创建nginx配置
    cat > frontend/nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # 处理SPA路由
        location / {
            try_files $uri $uri/ /index.html;
        }

        # API代理
        location /api/ {
            proxy_pass http://backend:8080/api/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
EOF
    
    log_success "前端Dockerfile创建完成"
}

# 创建docker-compose.yml
create_docker_compose() {
    log_info "创建docker-compose.yml..."
    
    # 生成随机密码
    DB_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    JWT_SECRET=$(openssl rand -base64 64 | tr -d "=+/" | cut -c1-50)
    
    cat > docker-compose.yml << EOF
version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: mes-postgres
    environment:
      POSTGRES_DB: mes_db
      POSTGRES_USER: mes_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - mes-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U mes_user -d mes_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 后端API服务
  backend:
    build: .
    container_name: mes-backend
    environment:
      DATABASE_URL: postgresql://mes_user:${DB_PASSWORD}@postgres:5432/mes_db
      JWT_SECRET: ${JWT_SECRET}
      RUST_LOG: info
      SERVER_HOST: 0.0.0.0
      SERVER_PORT: 9000
    ports:
      - "9000:9000"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - mes-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端Web服务
  frontend:
    build: ./frontend
    container_name: mes-frontend
    ports:
      - "3000:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - mes-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local

networks:
  mes-network:
    driver: bridge
EOF
    
    # 创建数据库初始化脚本
    cat > init-db.sql << 'EOF'
-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 设置时区
SET timezone = 'Asia/Shanghai';
EOF
    
    # 保存密码到.env文件
    cat > .env.docker << EOF
# Docker环境配置
POSTGRES_PASSWORD=${DB_PASSWORD}
JWT_SECRET=${JWT_SECRET}

# 服务地址
BACKEND_URL=http://localhost:8080
FRONTEND_URL=http://localhost:3000
DATABASE_URL=postgresql://mes_user:${DB_PASSWORD}@localhost:5432/mes_db
EOF
    
    log_success "docker-compose.yml创建完成"
    log_info "数据库密码已保存到.env.docker文件中"
}

# 创建Docker管理脚本
create_docker_scripts() {
    log_info "创建Docker管理脚本..."
    
    # 启动脚本
    cat > docker-start.sh << 'EOF'
#!/bin/bash
echo "启动MES系统Docker容器..."
docker-compose up -d

echo "等待服务启动..."
sleep 10

echo "检查服务状态..."
docker-compose ps

echo ""
echo "🎉 MES系统启动完成！"
echo "前端地址: http://localhost:3000"
echo "后端API: http://localhost:8080"
echo "默认登录: admin/admin123"
EOF
    
    # 停止脚本
    cat > docker-stop.sh << 'EOF'
#!/bin/bash
echo "停止MES系统Docker容器..."
docker-compose down
echo "容器已停止"
EOF
    
    # 重启脚本
    cat > docker-restart.sh << 'EOF'
#!/bin/bash
echo "重启MES系统Docker容器..."
docker-compose restart
echo "容器已重启"
EOF
    
    # 查看日志脚本
    cat > docker-logs.sh << 'EOF'
#!/bin/bash
if [ -z "$1" ]; then
    echo "查看所有服务日志..."
    docker-compose logs -f
else
    echo "查看 $1 服务日志..."
    docker-compose logs -f $1
fi
EOF
    
    # 清理脚本
    cat > docker-clean.sh << 'EOF'
#!/bin/bash
echo "清理MES系统Docker资源..."
read -p "这将删除所有容器、镜像和数据，确定继续？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    docker-compose down -v --rmi all
    docker system prune -f
    echo "清理完成"
else
    echo "取消清理"
fi
EOF
    
    # 备份脚本
    cat > docker-backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="./backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="mes_backup_${TIMESTAMP}.sql"

mkdir -p $BACKUP_DIR

echo "备份数据库到 $BACKUP_DIR/$BACKUP_FILE ..."
docker-compose exec postgres pg_dump -U mes_user mes_db > "$BACKUP_DIR/$BACKUP_FILE"

if [ $? -eq 0 ]; then
    echo "备份完成: $BACKUP_DIR/$BACKUP_FILE"
else
    echo "备份失败"
    exit 1
fi
EOF
    
    chmod +x docker-*.sh
    log_success "Docker管理脚本创建完成"
}

# 构建和启动服务
build_and_start() {
    log_info "构建Docker镜像..."
    
    # 构建镜像
    docker-compose build
    
    log_info "启动服务..."
    
    # 启动服务
    docker-compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    log_info "检查服务状态..."
    docker-compose ps
}

# 验证安装
verify_docker_installation() {
    log_info "验证Docker安装..."
    
    # 检查容器状态
    if docker-compose ps | grep -q "Up"; then
        log_success "容器运行正常"
    else
        log_error "容器启动失败"
        docker-compose logs
        return 1
    fi
    
    # 检查后端API
    sleep 5
    if curl -f http://localhost:8080/api/health > /dev/null 2>&1; then
        log_success "后端API响应正常"
    else
        log_warning "后端API响应异常，请检查日志"
    fi
    
    # 检查前端
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        log_success "前端服务响应正常"
    else
        log_warning "前端服务响应异常，请检查日志"
    fi
    
    log_success "Docker安装验证完成"
}

# 显示安装结果
show_docker_results() {
    echo
    echo "=================================="
    echo "🐳 MES系统Docker部署完成！"
    echo "=================================="
    echo
    echo "📋 服务信息:"
    echo "  - 前端界面: http://localhost:3000"
    echo "  - 后端API: http://localhost:8080"
    echo "  - 数据库: PostgreSQL (端口5432)"
    echo
    echo "🔑 默认登录信息:"
    echo "  - 用户名: admin"
    echo "  - 密码: admin123"
    echo
    echo "🐳 Docker管理命令:"
    echo "  - 启动服务: ./docker-start.sh"
    echo "  - 停止服务: ./docker-stop.sh"
    echo "  - 重启服务: ./docker-restart.sh"
    echo "  - 查看日志: ./docker-logs.sh [service]"
    echo "  - 备份数据: ./docker-backup.sh"
    echo "  - 清理资源: ./docker-clean.sh"
    echo
    echo "📚 更多信息:"
    echo "  - 容器状态: docker-compose ps"
    echo "  - 查看日志: docker-compose logs -f"
    echo "  - 进入容器: docker-compose exec [service] bash"
    echo
    echo "⚠️  重要提醒:"
    echo "  - 数据库密码保存在.env.docker文件中"
    echo "  - 定期使用./docker-backup.sh备份数据"
    echo "  - 生产环境请修改默认密码"
    echo
}

# 主安装流程
main() {
    echo "=================================="
    echo "🐳 MES系统Docker安装程序"
    echo "=================================="
    echo
    
    detect_os
    
    # 检查并安装Docker
    if ! check_docker; then
        install_docker
    fi
    
    # 检查并安装Docker Compose
    if ! check_docker_compose; then
        install_docker_compose
    fi
    
    log_info "开始创建Docker配置文件..."
    
    create_dockerfile
    create_frontend_dockerfile
    create_docker_compose
    create_docker_scripts
    
    log_info "开始构建和启动服务..."
    build_and_start
    
    if verify_docker_installation; then
        show_docker_results
    else
        log_error "Docker安装验证失败，请检查错误信息"
        exit 1
    fi
}

# 运行主程序
main "$@"
