#!/bin/bash

# MES系统Docker部署脚本
# 完整的部署、管理和维护解决方案

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env.docker"
PROJECT_NAME="mes-system"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查配置文件
    if [ ! -f "$COMPOSE_FILE" ]; then
        log_error "找不到docker-compose.yml文件"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 生成环境配置
setup_environment() {
    log_info "设置环境配置..."
    
    if [ ! -f "$ENV_FILE" ]; then
        log_info "生成Docker环境配置..."
        ./scripts/generate-env.sh docker
    else
        log_info "使用现有环境配置: $ENV_FILE"
    fi
    
    # 导出环境变量
    export $(cat $ENV_FILE | grep -v '^#' | xargs)
    
    log_success "环境配置完成"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 使用BuildKit加速构建
    export DOCKER_BUILDKIT=1
    export COMPOSE_DOCKER_CLI_BUILD=1
    
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE build --parallel
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动MES系统服务..."
    
    # 启动服务
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    check_services_health
    
    log_success "服务启动完成"
}

# 停止服务
stop_services() {
    log_info "停止MES系统服务..."
    
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE down
    
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启MES系统服务..."
    
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE restart
    
    # 等待服务重启
    sleep 20
    check_services_health
    
    log_success "服务重启完成"
}

# 检查服务健康状态
check_services_health() {
    log_info "检查服务健康状态..."
    
    # 显示容器状态
    docker-compose -f $COMPOSE_FILE ps
    
    # 检查各服务健康状态
    local services=("postgres" "redis" "backend" "frontend")
    local healthy_count=0
    
    for service in "${services[@]}"; do
        local health=$(docker-compose -f $COMPOSE_FILE ps -q $service | xargs docker inspect --format='{{.State.Health.Status}}' 2>/dev/null || echo "unknown")
        
        case $health in
            "healthy")
                log_success "$service: 健康"
                ((healthy_count++))
                ;;
            "unhealthy")
                log_error "$service: 不健康"
                ;;
            "starting")
                log_warning "$service: 启动中"
                ;;
            *)
                log_warning "$service: 状态未知"
                ;;
        esac
    done
    
    if [ $healthy_count -eq ${#services[@]} ]; then
        log_success "所有服务运行正常"
    else
        log_warning "部分服务可能存在问题，请检查日志"
    fi
}

# 查看日志
view_logs() {
    local service=${1:-""}
    
    if [ -z "$service" ]; then
        log_info "查看所有服务日志..."
        docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE logs -f
    else
        log_info "查看 $service 服务日志..."
        docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE logs -f $service
    fi
}

# 备份数据
backup_data() {
    log_info "备份系统数据..."
    
    local backup_dir="./backups"
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_file="mes_backup_${timestamp}.sql"
    
    mkdir -p $backup_dir
    
    # 备份数据库
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE exec -T postgres pg_dump -U $POSTGRES_USER $POSTGRES_DB > "$backup_dir/$backup_file"
    
    if [ $? -eq 0 ]; then
        log_success "数据备份完成: $backup_dir/$backup_file"
    else
        log_error "数据备份失败"
        exit 1
    fi
}

# 恢复数据
restore_data() {
    local backup_file=$1
    
    if [ -z "$backup_file" ]; then
        log_error "请指定备份文件"
        echo "用法: $0 restore <backup_file>"
        exit 1
    fi
    
    if [ ! -f "$backup_file" ]; then
        log_error "备份文件不存在: $backup_file"
        exit 1
    fi
    
    log_warning "这将覆盖现有数据，确定继续吗？(y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        log_info "取消恢复操作"
        exit 0
    fi
    
    log_info "恢复数据从: $backup_file"
    
    # 恢复数据库
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE exec -T postgres psql -U $POSTGRES_USER -d $POSTGRES_DB < "$backup_file"
    
    if [ $? -eq 0 ]; then
        log_success "数据恢复完成"
    else
        log_error "数据恢复失败"
        exit 1
    fi
}

# 清理系统
cleanup_system() {
    log_warning "这将删除所有容器、镜像和数据，确定继续吗？(y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        log_info "取消清理操作"
        exit 0
    fi
    
    log_info "清理MES系统..."
    
    # 停止并删除容器
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE down -v --rmi all
    
    # 清理未使用的资源
    docker system prune -f
    
    log_success "系统清理完成"
}

# 显示系统状态
show_status() {
    echo "=================================="
    echo "📊 MES系统状态"
    echo "=================================="
    echo
    
    # 显示容器状态
    echo "🐳 容器状态:"
    docker-compose -f $COMPOSE_FILE ps
    echo
    
    # 显示资源使用
    echo "💾 资源使用:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
    echo
    
    # 显示网络信息
    echo "🌐 网络信息:"
    docker network ls | grep mes
    echo
    
    # 显示卷信息
    echo "💿 存储卷:"
    docker volume ls | grep mes
    echo
}

# 显示帮助信息
show_help() {
    echo "MES系统Docker部署管理脚本"
    echo
    echo "用法: $0 <command> [options]"
    echo
    echo "命令:"
    echo "  deploy      - 完整部署系统"
    echo "  start       - 启动服务"
    echo "  stop        - 停止服务"
    echo "  restart     - 重启服务"
    echo "  status      - 显示系统状态"
    echo "  logs [svc]  - 查看日志"
    echo "  backup      - 备份数据"
    echo "  restore <f> - 恢复数据"
    echo "  cleanup     - 清理系统"
    echo "  health      - 检查健康状态"
    echo "  help        - 显示帮助"
    echo
    echo "示例:"
    echo "  $0 deploy           # 完整部署"
    echo "  $0 logs backend     # 查看后端日志"
    echo "  $0 restore backup.sql # 恢复数据"
}

# 主函数
main() {
    case ${1:-help} in
        "deploy")
            check_dependencies
            setup_environment
            build_images
            start_services
            show_status
            ;;
        "start")
            check_dependencies
            setup_environment
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "status")
            show_status
            ;;
        "logs")
            view_logs $2
            ;;
        "backup")
            backup_data
            ;;
        "restore")
            restore_data $2
            ;;
        "cleanup")
            cleanup_system
            ;;
        "health")
            check_services_health
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 运行主程序
main "$@"
