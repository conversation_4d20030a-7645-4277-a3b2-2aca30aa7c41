#!/bin/bash

# MES系统Docker交互式管理脚本
# 统一的Docker部署、管理和维护解决方案

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 配置
COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env.docker"
PROJECT_NAME="mes-system"
SCRIPT_VERSION="1.0.0"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}$1${NC}"
}

log_menu() {
    echo -e "${CYAN}$1${NC}"
}

# 显示横幅
show_banner() {
    clear
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    MES系统 Docker管理器                      ║"
    echo "║                     版本: $SCRIPT_VERSION                           ║"
    echo "║              制造执行系统 - 容器化部署解决方案                ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo
}

# 显示系统状态
show_system_status() {
    log_header "📊 系统状态概览"
    echo
    
    # Docker状态
    if command -v docker &> /dev/null; then
        echo -e "${GREEN}✓${NC} Docker: $(docker --version | cut -d' ' -f3 | cut -d',' -f1)"
    else
        echo -e "${RED}✗${NC} Docker: 未安装"
        return 1
    fi
    
    # Docker Compose状态
    if docker compose version &> /dev/null; then
        echo -e "${GREEN}✓${NC} Docker Compose: $(docker compose version --short)"
    else
        echo -e "${RED}✗${NC} Docker Compose: 未安装"
        return 1
    fi
    
    # 配置文件状态
    if [ -f "$COMPOSE_FILE" ]; then
        echo -e "${GREEN}✓${NC} Docker Compose配置: 存在"
    else
        echo -e "${RED}✗${NC} Docker Compose配置: 缺失"
    fi
    
    if [ -f "$ENV_FILE" ]; then
        echo -e "${GREEN}✓${NC} 环境配置: 存在"
    else
        echo -e "${YELLOW}!${NC} 环境配置: 将自动生成"
    fi
    
    # 容器状态
    echo
    if docker compose ps --format table 2>/dev/null | grep -q "mes-"; then
        log_header "🐳 容器状态:"
        docker compose ps --format table
    else
        echo -e "${YELLOW}!${NC} 容器状态: 未运行"
    fi
    
    echo
}

# 主菜单
show_main_menu() {
    log_header "🚀 主菜单 - 请选择操作:"
    echo
    log_menu "   1) 🏗️  完整部署系统"
    log_menu "   2) ▶️  启动服务"
    log_menu "   3) ⏹️  停止服务"
    log_menu "   4) 🔄 重启服务"
    log_menu "   5) 📊 查看状态"
    log_menu "   6) 📋 查看日志"
    log_menu "   7) ⚙️  配置管理"
    log_menu "   8) 🛠️  维护工具"
    log_menu "   9) 📚 帮助信息"
    log_menu "   0) 🚪 退出"
    echo
    echo -n "请输入选项 [0-9]: "
}

# 配置管理菜单
show_config_menu() {
    clear
    show_banner
    log_header "⚙️ 配置管理"
    echo
    log_menu "   1) 🔧 生成环境配置"
    log_menu "   2) 📝 编辑环境配置"
    log_menu "   3) 🔍 查看当前配置"
    log_menu "   4) 🔄 重置配置"
    log_menu "   5) 📋 配置模板"
    log_menu "   0) ⬅️  返回主菜单"
    echo
    echo -n "请输入选项 [0-5]: "
}

# 维护工具菜单
show_maintenance_menu() {
    clear
    show_banner
    log_header "🛠️ 维护工具"
    echo
    log_menu "   1) 💾 备份数据"
    log_menu "   2) 📥 恢复数据"
    log_menu "   3) 🧹 清理系统"
    log_menu "   4) 🔍 健康检查"
    log_menu "   5) 📈 性能监控"
    log_menu "   6) 🗂️  镜像管理"
    log_menu "   0) ⬅️  返回主菜单"
    echo
    echo -n "请输入选项 [0-6]: "
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        echo "安装指南: https://docs.docker.com/get-docker/"
        return 1
    fi
    
    if ! docker compose version &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        return 1
    fi
    
    if [ ! -f "$COMPOSE_FILE" ]; then
        log_error "找不到docker-compose.yml文件"
        return 1
    fi
    
    log_success "依赖检查通过"
    return 0
}

# 生成环境配置
generate_env_config() {
    log_info "生成环境配置..."
    
    if [ -f "$ENV_FILE" ]; then
        echo -n "环境配置已存在，是否覆盖? [y/N]: "
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log_info "保持现有配置"
            return 0
        fi
    fi
    
    if [ -f "scripts/generate-env.sh" ]; then
        ./scripts/generate-env.sh docker
    else
        log_warning "环境生成脚本不存在，使用默认配置"
        create_default_env
    fi
    
    log_success "环境配置生成完成"
}

# 创建默认环境配置
create_default_env() {
    local db_password=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    local redis_password=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    local jwt_secret=$(openssl rand -base64 64 | tr -d "=+/" | cut -c1-50)
    
    cat > $ENV_FILE << EOF
# MES系统Docker环境配置
POSTGRES_DB=mes_db
POSTGRES_USER=mes_user
POSTGRES_PASSWORD=$db_password
DATABASE_URL=************************************************/mes_db

REDIS_PASSWORD=$redis_password
REDIS_URL=redis://:$redis_password@redis:6379

JWT_SECRET=$jwt_secret
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
RUST_LOG=info

TIMEZONE=Asia/Shanghai
NODE_ENV=production
RUST_ENV=production
ENABLE_IPV6=true
EOF
}

# 完整部署
full_deploy() {
    log_header "🏗️ 开始完整部署..."
    echo
    
    # 检查依赖
    if ! check_dependencies; then
        return 1
    fi
    
    # 生成环境配置
    generate_env_config
    
    # 构建镜像
    log_info "构建Docker镜像..."
    if docker compose --env-file $ENV_FILE build; then
        log_success "镜像构建完成"
    else
        log_error "镜像构建失败"
        return 1
    fi
    
    # 启动服务
    start_services
    
    # 显示部署结果
    show_deployment_result
}

# 启动服务
start_services() {
    log_info "启动MES系统服务..."
    
    if [ ! -f "$ENV_FILE" ]; then
        generate_env_config
    fi
    
    # 启动服务
    if docker compose --env-file $ENV_FILE up -d; then
        log_success "服务启动中..."
        
        # 等待服务启动
        log_info "等待服务启动完成..."
        sleep 10
        
        # 检查服务状态
        check_services_health
        
        log_success "服务启动完成"
    else
        log_error "服务启动失败"
        return 1
    fi
}

# 停止服务
stop_services() {
    log_info "停止MES系统服务..."
    
    if docker compose --env-file $ENV_FILE down; then
        log_success "服务已停止"
    else
        log_error "服务停止失败"
        return 1
    fi
}

# 重启服务
restart_services() {
    log_info "重启MES系统服务..."
    
    if docker compose --env-file $ENV_FILE restart; then
        log_success "服务重启中..."
        sleep 10
        check_services_health
        log_success "服务重启完成"
    else
        log_error "服务重启失败"
        return 1
    fi
}

# 检查服务健康状态
check_services_health() {
    log_info "检查服务健康状态..."
    echo
    
    # 显示容器状态
    docker compose --env-file $ENV_FILE ps
    echo
    
    # 检查各服务健康状态
    local services=("postgres" "redis" "backend" "frontend")
    local healthy_count=0
    
    for service in "${services[@]}"; do
        local container_id=$(docker compose --env-file $ENV_FILE ps -q $service 2>/dev/null)
        if [ -n "$container_id" ]; then
            local health=$(docker inspect --format='{{.State.Health.Status}}' $container_id 2>/dev/null || echo "unknown")
            
            case $health in
                "healthy")
                    echo -e "${GREEN}✓${NC} $service: 健康"
                    ((healthy_count++))
                    ;;
                "unhealthy")
                    echo -e "${RED}✗${NC} $service: 不健康"
                    ;;
                "starting")
                    echo -e "${YELLOW}⏳${NC} $service: 启动中"
                    ;;
                *)
                    echo -e "${YELLOW}?${NC} $service: 状态未知"
                    ;;
            esac
        else
            echo -e "${RED}✗${NC} $service: 未运行"
        fi
    done
    
    echo
    if [ $healthy_count -eq ${#services[@]} ]; then
        log_success "所有服务运行正常"
    else
        log_warning "部分服务可能存在问题"
    fi
}

# 查看日志
view_logs() {
    clear
    show_banner
    log_header "📋 日志查看"
    echo
    log_menu "   1) 📄 查看所有服务日志"
    log_menu "   2) 🗄️  查看数据库日志"
    log_menu "   3) 🔄 查看缓存日志"
    log_menu "   4) ⚙️  查看后端日志"
    log_menu "   5) 🌐 查看前端日志"
    log_menu "   0) ⬅️  返回主菜单"
    echo
    echo -n "请输入选项 [0-5]: "
    
    read -r choice
    case $choice in
        1) docker compose --env-file $ENV_FILE logs -f ;;
        2) docker compose --env-file $ENV_FILE logs -f postgres ;;
        3) docker compose --env-file $ENV_FILE logs -f redis ;;
        4) docker compose --env-file $ENV_FILE logs -f backend ;;
        5) docker compose --env-file $ENV_FILE logs -f frontend ;;
        0) return ;;
        *) log_error "无效选项" ;;
    esac
}

# 显示部署结果
show_deployment_result() {
    echo
    log_header "🎉 MES系统部署完成！"
    echo
    echo -e "${GREEN}📋 服务信息:${NC}"
    echo "  - 前端界面: http://localhost:3000"
    echo "  - 后端API: http://localhost:8080"
    echo "  - 数据库: PostgreSQL (端口5432)"
    echo "  - 缓存: Redis (端口6379)"
    echo
    echo -e "${GREEN}🔑 默认登录信息:${NC}"
    echo "  - 用户名: admin"
    echo "  - 密码: admin123"
    echo
    echo -e "${GREEN}🛠️ 管理命令:${NC}"
    echo "  - 查看状态: docker compose ps"
    echo "  - 查看日志: docker compose logs -f"
    echo "  - 进入容器: docker compose exec <service> bash"
    echo
    echo -e "${YELLOW}⚠️ 重要提醒:${NC}"
    echo "  - 数据库密码保存在 $ENV_FILE 文件中"
    echo "  - 生产环境请修改默认密码"
    echo "  - 定期备份重要数据"
    echo
}

# 备份数据
backup_data() {
    log_info "备份系统数据..."
    
    local backup_dir="./backups"
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_file="mes_backup_${timestamp}.sql"
    
    mkdir -p $backup_dir
    
    if docker compose --env-file $ENV_FILE exec -T postgres pg_dump -U mes_user mes_db > "$backup_dir/$backup_file"; then
        log_success "数据备份完成: $backup_dir/$backup_file"
    else
        log_error "数据备份失败"
        return 1
    fi
}

# 清理系统
cleanup_system() {
    echo
    log_warning "⚠️ 这将删除所有容器、镜像和数据！"
    echo -n "确定要继续吗？请输入 'YES' 确认: "
    read -r confirmation
    
    if [ "$confirmation" = "YES" ]; then
        log_info "清理MES系统..."
        
        docker compose --env-file $ENV_FILE down -v --rmi all
        docker system prune -f
        
        log_success "系统清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 显示帮助信息
show_help() {
    clear
    show_banner
    log_header "📚 帮助信息"
    echo
    echo -e "${WHITE}MES系统Docker管理器使用指南${NC}"
    echo
    echo -e "${CYAN}主要功能:${NC}"
    echo "  • 一键部署MES系统"
    echo "  • 服务启动/停止/重启"
    echo "  • 实时状态监控"
    echo "  • 日志查看和分析"
    echo "  • 数据备份和恢复"
    echo "  • 系统维护工具"
    echo
    echo -e "${CYAN}系统要求:${NC}"
    echo "  • Docker 20.10+"
    echo "  • Docker Compose 2.0+"
    echo "  • 4GB+ 可用内存"
    echo "  • 10GB+ 可用磁盘空间"
    echo
    echo -e "${CYAN}支持特性:${NC}"
    echo "  • IPv4/IPv6 双栈网络"
    echo "  • 移动设备优化"
    echo "  • 自动健康检查"
    echo "  • 资源限制管理"
    echo "  • 安全配置"
    echo
    echo -e "${CYAN}故障排除:${NC}"
    echo "  • 查看日志: 选项6"
    echo "  • 健康检查: 选项8-4"
    echo "  • 重启服务: 选项4"
    echo "  • 清理重建: 选项8-3"
    echo
    echo -n "按Enter键返回主菜单..."
    read -r
}

# 主循环
main() {
    while true; do
        clear
        show_banner
        show_system_status
        show_main_menu
        
        read -r choice
        case $choice in
            1)
                clear
                show_banner
                full_deploy
                echo
                echo -n "按Enter键继续..."
                read -r
                ;;
            2)
                clear
                show_banner
                start_services
                echo
                echo -n "按Enter键继续..."
                read -r
                ;;
            3)
                clear
                show_banner
                stop_services
                echo
                echo -n "按Enter键继续..."
                read -r
                ;;
            4)
                clear
                show_banner
                restart_services
                echo
                echo -n "按Enter键继续..."
                read -r
                ;;
            5)
                clear
                show_banner
                check_services_health
                echo
                echo -n "按Enter键继续..."
                read -r
                ;;
            6)
                view_logs
                ;;
            7)
                config_management
                ;;
            8)
                maintenance_tools
                ;;
            9)
                show_help
                ;;
            0)
                echo
                log_success "感谢使用MES系统Docker管理器！"
                exit 0
                ;;
            *)
                log_error "无效选项，请重新选择"
                sleep 1
                ;;
        esac
    done
}

# 配置管理
config_management() {
    while true; do
        show_config_menu
        read -r choice
        case $choice in
            1)
                clear
                show_banner
                generate_env_config
                echo
                echo -n "按Enter键继续..."
                read -r
                ;;
            2)
                if command -v nano &> /dev/null; then
                    nano $ENV_FILE
                elif command -v vi &> /dev/null; then
                    vi $ENV_FILE
                else
                    log_error "未找到文本编辑器"
                fi
                ;;
            3)
                clear
                show_banner
                if [ -f "$ENV_FILE" ]; then
                    log_header "📋 当前环境配置:"
                    echo
                    cat $ENV_FILE
                else
                    log_warning "环境配置文件不存在"
                fi
                echo
                echo -n "按Enter键继续..."
                read -r
                ;;
            4)
                echo -n "确定要重置配置吗? [y/N]: "
                read -r response
                if [[ "$response" =~ ^[Yy]$ ]]; then
                    rm -f $ENV_FILE
                    generate_env_config
                fi
                ;;
            5)
                clear
                show_banner
                if [ -f ".env.example" ]; then
                    log_header "📋 配置模板:"
                    echo
                    cat .env.example
                else
                    log_warning "配置模板文件不存在"
                fi
                echo
                echo -n "按Enter键继续..."
                read -r
                ;;
            0)
                break
                ;;
            *)
                log_error "无效选项"
                sleep 1
                ;;
        esac
    done
}

# 维护工具
maintenance_tools() {
    while true; do
        show_maintenance_menu
        read -r choice
        case $choice in
            1)
                clear
                show_banner
                backup_data
                echo
                echo -n "按Enter键继续..."
                read -r
                ;;
            2)
                clear
                show_banner
                log_info "数据恢复功能开发中..."
                echo
                echo -n "按Enter键继续..."
                read -r
                ;;
            3)
                clear
                show_banner
                cleanup_system
                echo
                echo -n "按Enter键继续..."
                read -r
                ;;
            4)
                clear
                show_banner
                check_services_health
                echo
                echo -n "按Enter键继续..."
                read -r
                ;;
            5)
                clear
                show_banner
                log_info "性能监控功能开发中..."
                echo
                echo -n "按Enter键继续..."
                read -r
                ;;
            6)
                clear
                show_banner
                log_header "🗂️ Docker镜像列表:"
                docker images | grep -E "(mes-|postgres|redis|nginx)"
                echo
                echo -n "按Enter键继续..."
                read -r
                ;;
            0)
                break
                ;;
            *)
                log_error "无效选项"
                sleep 1
                ;;
        esac
    done
}

# 运行主程序
main "$@"
