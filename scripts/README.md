# MES系统脚本目录

本目录包含MES系统的各种脚本，按功能分类整理。

## 📁 目录结构

### 核心启动脚本 (根目录)
- `mes-launcher.sh` - **统一启动器** (推荐使用)
- `start-mes-system.sh` - 完整的后端启动脚本
- `quick-start.sh` - 后端快速启动
- `install-production.sh` - 生产环境安装

### 前端脚本 (frontend/)
- `frontend/start-frontend.sh` - 前端完整启动脚本
- `frontend/quick-start-frontend.sh` - 前端快速启动

### 分类脚本目录

#### archived/ - 已归档的重复脚本
包含被新统一启动器替代的旧脚本，保留作为参考。

#### windows/ - Windows批处理脚本
包含所有Windows环境的.bat脚本文件。

#### docker/ - Docker相关脚本
- Docker构建和部署脚本
- Docker管理工具

#### testing/ - 测试脚本
- API测试脚本
- 功能模块测试
- 系统集成测试

#### database/ - 数据库相关脚本
- 数据库初始化
- 数据清理工具
- 密码重置工具

## 🚀 推荐使用方式

### 1. 统一启动器 (最推荐)
```bash
./mes-launcher.sh
```
提供交互式菜单，包含所有常用功能。

### 2. 快速启动
```bash
# 后端
./quick-start.sh

# 前端
./frontend/quick-start-frontend.sh
```

### 3. 完整功能启动
```bash
# 后端
./start-mes-system.sh start

# 前端
./frontend/start-frontend.sh prod
```

## 📋 脚本迁移说明

以下脚本已被整合到统一启动器中：
- `start.sh` → `mes-launcher.sh`
- `start_all.sh` → `mes-launcher.sh`
- `stop.sh` → `mes-launcher.sh`
- `status.sh` → `mes-launcher.sh`

旧脚本已移动到 `archived/` 目录，如需要可以继续使用。
