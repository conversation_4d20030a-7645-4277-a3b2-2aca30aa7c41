# MES系统 - Windows安装和使用指南

## 📋 概述

MES (Manufacturing Execution System) 制造执行系统的Windows版本安装和部署指南。本系统提供了完整的Windows批处理脚本，支持一键安装、配置和启动。

## 🎯 快速开始

### 方式一：一键快速启动（推荐新用户）

```batch
# 双击运行或在命令行执行
quick_start.bat
```

这个脚本会自动完成：
- 检查和安装所需依赖
- 初始化数据库
- 构建项目
- 启动所有服务

### 方式二：分步骤安装

1. **安装依赖环境**
   ```batch
   install.bat
   ```

2. **初始化数据库**
   ```batch
   init-database.bat
   ```

3. **构建项目**
   ```batch
   build_production.bat
   ```

4. **启动系统**
   ```batch
   start_all.bat
   ```

## 📁 Windows脚本文件说明

| 脚本文件 | 功能描述 | 使用场景 |
|---------|---------|---------|
| `quick_start.bat` | 一键快速启动 | 首次安装或快速部署 |
| `install.bat` | 安装系统依赖 | 安装Rust、Node.js、PostgreSQL |
| `init-database.bat` | 初始化数据库 | 创建数据库和基础数据 |
| `build_production.bat` | 构建生产版本 | 编译后端和前端 |
| `start_all.bat` | 启动所有服务 | 日常启动系统 |
| `stop_all.bat` | 停止所有服务 | 停止系统服务 |
| `check-system.bat` | 系统状态检查 | 诊断系统问题 |

## 🔧 系统要求

### 最低要求
- **操作系统**: Windows 10 或更高版本
- **内存**: 4GB RAM
- **存储**: 2GB 可用空间
- **网络**: 互联网连接（用于下载依赖）

### 推荐配置
- **操作系统**: Windows 11
- **内存**: 8GB RAM 或更多
- **存储**: 5GB 可用空间
- **处理器**: 多核CPU

## 📦 依赖组件

系统会自动安装以下组件：

### 1. Rust 开发环境
- **版本**: 最新稳定版
- **用途**: 后端API服务编译
- **安装**: 通过 rustup-init.exe

### 2. Node.js 运行环境
- **版本**: LTS 20.x
- **用途**: 前端开发和构建
- **包含**: npm 包管理器

### 3. PostgreSQL 数据库
- **版本**: 15.x 或更高
- **用途**: 数据存储
- **注意**: 需要手动安装

## 🚀 详细安装步骤

### 步骤1: 准备环境

1. **下载项目代码**
   ```batch
   git clone <repository-url>
   cd mes-system
   ```

2. **以管理员身份运行命令提示符**（推荐）
   - 右键点击"命令提示符"
   - 选择"以管理员身份运行"

### 步骤2: 安装依赖

运行安装脚本：
```batch
install.bat
```

**注意**: PostgreSQL需要手动安装
- 访问: https://www.postgresql.org/download/windows/
- 下载并安装PostgreSQL 15或更高版本
- 记住设置的数据库密码

### 步骤3: 配置数据库

运行数据库初始化脚本：
```batch
init-database.bat
```

按提示输入数据库连接信息：
- 主机: localhost（默认）
- 端口: 5432（默认）
- 数据库名: mes_db（默认）
- 用户: postgres（默认）
- 密码: [您设置的密码]

### 步骤4: 构建项目

运行构建脚本：
```batch
build_production.bat
```

选择构建选项：
1. 完整构建（推荐）
2. 仅构建后端
3. 仅构建前端
4. 清理并重新构建

### 步骤5: 启动系统

运行启动脚本：
```batch
start_all.bat
```

选择启动模式：
1. 快速启动（推荐）
2. 开发模式启动
3. 生产模式启动
4. 仅启动后端
5. 仅启动前端

## 🌐 访问系统

启动成功后，可以通过以下地址访问：

- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8080
- **网络访问**: http://[您的IP]:3000

### 默认管理员账户
- **用户名**: admin
- **密码**: admin123

## 🛠️ 常用操作

### 检查系统状态
```batch
check-system.bat
```

### 停止所有服务
```batch
stop_all.bat
```

### 重新启动服务
```batch
stop_all.bat
start_all.bat
```

### 重新构建项目
```batch
build_production.bat
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```
   错误: 端口8080或3000被占用
   解决: 运行 stop_all.bat 清理端口
   ```

2. **数据库连接失败**
   ```
   错误: 无法连接到PostgreSQL
   解决: 检查PostgreSQL服务是否启动
   ```

3. **编译失败**
   ```
   错误: Rust编译失败
   解决: 检查代码错误，运行 cargo clean 清理缓存
   ```

4. **前端构建失败**
   ```
   错误: npm构建失败
   解决: 删除 node_modules，重新运行 npm install
   ```

### 诊断命令

```batch
# 检查系统状态
check-system.bat

# 查看端口占用
netstat -ano | findstr ":8080"
netstat -ano | findstr ":3000"

# 查看进程
tasklist | findstr "mes-system"
tasklist | findstr "node"
```

## 📝 配置文件

### 环境配置文件 (.env)
```
DATABASE_URL=postgresql://postgres:password@localhost:5432/mes_db
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
JWT_SECRET=mes-system-jwt-secret-key-development-only
RUST_LOG=info
```

### 修改配置
1. 编辑 `.env` 文件
2. 重新启动服务

## 🔄 更新系统

1. **停止服务**
   ```batch
   stop_all.bat
   ```

2. **更新代码**
   ```batch
   git pull
   ```

3. **重新构建**
   ```batch
   build_production.bat
   ```

4. **启动服务**
   ```batch
   start_all.bat
   ```

## 📞 技术支持

如果遇到问题，请：

1. 运行 `check-system.bat` 检查系统状态
2. 查看错误日志
3. 参考故障排除部分
4. 联系技术支持

## 📄 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。
