-- 插入质量模块测试数据

-- 首先确保有基础数据（项目、零件、工艺路线等）
-- 这些数据应该已经存在，如果不存在需要先创建

-- 插入一些质量检查点
INSERT INTO quality_checkpoints (routing_step_id, checkpoint_name, description, required, measurement_type, tolerance_min, tolerance_max, unit_of_measure) VALUES
(1, '外观检查', '检查零件表面是否有划痕、变形等缺陷', true, 'visual', NULL, NULL, NULL),
(1, '尺寸检查', '检查零件关键尺寸是否符合图纸要求', true, 'dimensional', 9.8, 10.2, 'mm'),
(2, '功能测试', '测试零件功能是否正常', true, 'functional', NULL, NULL, NULL),
(2, '材料硬度', '检测材料硬度是否达标', false, 'material', 45.0, 55.0, 'HRC');

-- 插入一些质量检验记录（只使用存在的plan_task_id=1）
INSERT INTO quality_inspections (plan_task_id, inspector_user_id, inspection_type, status, result, notes, inspection_date) VALUES
(1, 1, 'in_process', 'completed', 'pass', '检验合格，所有项目均符合要求', '2025-07-17 10:00:00+00'),
(1, 1, 'in_process', 'completed', 'fail', '尺寸超差，需要返工', '2025-07-17 11:30:00+00'),
(1, 1, 'final', 'completed', 'pass', '最终检验合格', '2025-07-17 14:00:00+00'),
(1, 1, 'first_article', 'in_progress', 'pending', '首件检验进行中', '2025-07-17 15:00:00+00');

-- 插入质量测量数据
INSERT INTO quality_measurements (inspection_id, checkpoint_id, measured_value, text_value, pass_fail, notes) VALUES
-- 第一次检验的测量数据
(5, 1, NULL, '外观良好，无缺陷', 'pass', '表面光洁度符合要求'),
(5, 2, 10.05, NULL, 'pass', '尺寸在公差范围内'),

-- 第二次检验的测量数据（不合格）
(6, 1, NULL, '外观良好', 'pass', '外观检查通过'),
(6, 2, 10.35, NULL, 'fail', '尺寸超出上限公差'),

-- 第三次检验的测量数据
(7, 3, NULL, '功能正常', 'pass', '所有功能测试通过'),
(7, 4, 48.5, NULL, 'pass', '硬度值在要求范围内');

-- 插入一些质量警报
INSERT INTO quality_alerts (alert_type, severity, title, description, inspection_id, threshold_value, actual_value, acknowledged) VALUES
('dimension_out_of_tolerance', 'high', '尺寸超差警报', '零件尺寸超出公差范围，需要立即处理', 6, 10.2, 10.35, false),
('quality_trend_declining', 'medium', '质量趋势下降', '最近几批产品的合格率有下降趋势', NULL, 95.0, 87.5, false),
('first_pass_yield_low', 'medium', '一次通过率偏低', '今日一次通过率低于目标值', NULL, 90.0, 75.0, true);

-- 更新一些统计信息（这些通常由触发器或定期任务更新）
-- 这里手动插入一些示例数据用于测试

COMMENT ON TABLE quality_inspections IS '质量检验记录表';
COMMENT ON TABLE quality_checkpoints IS '质量检查点配置表';
COMMENT ON TABLE quality_measurements IS '质量测量数据表';
COMMENT ON TABLE quality_alerts IS '质量警报表';

-- 创建一些视图用于质量报表
CREATE OR REPLACE VIEW quality_inspection_summary AS
SELECT 
    qi.id,
    qi.plan_task_id,
    qi.inspection_type,
    qi.status,
    qi.result,
    qi.inspection_date,
    u.full_name as inspector_name,
    COUNT(qm.id) as measurement_count,
    COUNT(CASE WHEN qm.pass_fail = 'pass' THEN 1 END) as pass_count,
    COUNT(CASE WHEN qm.pass_fail = 'fail' THEN 1 END) as fail_count
FROM quality_inspections qi
LEFT JOIN users u ON qi.inspector_user_id = u.id
LEFT JOIN quality_measurements qm ON qi.id = qm.inspection_id
GROUP BY qi.id, qi.plan_task_id, qi.inspection_type, qi.status, qi.result, qi.inspection_date, u.full_name;

-- 创建质量统计视图
CREATE OR REPLACE VIEW quality_daily_stats AS
SELECT 
    DATE(inspection_date) as inspection_date,
    COUNT(*) as total_inspections,
    COUNT(CASE WHEN result = 'pass' THEN 1 END) as passed_inspections,
    COUNT(CASE WHEN result = 'fail' THEN 1 END) as failed_inspections,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_inspections,
    ROUND(
        COUNT(CASE WHEN result = 'pass' THEN 1 END) * 100.0 / 
        NULLIF(COUNT(CASE WHEN result IN ('pass', 'fail') THEN 1 END), 0), 
        2
    ) as pass_rate
FROM quality_inspections
GROUP BY DATE(inspection_date)
ORDER BY inspection_date DESC;
