#!/usr/bin/env python3
"""
测试gongyi用户的权限和零件创建功能
"""

import requests
import json
import time

BASE_URL = "http://localhost:9001/api"

def main():
    print("🔍 测试gongyi用户的权限和零件创建功能...")
    
    # 1. 登录
    print("1. 登录测试...")
    login_response = requests.post(f"{BASE_URL}/auth/login", json={
        "username": "gongyi",
        "password": "gongyi"
    })
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.status_code}")
        print(f"错误信息: {login_response.text}")
        return 1
    
    login_data = login_response.json()
    token = login_data["token"]
    user_info = login_data["user"]
    
    print("✅ 登录成功")
    print(f"用户信息: {json.dumps(user_info, ensure_ascii=False, indent=2)}")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 2. 获取详细用户信息
    print("\n2. 获取详细用户信息...")
    me_response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
    if me_response.status_code == 200:
        me_data = me_response.json()
        print(f"详细用户信息: {json.dumps(me_data, ensure_ascii=False, indent=2)}")
    else:
        print(f"❌ 获取用户信息失败: {me_response.status_code}")
    
    # 3. 检查零件列表访问权限
    print("\n3. 测试零件列表访问...")
    parts_response = requests.get(f"{BASE_URL}/parts", headers=headers)
    print(f"零件列表访问状态: {parts_response.status_code}")
    
    if parts_response.status_code == 200:
        parts_data = parts_response.json()
        print(f"✅ 可以访问零件列表 (共 {parts_data.get('data', {}).get('total_count', 0)} 个零件)")
    elif parts_response.status_code == 403:
        print("❌ 没有访问零件列表的权限")
        print(f"错误信息: {parts_response.text}")
    else:
        print(f"❌ 访问零件列表失败: {parts_response.status_code}")
        print(f"错误信息: {parts_response.text}")
    
    # 4. 检查具体的权限配置
    print("\n4. 检查权限配置...")
    
    # 获取所有角色
    roles_response = requests.get(f"{BASE_URL}/auth/roles", headers=headers)
    if roles_response.status_code == 200:
        roles_data = roles_response.json()
        print("可用角色:")
        for role in roles_data.get("roles", []):
            print(f"  - {role['role_name']}: {role.get('description', 'N/A')}")
    
    # 5. 测试零件创建
    print("\n5. 测试零件创建...")
    part_data = {
        "part_number": f"GONGYI-TEST-{int(time.time())}",
        "part_name": "gongyi测试零件",
        "version": "1.0",
        "specifications": "这是gongyi用户创建的测试零件"
    }
    
    print(f"创建零件数据:")
    print(json.dumps(part_data, ensure_ascii=False, indent=2))
    
    create_response = requests.post(f"{BASE_URL}/parts", 
                                  headers=headers, 
                                  json=part_data)
    
    print(f"\n零件创建响应状态: {create_response.status_code}")
    print(f"响应头: {dict(create_response.headers)}")
    
    if create_response.status_code in [200, 201]:
        result = create_response.json()
        print("✅ 零件创建成功!")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        return 0
    else:
        print("❌ 零件创建失败!")
        print(f"错误响应: {create_response.text}")
        
        # 详细分析错误
        if create_response.status_code == 403:
            print("\n🔍 403权限错误分析:")
            print("可能的原因:")
            print("1. 用户角色没有CREATE_PART权限")
            print("2. JWT token中的角色信息过期")
            print("3. 权限验证逻辑有问题")
            print("4. 数据库中的权限配置不正确")
            
            # 解析JWT token查看角色信息
            try:
                import base64
                token_parts = token.split('.')
                if len(token_parts) >= 2:
                    # 解码JWT payload
                    payload_encoded = token_parts[1]
                    # 添加padding if needed
                    payload_encoded += '=' * (4 - len(payload_encoded) % 4)
                    payload_decoded = base64.b64decode(payload_encoded)
                    payload_json = json.loads(payload_decoded)
                    print(f"\nJWT Token中的角色信息:")
                    print(f"  用户ID: {payload_json.get('sub')}")
                    print(f"  用户名: {payload_json.get('username')}")
                    print(f"  角色: {payload_json.get('roles', [])}")
                    print(f"  技能: {payload_json.get('skills', [])}")
                    print(f"  过期时间: {payload_json.get('exp')}")
            except Exception as e:
                print(f"JWT解析失败: {e}")
        
        return 1

if __name__ == "__main__":
    exit(main())
