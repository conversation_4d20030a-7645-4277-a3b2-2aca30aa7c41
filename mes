#!/bin/bash

# MES系统快速启动器
# 简化的命令行接口

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MANAGER_SCRIPT="$SCRIPT_DIR/docker-manager.sh"

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

# 显示简化帮助
show_quick_help() {
    echo -e "${BLUE}MES系统快速命令:${NC}"
    echo
    echo "  mes                    - 打开交互式管理界面"
    echo "  mes start             - 快速启动服务"
    echo "  mes stop              - 停止服务"
    echo "  mes restart           - 重启服务"
    echo "  mes status            - 查看状态"
    echo "  mes logs [service]    - 查看日志"
    echo "  mes deploy            - 完整部署"
    echo "  mes backup            - 备份数据"
    echo "  mes help              - 显示帮助"
    echo
}

# 检查管理脚本
if [ ! -f "$MANAGER_SCRIPT" ]; then
    echo "错误: 找不到docker-manager.sh脚本"
    exit 1
fi

# 处理命令行参数
case ${1:-""} in
    "start")
        echo -e "${GREEN}🚀 启动MES系统...${NC}"
        cd "$SCRIPT_DIR"
        if [ ! -f ".env.docker" ]; then
            echo "生成环境配置..."
            if [ -f "scripts/generate-env.sh" ]; then
                ./scripts/generate-env.sh docker
            fi
        fi
        docker compose --env-file .env.docker up -d
        echo -e "${GREEN}✅ 服务启动完成${NC}"
        echo "访问地址: http://localhost:3000"
        ;;
    "stop")
        echo -e "${GREEN}⏹️ 停止MES系统...${NC}"
        cd "$SCRIPT_DIR"
        docker compose --env-file .env.docker down
        echo -e "${GREEN}✅ 服务已停止${NC}"
        ;;
    "restart")
        echo -e "${GREEN}🔄 重启MES系统...${NC}"
        cd "$SCRIPT_DIR"
        docker compose --env-file .env.docker restart
        echo -e "${GREEN}✅ 服务已重启${NC}"
        ;;
    "status")
        cd "$SCRIPT_DIR"
        docker compose --env-file .env.docker ps
        ;;
    "logs")
        cd "$SCRIPT_DIR"
        if [ -n "$2" ]; then
            docker compose --env-file .env.docker logs -f "$2"
        else
            docker compose --env-file .env.docker logs -f
        fi
        ;;
    "deploy")
        echo -e "${GREEN}🏗️ 开始完整部署...${NC}"
        cd "$SCRIPT_DIR"
        exec "$MANAGER_SCRIPT"
        ;;
    "backup")
        echo -e "${GREEN}💾 备份数据...${NC}"
        cd "$SCRIPT_DIR"
        mkdir -p backups
        timestamp=$(date +%Y%m%d_%H%M%S)
        docker compose --env-file .env.docker exec -T postgres pg_dump -U mes_user mes_db > "backups/mes_backup_${timestamp}.sql"
        echo -e "${GREEN}✅ 备份完成: backups/mes_backup_${timestamp}.sql${NC}"
        ;;
    "help"|"-h"|"--help")
        show_quick_help
        ;;
    "")
        # 无参数时启动交互式界面
        cd "$SCRIPT_DIR"
        exec "$MANAGER_SCRIPT"
        ;;
    *)
        echo "未知命令: $1"
        echo
        show_quick_help
        exit 1
        ;;
esac
