#!/bin/bash

# MES系统生产环境安装脚本
# 用于将构建好的系统部署到生产服务器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
INSTALL_DIR="/opt/mes-system"
SERVICE_USER="mes"
SERVICE_GROUP="mes"
SYSTEMD_SERVICE="/etc/systemd/system/mes-system.service"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查权限
check_permissions() {
    if [ "$EUID" -ne 0 ]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检查构建文件
check_build_files() {
    log_info "检查构建文件..."
    
    if [ ! -f "target/release/mes-system" ]; then
        log_error "发布版本二进制文件不存在"
        log_info "请先运行: cargo build --release"
        exit 1
    fi
    
    if [ ! -d "frontend/dist" ]; then
        log_error "前端构建文件不存在"
        log_info "请先运行: cd frontend && npm run build"
        exit 1
    fi
    
    log_success "构建文件检查完成"
}

# 创建用户和组
create_user() {
    log_info "创建系统用户和组..."
    
    if ! getent group "$SERVICE_GROUP" > /dev/null 2>&1; then
        groupadd --system "$SERVICE_GROUP"
        log_success "创建组: $SERVICE_GROUP"
    else
        log_info "组已存在: $SERVICE_GROUP"
    fi
    
    if ! getent passwd "$SERVICE_USER" > /dev/null 2>&1; then
        useradd --system --gid "$SERVICE_GROUP" --home-dir "$INSTALL_DIR" \
                --shell /bin/false --comment "MES System User" "$SERVICE_USER"
        log_success "创建用户: $SERVICE_USER"
    else
        log_info "用户已存在: $SERVICE_USER"
    fi
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."
    
    mkdir -p "$INSTALL_DIR"/{bin,frontend,logs,config}
    chown -R "$SERVICE_USER:$SERVICE_GROUP" "$INSTALL_DIR"
    chmod 755 "$INSTALL_DIR"
    chmod 755 "$INSTALL_DIR"/{bin,frontend,config}
    chmod 750 "$INSTALL_DIR/logs"
    
    log_success "目录结构创建完成"
}

# 复制文件
copy_files() {
    log_info "复制应用文件..."
    
    # 复制二进制文件
    cp target/release/mes-system "$INSTALL_DIR/bin/"
    chmod 755 "$INSTALL_DIR/bin/mes-system"
    
    # 复制前端文件
    cp -r frontend/dist/* "$INSTALL_DIR/frontend/"
    
    # 复制配置文件
    if [ -f ".env.example" ]; then
        cp .env.example "$INSTALL_DIR/config/"
    fi
    
    # 复制启动脚本
    cp start-mes-system.sh "$INSTALL_DIR/"
    chmod 755 "$INSTALL_DIR/start-mes-system.sh"
    
    # 设置所有权
    chown -R "$SERVICE_USER:$SERVICE_GROUP" "$INSTALL_DIR"
    
    log_success "文件复制完成"
}

# 安装systemd服务
install_service() {
    log_info "安装systemd服务..."
    
    # 更新服务文件中的路径
    sed "s|/opt/mes-system|$INSTALL_DIR|g" mes-system.service > "$SYSTEMD_SERVICE"
    sed -i "s|User=mes|User=$SERVICE_USER|g" "$SYSTEMD_SERVICE"
    sed -i "s|Group=mes|Group=$SERVICE_GROUP|g" "$SYSTEMD_SERVICE"
    
    # 重新加载systemd
    systemctl daemon-reload
    
    # 启用服务
    systemctl enable mes-system.service
    
    log_success "systemd服务安装完成"
}

# 创建环境配置文件
create_env_file() {
    log_info "创建环境配置文件..."
    
    if [ ! -f "$INSTALL_DIR/.env" ]; then
        cat > "$INSTALL_DIR/.env" << EOF
# MES系统环境配置
RUST_LOG=info
MES_PORT=9001
MES_HOST=0.0.0.0

# 数据库配置 (请根据实际情况修改)
DATABASE_URL=postgresql://mes_user:mes_password@localhost:5432/mes_db

# JWT密钥 (请修改为随机字符串)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# 其他配置
MAX_CONNECTIONS=100
EOF
        
        chown "$SERVICE_USER:$SERVICE_GROUP" "$INSTALL_DIR/.env"
        chmod 600 "$INSTALL_DIR/.env"
        
        log_success "环境配置文件创建完成"
        log_warning "请编辑 $INSTALL_DIR/.env 文件，配置数据库连接等信息"
    else
        log_info "环境配置文件已存在，跳过创建"
    fi
}

# 设置防火墙
setup_firewall() {
    log_info "配置防火墙..."
    
    if command -v ufw >/dev/null 2>&1; then
        ufw allow 9001/tcp comment "MES System"
        log_success "UFW防火墙规则已添加"
    elif command -v firewall-cmd >/dev/null 2>&1; then
        firewall-cmd --permanent --add-port=9001/tcp
        firewall-cmd --reload
        log_success "firewalld防火墙规则已添加"
    else
        log_warning "未检测到防火墙管理工具，请手动开放端口9001"
    fi
}

# 显示安装信息
show_install_info() {
    echo ""
    log_success "=== MES系统安装完成 ==="
    echo ""
    log_info "安装目录: $INSTALL_DIR"
    log_info "服务用户: $SERVICE_USER"
    log_info "配置文件: $INSTALL_DIR/.env"
    log_info "日志目录: $INSTALL_DIR/logs"
    echo ""
    log_info "服务管理命令:"
    log_info "  启动服务: sudo systemctl start mes-system"
    log_info "  停止服务: sudo systemctl stop mes-system"
    log_info "  重启服务: sudo systemctl restart mes-system"
    log_info "  查看状态: sudo systemctl status mes-system"
    log_info "  查看日志: sudo journalctl -u mes-system -f"
    echo ""
    log_info "手动启动:"
    log_info "  cd $INSTALL_DIR && sudo -u $SERVICE_USER ./start-mes-system.sh"
    echo ""
    log_warning "下一步操作:"
    log_warning "1. 编辑配置文件: sudo nano $INSTALL_DIR/.env"
    log_warning "2. 配置数据库连接信息"
    log_warning "3. 启动服务: sudo systemctl start mes-system"
    echo ""
}

# 主函数
main() {
    log_info "开始安装MES系统到生产环境..."
    
    check_permissions
    check_build_files
    create_user
    create_directories
    copy_files
    install_service
    create_env_file
    setup_firewall
    show_install_info
}

# 执行主函数
main "$@"
