#!/bin/bash

# MES系统快速管理脚本
# 专门用于快速启动不同的前后端组合模式
# 版本: 1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m'

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 默认端口配置
DEFAULT_BACKEND_PORT=9001
DEFAULT_FRONTEND_PROD_PORT=3080
DEFAULT_FRONTEND_DEV_PORT=3000

# 加载端口配置
if [ -f ".env.ports" ]; then
    source .env.ports
fi

BACKEND_PORT=${MES_PORT:-$DEFAULT_BACKEND_PORT}
FRONTEND_PROD_PORT=${FRONTEND_PORT:-$DEFAULT_FRONTEND_PROD_PORT}
FRONTEND_DEV_PORT=${VITE_PORT:-$DEFAULT_FRONTEND_DEV_PORT}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}$1${NC}"
}

# 显示横幅
show_banner() {
    clear
    echo -e "${CYAN}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                MES 快速管理工具                              ║
║              Quick Management Tool                          ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
    echo -e "${WHITE}专用于快速启动不同的前后端组合模式${NC}"
    echo ""
}

# 显示菜单
show_menu() {
    log_header "═══════════════ 快速启动选项 ═══════════════"
    echo ""
    echo -e "${WHITE}🚀 生产模式组合${NC}"
    echo "  1) 后端生产 + 前端生产 (前台)"
    echo "  2) 后端生产 + 前端生产 (后台)"
    echo ""
    echo -e "${WHITE}🔧 开发模式组合${NC}"
    echo "  3) 后端开发 + 前端开发 (前台)"
    echo "  4) 后端开发 + 前端开发 (后台)"
    echo ""
    echo -e "${WHITE}🔄 混合模式组合${NC}"
    echo "  5) 后端生产 + 前端开发 (前台)"
    echo "  6) 后端生产 + 前端开发 (后台)"
    echo "  7) 后端开发 + 前端生产 (前台)"
    echo "  8) 后端开发 + 前端生产 (后台)"
    echo ""
    echo -e "${WHITE}🛠️  管理选项${NC}"
    echo "  9) 查看运行状态"
    echo " 10) 停止所有服务"
    echo " 11) 查看日志"
    echo " 12) 构建项目"
    echo ""
    echo "  0) 退出"
    echo ""
    echo -e "${CYAN}═══════════════════════════════════════${NC}"
    echo -e "${YELLOW}当前端口: 后端=$BACKEND_PORT, 前端生产=$FRONTEND_PROD_PORT, 前端开发=$FRONTEND_DEV_PORT${NC}"
}

# 等待用户输入
wait_for_input() {
    echo ""
    echo -e "${YELLOW}按任意键继续...${NC}"
    read -n 1 -s
}

# 检查构建状态
check_build_status() {
    local need_backend_build=false
    local need_frontend_build=false
    
    if [ ! -f "target/release/mes-system" ]; then
        need_backend_build=true
    fi
    
    if [ ! -d "frontend/dist" ]; then
        need_frontend_build=true
    fi
    
    if [ "$need_backend_build" = true ] || [ "$need_frontend_build" = true ]; then
        log_warning "检测到未构建的组件："
        [ "$need_backend_build" = true ] && echo "  - 后端需要构建"
        [ "$need_frontend_build" = true ] && echo "  - 前端需要构建"
        echo ""
        echo -n "是否现在构建? (y/N): "
        read -r confirm
        
        if [[ $confirm =~ ^[Yy]$ ]]; then
            if [ "$need_backend_build" = true ]; then
                log_info "构建后端..."
                cargo build --release
            fi
            
            if [ "$need_frontend_build" = true ]; then
                log_info "构建前端..."
                cd frontend && npm run build && cd ..
            fi
            
            log_success "构建完成！"
        else
            log_warning "跳过构建，某些模式可能无法启动"
        fi
        echo ""
    fi
}

# 启动函数模板
start_services() {
    local backend_mode=$1  # "prod" 或 "dev"
    local frontend_mode=$2 # "prod" 或 "dev"
    local run_mode=$3      # "foreground" 或 "background"
    
    log_header "启动模式: 后端${backend_mode} + 前端${frontend_mode} (${run_mode})"
    
    # 设置环境变量
    export MES_PORT=$BACKEND_PORT
    export VITE_PORT=$FRONTEND_DEV_PORT
    export VITE_API_PROXY_TARGET="http://localhost:$BACKEND_PORT"
    export RUST_LOG=${RUST_LOG:-info}
    
    if [ "$run_mode" = "foreground" ]; then
        start_services_foreground "$backend_mode" "$frontend_mode"
    else
        start_services_background "$backend_mode" "$frontend_mode"
    fi
}

# 前台启动
start_services_foreground() {
    local backend_mode=$1
    local frontend_mode=$2
    
    # 启动后端
    if [ "$backend_mode" = "prod" ]; then
        log_info "启动后端生产服务 (端口$BACKEND_PORT)..."
        ./target/release/mes-system &
    else
        log_info "启动后端开发服务 (端口$BACKEND_PORT)..."
        cargo run &
    fi
    BACKEND_PID=$!
    
    sleep 3
    
    # 启动前端
    if [ "$frontend_mode" = "prod" ]; then
        log_info "启动前端生产服务 (端口$FRONTEND_PROD_PORT)..."
        cd frontend
        python3 -m http.server $FRONTEND_PROD_PORT --directory dist &
        FRONTEND_PID=$!
        cd ..
        local frontend_port=$FRONTEND_PROD_PORT
    else
        log_info "启动前端开发服务 (端口$FRONTEND_DEV_PORT)..."
        cd frontend
        npm run dev &
        FRONTEND_PID=$!
        cd ..
        local frontend_port=$FRONTEND_DEV_PORT
    fi
    
    log_success "服务启动完成！"
    log_info "后端地址: http://localhost:$BACKEND_PORT (${backend_mode}模式)"
    log_info "前端地址: http://localhost:$frontend_port (${frontend_mode}模式)"
    log_info "按 Ctrl+C 停止所有服务"
    
    trap 'kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; log_info "正在停止服务..."; exit 0' INT
    wait
}

# 后台启动
start_services_background() {
    local backend_mode=$1
    local frontend_mode=$2
    
    mkdir -p logs
    
    # 启动后端
    if [ "$backend_mode" = "prod" ]; then
        log_info "启动后端生产服务 (后台)..."
        ./mes-process-manager.sh start-backend $BACKEND_PORT
    else
        log_info "启动后端开发服务 (后台)..."
        nohup cargo run > logs/backend-dev.log 2>&1 &
        echo $! > logs/backend-dev.pid
    fi
    
    sleep 3
    
    # 启动前端
    if [ "$frontend_mode" = "prod" ]; then
        log_info "启动前端生产服务 (后台)..."
        ./mes-process-manager.sh start-frontend $FRONTEND_PROD_PORT
        local frontend_port=$FRONTEND_PROD_PORT
    else
        log_info "启动前端开发服务 (后台)..."
        cd frontend
        nohup npm run dev > ../logs/frontend-dev.log 2>&1 &
        echo $! > ../logs/frontend-dev.pid
        cd ..
        local frontend_port=$FRONTEND_DEV_PORT
    fi
    
    sleep 2
    
    log_success "服务后台启动完成！"
    log_info "后端地址: http://localhost:$BACKEND_PORT (${backend_mode}模式)"
    log_info "前端地址: http://localhost:$frontend_port (${frontend_mode}模式)"
    log_info "使用选项10停止服务，选项9查看状态"
    
    wait_for_input
}

# 查看状态
show_status() {
    log_header "服务运行状态"
    echo ""
    
    # 检查后端状态
    local backend_running=false
    if [ -f "logs/backend.pid" ]; then
        local pid=$(cat logs/backend.pid 2>/dev/null)
        if [ -n "$pid" ] && ps -p "$pid" > /dev/null 2>&1; then
            echo -e "${GREEN}✓${NC} 后端生产服务: 运行中 (PID: $pid)"
            backend_running=true
        fi
    fi
    
    if [ -f "logs/backend-dev.pid" ]; then
        local pid=$(cat logs/backend-dev.pid 2>/dev/null)
        if [ -n "$pid" ] && ps -p "$pid" > /dev/null 2>&1; then
            echo -e "${GREEN}✓${NC} 后端开发服务: 运行中 (PID: $pid)"
            backend_running=true
        fi
    fi
    
    if [ "$backend_running" = false ]; then
        echo -e "${RED}✗${NC} 后端服务: 未运行"
    fi
    
    # 检查前端状态
    local frontend_running=false
    if [ -f "logs/frontend.pid" ]; then
        local pid=$(cat logs/frontend.pid 2>/dev/null)
        if [ -n "$pid" ] && ps -p "$pid" > /dev/null 2>&1; then
            echo -e "${GREEN}✓${NC} 前端生产服务: 运行中 (PID: $pid)"
            frontend_running=true
        fi
    fi
    
    if [ -f "logs/frontend-dev.pid" ]; then
        local pid=$(cat logs/frontend-dev.pid 2>/dev/null)
        if [ -n "$pid" ] && ps -p "$pid" > /dev/null 2>&1; then
            echo -e "${GREEN}✓${NC} 前端开发服务: 运行中 (PID: $pid)"
            frontend_running=true
        fi
    fi
    
    if [ "$frontend_running" = false ]; then
        echo -e "${RED}✗${NC} 前端服务: 未运行"
    fi
    
    echo ""
    wait_for_input
}

# 停止所有服务
stop_all_services() {
    log_header "停止所有服务"

    # 停止各种模式的服务
    local stopped_any=false

    # 停止生产模式服务
    ./mes-process-manager.sh stop-all 2>/dev/null && stopped_any=true

    # 停止开发模式服务
    if [ -f "start-dev-background.sh" ]; then
        ./start-dev-background.sh stop 2>/dev/null && stopped_any=true
    fi

    # 停止混合模式服务
    for pidfile in logs/backend-dev.pid logs/frontend-dev.pid; do
        if [ -f "$pidfile" ]; then
            local pid=$(cat "$pidfile" 2>/dev/null)
            if [ -n "$pid" ] && ps -p "$pid" > /dev/null 2>&1; then
                log_info "停止进程 (PID: $pid)..."
                kill -TERM "$pid" 2>/dev/null || true
                sleep 2
                if ps -p "$pid" > /dev/null 2>&1; then
                    kill -KILL "$pid" 2>/dev/null || true
                fi
                stopped_any=true
            fi
            rm -f "$pidfile"
        fi
    done

    # 清理残留进程
    pkill -f "mes-system" 2>/dev/null && stopped_any=true
    pkill -f "vite.*dev" 2>/dev/null && stopped_any=true
    pkill -f "npm.*dev" 2>/dev/null && stopped_any=true
    pkill -f "python.*http.server.*308" 2>/dev/null && stopped_any=true
    pkill -f "python.*http.server.*300" 2>/dev/null && stopped_any=true

    if [ "$stopped_any" = true ]; then
        log_success "服务停止完成"
    else
        log_info "没有发现运行中的服务"
    fi

    wait_for_input
}

# 查看日志
show_logs() {
    log_header "服务日志"
    echo ""

    echo -e "${GREEN}=== 后端日志 (最后10行) ===${NC}"
    if [ -f "logs/backend.log" ]; then
        tail -10 logs/backend.log
    elif [ -f "logs/backend-dev.log" ]; then
        tail -10 logs/backend-dev.log
    else
        echo "没有找到后端日志文件"
    fi

    echo ""
    echo -e "${GREEN}=== 前端日志 (最后10行) ===${NC}"
    if [ -f "logs/frontend.log" ]; then
        tail -10 logs/frontend.log
    elif [ -f "logs/frontend-dev.log" ]; then
        tail -10 logs/frontend-dev.log
    else
        echo "没有找到前端日志文件"
    fi

    echo ""
    wait_for_input
}

# 构建项目
build_project() {
    log_header "构建项目"

    echo -n "选择构建选项 (1=后端, 2=前端, 3=全部): "
    read -r build_choice

    case $build_choice in
        1)
            log_info "构建后端..."
            cargo build --release
            log_success "后端构建完成"
            ;;
        2)
            log_info "构建前端..."
            cd frontend && npm run build && cd ..
            log_success "前端构建完成"
            ;;
        3)
            log_info "构建后端..."
            cargo build --release
            log_info "构建前端..."
            cd frontend && npm run build && cd ..
            log_success "全部构建完成"
            ;;
        *)
            log_error "无效选择"
            ;;
    esac

    wait_for_input
}

# 主循环
main_loop() {
    while true; do
        show_banner
        show_menu

        echo -n -e "${CYAN}请选择操作 [0-12]: ${NC}"
        read -r choice

        case $choice in
            1)
                check_build_status
                start_services "prod" "prod" "foreground"
                ;;
            2)
                check_build_status
                start_services "prod" "prod" "background"
                ;;
            3)
                start_services "dev" "dev" "foreground"
                ;;
            4)
                start_services "dev" "dev" "background"
                ;;
            5)
                check_build_status
                start_services "prod" "dev" "foreground"
                ;;
            6)
                check_build_status
                start_services "prod" "dev" "background"
                ;;
            7)
                check_build_status
                start_services "dev" "prod" "foreground"
                ;;
            8)
                check_build_status
                start_services "dev" "prod" "background"
                ;;
            9)
                show_status
                ;;
            10)
                stop_all_services
                ;;
            11)
                show_logs
                ;;
            12)
                build_project
                ;;
            0)
                log_info "感谢使用MES快速管理工具！"
                exit 0
                ;;
            *)
                log_error "无效选择，请重新输入 (0-12)"
                sleep 2
                ;;
        esac
    done
}

# 检查参数
if [ $# -gt 0 ]; then
    case $1 in
        --help|-h)
            show_banner
            echo "MES快速管理工具"
            echo ""
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --help, -h     显示帮助信息"
            echo "  --status       显示服务状态"
            echo "  --stop         停止所有服务"
            echo ""
            echo "无参数运行将进入交互模式"
            exit 0
            ;;
        --status)
            show_banner
            show_status
            exit 0
            ;;
        --stop)
            stop_all_services
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            log_info "使用 --help 查看帮助"
            exit 1
            ;;
    esac
fi

# 启动主循环
main_loop
