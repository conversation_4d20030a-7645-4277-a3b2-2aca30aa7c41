<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限系统测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { margin: 5px; padding: 8px 15px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>MES系统权限测试</h1>
    
    <div class="test-section">
        <h2>1. 登录测试</h2>
        <button onclick="testAdminLogin()">测试Admin登录</button>
        <button onclick="testPlannerLogin()">测试Planner登录</button>
        <div id="login-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 权限API测试</h2>
        <button onclick="testPermissionsAPI()">测试权限API访问</button>
        <button onclick="testProjectsAPI()">测试项目API访问</button>
        <div id="api-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 页面访问测试</h2>
        <button onclick="testPageAccess('/permission-config')">测试权限配置页面</button>
        <button onclick="testPageAccess('/role-permissions')">测试角色权限页面</button>
        <button onclick="testPageAccess('/users')">测试用户管理页面</button>
        <div id="page-result" class="result"></div>
    </div>

    <script>
        let currentToken = null;
        
        async function testAdminLogin() {
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'admin', password: 'admin123' })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    currentToken = data.token;
                    document.getElementById('login-result').innerHTML = 
                        `<span class="success">✓ Admin登录成功</span><br>
                         用户: ${data.user.username} (${data.user.full_name})<br>
                         角色: ${data.user.roles.join(', ')}<br>
                         Token: ${data.token.substring(0, 50)}...`;
                } else {
                    document.getElementById('login-result').innerHTML = 
                        `<span class="error">✗ Admin登录失败: ${response.status}</span>`;
                }
            } catch (error) {
                document.getElementById('login-result').innerHTML = 
                    `<span class="error">✗ 登录错误: ${error.message}</span>`;
            }
        }
        
        async function testPlannerLogin() {
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'planner1', password: 'password123' })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    currentToken = data.token;
                    document.getElementById('login-result').innerHTML = 
                        `<span class="success">✓ Planner登录成功</span><br>
                         用户: ${data.user.username} (${data.user.full_name})<br>
                         角色: ${data.user.roles.join(', ')}<br>
                         Token: ${data.token.substring(0, 50)}...`;
                } else {
                    document.getElementById('login-result').innerHTML = 
                        `<span class="error">✗ Planner登录失败: ${response.status}</span>`;
                }
            } catch (error) {
                document.getElementById('login-result').innerHTML = 
                    `<span class="error">✗ 登录错误: ${error.message}</span>`;
            }
        }
        
        async function testPermissionsAPI() {
            if (!currentToken) {
                document.getElementById('api-result').innerHTML = 
                    '<span class="error">请先登录</span>';
                return;
            }
            
            try {
                const response = await fetch('/api/permissions', {
                    headers: { 'Authorization': `Bearer ${currentToken}` }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('api-result').innerHTML = 
                        `<span class="success">✓ 权限API访问成功</span><br>
                         权限数量: ${data.length}`;
                } else {
                    const errorData = await response.json();
                    document.getElementById('api-result').innerHTML = 
                        `<span class="error">✗ 权限API访问失败: ${response.status}</span><br>
                         错误: ${errorData.message || errorData.error}`;
                }
            } catch (error) {
                document.getElementById('api-result').innerHTML = 
                    `<span class="error">✗ API错误: ${error.message}</span>`;
            }
        }
        
        async function testProjectsAPI() {
            if (!currentToken) {
                document.getElementById('api-result').innerHTML = 
                    '<span class="error">请先登录</span>';
                return;
            }
            
            try {
                const response = await fetch('/api/projects', {
                    headers: { 'Authorization': `Bearer ${currentToken}` }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('api-result').innerHTML = 
                        `<span class="success">✓ 项目API访问成功</span><br>
                         项目数量: ${data.projects ? data.projects.length : 0}`;
                } else {
                    document.getElementById('api-result').innerHTML = 
                        `<span class="error">✗ 项目API访问失败: ${response.status}</span>`;
                }
            } catch (error) {
                document.getElementById('api-result').innerHTML = 
                    `<span class="error">✗ API错误: ${error.message}</span>`;
            }
        }
        
        function testPageAccess(page) {
            const testUrl = `http://localhost:3000/#${page}`;
            document.getElementById('page-result').innerHTML = 
                `<span class="info">正在测试页面访问: ${page}</span><br>
                 <a href="${testUrl}" target="_blank">点击访问 ${page}</a><br>
                 <small>请在新窗口中检查是否能正常访问或显示403错误</small>`;
        }
        
        // 页面加载时的初始化
        window.onload = function() {
            document.getElementById('login-result').innerHTML = 
                '<span class="info">请先测试登录功能</span>';
            document.getElementById('api-result').innerHTML = 
                '<span class="info">请先登录后测试API访问</span>';
            document.getElementById('page-result').innerHTML = 
                '<span class="info">点击按钮测试页面访问权限</span>';
        };
    </script>
</body>
</html>
