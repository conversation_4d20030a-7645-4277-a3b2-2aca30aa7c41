#!/bin/bash

# MES系统快速启动脚本 (发布版本)
# 使用预编译的二进制文件启动

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${GREEN}=== MES系统快速启动 (发布版本) ===${NC}"

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 检查二进制文件
if [ ! -f "target/release/mes-system" ]; then
    echo -e "${RED}错误: 发布版本二进制文件不存在${NC}"
    echo -e "${YELLOW}请先运行构建命令:${NC}"
    echo "  cargo build --release"
    echo ""
    echo -e "${YELLOW}或使用完整启动脚本:${NC}"
    echo "  ./start-mes-system.sh build"
    exit 1
fi

# 检查前端构建文件
if [ ! -d "frontend/dist" ]; then
    echo -e "${RED}错误: 前端构建文件不存在${NC}"
    echo -e "${YELLOW}请先构建前端:${NC}"
    echo "  cd frontend && npm run build"
    echo ""
    echo -e "${YELLOW}或使用完整启动脚本:${NC}"
    echo "  ./start-mes-system.sh build"
    exit 1
fi

# 设置环境变量
export RUST_LOG=${RUST_LOG:-info}
export MES_PORT=${MES_PORT:-9001}
export MES_HOST=${MES_HOST:-0.0.0.0}

# 检查环境变量文件
if [ -f ".env" ]; then
    echo -e "${BLUE}加载环境变量文件...${NC}"
    source .env
fi

echo -e "${GREEN}启动配置:${NC}"
echo "  - 主机: $MES_HOST"
echo "  - 端口: $MES_PORT"
echo "  - 日志级别: $RUST_LOG"
echo "  - 二进制文件: target/release/mes-system"
echo "  - 前端文件: frontend/dist"
echo ""

echo -e "${GREEN}启动MES系统...${NC}"
echo -e "${YELLOW}访问地址: http://localhost:$MES_PORT${NC}"
echo -e "${YELLOW}按 Ctrl+C 停止服务${NC}"
echo ""

# 启动系统
exec ./target/release/mes-system
