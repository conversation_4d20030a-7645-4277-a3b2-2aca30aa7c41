# MES系统部署指南

本文档介绍如何构建和部署MES系统的发布版本。

## 📦 构建发布版本

### 1. 构建后端 (Rust)
```bash
# 构建发布版本 (优化编译)
cargo build --release

# 二进制文件位置: target/release/mes-system
```

### 2. 构建前端 (React + TypeScript)
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 构建生产版本
npm run build

# 构建文件位置: frontend/dist/
```

## 🚀 启动脚本

### 后端启动

#### 快速启动 (推荐)
```bash
# 使用预编译的二进制文件快速启动
./quick-start.sh
```

#### 完整启动脚本
```bash
# 功能完整的启动脚本，支持多种选项
./start-mes-system.sh [命令] [选项]

# 示例:
./start-mes-system.sh start           # 前台启动
./start-mes-system.sh start -d        # 后台启动
./start-mes-system.sh start -p 8080   # 指定端口
./start-mes-system.sh stop            # 停止服务
./start-mes-system.sh restart         # 重启服务
./start-mes-system.sh status          # 查看状态
./start-mes-system.sh logs            # 查看日志
./start-mes-system.sh build           # 重新构建
```

#### Windows启动
```cmd
# Windows批处理文件
start-mes-system.bat
```

### 前端启动

#### 快速启动 (推荐)
```bash
# 自动检测模式启动
./frontend/quick-start-frontend.sh
```

#### 完整启动脚本
```bash
# 开发模式
./frontend/start-frontend.sh dev
./frontend/start-frontend.sh dev -p 3001 -o    # 指定端口并打开浏览器

# 生产模式
./frontend/start-frontend.sh prod
./frontend/start-frontend.sh prod -p 8080      # 指定端口

# 构建
./frontend/start-frontend.sh build

# 预览
./frontend/start-frontend.sh preview
```

#### npm脚本
```bash
cd frontend

# 开发模式
npm run dev

# 生产构建并启动
npm run start:prod

# 仅启动静态服务器
npm run serve
```

#### Windows启动
```cmd
# Windows批处理文件
frontend\start-frontend.bat
```

## 🔧 环境配置

### 环境变量
```bash
# 服务配置
export MES_PORT=9001                    # 服务端口
export MES_HOST=0.0.0.0                # 监听地址
export RUST_LOG=info                   # 日志级别

# 数据库配置
export DATABASE_URL="postgresql://mes_user:mes_password@localhost:5432/mes_db"

# JWT配置
export JWT_SECRET="your-secret-key"
```

### .env文件
创建 `.env` 文件来配置环境变量：
```env
# MES系统环境配置
RUST_LOG=info
MES_PORT=9001
MES_HOST=0.0.0.0

# 数据库配置
DATABASE_URL=postgresql://mes_user:mes_password@localhost:5432/mes_db

# JWT密钥
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
```

## 🏭 生产环境部署

### 自动安装脚本
```bash
# 以root权限运行安装脚本
sudo ./install-production.sh
```

安装脚本会：
- 创建系统用户 `mes`
- 安装到 `/opt/mes-system`
- 配置systemd服务
- 设置防火墙规则
- 创建日志目录

### 手动部署步骤

1. **创建部署目录**
```bash
sudo mkdir -p /opt/mes-system/{bin,frontend,logs,config}
```

2. **复制文件**
```bash
# 复制二进制文件
sudo cp target/release/mes-system /opt/mes-system/bin/

# 复制前端文件
sudo cp -r frontend/dist/* /opt/mes-system/frontend/

# 复制配置文件
sudo cp .env.example /opt/mes-system/config/.env
```

3. **创建系统用户**
```bash
sudo useradd --system --home-dir /opt/mes-system mes
sudo chown -R mes:mes /opt/mes-system
```

4. **安装systemd服务**
```bash
sudo cp mes-system.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable mes-system
```

### systemd服务管理
```bash
# 启动服务
sudo systemctl start mes-system

# 停止服务
sudo systemctl stop mes-system

# 重启服务
sudo systemctl restart mes-system

# 查看状态
sudo systemctl status mes-system

# 查看日志
sudo journalctl -u mes-system -f

# 开机自启
sudo systemctl enable mes-system
```

## 📁 文件结构

```
mes-system/
├── target/release/
│   └── mes-system              # 后端二进制文件
├── frontend/dist/              # 前端构建文件
├── start-mes-system.sh         # 完整启动脚本
├── quick-start.sh              # 快速启动脚本
├── start-mes-system.bat        # Windows启动脚本
├── install-production.sh       # 生产环境安装脚本
├── mes-system.service          # systemd服务配置
└── .env                        # 环境变量配置
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
```bash
# 查看端口占用
sudo netstat -tlnp | grep :9001

# 杀死占用进程
sudo pkill -f mes-system
```

2. **权限问题**
```bash
# 确保脚本可执行
chmod +x *.sh

# 确保二进制文件可执行
chmod +x target/release/mes-system
```

3. **数据库连接失败**
- 检查PostgreSQL服务是否运行
- 验证数据库连接字符串
- 确保数据库用户有足够权限

4. **前端文件404**
- 确保前端已构建: `cd frontend && npm run build`
- 检查 `frontend/dist` 目录是否存在

### 日志查看
```bash
# 查看应用日志
tail -f logs/mes-system.log

# 查看systemd日志
sudo journalctl -u mes-system -f

# 查看错误日志
sudo journalctl -u mes-system -p err
```

## 🔒 安全建议

1. **更改默认密钥**
   - 修改JWT_SECRET为随机字符串
   - 使用强密码配置数据库

2. **网络安全**
   - 配置防火墙规则
   - 使用HTTPS (配置反向代理)
   - 限制数据库访问

3. **文件权限**
   - 确保配置文件权限为600
   - 使用专用系统用户运行服务

4. **定期更新**
   - 定期更新系统依赖
   - 监控安全漏洞

## 📊 性能优化

1. **数据库优化**
   - 配置连接池
   - 添加适当索引
   - 定期维护

2. **应用优化**
   - 调整日志级别
   - 配置资源限制
   - 使用CDN加速静态资源

3. **监控**
   - 配置系统监控
   - 设置告警规则
   - 定期备份数据

## 📞 支持

如有问题，请查看：
- 应用日志: `logs/mes-system.log`
- 系统日志: `sudo journalctl -u mes-system`
- 项目文档: README.md
