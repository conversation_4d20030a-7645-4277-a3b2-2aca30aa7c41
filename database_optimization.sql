-- MES系统数据库性能优化脚本
-- 创建必要的索引以提升查询性能

-- ==================== 分析当前查询模式 ====================

-- 1. 项目相关查询优化
-- 常见查询：按项目查找工单、按项目查找计划任务

-- 2. 工单相关查询优化  
-- 常见查询：按项目BOM查找工单、按状态和日期查找工单

-- 3. 计划任务相关查询优化
-- 常见查询：按工单查找任务、按时间范围查找任务、按状态和机器查找任务

-- ==================== 创建性能索引 ====================

-- 1. project_boms表索引优化
-- 按项目ID查找BOM（项目详情页面常用）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_project_boms_project_id 
ON project_boms(project_id);

-- 按零件ID查找BOM（零件使用情况查询）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_project_boms_part_id 
ON project_boms(part_id);

-- 复合索引：项目+零件（避免重复BOM检查）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_project_boms_project_part 
ON project_boms(project_id, part_id);

-- 2. work_orders表索引优化
-- 按项目BOM查找工单（最常用的查询）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_work_orders_project_bom_id 
ON work_orders(project_bom_id);

-- 复合索引：项目BOM+状态（工单列表页面）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_work_orders_bom_status 
ON work_orders(project_bom_id, status);

-- 按到期日期查找工单（生产计划）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_work_orders_due_date 
ON work_orders(due_date) WHERE due_date IS NOT NULL;

-- 复合索引：状态+到期日期（紧急工单查询）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_work_orders_status_due_date 
ON work_orders(status, due_date) WHERE due_date IS NOT NULL;

-- 3. plan_tasks表索引优化
-- 按工单查找计划任务（最重要的关联查询）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_plan_tasks_work_order_id 
ON plan_tasks(work_order_id);

-- 复合索引：工单+状态（任务进度查询）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_plan_tasks_work_order_status 
ON plan_tasks(work_order_id, status);

-- 按机器查找任务（设备调度）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_plan_tasks_machine_id 
ON plan_tasks(machine_id) WHERE machine_id IS NOT NULL;

-- 复合索引：机器+时间范围（设备利用率分析）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_plan_tasks_machine_time 
ON plan_tasks(machine_id, planned_start, planned_end) WHERE machine_id IS NOT NULL;

-- 按技能组查找任务（人员调度）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_plan_tasks_skill_group_id 
ON plan_tasks(skill_group_id);

-- 复合索引：技能组+状态+时间（人员工作负荷）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_plan_tasks_skill_status_time 
ON plan_tasks(skill_group_id, status, planned_start);

-- 时间范围查询优化（甘特图）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_plan_tasks_time_range 
ON plan_tasks(planned_start, planned_end);

-- 复合索引：状态+时间范围（活动任务查询）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_plan_tasks_status_time_range 
ON plan_tasks(status, planned_start, planned_end);

-- 4. 质量管理相关索引
-- 按计划任务查找质量检验
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_quality_inspections_plan_task_id 
ON quality_inspections(plan_task_id);

-- 按检验状态查找
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_quality_inspections_status 
ON quality_inspections(status);

-- 复合索引：任务+状态（质量跟踪）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_quality_inspections_task_status 
ON quality_inspections(plan_task_id, status);

-- 5. 执行日志相关索引
-- 按计划任务查找执行日志
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_execution_logs_plan_task_id 
ON execution_logs(plan_task_id);

-- 按执行时间查找日志
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_execution_logs_executed_at 
ON execution_logs(executed_at);

-- 复合索引：任务+时间（任务执行历史）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_execution_logs_task_time 
ON execution_logs(plan_task_id, executed_at);

-- 6. 审计日志索引优化
-- 按用户查找审计日志
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_user_id 
ON audit_logs(user_id);

-- 按操作时间查找
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_created_at 
ON audit_logs(created_at);

-- 按操作类型查找
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_action 
ON audit_logs(action);

-- 复合索引：用户+时间（用户操作历史）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_user_time 
ON audit_logs(user_id, created_at);

-- ==================== 分析表统计信息 ====================

-- 更新表统计信息以优化查询计划
ANALYZE projects;
ANALYZE project_boms;
ANALYZE work_orders;
ANALYZE plan_tasks;
ANALYZE quality_inspections;
ANALYZE execution_logs;
ANALYZE audit_logs;

-- ==================== 查询性能验证 ====================

-- 验证索引是否被正确使用
-- 可以使用 EXPLAIN ANALYZE 来检查查询计划

-- 示例查询1：按项目查找所有工单
-- EXPLAIN ANALYZE 
-- SELECT wo.* FROM work_orders wo 
-- JOIN project_boms pb ON wo.project_bom_id = pb.id 
-- WHERE pb.project_id = 1;

-- 示例查询2：按工单查找所有计划任务
-- EXPLAIN ANALYZE 
-- SELECT * FROM plan_tasks WHERE work_order_id = 1;

-- 示例查询3：甘特图时间范围查询
-- EXPLAIN ANALYZE 
-- SELECT * FROM plan_tasks 
-- WHERE planned_start >= '2024-01-01' AND planned_end <= '2024-12-31';

-- ==================== 索引维护建议 ====================

-- 1. 定期重建索引（可选，PostgreSQL自动维护）
-- REINDEX INDEX CONCURRENTLY idx_plan_tasks_work_order_id;

-- 2. 监控索引使用情况
-- SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
-- FROM pg_stat_user_indexes 
-- WHERE schemaname = 'public'
-- ORDER BY idx_scan DESC;

-- 3. 检查未使用的索引
-- SELECT schemaname, tablename, indexname, idx_scan
-- FROM pg_stat_user_indexes 
-- WHERE idx_scan = 0 AND schemaname = 'public';

\echo '✅ 数据库性能优化索引创建完成'
\echo '📊 建议运行 EXPLAIN ANALYZE 验证查询性能提升'
\echo '🔍 使用 pg_stat_user_indexes 监控索引使用情况'
