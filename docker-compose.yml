services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: mes-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-mes_db}
      POSTGRES_USER: ${POSTGRES_USER:-mes_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
      TZ: Asia/Shanghai
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d
    ports:
      # 支持IPv4和IPv6
      - "5432:5432"
      - "[::]:5432:5432"
    networks:
      - mes-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-mes_user} -d ${POSTGRES_DB:-mes_db}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Redis缓存（可选）
  redis:
    image: redis:7-alpine
    container_name: mes-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
      - "[::]:6379:6379"
    networks:
      - mes-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # 后端API服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile.simple
    container_name: mes-backend
    environment:
      DATABASE_URL: postgresql://${POSTGRES_USER:-mes_user}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-mes_db}
      JWT_SECRET: ${JWT_SECRET}
      RUST_LOG: ${RUST_LOG:-info}
      SERVER_HOST: ${SERVER_HOST:-0.0.0.0}
      SERVER_PORT: ${SERVER_PORT:-9000}
      REDIS_URL: redis://:${REDIS_PASSWORD:-redis123}@redis:6379
      TZ: Asia/Shanghai
    ports:
      - "9000:9000"
      - "[::]:9000:9000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - mes-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # 前端Web服务
  frontend:
    build: 
      context: ./frontend
      dockerfile: Dockerfile
    container_name: mes-frontend
    environment:
      TZ: Asia/Shanghai
    ports:
      - "3000:80"
      - "[::]:3000:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - mes-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  mes-network:
    driver: bridge
    enable_ipv6: true
    ipam:
      driver: default
      config:
        - subnet: **********/16
        - subnet: 2001:db8::/32
