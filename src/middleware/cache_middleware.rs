use axum::{
    extract::{Request, State},
    http::{HeaderMap, HeaderValue, Method, StatusCode, Uri},
    middleware::Next,
    response::{IntoResponse, Response},
    body::Body,
};
use std::collections::HashMap;
use tracing::{info, warn, error};
use std::collections::hash_map::DefaultHasher;
use std::hash::{Hash, Hasher};

use crate::services::cache_service::{CacheService, CacheStrategy, CacheTTL};

// 缓存配置
#[derive(Clone)]
pub struct CacheConfig {
    pub enabled: bool,
    pub default_ttl: u64,
    pub cache_methods: Vec<Method>,
    pub cache_patterns: HashMap<String, CacheStrategy>,
    pub exclude_patterns: Vec<String>,
    pub cache_headers: Vec<String>,
}

impl Default for CacheConfig {
    fn default() -> Self {
        let mut cache_patterns = HashMap::new();
        
        // 配置不同API的缓存策略
        cache_patterns.insert("projects".to_string(), CacheStrategy::HotData {
            base_ttl: CacheTTL::MEDIUM,
            max_ttl: CacheTTL::LONG,
            access_threshold: 5,
        });
        
        cache_patterns.insert("parts".to_string(), CacheStrategy::ReadOnly {
            ttl: CacheTTL::LONG,
        });
        
        cache_patterns.insert("machines".to_string(), CacheStrategy::ReadOnly {
            ttl: CacheTTL::VERY_LONG,
        });
        
        cache_patterns.insert("users".to_string(), CacheStrategy::Simple {
            ttl: CacheTTL::MEDIUM,
        });
        
        cache_patterns.insert("dashboard".to_string(), CacheStrategy::Simple {
            ttl: CacheTTL::SHORT,
        });

        Self {
            enabled: true,
            default_ttl: CacheTTL::MEDIUM,
            cache_methods: vec![Method::GET],
            cache_patterns,
            exclude_patterns: vec![
                "auth".to_string(),
                "login".to_string(),
                "logout".to_string(),
                "upload".to_string(),
            ],
            cache_headers: vec![
                "user-id".to_string(),
                "authorization".to_string(),
            ],
        }
    }
}

// 缓存中间件
pub async fn cache_middleware(
    State(cache_service): State<CacheService>,
    uri: Uri,
    method: Method,
    headers: HeaderMap,
    request: Request,
    next: Next,
) -> Response {
    let config = CacheConfig::default();
    
    // 检查是否启用缓存
    if !config.enabled {
        return next.run(request).await;
    }
    
    // 检查HTTP方法是否支持缓存
    if !config.cache_methods.contains(&method) {
        return next.run(request).await;
    }
    
    let path = uri.path();
    
    // 检查是否在排除列表中
    if config.exclude_patterns.iter().any(|pattern| path.contains(pattern)) {
        return next.run(request).await;
    }
    
    // 生成缓存键
    let cache_key = generate_cache_key(&uri, &headers, &config.cache_headers);
    
    // 尝试从缓存获取响应
    if let Ok(Some(cached_response)) = cache_service.get::<CachedResponse>(&cache_key).await {
        info!("缓存命中: {}", cache_key);
        return cached_response.into_response();
    }
    
    // 缓存未命中，执行请求
    let response = next.run(request).await;

    // 简单记录缓存未命中（实际缓存逻辑可以在响应处理器中实现）
    info!("缓存未命中: {}", cache_key);

    response
}

// 缓存的响应结构
#[derive(serde::Serialize, serde::Deserialize, Clone)]
struct CachedResponse {
    status: u16,
    headers: HashMap<String, String>,
    body: Vec<u8>,
}

impl CachedResponse {
    fn new(status: StatusCode, headers: &HeaderMap, body: Vec<u8>) -> Self {
        let headers_map = headers
            .iter()
            .filter_map(|(name, value)| {
                value.to_str().ok().map(|v| (name.to_string(), v.to_string()))
            })
            .collect();
            
        Self {
            status: status.as_u16(),
            headers: headers_map,
            body,
        }
    }
}

impl IntoResponse for CachedResponse {
    fn into_response(self) -> Response {
        let mut response = Response::builder().status(self.status);
        
        // 设置响应头
        if let Some(headers) = response.headers_mut() {
            for (name, value) in self.headers {
                if let (Ok(header_name), Ok(header_value)) = (
                    name.parse::<axum::http::HeaderName>(),
                    HeaderValue::from_str(&value)
                ) {
                    headers.insert(header_name, header_value);
                }
            }
            
            // 添加缓存标识头
            headers.insert("X-Cache", HeaderValue::from_static("HIT"));
        }
        
        response.body(Body::from(self.body)).unwrap()
    }
}

// 生成缓存键
fn generate_cache_key(uri: &Uri, headers: &HeaderMap, cache_headers: &[String]) -> String {
    let mut hasher = DefaultHasher::new();

    // 添加URI
    uri.to_string().hash(&mut hasher);

    // 添加相关请求头
    for header_name in cache_headers {
        if let Some(header_value) = headers.get(header_name) {
            header_name.hash(&mut hasher);
            header_value.as_bytes().hash(&mut hasher);
        }
    }

    let hash = hasher.finish();
    format!("api_cache:{:x}", hash)
}

// 获取路径对应的TTL
fn get_ttl_for_path(path: &str, config: &CacheConfig) -> u64 {
    for (pattern, strategy) in &config.cache_patterns {
        if path.contains(pattern) {
            return match strategy {
                CacheStrategy::Simple { ttl } => *ttl,
                CacheStrategy::WriteThrough { ttl } => *ttl,
                CacheStrategy::WriteBack { ttl, .. } => *ttl,
                CacheStrategy::ReadOnly { ttl } => *ttl,
                CacheStrategy::HotData { base_ttl, .. } => *base_ttl,
            };
        }
    }
    config.default_ttl
}

// 简化的缓存响应函数（暂时不使用）
#[allow(dead_code)]
async fn cache_response(
    _cache_service: CacheService,
    _cache_key: String,
    _ttl: u64,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    // 简化实现，避免复杂的响应体处理
    Ok(())
}

// 缓存失效中间件
pub async fn cache_invalidation_middleware(
    State(cache_service): State<CacheService>,
    uri: Uri,
    method: Method,
    request: Request,
    next: Next,
) -> Response {
    let response = next.run(request).await;
    
    // 如果是修改操作且成功，则失效相关缓存
    if matches!(method, Method::POST | Method::PUT | Method::DELETE | Method::PATCH) 
        && response.status().is_success() {
        
        let path = uri.path();
        let invalidation_patterns = get_invalidation_patterns(path);
        
        if !invalidation_patterns.is_empty() {
            let cache_service_clone = cache_service.clone();
            tokio::spawn(async move {
                for pattern in invalidation_patterns {
                    if let Err(e) = cache_service_clone.delete_pattern(&pattern).await {
                        error!("缓存失效失败: {} - {}", pattern, e);
                    } else {
                        info!("缓存失效成功: {}", pattern);
                    }
                }
            });
        }
    }
    
    response
}

// 获取需要失效的缓存模式
fn get_invalidation_patterns(path: &str) -> Vec<String> {
    let mut patterns = Vec::new();
    
    if path.contains("projects") {
        patterns.push("api_cache:*projects*".to_string());
        patterns.push("api_cache:*dashboard*".to_string());
    }
    
    if path.contains("parts") {
        patterns.push("api_cache:*parts*".to_string());
        patterns.push("api_cache:*projects*".to_string());
    }
    
    if path.contains("work-orders") {
        patterns.push("api_cache:*work-orders*".to_string());
        patterns.push("api_cache:*projects*".to_string());
        patterns.push("api_cache:*dashboard*".to_string());
    }
    
    if path.contains("plan-tasks") {
        patterns.push("api_cache:*plan-tasks*".to_string());
        patterns.push("api_cache:*dashboard*".to_string());
    }
    
    patterns
}
