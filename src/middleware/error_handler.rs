use axum::{
    extract::Request,
    http::{StatusCode, Uri},
    middleware::Next,
    response::{IntoResponse, Response},
    J<PERSON>,
};
use tracing::error;
use uuid::Uuid;
use std::time::Instant;

use crate::utils::error::{AppError, ErrorResponse, ErrorDetail};

/// 错误处理中间件
pub async fn error_handler_middleware(
    uri: Uri,
    request: Request,
    next: Next,
) -> Response {
    let start_time = Instant::now();
    let request_id = Uuid::new_v4().to_string();
    let path = uri.path().to_string();

    // 在请求头中添加请求ID（如果需要的话）
    let response = next.run(request).await;
    
    let duration = start_time.elapsed();
    
    // 记录请求日志
    match response.status() {
        status if status.is_success() => {
            tracing::info!(
                request_id = %request_id,
                path = %path,
                status_code = %status.as_u16(),
                duration_ms = %duration.as_millis(),
                "Request completed successfully"
            );
        }
        status if status.is_client_error() => {
            tracing::warn!(
                request_id = %request_id,
                path = %path,
                status_code = %status.as_u16(),
                duration_ms = %duration.as_millis(),
                "Client error occurred"
            );
        }
        status if status.is_server_error() => {
            tracing::error!(
                request_id = %request_id,
                path = %path,
                status_code = %status.as_u16(),
                duration_ms = %duration.as_millis(),
                "Server error occurred"
            );
        }
        _ => {}
    }

    response
}

/// 全局错误处理器
pub async fn global_error_handler(error: Box<dyn std::error::Error + Send + Sync>) -> Response {
    let request_id = Uuid::new_v4().to_string();
    
    error!(
        request_id = %request_id,
        error = %error,
        "Unhandled error occurred"
    );

    let error_response = ErrorResponse {
        error: ErrorDetail {
            code: "INTERNAL_SERVER_ERROR".to_string(),
            message: "服务器内部错误".to_string(),
            details: Some(format!("Request ID: {}", request_id)),
        },
        timestamp: chrono::Utc::now().to_rfc3339(),
        path: None,
        request_id: Some(request_id),
        user_id: None,
    };

    (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)).into_response()
}

/// 数据库错误转换器
pub fn handle_database_error(error: sqlx::Error) -> AppError {
    match error {
        sqlx::Error::RowNotFound => {
            AppError::not_found("记录")
        }
        sqlx::Error::Database(db_error) => {
            let constraint_name = db_error.constraint().unwrap_or("unknown");
            
            // 处理常见的数据库约束错误
            if constraint_name.contains("unique") || constraint_name.contains("pk") {
                AppError::conflict("数据已存在，不能重复创建".to_string())
            } else if constraint_name.contains("fk") || constraint_name.contains("foreign") {
                AppError::business_error("关联的数据不存在".to_string())
            } else if constraint_name.contains("check") {
                AppError::validation_error("数据不符合业务规则".to_string())
            } else {
                AppError::database_error(format!("数据库约束错误: {}", constraint_name))
            }
        }
        sqlx::Error::Io(io_error) => {
            AppError::database_error(format!("数据库连接错误: {}", io_error))
        }
        sqlx::Error::Tls(tls_error) => {
            AppError::database_error(format!("数据库TLS错误: {}", tls_error))
        }
        sqlx::Error::Protocol(protocol_error) => {
            AppError::database_error(format!("数据库协议错误: {}", protocol_error))
        }
        sqlx::Error::TypeNotFound { type_name } => {
            AppError::database_error(format!("数据库类型错误: {}", type_name))
        }
        sqlx::Error::ColumnIndexOutOfBounds { index, len } => {
            AppError::database_error(format!("列索引超出范围: {} >= {}", index, len))
        }
        sqlx::Error::ColumnNotFound(column_name) => {
            AppError::database_error(format!("列不存在: {}", column_name))
        }
        sqlx::Error::ColumnDecode { index, source } => {
            AppError::database_error(format!("列解码错误 [{}]: {}", index, source))
        }
        sqlx::Error::Decode(decode_error) => {
            AppError::database_error(format!("数据解码错误: {}", decode_error))
        }
        sqlx::Error::PoolTimedOut => {
            AppError::timeout_error()
        }
        sqlx::Error::PoolClosed => {
            AppError::service_unavailable()
        }
        sqlx::Error::WorkerCrashed => {
            AppError::service_unavailable()
        }
        _ => {
            AppError::database_error(format!("未知数据库错误: {}", error))
        }
    }
}

/// JSON解析错误处理器
pub fn handle_json_error(error: serde_json::Error) -> AppError {
    AppError::validation_error(format!("JSON格式错误: {}", error))
}

/// 验证错误处理器
pub fn handle_validation_error(field: &str, message: &str) -> AppError {
    AppError::validation_error(format!("字段 '{}' 验证失败: {}", field, message))
}

/// 业务逻辑错误处理器
pub fn handle_business_error(operation: &str, reason: &str) -> AppError {
    AppError::business_error(format!("操作 '{}' 失败: {}", operation, reason))
}

/// 权限检查错误处理器
pub fn handle_permission_error(resource: &str, action: &str) -> AppError {
    AppError::forbidden(Some(format!("没有权限对 '{}' 执行 '{}' 操作", resource, action)))
}

/// 资源冲突错误处理器
pub fn handle_conflict_error(resource: &str, reason: &str) -> AppError {
    AppError::conflict(format!("{} 冲突: {}", resource, reason))
}

/// 外部服务错误处理器
pub fn handle_external_service_error(_service: &str, _error: &str) -> AppError {
    AppError::service_unavailable()
}

/// 文件操作错误处理器
pub fn handle_file_error(operation: &str, error: std::io::Error) -> AppError {
    match error.kind() {
        std::io::ErrorKind::NotFound => {
            AppError::not_found("文件")
        }
        std::io::ErrorKind::PermissionDenied => {
            AppError::forbidden(Some("文件访问权限不足".to_string()))
        }
        std::io::ErrorKind::AlreadyExists => {
            AppError::conflict("文件已存在".to_string())
        }
        _ => {
            AppError::internal_server_error(format!("文件{}操作失败: {}", operation, error))
        }
    }
}
