use axum::{
    extract::Request,
    http::{Method, StatusCode, Uri},
    middleware::Next,
    response::Response,
};
use std::time::{Duration, Instant};
use std::sync::{Arc, Mutex};
use std::collections::HashMap;
use tracing::{info, warn, error};

// 请求指标
#[derive(Debug, Clone, serde::Serialize)]
pub struct RequestMetrics {
    pub path: String,
    pub method: String,
    pub status_code: u16,
    pub response_time_ms: u64,
    pub timestamp: u64,
}

// 聚合指标
#[derive(Debug, Clone, serde::Serialize)]
pub struct AggregatedMetrics {
    pub total_requests: u64,
    pub total_errors: u64,
    pub average_response_time_ms: f64,
    pub requests_per_minute: f64,
    pub error_rate_percent: f64,
    pub slowest_endpoints: Vec<(String, f64)>,
    pub most_requested_endpoints: Vec<(String, u64)>,
    pub status_code_distribution: HashMap<u16, u64>,
}

// 指标收集器
#[derive(Debug)]
pub struct MetricsCollector {
    requests: Vec<RequestMetrics>,
    endpoint_stats: HashMap<String, EndpointStats>,
    start_time: Instant,
}

#[derive(Debug, Clone)]
struct EndpointStats {
    count: u64,
    total_time_ms: u64,
    error_count: u64,
    min_time_ms: u64,
    max_time_ms: u64,
}

impl MetricsCollector {
    pub fn new() -> Self {
        Self {
            requests: Vec::new(),
            endpoint_stats: HashMap::new(),
            start_time: Instant::now(),
        }
    }

    pub fn record_request(&mut self, metrics: RequestMetrics) {
        let endpoint_key = format!("{} {}", metrics.method, metrics.path);
        
        // 更新端点统计
        let stats = self.endpoint_stats.entry(endpoint_key).or_insert(EndpointStats {
            count: 0,
            total_time_ms: 0,
            error_count: 0,
            min_time_ms: u64::MAX,
            max_time_ms: 0,
        });

        stats.count += 1;
        stats.total_time_ms += metrics.response_time_ms;
        stats.min_time_ms = stats.min_time_ms.min(metrics.response_time_ms);
        stats.max_time_ms = stats.max_time_ms.max(metrics.response_time_ms);

        if metrics.status_code >= 400 {
            stats.error_count += 1;
        }

        // 保留最近的请求记录（限制数量避免内存泄漏）
        self.requests.push(metrics);
        if self.requests.len() > 10000 {
            self.requests.drain(0..5000); // 保留最近5000条
        }
    }

    pub fn get_aggregated_metrics(&self) -> AggregatedMetrics {
        let total_requests = self.requests.len() as u64;
        let total_errors = self.requests.iter()
            .filter(|r| r.status_code >= 400)
            .count() as u64;

        let average_response_time_ms = if total_requests > 0 {
            self.requests.iter()
                .map(|r| r.response_time_ms as f64)
                .sum::<f64>() / total_requests as f64
        } else {
            0.0
        };

        let uptime_minutes = self.start_time.elapsed().as_secs() as f64 / 60.0;
        let requests_per_minute = if uptime_minutes > 0.0 {
            total_requests as f64 / uptime_minutes
        } else {
            0.0
        };

        let error_rate_percent = if total_requests > 0 {
            (total_errors as f64 / total_requests as f64) * 100.0
        } else {
            0.0
        };

        // 最慢的端点
        let mut slowest_endpoints: Vec<(String, f64)> = self.endpoint_stats
            .iter()
            .map(|(endpoint, stats)| {
                let avg_time = stats.total_time_ms as f64 / stats.count as f64;
                (endpoint.clone(), avg_time)
            })
            .collect();
        slowest_endpoints.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap());
        slowest_endpoints.truncate(10);

        // 最常请求的端点
        let mut most_requested_endpoints: Vec<(String, u64)> = self.endpoint_stats
            .iter()
            .map(|(endpoint, stats)| (endpoint.clone(), stats.count))
            .collect();
        most_requested_endpoints.sort_by(|a, b| b.1.cmp(&a.1));
        most_requested_endpoints.truncate(10);

        // 状态码分布
        let mut status_code_distribution = HashMap::new();
        for request in &self.requests {
            *status_code_distribution.entry(request.status_code).or_insert(0) += 1;
        }

        AggregatedMetrics {
            total_requests,
            total_errors,
            average_response_time_ms,
            requests_per_minute,
            error_rate_percent,
            slowest_endpoints,
            most_requested_endpoints,
            status_code_distribution,
        }
    }

    pub fn get_recent_requests(&self, limit: usize) -> Vec<RequestMetrics> {
        self.requests
            .iter()
            .rev()
            .take(limit)
            .cloned()
            .collect()
    }

    pub fn clear_old_data(&mut self, keep_minutes: u64) {
        let cutoff_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs() - (keep_minutes * 60);

        self.requests.retain(|r| r.timestamp > cutoff_time);
    }
}

// 全局指标收集器
lazy_static::lazy_static! {
    static ref METRICS_COLLECTOR: Arc<Mutex<MetricsCollector>> = 
        Arc::new(Mutex::new(MetricsCollector::new()));
}

/// 指标收集中间件
pub async fn metrics_middleware(
    uri: Uri,
    method: Method,
    request: Request,
    next: Next,
) -> Response {
    let start_time = Instant::now();
    let path = uri.path().to_string();
    let method_str = method.to_string();

    // 执行请求
    let response = next.run(request).await;
    
    // 计算响应时间
    let response_time = start_time.elapsed();
    let response_time_ms = response_time.as_millis() as u64;
    let status_code = response.status().as_u16();

    // 记录指标
    let metrics = RequestMetrics {
        path: path.clone(),
        method: method_str.clone(),
        status_code,
        response_time_ms,
        timestamp: std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs(),
    };

    // 添加到收集器
    if let Ok(mut collector) = METRICS_COLLECTOR.lock() {
        collector.record_request(metrics);
    }

    // 记录日志
    let log_level = match status_code {
        200..=299 => "info",
        300..=399 => "info", 
        400..=499 => "warn",
        500..=599 => "error",
        _ => "info",
    };

    match log_level {
        "info" => info!(
            method = %method_str,
            path = %path,
            status = %status_code,
            duration_ms = %response_time_ms,
            "Request completed"
        ),
        "warn" => warn!(
            method = %method_str,
            path = %path,
            status = %status_code,
            duration_ms = %response_time_ms,
            "Request completed with client error"
        ),
        "error" => error!(
            method = %method_str,
            path = %path,
            status = %status_code,
            duration_ms = %response_time_ms,
            "Request completed with server error"
        ),
        _ => {}
    }

    // 检查慢请求
    if response_time_ms > 1000 {
        warn!(
            method = %method_str,
            path = %path,
            duration_ms = %response_time_ms,
            "Slow request detected"
        );
    }

    response
}

/// 获取聚合指标
pub fn get_aggregated_metrics() -> Option<AggregatedMetrics> {
    METRICS_COLLECTOR
        .lock()
        .ok()
        .map(|collector| collector.get_aggregated_metrics())
}

/// 获取最近的请求
pub fn get_recent_requests(limit: usize) -> Vec<RequestMetrics> {
    METRICS_COLLECTOR
        .lock()
        .map(|collector| collector.get_recent_requests(limit))
        .unwrap_or_default()
}

/// 清理旧数据
pub fn cleanup_old_metrics(keep_minutes: u64) {
    if let Ok(mut collector) = METRICS_COLLECTOR.lock() {
        collector.clear_old_data(keep_minutes);
    }
}

/// 重置所有指标
pub fn reset_metrics() {
    if let Ok(mut collector) = METRICS_COLLECTOR.lock() {
        *collector = MetricsCollector::new();
    }
}

// 定期清理任务
pub fn start_metrics_cleanup_task() {
    tokio::spawn(async {
        let mut interval = tokio::time::interval(Duration::from_secs(300)); // 每5分钟清理一次
        
        loop {
            interval.tick().await;
            cleanup_old_metrics(60); // 保留最近60分钟的数据
            info!("Metrics cleanup completed");
        }
    });
}
