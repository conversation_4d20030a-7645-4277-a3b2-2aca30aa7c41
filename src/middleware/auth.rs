use axum::{
    extract::{Request, State},
    http::{StatusCode, header::AUTHORIZATION},
    middleware::Next,
    response::Response,
};
use sqlx::PgPool;

use crate::utils::jwt;

#[derive(Clone)]
pub struct AuthUser {
    pub id: i32,
    pub username: String,
    pub roles: Vec<String>,
    pub skills: Vec<String>,
}

pub async fn auth_middleware(
    State(pool): State<PgPool>,
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let uri = request.uri().clone();
    let method = request.method().clone();
    tracing::debug!("Auth middleware processing request: {} {}", method, uri);

    let auth_header = request
        .headers()
        .get(AUTHORIZATION)
        .and_then(|header| header.to_str().ok());

    let token = match auth_header.and_then(jwt::extract_token_from_header) {
        Some(token) => {
            tracing::debug!("Authorization token found");
            token
        },
        None => {
            tracing::debug!("No authorization token found");
            return Err(StatusCode::UNAUTHORIZED);
        },
    };

    let claims = match jwt::verify_jwt(token) {
        Ok(claims) => {
            tracing::debug!("JWT verified successfully for user: {}", claims.sub);
            claims
        },
        Err(e) => {
            tracing::warn!("JWT verification failed: {:?}", e);
            return Err(StatusCode::UNAUTHORIZED);
        },
    };

    // Verify user still exists and is active
    let user_id: i32 = claims.sub.parse().map_err(|e| {
        tracing::error!("Failed to parse user_id: {:?}", e);
        StatusCode::UNAUTHORIZED
    })?;

    let user_exists = sqlx::query_scalar!(
        "SELECT EXISTS(SELECT 1 FROM users WHERE id = $1 AND is_active = true)",
        user_id
    )
    .fetch_one(&pool)
    .await
    .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?
    .unwrap_or(false);

    if !user_exists {
        return Err(StatusCode::UNAUTHORIZED);
    }

    let auth_user = AuthUser {
        id: user_id,
        username: claims.username,
        roles: claims.roles,
        skills: claims.skills,
    };

    request.extensions_mut().insert(auth_user);
    Ok(next.run(request).await)
}

#[allow(dead_code)]
pub fn require_role(required_role: &str) -> impl Fn(AuthUser) -> Result<(), StatusCode> + '_ {
    move |auth_user: AuthUser| {
        if auth_user.roles.contains(&required_role.to_string()) {
            Ok(())
        } else {
            Err(StatusCode::FORBIDDEN)
        }
    }
}

/// 检查用户是否有指定权限的中间件函数
#[allow(dead_code)]
pub async fn require_permission(
    pool: &sqlx::PgPool,
    user_id: i32,
    permission_code: &str,
) -> Result<(), StatusCode> {
    use crate::services::permission_service::PermissionService;

    let permission_service = PermissionService::new(pool.clone());

    match permission_service.has_permission(user_id, permission_code).await {
        Ok(true) => Ok(()),
        Ok(false) => Err(StatusCode::FORBIDDEN),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

/// 检查用户是否有任一权限的中间件函数
#[allow(dead_code)]
pub async fn require_any_permission(
    pool: &sqlx::PgPool,
    user_id: i32,
    permission_codes: &[&str],
) -> Result<(), StatusCode> {
    use crate::services::permission_service::PermissionService;

    let permission_service = PermissionService::new(pool.clone());

    match permission_service.has_any_permission(user_id, permission_codes).await {
        Ok(true) => Ok(()),
        Ok(false) => Err(StatusCode::FORBIDDEN),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

#[allow(dead_code)]
pub fn require_skill(required_skill: &str) -> impl Fn(AuthUser) -> Result<(), StatusCode> + '_ {
    move |auth_user: AuthUser| {
        if auth_user.skills.contains(&required_skill.to_string()) {
            Ok(())
        } else {
            Err(StatusCode::FORBIDDEN)
        }
    }
}
