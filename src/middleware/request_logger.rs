use axum::{
    extract::Request,
    http::StatusCode,
    middleware::Next,
    response::Response,
};

pub async fn request_logger_middleware(
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let method = request.method().clone();
    let uri = request.uri().clone();
    let _headers = request.headers().clone();
    
    tracing::debug!("Request: {} {}", method, uri);

    let response = next.run(request).await;
    let status = response.status();

    tracing::debug!("Response: {} {}", method, status);
    
    Ok(response)
}
