use axum::{
    extract::{State, Query},
    response::Json,
    Extension,
};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::time::{SystemTime, UNIX_EPOCH};
use sqlx::PgPool;

use crate::{
    services::cache_service::CacheService,
    utils::error::AppError,
    middleware::{
        auth::AuthUser,
        metrics_middleware::{get_aggregated_metrics, get_recent_requests, AggregatedMetrics, RequestMetrics},
    },
};

// 健康检查响应
#[derive(Serialize)]
pub struct HealthResponse {
    pub status: String,
    pub timestamp: u64,
    pub version: String,
    pub uptime_seconds: u64,
    pub checks: HealthChecks,
}

// 各项健康检查
#[derive(Serialize)]
pub struct HealthChecks {
    pub database: HealthCheck,
    pub cache: HealthCheck,
    pub memory: HealthCheck,
    pub disk: HealthCheck,
}

// 单项健康检查
#[derive(Serialize)]
pub struct HealthCheck {
    pub status: String,
    pub message: String,
    pub response_time_ms: u64,
    pub details: Option<Value>,
}

// 系统指标
#[derive(Serialize)]
pub struct SystemMetrics {
    pub cpu_usage_percent: f64,
    pub memory_usage_mb: u64,
    pub memory_total_mb: u64,
    pub memory_usage_percent: f64,
    pub disk_usage_gb: f64,
    pub disk_total_gb: f64,
    pub disk_usage_percent: f64,
    pub active_connections: u32,
    pub request_count_last_minute: u64,
    pub average_response_time_ms: f64,
}

// 应用启动时间（全局变量）
static mut APP_START_TIME: Option<SystemTime> = None;

pub fn init_health_monitor() {
    unsafe {
        APP_START_TIME = Some(SystemTime::now());
    }
}

/// 基本健康检查端点
pub async fn health_check() -> Json<Value> {
    Json(json!({
        "status": "healthy",
        "timestamp": SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs(),
        "service": "MES System",
        "version": env!("CARGO_PKG_VERSION")
    }))
}

/// 详细健康检查端点
pub async fn detailed_health_check(
    State(pool): State<PgPool>,
) -> Result<Json<HealthResponse>, AppError> {
    let start_time = SystemTime::now();
    
    // 计算运行时间
    let uptime_seconds = unsafe {
        APP_START_TIME
            .map(|start| start_time.duration_since(start).unwrap().as_secs())
            .unwrap_or(0)
    };

    // 数据库健康检查
    let database_check = check_database_health(&pool).await;

    // 缓存健康检查（简化版）
    let cache_check = HealthCheck {
        status: "healthy".to_string(),
        message: "缓存服务未配置".to_string(),
        response_time_ms: 0,
        details: None,
    };
    
    // 内存健康检查
    let memory_check = check_memory_health().await;
    
    // 磁盘健康检查
    let disk_check = check_disk_health().await;

    // 确定整体状态
    let overall_status = if database_check.status == "healthy" 
        && cache_check.status == "healthy"
        && memory_check.status == "healthy"
        && disk_check.status == "healthy" {
        "healthy"
    } else if database_check.status == "critical" || cache_check.status == "critical" {
        "critical"
    } else {
        "degraded"
    };

    let response = HealthResponse {
        status: overall_status.to_string(),
        timestamp: start_time.duration_since(UNIX_EPOCH).unwrap().as_secs(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        uptime_seconds,
        checks: HealthChecks {
            database: database_check,
            cache: cache_check,
            memory: memory_check,
            disk: disk_check,
        },
    };

    Ok(Json(response))
}

/// 系统指标端点
pub async fn system_metrics(
    State(pool): State<PgPool>,
) -> Result<Json<SystemMetrics>, AppError> {
    let memory_info = get_memory_info();
    let disk_info = get_disk_info();
    
    // 获取数据库连接数
    let active_connections = get_active_connections(&pool).await.unwrap_or(0);

    let metrics = SystemMetrics {
        cpu_usage_percent: get_cpu_usage(),
        memory_usage_mb: memory_info.0,
        memory_total_mb: memory_info.1,
        memory_usage_percent: if memory_info.1 > 0 {
            (memory_info.0 as f64 / memory_info.1 as f64) * 100.0
        } else {
            0.0
        },
        disk_usage_gb: disk_info.0,
        disk_total_gb: disk_info.1,
        disk_usage_percent: if disk_info.1 > 0.0 {
            (disk_info.0 / disk_info.1) * 100.0
        } else {
            0.0
        },
        active_connections,
        request_count_last_minute: 0, // 需要实际的请求计数器
        average_response_time_ms: 0.0, // 需要实际的响应时间统计
    };

    Ok(Json(metrics))
}

// 数据库健康检查
async fn check_database_health(pool: &PgPool) -> HealthCheck {
    let start = SystemTime::now();
    
    match sqlx::query("SELECT 1").fetch_one(pool).await {
        Ok(_) => {
            let response_time = start.elapsed().unwrap().as_millis() as u64;
            HealthCheck {
                status: "healthy".to_string(),
                message: "数据库连接正常".to_string(),
                response_time_ms: response_time,
                details: Some(json!({
                    "connection_pool_size": pool.size(),
                    "idle_connections": pool.num_idle(),
                })),
            }
        }
        Err(e) => {
            let response_time = start.elapsed().unwrap().as_millis() as u64;
            HealthCheck {
                status: "critical".to_string(),
                message: format!("数据库连接失败: {}", e),
                response_time_ms: response_time,
                details: None,
            }
        }
    }
}

// 缓存健康检查
async fn check_cache_health(cache_service: &CacheService) -> HealthCheck {
    let start = SystemTime::now();
    
    match cache_service.get_connection().await {
        Ok(mut conn) => {
            match redis::cmd("PING").query_async::<_, String>(&mut conn).await {
                Ok(pong) if pong == "PONG" => {
                    let response_time = start.elapsed().unwrap().as_millis() as u64;
                    HealthCheck {
                        status: "healthy".to_string(),
                        message: "缓存服务正常".to_string(),
                        response_time_ms: response_time,
                        details: Some(json!({
                            "redis_version": "unknown",
                            "connected_clients": "unknown"
                        })),
                    }
                }
                _ => {
                    let response_time = start.elapsed().unwrap().as_millis() as u64;
                    HealthCheck {
                        status: "degraded".to_string(),
                        message: "缓存服务响应异常".to_string(),
                        response_time_ms: response_time,
                        details: None,
                    }
                }
            }
        }
        Err(e) => {
            let response_time = start.elapsed().unwrap().as_millis() as u64;
            HealthCheck {
                status: "critical".to_string(),
                message: format!("缓存服务连接失败: {}", e),
                response_time_ms: response_time,
                details: None,
            }
        }
    }
}

// 内存健康检查
async fn check_memory_health() -> HealthCheck {
    let start = SystemTime::now();
    let (used_mb, total_mb) = get_memory_info();
    let usage_percent = if total_mb > 0 {
        (used_mb as f64 / total_mb as f64) * 100.0
    } else {
        0.0
    };

    let (status, message) = if usage_percent > 90.0 {
        ("critical", "内存使用率过高")
    } else if usage_percent > 80.0 {
        ("degraded", "内存使用率较高")
    } else {
        ("healthy", "内存使用正常")
    };

    let response_time = start.elapsed().unwrap().as_millis() as u64;
    
    HealthCheck {
        status: status.to_string(),
        message: message.to_string(),
        response_time_ms: response_time,
        details: Some(json!({
            "used_mb": used_mb,
            "total_mb": total_mb,
            "usage_percent": usage_percent
        })),
    }
}

// 磁盘健康检查
async fn check_disk_health() -> HealthCheck {
    let start = SystemTime::now();
    let (used_gb, total_gb) = get_disk_info();
    let usage_percent = if total_gb > 0.0 {
        (used_gb / total_gb) * 100.0
    } else {
        0.0
    };

    let (status, message) = if usage_percent > 95.0 {
        ("critical", "磁盘空间不足")
    } else if usage_percent > 85.0 {
        ("degraded", "磁盘空间较少")
    } else {
        ("healthy", "磁盘空间充足")
    };

    let response_time = start.elapsed().unwrap().as_millis() as u64;
    
    HealthCheck {
        status: status.to_string(),
        message: message.to_string(),
        response_time_ms: response_time,
        details: Some(json!({
            "used_gb": used_gb,
            "total_gb": total_gb,
            "usage_percent": usage_percent
        })),
    }
}

// 获取内存信息 (used_mb, total_mb)
fn get_memory_info() -> (u64, u64) {
    // 简化实现，实际应该读取 /proc/meminfo 或使用系统API
    (1024, 4096) // 示例：1GB使用，4GB总计
}

// 获取磁盘信息 (used_gb, total_gb)
fn get_disk_info() -> (f64, f64) {
    // 简化实现，实际应该使用 statvfs 或类似API
    (50.0, 100.0) // 示例：50GB使用，100GB总计
}

// 获取CPU使用率
fn get_cpu_usage() -> f64 {
    // 简化实现，实际应该读取 /proc/stat
    15.5 // 示例：15.5%
}

// 获取活跃数据库连接数
async fn get_active_connections(pool: &PgPool) -> Result<u32, sqlx::Error> {
    let row: (i64,) = sqlx::query_as(
        "SELECT count(*) FROM pg_stat_activity WHERE state = 'active'"
    )
    .fetch_one(pool)
    .await?;

    Ok(row.0 as u32)
}

// 查询参数
#[derive(serde::Deserialize)]
pub struct MetricsQuery {
    pub limit: Option<usize>,
}

/// 获取应用指标
pub async fn get_application_metrics(
    Extension(auth_user): Extension<AuthUser>,
) -> Result<Json<AggregatedMetrics>, AppError> {
    // 检查权限 - 只有管理员可以查看指标
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err(AppError::forbidden(Some("只有管理员可以查看应用指标".to_string())));
    }

    match get_aggregated_metrics() {
        Some(metrics) => Ok(Json(metrics)),
        None => Err(AppError::internal_server_error("无法获取应用指标".to_string())),
    }
}

/// 获取最近的请求记录
pub async fn get_recent_request_logs(
    Extension(auth_user): Extension<AuthUser>,
    Query(params): Query<MetricsQuery>,
) -> Result<Json<Vec<RequestMetrics>>, AppError> {
    // 检查权限 - 只有管理员可以查看请求日志
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err(AppError::forbidden(Some("只有管理员可以查看请求日志".to_string())));
    }

    let limit = params.limit.unwrap_or(100).min(1000); // 最多返回1000条
    let requests = get_recent_requests(limit);

    Ok(Json(requests))
}

/// 性能监控仪表板数据
pub async fn get_performance_dashboard(
    Extension(auth_user): Extension<AuthUser>,
    State(pool): State<PgPool>,
) -> Result<Json<Value>, AppError> {
    // 检查权限 - 只有管理员可以查看性能仪表板
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err(AppError::forbidden(Some("只有管理员可以查看性能仪表板".to_string())));
    }

    // 获取应用指标
    let app_metrics = get_aggregated_metrics();

    // 获取系统指标
    let system_metrics = system_metrics(State(pool.clone())).await?.0;

    // 获取健康检查
    let health_check = detailed_health_check(State(pool)).await?.0;

    let dashboard_data = json!({
        "application_metrics": app_metrics,
        "system_metrics": system_metrics,
        "health_status": health_check,
        "timestamp": SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs()
    });

    Ok(Json(dashboard_data))
}
