use axum::{
    extract::{Extension, Path, State},
    http::StatusC<PERSON>,
    response::<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use sqlx::PgPool;

use crate::{
    middleware::auth::AuthUser,
    services::user_service::UserService,
    models::user::{UpdateProfileRequest, ChangePasswordRequest},
    utils::validation::ErrorResponse,
};

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateUserStatusRequest {
    pub is_active: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateUserRolesRequest {
    pub role_ids: Vec<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateUserSkillsRequest {
    pub skill_group_ids: Vec<i32>,
}

pub async fn get_all_users(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
) -> Result<Json<serde_json::Value>, (StatusC<PERSON>, Json<ErrorResponse>)> {
    // Check if user has admin or process_engineer role
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators and process engineers can view users".to_string(),
            }),
        ));
    }

    let user_service = UserService::new(pool);

    match user_service.get_all_users().await {
        Ok(users) => Ok(Json(serde_json::json!({ "users": users }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_user_by_id(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(user_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Check if user has admin role or is requesting their own info
    if !auth_user.roles.contains(&"admin".to_string()) && auth_user.id != user_id {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "You can only view your own user information".to_string(),
            }),
        ));
    }

    let user_service = UserService::new(pool);

    match user_service.get_user_by_id(user_id).await {
        Ok(Some(user)) => Ok(Json(serde_json::json!({ "user": user }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "user_not_found".to_string(),
                message: "User not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn update_user_status(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(user_id): Path<i32>,
    Json(request): Json<UpdateUserStatusRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins can update user status
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can update user status".to_string(),
            }),
        ));
    }

    // Prevent admin from deactivating themselves
    if auth_user.id == user_id && !request.is_active {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "invalid_operation".to_string(),
                message: "You cannot deactivate your own account".to_string(),
            }),
        ));
    }

    let user_service = UserService::new(pool);

    match user_service
        .update_user_status(user_id, request.is_active)
        .await
    {
        Ok(()) => Ok(Json(serde_json::json!({
            "message": "User status updated successfully"
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "update_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn delete_user(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(user_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins can delete users
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can delete users".to_string(),
            }),
        ));
    }

    // Prevent admin from deleting themselves
    if auth_user.id == user_id {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "invalid_operation".to_string(),
                message: "You cannot delete your own account".to_string(),
            }),
        ));
    }

    let user_service = UserService::new(pool);

    match user_service.delete_user(user_id).await {
        Ok(()) => Ok(Json(serde_json::json!({
            "message": "User deleted successfully"
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "delete_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn update_user_roles(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(user_id): Path<i32>,
    Json(request): Json<UpdateUserRolesRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins can update user roles
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can update user roles".to_string(),
            }),
        ));
    }

    let user_service = UserService::new(pool);

    match user_service
        .update_user_roles(user_id, request.role_ids)
        .await
    {
        Ok(()) => Ok(Json(serde_json::json!({
            "message": "User roles updated successfully"
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "update_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn update_user_skills(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(user_id): Path<i32>,
    Json(request): Json<UpdateUserSkillsRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Admins and process engineers can update user skills
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators and process engineers can update user skills"
                    .to_string(),
            }),
        ));
    }

    let user_service = UserService::new(pool);

    match user_service
        .update_user_skills(user_id, request.skill_group_ids)
        .await
    {
        Ok(()) => Ok(Json(serde_json::json!({
            "message": "User skills updated successfully"
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "update_failed".to_string(),
                message: error,
            }),
        )),
    }
}

// 调试端点：检查用户技能绑定状态 - 生产环境中已注释
/*
pub async fn debug_user_skills(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // 只有管理员可以访问调试信息
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can access debug information".to_string(),
            }),
        ));
    }

    // 获取所有用户及其技能组
    let user_skills = sqlx::query!(
        "SELECT
            u.id as user_id,
            u.username,
            u.full_name,
            sg.id as skill_group_id,
            sg.group_name as skill_group_name
         FROM users u
         LEFT JOIN user_skills us ON u.id = us.user_id
         LEFT JOIN skill_groups sg ON us.skill_group_id = sg.id
         ORDER BY u.id, sg.id"
    )
    .fetch_all(&pool)
    .await
    .map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: format!("Failed to fetch user skills: {}", e),
            }),
        )
    })?;

    // 获取没有技能组的用户
    let users_without_skills = sqlx::query!(
        "SELECT
            u.id,
            u.username,
            u.full_name,
            u.is_active
         FROM users u
         LEFT JOIN user_skills us ON u.id = us.user_id
         WHERE us.user_id IS NULL
         ORDER BY u.id"
    )
    .fetch_all(&pool)
    .await
    .map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: format!("Failed to fetch users without skills: {}", e),
            }),
        )
    })?;

    // 获取统计信息
    let total_users = sqlx::query_scalar!("SELECT COUNT(*) FROM users")
        .fetch_one(&pool)
        .await
        .unwrap_or(Some(0))
        .unwrap_or(0);

    let users_with_skills = sqlx::query_scalar!(
        "SELECT COUNT(DISTINCT us.user_id) FROM user_skills us"
    )
    .fetch_one(&pool)
    .await
    .unwrap_or(Some(0))
    .unwrap_or(0);

    Ok(Json(serde_json::json!({
        "user_skills": user_skills.into_iter().map(|row| {
            serde_json::json!({
                "user_id": row.user_id,
                "username": row.username,
                "full_name": row.full_name,
                "skill_group_id": row.skill_group_id,
                "skill_group_name": row.skill_group_name
            })
        }).collect::<Vec<_>>(),
        "users_without_skills": users_without_skills.into_iter().map(|row| {
            serde_json::json!({
                "id": row.id,
                "username": row.username,
                "full_name": row.full_name,
                "is_active": row.is_active
            })
        }).collect::<Vec<_>>(),
        "statistics": {
            "total_users": total_users,
            "users_with_skills": users_with_skills,
            "users_without_skills": total_users - users_with_skills
        }
    })))
}
*/

// 更新用户个人资料
pub async fn update_profile(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<UpdateProfileRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let user_service = UserService::new(pool);

    match user_service.update_profile(auth_user.id, request).await {
        Ok(user) => Ok(Json(serde_json::json!({
            "message": "个人资料更新成功",
            "user": {
                "id": user.id,
                "username": user.username,
                "full_name": user.full_name,
                "is_active": user.is_active,
                "created_at": user.created_at
            }
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "profile_update_failed".to_string(),
                message: error,
            }),
        )),
    }
}

// 修改密码
pub async fn change_password(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<ChangePasswordRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let user_service = UserService::new(pool);

    match user_service.change_password(auth_user.id, request).await {
        Ok(()) => Ok(Json(serde_json::json!({
            "message": "密码修改成功"
        }))),
        Err(error) => {
            let status_code = if error.contains("当前密码不正确") {
                StatusCode::BAD_REQUEST
            } else {
                StatusCode::INTERNAL_SERVER_ERROR
            };

            Err((
                status_code,
                Json(ErrorResponse {
                    error: "password_change_failed".to_string(),
                    message: error,
                }),
            ))
        }
    }
}
