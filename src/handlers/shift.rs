use axum::{
    extract::{Extension, Path, Query, State},
    http::StatusCode,
    response::J<PERSON>,
};
use sqlx::PgPool;
use serde_json::Value;

use crate::{
    middleware::auth::AuthUser,
    models::shift::{
        CreateShiftTemplateRequest, UpdateShiftTemplateRequest, CreatePlanGroupRequest,
        UpdatePlanGroupRequest, CreatePlanGroupShiftConfigRequest, CreateShiftInstanceRequest,
        ShiftQuery,
    },
    services::shift_service::ShiftService,
    utils::validation::ErrorResponse,
};

// ==================== 班次模板管理 ====================

pub async fn create_shift_template(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<CreateShiftTemplateRequest>,
) -> Result<Json<Value>, (StatusCode, <PERSON>son<ErrorResponse>)> {
    // 检查权限 - 只有管理员和计划员可以创建班次模板
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "只有管理员和计划员可以创建班次模板".to_string(),
            }),
        ));
    }

    let shift_service = ShiftService::new(pool);

    match shift_service.create_shift_template(request).await {
        Ok(template) => Ok(Json(serde_json::json!({
            "message": "班次模板创建成功",
            "template": template
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "creation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_shift_templates(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Query(query): Query<std::collections::HashMap<String, String>>,
) -> Result<Json<Value>, (StatusCode, Json<ErrorResponse>)> {
    let active_only = query
        .get("active_only")
        .and_then(|v| v.parse::<bool>().ok())
        .unwrap_or(true);

    let shift_service = ShiftService::new(pool);

    match shift_service.get_shift_templates(active_only).await {
        Ok(templates) => Ok(Json(serde_json::json!({
            "templates": templates,
            "total": templates.len()
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "query_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_shift_template_by_id(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Path(id): Path<i32>,
) -> Result<Json<Value>, (StatusCode, Json<ErrorResponse>)> {
    let shift_service = ShiftService::new(pool);

    match shift_service.get_shift_template_by_id(id).await {
        Ok(Some(template)) => Ok(Json(serde_json::json!({
            "template": template
        }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "not_found".to_string(),
                message: "班次模板不存在".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "query_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn update_shift_template(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(id): Path<i32>,
    Json(request): Json<UpdateShiftTemplateRequest>,
) -> Result<Json<Value>, (StatusCode, Json<ErrorResponse>)> {
    // 检查权限
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "只有管理员和计划员可以修改班次模板".to_string(),
            }),
        ));
    }

    let shift_service = ShiftService::new(pool);

    match shift_service.update_shift_template(id, request).await {
        Ok(template) => Ok(Json(serde_json::json!({
            "message": "班次模板更新成功",
            "template": template
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "update_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn delete_shift_template(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(id): Path<i32>,
) -> Result<Json<Value>, (StatusCode, Json<ErrorResponse>)> {
    // 检查权限
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "只有管理员可以删除班次模板".to_string(),
            }),
        ));
    }

    // 检查是否为系统模板
    let shift_service = ShiftService::new(pool.clone());
    match shift_service.get_shift_template_by_id(id).await {
        Ok(Some(template)) => {
            if template.is_system_template {
                return Err((
                    StatusCode::FORBIDDEN,
                    Json(ErrorResponse {
                        error: "system_template".to_string(),
                        message: "系统预设模板不能删除".to_string(),
                    }),
                ));
            }
        }
        Ok(None) => {
            return Err((
                StatusCode::NOT_FOUND,
                Json(ErrorResponse {
                    error: "not_found".to_string(),
                    message: "班次模板不存在".to_string(),
                }),
            ));
        }
        Err(error) => {
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "query_failed".to_string(),
                    message: error,
                }),
            ));
        }
    }

    // 软删除（设置为非活跃状态）
    let result = sqlx::query!(
        "UPDATE shift_templates SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1",
        id
    )
    .execute(&pool)
    .await;

    match result {
        Ok(_) => Ok(Json(serde_json::json!({
            "message": "班次模板删除成功"
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "delete_failed".to_string(),
                message: format!("删除失败: {}", error),
            }),
        )),
    }
}

// ==================== 计划组管理 ====================

pub async fn create_plan_group(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<CreatePlanGroupRequest>,
) -> Result<Json<Value>, (StatusCode, Json<ErrorResponse>)> {
    // 检查权限
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "只有管理员和计划员可以创建计划组".to_string(),
            }),
        ));
    }

    let shift_service = ShiftService::new(pool);

    match shift_service.create_plan_group(request, auth_user.id).await {
        Ok(group) => Ok(Json(serde_json::json!({
            "message": "计划组创建成功",
            "group": group
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "creation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_plan_groups(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Query(query): Query<std::collections::HashMap<String, String>>,
) -> Result<Json<Value>, (StatusCode, Json<ErrorResponse>)> {
    let active_only = query
        .get("active_only")
        .and_then(|v| v.parse::<bool>().ok())
        .unwrap_or(true);

    let shift_service = ShiftService::new(pool);

    match shift_service.get_plan_groups(active_only).await {
        Ok(groups) => Ok(Json(serde_json::json!({
            "groups": groups,
            "total": groups.len()
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "query_failed".to_string(),
                message: error,
            }),
        )),
    }
}

// ==================== 班次实例管理 ====================

pub async fn create_shift_instance(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<CreateShiftInstanceRequest>,
) -> Result<Json<Value>, (StatusCode, Json<ErrorResponse>)> {
    // 检查权限
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "只有管理员和计划员可以创建班次实例".to_string(),
            }),
        ));
    }

    let shift_service = ShiftService::new(pool);

    match shift_service.create_shift_instance(request, auth_user.id).await {
        Ok(instance) => Ok(Json(serde_json::json!({
            "message": "班次实例创建成功",
            "instance": instance
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "creation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

// ==================== 班次冲突检测 ====================

pub async fn check_shift_conflicts(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Json(payload): Json<Value>,
) -> Result<Json<Value>, (StatusCode, Json<ErrorResponse>)> {
    let plan_group_id = payload["plan_group_id"]
        .as_i64()
        .ok_or_else(|| {
            (
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse {
                    error: "missing_field".to_string(),
                    message: "缺少计划组ID".to_string(),
                }),
            )
        })? as i32;

    let start_time = payload["start_time"]
        .as_str()
        .and_then(|s| chrono::DateTime::parse_from_rfc3339(s).ok())
        .map(|dt| dt.with_timezone(&chrono::Utc))
        .ok_or_else(|| {
            (
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse {
                    error: "invalid_time".to_string(),
                    message: "无效的开始时间格式".to_string(),
                }),
            )
        })?;

    let end_time = payload["end_time"]
        .as_str()
        .and_then(|s| chrono::DateTime::parse_from_rfc3339(s).ok())
        .map(|dt| dt.with_timezone(&chrono::Utc))
        .ok_or_else(|| {
            (
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse {
                    error: "invalid_time".to_string(),
                    message: "无效的结束时间格式".to_string(),
                }),
            )
        })?;

    let exclude_shift_id = payload["exclude_shift_id"]
        .as_i64()
        .map(|id| id as i32);

    let shift_service = ShiftService::new(pool);

    match shift_service.check_shift_conflicts(plan_group_id, start_time, end_time, exclude_shift_id).await {
        Ok(result) => Ok(Json(serde_json::json!({
            "conflict_result": result
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "check_failed".to_string(),
                message: error,
            }),
        )),
    }
}
