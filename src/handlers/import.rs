use axum::{
    extract::{Extension, Path, Query, State, Multipart},
    http::StatusCode,
    response::Json,
};
use sqlx::PgPool;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

use crate::{
    middleware::auth::AuthUser,
    models::import::*,
    services::import_service::ImportService,
    utils::validation::ErrorResponse,
};

#[derive(Debug, Serialize, Deserialize)]
pub struct UploadResponse {
    pub import_job_id: i32,
    pub message: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TemplateDownloadQuery {
    pub module_type: String,
}

/// 上传导入文件
pub async fn upload_import_file(
    State(pool): State<PgPool>,
    Extension(user): Extension<AuthUser>,
    mut multipart: Multipart,
) -> Result<Json<UploadResponse>, (StatusCode, Json<ErrorResponse>)> {
    let import_service = ImportService::new(pool);
    
    let mut file_content: Option<Vec<u8>> = None;
    let mut file_name: Option<String> = None;
    let mut module_type: Option<String> = None;

    // 解析multipart数据
    while let Some(field) = multipart.next_field().await.map_err(|e| {
        (
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: format!("解析上传数据失败: {}", e),
                message: "文件上传失败".to_string(),
            }),
        )
    })? {
        let field_name = field.name().unwrap_or("").to_string();
        
        match field_name.as_str() {
            "file" => {
                file_name = field.file_name().map(|s| s.to_string());
                file_content = Some(field.bytes().await.map_err(|e| {
                    (
                        StatusCode::BAD_REQUEST,
                        Json(ErrorResponse {
                            error: format!("读取文件内容失败: {}", e),
                            message: "文件读取失败".to_string(),
                        }),
                    )
                })?.to_vec());
            }
            "module_type" => {
                module_type = Some(field.text().await.map_err(|e| {
                    (
                        StatusCode::BAD_REQUEST,
                        Json(ErrorResponse {
                            error: format!("读取模块类型失败: {}", e),
                            message: "参数解析失败".to_string(),
                        }),
                    )
                })?);
            }
            _ => {}
        }
    }

    let file_content = file_content.ok_or_else(|| {
        (
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "缺少文件".to_string(),
                message: "请选择要上传的文件".to_string(),
            }),
        )
    })?;

    let file_name = file_name.ok_or_else(|| {
        (
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "缺少文件名".to_string(),
                message: "文件名无效".to_string(),
            }),
        )
    })?;

    let module_type = module_type.ok_or_else(|| {
        (
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "缺少模块类型".to_string(),
                message: "请指定导入的数据类型".to_string(),
            }),
        )
    })?;

    // 验证文件大小（10MB限制）
    if file_content.len() > 10 * 1024 * 1024 {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "文件大小超过10MB限制".to_string(),
                message: "文件过大".to_string(),
            }),
        ));
    }

    // 验证文件格式
    let file_extension = std::path::Path::new(&file_name)
        .extension()
        .and_then(|ext| ext.to_str())
        .unwrap_or("");
    
    if !["csv", "xlsx"].contains(&file_extension) {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "只支持CSV和Excel文件格式".to_string(),
                message: "文件格式不支持".to_string(),
            }),
        ));
    }

    let request = CreateImportJobRequest {
        module_type,
        file_name,
    };

    match import_service.create_import_job(user.id, request, file_content).await {
        Ok(import_job) => Ok(Json(UploadResponse {
            import_job_id: import_job.id,
            message: "文件上传成功".to_string(),
        })),
        Err(e) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: e.clone(),
                message: "文件上传失败".to_string(),
            }),
        )),
    }
}

/// 预览导入数据
pub async fn preview_import_data(
    State(pool): State<PgPool>,
    Extension(user): Extension<AuthUser>,
    Path(import_job_id): Path<i32>,
) -> Result<Json<ImportPreviewResponse>, (StatusCode, Json<ErrorResponse>)> {
    let import_service = ImportService::new(pool);

    match import_service.preview_import_data(import_job_id, user.id).await {
        Ok(preview) => Ok(Json(preview)),
        Err(e) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: e.clone(),
                message: "数据预览失败".to_string(),
            }),
        )),
    }
}

/// 执行导入
pub async fn execute_import(
    State(pool): State<PgPool>,
    Extension(user): Extension<AuthUser>,
    Path(import_job_id): Path<i32>,
    Json(request): Json<ImportExecuteRequest>,
) -> Result<Json<ImportResult>, (StatusCode, Json<ErrorResponse>)> {
    let import_service = ImportService::new(pool);

    match import_service.execute_import(import_job_id, user.id, request.options).await {
        Ok(result) => Ok(Json(result)),
        Err(e) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: e.clone(),
                message: "导入执行失败".to_string(),
            }),
        )),
    }
}

/// 查询导入状态
pub async fn get_import_status(
    State(pool): State<PgPool>,
    Extension(user): Extension<AuthUser>,
    Path(import_job_id): Path<i32>,
) -> Result<Json<ImportStatusResponse>, (StatusCode, Json<ErrorResponse>)> {
    let import_service = ImportService::new(pool);

    match import_service.get_import_status(import_job_id, user.id).await {
        Ok(status) => Ok(Json(status)),
        Err(e) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: e.clone(),
                message: "状态查询失败".to_string(),
            }),
        )),
    }
}

/// 下载导入模板
pub async fn download_template(
    Query(query): Query<TemplateDownloadQuery>,
) -> Result<(StatusCode, Vec<u8>), (StatusCode, Json<ErrorResponse>)> {
    // 验证模块类型
    if !get_valid_module_types().contains(&query.module_type.as_str()) {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "不支持的模块类型".to_string(),
                message: "模板下载失败".to_string(),
            }),
        ));
    }

    // 生成CSV模板内容
    let template_content = generate_template_csv(&query.module_type)?;
    
    Ok((StatusCode::OK, template_content.into_bytes()))
}

/// 生成CSV模板内容
fn generate_template_csv(module_type: &str) -> Result<String, (StatusCode, Json<ErrorResponse>)> {
    let mapping = get_module_field_mapping(module_type).ok_or_else(|| {
        (
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "不支持的模块类型".to_string(),
                message: "模板生成失败".to_string(),
            }),
        )
    })?;

    let mut csv_content = String::new();
    
    // 添加标题行
    let headers: Vec<String> = mapping.fields.iter()
        .map(|field| field.csv_header.clone())
        .collect();
    csv_content.push_str(&headers.join(","));
    csv_content.push('\n');

    // 添加示例数据行
    match module_type {
        MODULE_PROJECTS => {
            csv_content.push_str("示例项目,示例客户\n");
        }
        MODULE_PARTS => {
            csv_content.push_str("P001,示例零件,1.0,示例规格说明\n");
        }
        MODULE_SKILL_GROUPS => {
            csv_content.push_str("示例技能组\n");
        }
        MODULE_MACHINES => {
            csv_content.push_str("CNC-001,数控加工,available\n");
        }
        MODULE_BOM => {
            csv_content.push_str("示例项目,P001,1.0,2\n");
        }
        _ => {
            // 为其他模块添加空行作为示例
            let empty_row = vec![""; headers.len()].join(",");
            csv_content.push_str(&empty_row);
            csv_content.push('\n');
        }
    }

    Ok(csv_content)
}

/// 获取用户的导入历史
pub async fn get_import_history(
    State(pool): State<PgPool>,
    Extension(user): Extension<AuthUser>,
    Query(params): Query<HashMap<String, String>>,
) -> Result<Json<Vec<ImportJob>>, (StatusCode, Json<ErrorResponse>)> {
    let limit = params.get("limit")
        .and_then(|s| s.parse::<i64>().ok())
        .unwrap_or(20);
    
    let offset = params.get("offset")
        .and_then(|s| s.parse::<i64>().ok())
        .unwrap_or(0);

    let module_type = params.get("module_type");

    let mut query_builder = sqlx::QueryBuilder::new(
        r#"
        SELECT id, user_id, module_type, file_name, file_path, status,
               total_rows, processed_rows, success_rows, error_rows,
               error_details, created_at, started_at, completed_at
        FROM import_jobs 
        WHERE user_id = 
        "#
    );
    
    query_builder.push_bind(user.id);

    if let Some(module_type) = module_type {
        query_builder.push(" AND module_type = ");
        query_builder.push_bind(module_type);
    }

    query_builder.push(" ORDER BY created_at DESC LIMIT ");
    query_builder.push_bind(limit);
    query_builder.push(" OFFSET ");
    query_builder.push_bind(offset);

    let import_jobs = query_builder
        .build_query_as::<ImportJob>()
        .fetch_all(&pool)
        .await
        .map_err(|e| {
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: format!("查询导入历史失败: {}", e),
                    message: "历史记录查询失败".to_string(),
                }),
            )
        })?;

    Ok(Json(import_jobs))
}
