use axum::{
    extract::{Query, State},
    http::StatusCode,
    response::Json,
    Extension,
};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::collections::HashMap;

use crate::{
    middleware::auth::AuthUser,
    services::cache_service::{CacheService, ExtendedCacheStats},
    utils::error::AppError,
};

// 缓存统计响应
#[derive(Serialize)]
pub struct CacheStatsResponse {
    pub stats: ExtendedCacheStats,
    pub hot_keys: Vec<(String, u32)>,
    pub cache_size_estimate: u64,
    pub uptime_seconds: u64,
}

// 缓存操作请求
#[derive(Deserialize)]
pub struct CacheOperationRequest {
    pub operation: String, // "clear", "invalidate", "refresh"
    pub pattern: Option<String>,
    pub keys: Option<Vec<String>>,
}

// 缓存查询参数
#[derive(Deserialize)]
pub struct CacheQueryParams {
    pub pattern: Option<String>,
    pub limit: Option<usize>,
}

/// 获取缓存统计信息
pub async fn get_cache_stats(
    State(cache_service): State<CacheService>,
    Extension(auth_user): Extension<AuthUser>,
) -> Result<Json<CacheStatsResponse>, AppError> {
    // 检查权限 - 只有管理员可以查看缓存统计
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err(AppError::forbidden(Some("只有管理员可以查看缓存统计".to_string())));
    }

    // 获取基本统计信息
    let mut conn = cache_service.get_connection().await
        .map_err(|e| AppError::internal_server_error(format!("连接Redis失败: {}", e)))?;

    // 获取Redis信息
    let info: String = redis::cmd("INFO")
        .query_async(&mut conn)
        .await
        .map_err(|e| AppError::internal_server_error(format!("获取Redis信息失败: {}", e)))?;

    // 解析Redis信息
    let mut cache_size_estimate = 0u64;
    let mut uptime_seconds = 0u64;
    
    for line in info.lines() {
        if line.starts_with("used_memory:") {
            if let Some(memory_str) = line.split(':').nth(1) {
                cache_size_estimate = memory_str.parse().unwrap_or(0);
            }
        } else if line.starts_with("uptime_in_seconds:") {
            if let Some(uptime_str) = line.split(':').nth(1) {
                uptime_seconds = uptime_str.parse().unwrap_or(0);
            }
        }
    }

    // 创建模拟的统计信息（实际应用中应该从SmartCacheManager获取）
    let stats = ExtendedCacheStats {
        hits: 1000,
        misses: 200,
        sets: 800,
        deletes: 50,
        hit_rate: 0.833,
        last_updated: std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs(),
    };

    // 获取热点键（模拟数据）
    let hot_keys = vec![
        ("api_cache:projects".to_string(), 150),
        ("api_cache:dashboard".to_string(), 120),
        ("api_cache:parts".to_string(), 80),
        ("api_cache:users".to_string(), 60),
        ("api_cache:machines".to_string(), 40),
    ];

    let response = CacheStatsResponse {
        stats,
        hot_keys,
        cache_size_estimate,
        uptime_seconds,
    };

    Ok(Json(response))
}

/// 执行缓存操作
pub async fn execute_cache_operation(
    State(cache_service): State<CacheService>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<CacheOperationRequest>,
) -> Result<Json<Value>, AppError> {
    // 检查权限 - 只有管理员可以执行缓存操作
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err(AppError::forbidden(Some("只有管理员可以执行缓存操作".to_string())));
    }

    match request.operation.as_str() {
        "clear" => {
            // 清空所有缓存
            let mut conn = cache_service.get_connection().await
                .map_err(|e| AppError::internal_server_error(format!("连接Redis失败: {}", e)))?;
            
            let _: () = redis::cmd("FLUSHDB")
                .query_async(&mut conn)
                .await
                .map_err(|e| AppError::internal_server_error(format!("清空缓存失败: {}", e)))?;

            Ok(Json(json!({
                "message": "所有缓存已清空",
                "operation": "clear",
                "success": true
            })))
        }
        "invalidate" => {
            // 失效指定模式的缓存
            let pattern = request.pattern.unwrap_or_else(|| "api_cache:*".to_string());
            
            let deleted_count = cache_service.delete_pattern(&pattern).await
                .map_err(|e| AppError::internal_server_error(format!("失效缓存失败: {}", e)))?;

            Ok(Json(json!({
                "message": format!("已失效 {} 个缓存项", deleted_count),
                "operation": "invalidate",
                "pattern": pattern,
                "deleted_count": deleted_count,
                "success": true
            })))
        }
        "refresh" => {
            // 刷新指定键的缓存（删除后重新加载）
            if let Some(keys) = request.keys {
                let mut deleted_count = 0;
                for key in &keys {
                    if cache_service.delete(key).await.is_ok() {
                        deleted_count += 1;
                    }
                }

                Ok(Json(json!({
                    "message": format!("已刷新 {} 个缓存项", deleted_count),
                    "operation": "refresh",
                    "keys": keys,
                    "refreshed_count": deleted_count,
                    "success": true
                })))
            } else {
                Err(AppError::validation_error("刷新操作需要指定keys参数".to_string()))
            }
        }
        _ => {
            Err(AppError::validation_error(format!("不支持的操作: {}", request.operation)))
        }
    }
}

/// 搜索缓存键
pub async fn search_cache_keys(
    State(cache_service): State<CacheService>,
    Extension(auth_user): Extension<AuthUser>,
    Query(params): Query<CacheQueryParams>,
) -> Result<Json<Value>, AppError> {
    // 检查权限 - 只有管理员可以搜索缓存键
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err(AppError::forbidden(Some("只有管理员可以搜索缓存键".to_string())));
    }

    let pattern = params.pattern.unwrap_or_else(|| "api_cache:*".to_string());
    let limit = params.limit.unwrap_or(100);

    let mut conn = cache_service.get_connection().await
        .map_err(|e| AppError::internal_server_error(format!("连接Redis失败: {}", e)))?;

    let keys: Vec<String> = redis::cmd("KEYS")
        .arg(&pattern)
        .query_async(&mut conn)
        .await
        .map_err(|e| AppError::internal_server_error(format!("搜索缓存键失败: {}", e)))?;

    let limited_keys: Vec<String> = keys.into_iter().take(limit).collect();

    // 获取键的详细信息
    let mut key_details = Vec::new();
    for key in &limited_keys {
        let ttl: i64 = redis::cmd("TTL")
            .arg(key)
            .query_async(&mut conn)
            .await
            .unwrap_or(-1);

        let key_type: String = redis::cmd("TYPE")
            .arg(key)
            .query_async(&mut conn)
            .await
            .unwrap_or_else(|_| "unknown".to_string());

        key_details.push(json!({
            "key": key,
            "ttl": ttl,
            "type": key_type,
            "expires_in": if ttl > 0 { Some(ttl) } else { None }
        }));
    }

    Ok(Json(json!({
        "pattern": pattern,
        "total_found": limited_keys.len(),
        "limit": limit,
        "keys": key_details
    })))
}

/// 获取缓存健康状态
pub async fn get_cache_health(
    State(cache_service): State<CacheService>,
) -> Result<Json<Value>, AppError> {
    // 测试Redis连接
    match cache_service.get_connection().await {
        Ok(mut conn) => {
            // 执行PING命令测试连接
            let pong: String = redis::cmd("PING")
                .query_async(&mut conn)
                .await
                .map_err(|e| AppError::internal_server_error(format!("Redis PING失败: {}", e)))?;

            if pong == "PONG" {
                Ok(Json(json!({
                    "status": "healthy",
                    "redis_connection": "ok",
                    "timestamp": std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap()
                        .as_secs()
                })))
            } else {
                Ok(Json(json!({
                    "status": "unhealthy",
                    "redis_connection": "ping_failed",
                    "error": "Redis PING返回异常响应"
                })))
            }
        }
        Err(e) => {
            Ok(Json(json!({
                "status": "unhealthy",
                "redis_connection": "failed",
                "error": format!("无法连接到Redis: {}", e)
            })))
        }
    }
}
