use axum::{
    extract::{Extension, Path, Query, State},
    http::StatusCode,
    response::Json,
};
use sqlx::PgPool;

use crate::{
    middleware::auth::AuthUser,
    models::auto_work_order::{
        AutoWorkOrderConfigQuery, AutoWorkOrderTriggerEvent, CreateAutoWorkOrderConfigRequest,
        UpdateAutoWorkOrderConfigRequest,
    },
    services::auto_work_order_service::AutoWorkOrderService,
    utils::validation::ErrorResponse,
};

// 获取自动工单配置列表
pub async fn get_auto_work_order_configs(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Query(query): Query<AutoWorkOrderConfigQuery>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let service = AutoWorkOrderService::new(pool);

    match service.get_configs(query).await {
        Ok(response) => Ok(Json(serde_json::json!(response))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

// 创建自动工单配置
pub async fn create_auto_work_order_config(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<CreateAutoWorkOrderConfigRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // 只有管理员和工艺工程师可以创建自动工单配置
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators and process engineers can create auto work order configs"
                    .to_string(),
            }),
        ));
    }

    let service = AutoWorkOrderService::new(pool);

    match service.create_config(request, auth_user.id).await {
        Ok(config) => Ok(Json(serde_json::json!({
            "message": "Auto work order config created successfully",
            "config": config
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "config_creation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

// 更新自动工单配置
pub async fn update_auto_work_order_config(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(config_id): Path<i32>,
    Json(request): Json<UpdateAutoWorkOrderConfigRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // 只有管理员和工艺工程师可以更新自动工单配置
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators and process engineers can update auto work order configs"
                    .to_string(),
            }),
        ));
    }

    let service = AutoWorkOrderService::new(pool);

    match service.update_config(config_id, request).await {
        Ok(Some(config)) => Ok(Json(serde_json::json!({
            "message": "Auto work order config updated successfully",
            "config": config
        }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "config_not_found".to_string(),
                message: "Auto work order config not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "config_update_failed".to_string(),
                message: error,
            }),
        )),
    }
}

// 删除自动工单配置
pub async fn delete_auto_work_order_config(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(config_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // 只有管理员可以删除自动工单配置
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can delete auto work order configs".to_string(),
            }),
        ));
    }

    let service = AutoWorkOrderService::new(pool);

    match service.delete_config(config_id).await {
        Ok(true) => Ok(Json(serde_json::json!({
            "message": "Auto work order config deleted successfully"
        }))),
        Ok(false) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "config_not_found".to_string(),
                message: "Auto work order config not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "config_deletion_failed".to_string(),
                message: error,
            }),
        )),
    }
}

// 手动触发自动工单创建（用于测试）
pub async fn trigger_auto_work_order_creation(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(event): Json<AutoWorkOrderTriggerEvent>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // 只有管理员和工艺工程师可以手动触发
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Insufficient permissions to trigger auto work order creation".to_string(),
            }),
        ));
    }

    let service = AutoWorkOrderService::new(pool);

    match service.handle_trigger_event(event).await {
        Ok(results) => Ok(Json(serde_json::json!({
            "message": format!("Created {} work orders automatically", results.len()),
            "results": results
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "auto_creation_failed".to_string(),
                message: error,
            }),
        )),
    }
}
