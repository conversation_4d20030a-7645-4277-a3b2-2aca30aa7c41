use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::Json,
    Extension,
};
use serde::{Deserialize, Serialize};
use sqlx::PgPool;
use validator::Validate;

use crate::middleware::auth::AuthUser;
use crate::utils::validation::ErrorResponse;

#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct CreateRoleRequest {
    #[validate(length(min = 2, max = 50, message = "Role name must be between 2 and 50 characters"))]
    pub role_name: String,
    pub description: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateRoleRequest {
    pub role_name: Option<String>,
    pub description: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DeleteRoleRequest {
    pub replacement_role_id: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RoleDependencyInfo {
    pub role_id: i32,
    pub role_name: String,
    pub can_delete: bool,
    pub blocking_reason: Option<String>,
    pub affected_users: Vec<UserInfo>,
    pub user_count: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserInfo {
    pub id: i32,
    pub username: String,
    pub full_name: Option<String>,
}

// 创建角色
pub async fn create_role(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<CreateRoleRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // 检查权限 - 只有管理员可以创建角色
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can create roles".to_string(),
            }),
        ));
    }

    // 验证请求
    if let Err(errors) = request.validate() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "validation_error".to_string(),
                message: format!("Validation failed: {:?}", errors),
            }),
        ));
    }

    // 检查角色名是否已存在
    let existing_role = sqlx::query!(
        "SELECT id FROM roles WHERE role_name = $1",
        request.role_name
    )
    .fetch_optional(&pool)
    .await;

    match existing_role {
        Ok(Some(_)) => {
            return Err((
                StatusCode::CONFLICT,
                Json(ErrorResponse {
                    error: "role_exists".to_string(),
                    message: "Role with this name already exists".to_string(),
                }),
            ));
        }
        Ok(None) => {
            // 角色不存在，可以创建
        }
        Err(error) => {
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "database_error".to_string(),
                    message: format!("Database error: {}", error),
                }),
            ));
        }
    }

    // 创建角色
    let result = sqlx::query!(
        "INSERT INTO roles (role_name) VALUES ($1) RETURNING id, role_name",
        request.role_name
    )
    .fetch_one(&pool)
    .await;

    match result {
        Ok(role) => Ok(Json(serde_json::json!({
            "message": "Role created successfully",
            "role": {
                "id": role.id,
                "role_name": role.role_name
            }
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "creation_failed".to_string(),
                message: format!("Failed to create role: {}", error),
            }),
        )),
    }
}

// 更新角色
pub async fn update_role(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(role_id): Path<i32>,
    Json(request): Json<UpdateRoleRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // 检查权限
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can update roles".to_string(),
            }),
        ));
    }

    // 检查是否为系统角色
    let role = sqlx::query!(
        "SELECT role_name FROM roles WHERE id = $1",
        role_id
    )
    .fetch_optional(&pool)
    .await;

    match role {
        Ok(Some(role)) => {
            let system_roles = ["admin", "process_engineer", "planner", "operator", "quality_inspector"];
            if system_roles.contains(&role.role_name.as_str()) {
                return Err((
                    StatusCode::FORBIDDEN,
                    Json(ErrorResponse {
                        error: "system_role_protected".to_string(),
                        message: "System roles cannot be modified".to_string(),
                    }),
                ));
            }
        }
        Ok(None) => {
            return Err((
                StatusCode::NOT_FOUND,
                Json(ErrorResponse {
                    error: "role_not_found".to_string(),
                    message: "Role not found".to_string(),
                }),
            ));
        }
        Err(error) => {
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "database_error".to_string(),
                    message: format!("Database error: {}", error),
                }),
            ));
        }
    }

    // 更新角色
    if let Some(role_name) = &request.role_name {
        let result = sqlx::query!(
            "UPDATE roles SET role_name = $1 WHERE id = $2 RETURNING id, role_name",
            role_name,
            role_id
        )
        .fetch_one(&pool)
        .await;

        match result {
            Ok(role) => Ok(Json(serde_json::json!({
                "message": "Role updated successfully",
                "role": {
                    "id": role.id,
                    "role_name": role.role_name
                }
            }))),
            Err(error) => Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "update_failed".to_string(),
                    message: format!("Failed to update role: {}", error),
                }),
            )),
        }
    } else {
        Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "no_updates".to_string(),
                message: "No updates provided".to_string(),
            }),
        ))
    }
}

// 检查角色依赖
pub async fn check_role_dependencies(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(role_id): Path<i32>,
) -> Result<Json<RoleDependencyInfo>, (StatusCode, Json<ErrorResponse>)> {
    // 检查权限
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can check role dependencies".to_string(),
            }),
        ));
    }

    // 获取角色信息
    let role = sqlx::query!(
        "SELECT role_name FROM roles WHERE id = $1",
        role_id
    )
    .fetch_optional(&pool)
    .await;

    let role = match role {
        Ok(Some(role)) => role,
        Ok(None) => {
            return Err((
                StatusCode::NOT_FOUND,
                Json(ErrorResponse {
                    error: "role_not_found".to_string(),
                    message: "Role not found".to_string(),
                }),
            ));
        }
        Err(error) => {
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "database_error".to_string(),
                    message: format!("Database error: {}", error),
                }),
            ));
        }
    };

    // 检查是否为系统角色
    let system_roles = ["admin", "process_engineer", "planner", "operator", "quality_inspector"];
    let is_system_role = system_roles.contains(&role.role_name.as_str());

    // 获取受影响的用户
    let affected_users = sqlx::query!(
        "SELECT u.id, u.username, u.full_name 
         FROM users u 
         JOIN user_roles ur ON u.id = ur.user_id 
         WHERE ur.role_id = $1",
        role_id
    )
    .fetch_all(&pool)
    .await;

    let affected_users: Vec<UserInfo> = match affected_users {
        Ok(users) => users.into_iter().map(|u| UserInfo {
            id: u.id,
            username: u.username,
            full_name: u.full_name,
        }).collect(),
        Err(error) => {
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "database_error".to_string(),
                    message: format!("Failed to fetch affected users: {}", error),
                }),
            ));
        }
    };

    let user_count = affected_users.len() as i32;
    let can_delete = !is_system_role && user_count == 0;
    let blocking_reason = if is_system_role {
        Some("System role cannot be deleted".to_string())
    } else if user_count > 0 {
        Some(format!("Role is assigned to {} user(s)", user_count))
    } else {
        None
    };

    Ok(Json(RoleDependencyInfo {
        role_id,
        role_name: role.role_name,
        can_delete,
        blocking_reason,
        affected_users,
        user_count,
    }))
}

// 删除角色
pub async fn delete_role(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(role_id): Path<i32>,
    Json(request): Json<DeleteRoleRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // 检查权限
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can delete roles".to_string(),
            }),
        ));
    }

    // 先检查依赖
    let dependency_check = check_role_dependencies(
        State(pool.clone()),
        Extension(auth_user.clone()),
        Path(role_id),
    ).await;

    let dependency_info = match dependency_check {
        Ok(Json(info)) => info,
        Err(error) => return Err(error),
    };

    // 如果不能删除且没有提供替换角色，返回错误
    if !dependency_info.can_delete && request.replacement_role_id.is_none() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "replacement_required".to_string(),
                message: "Replacement role ID is required for roles with dependencies".to_string(),
            }),
        ));
    }

    // 开始事务
    let mut tx = pool.begin().await.map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "transaction_error".to_string(),
                message: format!("Failed to start transaction: {}", e),
            }),
        )
    })?;

    // 如果有替换角色，更新用户角色
    if let Some(replacement_role_id) = request.replacement_role_id {
        sqlx::query!(
            "UPDATE user_roles SET role_id = $1 WHERE role_id = $2",
            replacement_role_id,
            role_id
        )
        .execute(&mut *tx)
        .await
        .map_err(|e| {
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "migration_failed".to_string(),
                    message: format!("Failed to migrate user roles: {}", e),
                }),
            )
        })?;
    }

    // 删除角色
    sqlx::query!("DELETE FROM roles WHERE id = $1", role_id)
        .execute(&mut *tx)
        .await
        .map_err(|e| {
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "deletion_failed".to_string(),
                    message: format!("Failed to delete role: {}", e),
                }),
            )
        })?;

    // 提交事务
    tx.commit().await.map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "commit_failed".to_string(),
                message: format!("Failed to commit transaction: {}", e),
            }),
        )
    })?;

    Ok(Json(serde_json::json!({
        "message": "Role deleted successfully"
    })))
}
