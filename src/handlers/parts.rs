use axum::{
    extract::{Extension, Path, Query, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use sqlx::PgPool;

use crate::{
    middleware::auth::AuthUser,
    models::part::{CreatePartRequest, PartSearchQuery, UpdatePartRequest},
    models::auto_work_order::{AutoWorkOrderTriggerEvent, TRIGGER_PART_CREATED},
    services::part_service::PartService,
    services::auto_work_order_service::AutoWorkOrderService,
    utils::validation::ErrorResponse,
};

pub async fn create_part(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): J<PERSON><CreatePartRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins and process engineers can create parts
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
    {
        return Err((
            StatusCode::FOR<PERSON>DDE<PERSON>,
            <PERSON><PERSON>(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators and process engineers can create parts".to_string(),
            }),
        ));
    }

    let part_service = PartService::new(pool.clone());

    match part_service.create_part(request).await {
        Ok(part) => {
            // 触发自动工单创建
            let auto_work_order_service = AutoWorkOrderService::new(pool);
            let trigger_event = AutoWorkOrderTriggerEvent {
                trigger_type: TRIGGER_PART_CREATED.to_string(),
                project_id: None, // 零件创建时通常不知道具体项目
                part_id: Some(part.id),
                bom_id: None,
                routing_id: None,
                quantity: None,
                user_id: auth_user.id,
            };

            // 异步触发自动工单创建（不阻塞零件创建响应）
            if let Ok(auto_results) = auto_work_order_service.handle_trigger_event(trigger_event).await {
                if !auto_results.is_empty() {
                    return Ok(Json(serde_json::json!({
                        "message": format!("Part created successfully and {} work orders created automatically", auto_results.len()),
                        "part": part,
                        "auto_work_orders": auto_results
                    })));
                }
            }

            Ok(Json(serde_json::json!({
                "message": "Part created successfully",
                "part": part
            })))
        },
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "creation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_all_parts(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Query(query): Query<PartSearchQuery>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let part_service = PartService::new(pool);

    let result =
        if query.part_number.is_some() || query.part_name.is_some() || query.version.is_some() {
            part_service.search_parts(query).await
        } else {
            part_service.get_all_parts(query.limit, query.offset).await
        };

    match result {
        Ok(search_result) => Ok(Json(serde_json::json!({"data": search_result}))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_part_by_id(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Path(part_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let part_service = PartService::new(pool);

    match part_service.get_part_by_id(part_id).await {
        Ok(Some(part)) => Ok(Json(serde_json::json!({ "part": part }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "part_not_found".to_string(),
                message: "Part not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn update_part(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(part_id): Path<i32>,
    Json(request): Json<UpdatePartRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins and process engineers can update parts
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators and process engineers can update parts".to_string(),
            }),
        ));
    }

    let part_service = PartService::new(pool);

    match part_service.update_part(part_id, request).await {
        Ok(Some(part)) => Ok(Json(serde_json::json!({
            "message": "Part updated successfully",
            "part": part
        }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "part_not_found".to_string(),
                message: "Part not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "update_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn delete_part(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(part_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins can delete parts
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can delete parts".to_string(),
            }),
        ));
    }

    let part_service = PartService::new(pool);

    match part_service.delete_part(part_id).await {
        Ok(true) => Ok(Json(serde_json::json!({
            "message": "Part deleted successfully"
        }))),
        Ok(false) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "part_not_found".to_string(),
                message: "Part not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "delete_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_part_projects(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Path(part_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let part_service = PartService::new(pool);

    match part_service.get_part_projects(part_id).await {
        Ok(projects) => Ok(Json(serde_json::json!({
            "projects": projects
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}
