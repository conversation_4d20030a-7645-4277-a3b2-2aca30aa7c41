use axum::{
    extract::{Extension, Path, State},
    http::StatusCode,
    response::J<PERSON>,
};
use sqlx::PgPool;

use crate::{
    middleware::auth::AuthUser,
    models::user_machine_binding::{
        CreateUserMachineBindingRequest, UpdateUserMachineBindingRequest,
    },
    services::user_machine_binding_service::UserMachineBindingService,
    utils::validation::ErrorResponse,
};

// 获取用户的设备绑定列表
pub async fn get_user_machine_bindings(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let service = UserMachineBindingService::new(pool);

    match service.get_user_machine_bindings(auth_user.id).await {
        Ok(response) => Ok(Json(serde_json::json!(response))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            J<PERSON>(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

// 创建设备绑定
pub async fn create_machine_binding(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<CreateUserMachineBindingRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let service = UserMachineBindingService::new(pool);

    match service
        .create_machine_binding(auth_user.id, request)
        .await
    {
        Ok(binding) => Ok(Json(serde_json::json!({
            "message": "设备绑定创建成功",
            "binding": binding
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "binding_creation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

// 更新设备绑定
pub async fn update_machine_binding(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(binding_id): Path<i32>,
    Json(request): Json<UpdateUserMachineBindingRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let service = UserMachineBindingService::new(pool);

    match service
        .update_machine_binding(auth_user.id, binding_id, request)
        .await
    {
        Ok(binding) => Ok(Json(serde_json::json!({
            "message": "设备绑定更新成功",
            "binding": binding
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "binding_update_failed".to_string(),
                message: error,
            }),
        )),
    }
}

// 删除设备绑定
pub async fn delete_machine_binding(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(binding_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let service = UserMachineBindingService::new(pool);

    match service
        .delete_machine_binding(auth_user.id, binding_id)
        .await
    {
        Ok(deleted) => {
            if deleted {
                Ok(Json(serde_json::json!({
                    "message": "设备绑定删除成功"
                })))
            } else {
                Err((
                    StatusCode::NOT_FOUND,
                    Json(ErrorResponse {
                        error: "binding_not_found".to_string(),
                        message: "绑定不存在或您没有权限删除".to_string(),
                    }),
                ))
            }
        }
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "binding_deletion_failed".to_string(),
                message: error,
            }),
        )),
    }
}

// 获取用户的主要设备
pub async fn get_user_primary_machine(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let service = UserMachineBindingService::new(pool);

    match service.get_user_primary_machine(auth_user.id).await {
        Ok(machine) => Ok(Json(serde_json::json!({
            "primary_machine": machine
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}
