use axum::{
    extract::{Extension, Path, Query, State},
    http::StatusC<PERSON>,
    response::<PERSON><PERSON>,
};
use sqlx::PgPool;

use crate::{
    middleware::auth::AuthUser,
    models::project::{
        CreateProjectBomRequest, CreateProjectRequest, UpdateProjectBomRequest,
        UpdateProjectRequest, ProjectQuery, ProjectStatusUpdate,
    },
    models::project_status::ProjectStatusQuery,
    models::auto_work_order::{AutoWorkOrderTriggerEvent, TRIGGER_BOM_ADDED},
    services::project_service::ProjectService,
    services::project_status_service::ProjectStatusService,
    services::auto_work_order_service::AutoWorkOrderService,
    utils::validation::ErrorResponse,
};

pub async fn create_project(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): <PERSON><PERSON><CreateProjectRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins, process engineers, and planners can create projects
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators, process engineers, and planners can create projects"
                    .to_string(),
            }),
        ));
    }

    let project_service = ProjectService::new(pool.clone());

    match project_service.create_project(request).await {
        Ok(project) => Ok(Json(serde_json::json!({
            "message": "Project created successfully",
            "project": project
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "creation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_all_projects(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Query(query): Query<ProjectQuery>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let project_service = ProjectService::new(pool);

    // If any query parameters are provided, use search; otherwise use get_all
    if query.status.is_some() || query.customer_name.is_some() || query.limit.is_some() || query.offset.is_some() {
        match project_service.search_projects(query).await {
            Ok(search_result) => Ok(Json(serde_json::json!(search_result))),
            Err(error) => Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "database_error".to_string(),
                    message: error,
                }),
            )),
        }
    } else {
        match project_service.get_all_projects().await {
            Ok(projects) => Ok(Json(serde_json::json!({ "projects": projects }))),
            Err(error) => Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "database_error".to_string(),
                    message: error,
                }),
            )),
        }
    }
}

pub async fn get_project_by_id(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Path(project_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let project_service = ProjectService::new(pool);

    match project_service.get_project_by_id(project_id).await {
        Ok(Some(project)) => Ok(Json(serde_json::json!({ "project": project }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "project_not_found".to_string(),
                message: "Project not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_project_with_bom(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Path(project_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let project_service = ProjectService::new(pool);

    match project_service.get_project_with_bom(project_id).await {
        Ok(Some(project)) => Ok(Json(serde_json::json!({ "project": project }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "project_not_found".to_string(),
                message: "Project not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn update_project(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(project_id): Path<i32>,
    Json(request): Json<UpdateProjectRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins, process engineers, and planners can update projects
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators, process engineers, and planners can update projects"
                    .to_string(),
            }),
        ));
    }

    let project_service = ProjectService::new(pool);

    match project_service.update_project(project_id, request).await {
        Ok(Some(project)) => Ok(Json(serde_json::json!({
            "message": "Project updated successfully",
            "project": project
        }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "project_not_found".to_string(),
                message: "Project not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "update_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn delete_project(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(project_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins can delete projects
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can delete projects".to_string(),
            }),
        ));
    }

    let project_service = ProjectService::new(pool);

    match project_service.delete_project(project_id).await {
        Ok(true) => Ok(Json(serde_json::json!({
            "message": "Project deleted successfully"
        }))),
        Ok(false) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "project_not_found".to_string(),
                message: "Project not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "delete_failed".to_string(),
                message: error,
            }),
        )),
    }
}

// BOM Management
pub async fn add_bom_item(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(project_id): Path<i32>,
    Json(request): Json<CreateProjectBomRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins, process engineers, and planners can manage BOMs
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators, process engineers, and planners can manage BOMs"
                    .to_string(),
            }),
        ));
    }

    let project_service = ProjectService::new(pool.clone());

    match project_service.add_bom_item(project_id, request).await {
        Ok(bom_item) => {
            // 触发自动工单创建
            let auto_work_order_service = AutoWorkOrderService::new(pool);
            let trigger_event = AutoWorkOrderTriggerEvent {
                trigger_type: TRIGGER_BOM_ADDED.to_string(),
                project_id: Some(project_id),
                part_id: Some(bom_item.part_id),
                bom_id: Some(bom_item.id),
                routing_id: None,
                quantity: Some(bom_item.quantity),
                user_id: auth_user.id,
            };

            // 异步触发自动工单创建
            if let Ok(auto_results) = auto_work_order_service.handle_trigger_event(trigger_event).await {
                if !auto_results.is_empty() {
                    return Ok(Json(serde_json::json!({
                        "message": format!("BOM item added successfully and {} work orders created automatically", auto_results.len()),
                        "bom_item": bom_item,
                        "auto_work_orders": auto_results
                    })));
                }
            }

            Ok(Json(serde_json::json!({
                "message": "BOM item added successfully",
                "bom_item": bom_item
            })))
        },
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "creation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn update_bom_item(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(bom_id): Path<i32>,
    Json(request): Json<UpdateProjectBomRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins, process engineers, and planners can manage BOMs
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators, process engineers, and planners can manage BOMs"
                    .to_string(),
            }),
        ));
    }

    let project_service = ProjectService::new(pool);

    match project_service.update_bom_item(bom_id, request).await {
        Ok(Some(bom_item)) => Ok(Json(serde_json::json!({
            "message": "BOM item updated successfully",
            "bom_item": bom_item
        }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "bom_item_not_found".to_string(),
                message: "BOM item not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "update_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn remove_bom_item(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(bom_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins, process engineers, and planners can manage BOMs
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators, process engineers, and planners can manage BOMs"
                    .to_string(),
            }),
        ));
    }

    let project_service = ProjectService::new(pool);

    match project_service.remove_bom_item(bom_id).await {
        Ok(true) => Ok(Json(serde_json::json!({
            "message": "BOM item removed successfully"
        }))),
        Ok(false) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "bom_item_not_found".to_string(),
                message: "BOM item not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "delete_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_project_bom(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Path(project_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let project_service = ProjectService::new(pool);

    match project_service.get_project_bom(project_id).await {
        Ok(bom_items) => Ok(Json(serde_json::json!({ "bom_items": bom_items }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_project_completion_status(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Path(project_id): Path<i32>,
    axum::extract::Query(query): axum::extract::Query<ProjectStatusQuery>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let project_status_service = ProjectStatusService::new(pool);

    match project_status_service
        .get_project_completion_status(project_id, query)
        .await
    {
        Ok(status) => Ok(Json(serde_json::json!({ "completion_status": status }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

// Update project status only
pub async fn update_project_status(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(project_id): Path<i32>,
    Json(status_update): Json<ProjectStatusUpdate>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins, process engineers, and planners can update project status
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators, process engineers, and planners can update project status"
                    .to_string(),
            }),
        ));
    }

    let project_service = ProjectService::new(pool);

    match project_service.update_project_status(project_id, status_update).await {
        Ok(Some(project)) => Ok(Json(serde_json::json!({
            "message": "Project status updated successfully",
            "project": project
        }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "project_not_found".to_string(),
                message: "Project not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "update_failed".to_string(),
                message: error,
            }),
        )),
    }
}

// Get project status statistics
pub async fn get_project_status_stats(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let project_service = ProjectService::new(pool);

    match project_service.get_project_status_stats().await {
        Ok(stats) => Ok(Json(serde_json::json!({ "status_stats": stats }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}
