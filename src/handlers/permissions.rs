use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
    Extension,
};
use serde::{Deserialize, Serialize};
use sqlx::{PgPool, Row};

use crate::middleware::auth::AuthUser;
use crate::models::user::{Permission, RoleWithPermissions, PermissionInfo, Role};
use crate::services::permission_service::PermissionService;
use crate::utils::validation::ErrorResponse;

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateRolePermissionsRequest {
    pub permissions: Vec<RolePermissionUpdate>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RolePermissionUpdate {
    pub permission_id: i32,
    pub granted: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreatePermissionRequest {
    pub permission_code: String,
    pub permission_name: String,
    pub description: Option<String>,
    pub category: String,
}

// 获取所有权限列表
pub async fn get_all_permissions(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
) -> Result<Json<Vec<Permission>>, (StatusCode, Json<ErrorResponse>)> {
    // 检查权限 - 只有管理员可以查看权限列表
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can view permissions".to_string(),
            }),
        ));
    }

    let permissions = sqlx::query_as!(
        Permission,
        "SELECT id, permission_code, permission_name, description, category, is_active, created_at 
         FROM permissions 
         WHERE is_active = true 
         ORDER BY category, permission_name"
    )
    .fetch_all(&pool)
    .await;

    match permissions {
        Ok(permissions) => Ok(Json(permissions)),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: format!("Failed to fetch permissions: {}", error),
            }),
        )),
    }
}

// 获取用户自己的权限（不需要管理员权限）
pub async fn get_user_permissions(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let permission_service = PermissionService::new(pool.clone());

    // 获取用户的所有角色权限
    let mut user_permissions = std::collections::HashMap::new();

    // 使用简单的查询获取角色ID
    let role_query = "SELECT id, role_name FROM roles WHERE role_name = ANY($1)";
    let role_results = sqlx::query(role_query)
        .bind(&auth_user.roles)
        .fetch_all(&pool)
        .await;

    let roles = match role_results {
        Ok(rows) => {
            let mut roles = Vec::new();
            for row in rows {
                let id: i32 = row.get("id");
                let role_name: String = row.get("role_name");
                roles.push((id, role_name));
            }
            roles
        }
        Err(e) => {
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "database_error".to_string(),
                    message: format!("Failed to fetch roles: {}", e),
                }),
            ));
        }
    };

    // 获取每个角色的权限
    for (role_id, role_name) in roles {
        match permission_service.get_role_permissions(role_id).await {
            Ok(permissions) => {
                user_permissions.insert(role_id.to_string(), permissions);
            }
            Err(e) => {
                tracing::warn!("Failed to fetch permissions for role {}: {}", role_name, e);
            }
        }
    }

    Ok(Json(serde_json::json!({
        "user_id": auth_user.id,
        "username": auth_user.username,
        "roles": auth_user.roles,
        "permissions": user_permissions
    })))
}

// 获取角色的权限配置（管理员专用）
pub async fn get_role_permissions(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(role_id): Path<i32>,
) -> Result<Json<RoleWithPermissions>, (StatusCode, Json<ErrorResponse>)> {
    // 检查权限 - 只有管理员可以查看角色权限
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can view role permissions".to_string(),
            }),
        ));
    }

    // 获取角色信息
    let role = sqlx::query!(
        "SELECT id, role_name, description, is_active, role_type, created_at 
         FROM roles 
         WHERE id = $1",
        role_id
    )
    .fetch_optional(&pool)
    .await;

    let role = match role {
        Ok(Some(role)) => role,
        Ok(None) => {
            return Err((
                StatusCode::NOT_FOUND,
                Json(ErrorResponse {
                    error: "role_not_found".to_string(),
                    message: "Role not found".to_string(),
                }),
            ));
        }
        Err(error) => {
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "database_error".to_string(),
                    message: format!("Database error: {}", error),
                }),
            ));
        }
    };

    // 获取所有权限及该角色的授权状态
    let permissions = sqlx::query!(
        "SELECT p.id, p.permission_code, p.permission_name, p.description, p.category,
                COALESCE(rp.granted, false) as granted
         FROM permissions p
         LEFT JOIN role_permissions rp ON p.id = rp.permission_id AND rp.role_id = $1
         WHERE p.is_active = true
         ORDER BY p.category, p.permission_name",
        role_id
    )
    .fetch_all(&pool)
    .await;

    let permissions: Vec<PermissionInfo> = match permissions {
        Ok(permissions) => permissions
            .into_iter()
            .map(|p| PermissionInfo {
                id: p.id,
                permission_code: p.permission_code,
                permission_name: p.permission_name,
                description: p.description,
                category: p.category,
                granted: p.granted.unwrap_or(false),
            })
            .collect(),
        Err(error) => {
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "database_error".to_string(),
                    message: format!("Failed to fetch permissions: {}", error),
                }),
            ));
        }
    };

    let role_with_permissions = RoleWithPermissions {
        id: role.id,
        role_name: role.role_name,
        description: role.description,
        is_active: role.is_active.unwrap_or(true),
        role_type: role.role_type.unwrap_or_else(|| "custom".to_string()),
        created_at: role.created_at.unwrap_or_else(chrono::Utc::now),
        permissions,
    };

    Ok(Json(role_with_permissions))
}

// 更新角色权限
pub async fn update_role_permissions(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(role_id): Path<i32>,
    Json(request): Json<UpdateRolePermissionsRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // 检查权限 - 只有管理员可以更新角色权限
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can update role permissions".to_string(),
            }),
        ));
    }

    // 验证请求 - 简单验证，因为我们移除了validator依赖
    if request.permissions.is_empty() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "validation_error".to_string(),
                message: "Permissions list cannot be empty".to_string(),
            }),
        ));
    }

    // 检查角色是否存在
    let role_exists = sqlx::query!(
        "SELECT id FROM roles WHERE id = $1",
        role_id
    )
    .fetch_optional(&pool)
    .await;

    match role_exists {
        Ok(None) => {
            return Err((
                StatusCode::NOT_FOUND,
                Json(ErrorResponse {
                    error: "role_not_found".to_string(),
                    message: "Role not found".to_string(),
                }),
            ));
        }
        Err(error) => {
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "database_error".to_string(),
                    message: format!("Database error: {}", error),
                }),
            ));
        }
        _ => {}
    }

    // 开始事务
    let mut tx = match pool.begin().await {
        Ok(tx) => tx,
        Err(error) => {
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "transaction_error".to_string(),
                    message: format!("Failed to start transaction: {}", error),
                }),
            ));
        }
    };

    // 删除现有的角色权限关联
    if let Err(error) = sqlx::query!(
        "DELETE FROM role_permissions WHERE role_id = $1",
        role_id
    )
    .execute(&mut *tx)
    .await
    {
        let _ = tx.rollback().await;
        return Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: format!("Failed to delete existing permissions: {}", error),
            }),
        ));
    }

    // 插入新的权限关联
    for permission_update in request.permissions {
        if permission_update.granted {
            if let Err(error) = sqlx::query!(
                "INSERT INTO role_permissions (role_id, permission_id, granted, granted_by) 
                 VALUES ($1, $2, $3, $4)",
                role_id,
                permission_update.permission_id,
                permission_update.granted,
                auth_user.id
            )
            .execute(&mut *tx)
            .await
            {
                let _ = tx.rollback().await;
                return Err((
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse {
                        error: "database_error".to_string(),
                        message: format!("Failed to insert permission: {}", error),
                    }),
                ));
            }
        }
    }

    // 提交事务
    if let Err(error) = tx.commit().await {
        return Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "transaction_error".to_string(),
                message: format!("Failed to commit transaction: {}", error),
            }),
        ));
    }

    Ok(Json(serde_json::json!({
        "message": "Role permissions updated successfully"
    })))
}

// 创建新权限
pub async fn create_permission(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<CreatePermissionRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // 检查权限 - 只有管理员可以创建权限
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can create permissions".to_string(),
            }),
        ));
    }

    // 验证请求
    if request.permission_code.len() < 2 || request.permission_code.len() > 100 {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "validation_error".to_string(),
                message: "Permission code must be between 2 and 100 characters".to_string(),
            }),
        ));
    }

    if request.permission_name.len() < 2 || request.permission_name.len() > 255 {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "validation_error".to_string(),
                message: "Permission name must be between 2 and 255 characters".to_string(),
            }),
        ));
    }

    if request.category.is_empty() || request.category.len() > 50 {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "validation_error".to_string(),
                message: "Category must be between 1 and 50 characters".to_string(),
            }),
        ));
    }

    // 检查权限代码是否已存在
    let existing_permission = sqlx::query!(
        "SELECT id FROM permissions WHERE permission_code = $1",
        request.permission_code
    )
    .fetch_optional(&pool)
    .await;

    match existing_permission {
        Ok(Some(_)) => {
            return Err((
                StatusCode::CONFLICT,
                Json(ErrorResponse {
                    error: "permission_exists".to_string(),
                    message: "Permission with this code already exists".to_string(),
                }),
            ));
        }
        Err(error) => {
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "database_error".to_string(),
                    message: format!("Database error: {}", error),
                }),
            ));
        }
        _ => {}
    }

    // 创建权限
    let result = sqlx::query!(
        "INSERT INTO permissions (permission_code, permission_name, description, category) 
         VALUES ($1, $2, $3, $4) 
         RETURNING id, permission_code, permission_name",
        request.permission_code,
        request.permission_name,
        request.description,
        request.category
    )
    .fetch_one(&pool)
    .await;

    match result {
        Ok(permission) => Ok(Json(serde_json::json!({
            "message": "Permission created successfully",
            "permission": {
                "id": permission.id,
                "permission_code": permission.permission_code,
                "permission_name": permission.permission_name
            }
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: format!("Failed to create permission: {}", error),
            }),
        )),
    }
}
