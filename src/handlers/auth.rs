use axum::{
    extract::{Extension, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use sqlx::PgPool;
use validator::Validate;

use crate::{
    middleware::auth::AuthUser,
    services::auth_service::AuthService,
    utils::validation::{CreateUserRequest, ErrorResponse, LoginRequest, LoginResponse, UserInfo},
    utils::jwt,
};

pub async fn login(
    State(pool): State<PgPool>,
    Json(request): Json<LoginRequest>,
) -> Result<Json<LoginResponse>, (StatusCode, Json<ErrorResponse>)> {
    tracing::info!("Login attempt for user: {}", request.username);

    // Validate request
    if let Err(errors) = request.validate() {
        tracing::warn!("Login validation failed for user {}: {:?}", request.username, errors);
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "validation_error".to_string(),
                message: format!("Validation failed: {:?}", errors),
            }),
        ));
    }

    let auth_service = AuthService::new(pool);

    match auth_service.login(request.clone()).await {
        Ok(response) => {
            tracing::info!("Login successful for user: {}", request.username);
            Ok(Json(response))
        },
        Err(error) => {
            tracing::warn!("Login failed for user {}: {}", request.username, error);
            Err((
                StatusCode::UNAUTHORIZED,
                Json(ErrorResponse {
                    error: "authentication_failed".to_string(),
                    message: error,
                }),
            ))
        },
    }
}

pub async fn create_user(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<CreateUserRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Check if user has admin role
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can create users".to_string(),
            }),
        ));
    }

    // Validate request
    if let Err(errors) = request.validate() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "validation_error".to_string(),
                message: format!("Validation failed: {:?}", errors),
            }),
        ));
    }

    let auth_service = AuthService::new(pool);

    match auth_service.create_user(request).await {
        Ok(user) => Ok(Json(serde_json::json!({
            "message": "User created successfully",
            "user": user
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "user_creation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_current_user(
    Extension(auth_user): Extension<AuthUser>,
    State(pool): State<PgPool>,
) -> Result<Json<UserInfo>, (StatusCode, Json<ErrorResponse>)> {
    let auth_service = AuthService::new(pool);

    match auth_service.get_user_info(auth_user.id).await {
        Ok(user_info) => Ok(Json(user_info)),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "user_fetch_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_roles(
    State(pool): State<PgPool>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let roles = sqlx::query!(
        "SELECT id, role_name, display_name, description, is_active, role_type, created_at
         FROM roles
         WHERE is_active = true
         ORDER BY role_name"
    )
    .fetch_all(&pool)
    .await;

    match roles {
        Ok(roles) => {
            let role_list: Vec<serde_json::Value> = roles
                .into_iter()
                .map(|role| {
                    serde_json::json!({
                        "id": role.id,
                        "role_name": role.role_name,
                        "display_name": role.display_name,
                        "description": role.description,
                        "is_active": role.is_active,
                        "role_type": role.role_type,
                        "created_at": role.created_at
                    })
                })
                .collect();
            Ok(Json(serde_json::json!({ "roles": role_list })))
        }
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: format!("Failed to fetch roles: {}", error),
            }),
        )),
    }
}

pub async fn get_skill_groups(
    State(pool): State<PgPool>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let skill_groups = sqlx::query!(
        "SELECT id, group_name, display_name, description
         FROM skill_groups
         ORDER BY group_name"
    )
    .fetch_all(&pool)
    .await;

    match skill_groups {
        Ok(skill_groups) => {
            let skill_group_list: Vec<serde_json::Value> = skill_groups
                .into_iter()
                .map(|sg| {
                    serde_json::json!({
                        "id": sg.id,
                        "group_name": sg.group_name,
                        "display_name": sg.display_name,
                        "description": sg.description
                    })
                })
                .collect();
            Ok(Json(
                serde_json::json!({ "skill_groups": skill_group_list }),
            ))
        }
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: format!("Failed to fetch skill groups: {}", error),
            }),
        )),
    }
}

pub async fn refresh_token(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    tracing::info!("Token refresh request for user: {}", auth_user.username);

    // Get user roles and skills from database to ensure they're up to date
    let roles = sqlx::query_scalar!(
        "SELECT r.role_name FROM roles r
         JOIN user_roles ur ON r.id = ur.role_id
         WHERE ur.user_id = $1",
        auth_user.id
    )
    .fetch_all(&pool)
    .await
    .map_err(|e| {
        tracing::error!("Failed to fetch user roles: {}", e);
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: "Failed to fetch user roles".to_string(),
            }),
        )
    })?;

    let skills = sqlx::query_scalar!(
        "SELECT sg.group_name FROM skill_groups sg
         JOIN user_skills us ON sg.id = us.skill_group_id
         WHERE us.user_id = $1",
        auth_user.id
    )
    .fetch_all(&pool)
    .await
    .map_err(|e| {
        tracing::error!("Failed to fetch user skills: {}", e);
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: "Failed to fetch user skills".to_string(),
            }),
        )
    })?;

    // Create new JWT token with updated claims
    let claims = jwt::Claims::new(
        auth_user.id,
        auth_user.username.clone(),
        roles,
        skills,
    );

    let new_token = jwt::create_jwt(&claims).map_err(|e| {
        tracing::error!("Failed to create JWT token: {}", e);
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "token_creation_error".to_string(),
                message: "Failed to create new token".to_string(),
            }),
        )
    })?;

    tracing::info!("Token refreshed successfully for user: {}", auth_user.username);

    Ok(Json(serde_json::json!({
        "token": new_token,
        "message": "Token refreshed successfully"
    })))
}
