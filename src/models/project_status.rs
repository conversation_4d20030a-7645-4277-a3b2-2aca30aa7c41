use chrono::{DateTime, NaiveDate, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectCompletionStatus {
    pub project_id: i32,
    pub project_name: String,
    pub customer_name: Option<String>,
    pub overall_progress: ProjectProgress,
    pub parts_status: Vec<PartCompletionStatus>,
    pub work_orders_summary: WorkOrdersSummary,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectProgress {
    pub total_parts: i32,
    pub completed_parts: i32,
    pub in_progress_parts: i32,
    pub not_started_parts: i32,
    pub overall_completion_percentage: f64,
    pub estimated_completion_date: Option<NaiveDate>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PartCompletionStatus {
    pub part_id: i32,
    pub part_number: String,
    pub part_name: Option<String>,
    pub version: String,
    pub specifications: Option<String>,
    pub bom_quantity: i32,
    pub total_work_orders: i32,
    pub completed_work_orders: i32,
    pub in_progress_work_orders: i32,
    pub pending_work_orders: i32,
    pub cancelled_work_orders: i32,
    pub completion_percentage: f64,
    pub status: PartStatus,
    pub work_orders: Vec<WorkOrderStatus>,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum PartStatus {
    #[serde(rename = "not_started")]
    NotStarted,
    #[serde(rename = "in_progress")]
    InProgress,
    #[serde(rename = "completed")]
    Completed,
    #[serde(rename = "cancelled")]
    Cancelled,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WorkOrderStatus {
    pub work_order_id: i32,
    pub part_id: i32,
    pub part_number: String,
    pub quantity: i32,
    pub status: String,
    pub due_date: Option<NaiveDate>,
    pub created_at: DateTime<Utc>,
    pub total_tasks: i32,
    pub completed_tasks: i32,
    pub in_progress_tasks: i32,
    pub planned_tasks: i32,
    pub progress_percentage: f64,
    pub task_details: Vec<TaskStatus>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TaskStatus {
    pub plan_task_id: i32,
    pub routing_step_id: i32,
    pub step_number: i32,
    pub process_name: String,
    pub status: String,
    pub planned_start: DateTime<Utc>,
    pub planned_end: DateTime<Utc>,
    pub actual_start: Option<DateTime<Utc>>,
    pub actual_end: Option<DateTime<Utc>>,
    pub skill_group_name: String,
    pub machine_name: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WorkOrdersSummary {
    pub total_work_orders: i32,
    pub pending_orders: i32,
    pub planned_orders: i32,
    pub in_progress_orders: i32,
    pub completed_orders: i32,
    pub cancelled_orders: i32,
    pub overdue_orders: i32,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct PartStatusQuery {
    pub part_id: i32,
    pub part_number: String,
    pub part_name: Option<String>,
    pub version: String,
    pub specifications: Option<String>,
    pub bom_quantity: i32,
    pub work_order_id: Option<i32>,
    pub work_order_quantity: Option<i32>,
    pub work_order_status: Option<String>,
    pub work_order_due_date: Option<NaiveDate>,
    pub work_order_created_at: Option<DateTime<Utc>>,
    pub total_tasks: i64,
    pub completed_tasks: i64,
    pub in_progress_tasks: i64,
    pub planned_tasks: i64,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct TaskStatusQuery {
    pub plan_task_id: i32,
    pub work_order_id: i32,
    pub routing_step_id: i32,
    pub step_number: i32,
    pub process_name: String,
    pub status: String,
    pub planned_start: DateTime<Utc>,
    pub planned_end: DateTime<Utc>,
    pub skill_group_name: String,
    pub machine_name: Option<String>,
    pub actual_start: Option<DateTime<Utc>>,
    pub actual_end: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectStatusQuery {
    pub include_work_orders: Option<bool>,
    pub include_tasks: Option<bool>,
    pub include_execution_logs: Option<bool>,
}

impl Default for ProjectStatusQuery {
    fn default() -> Self {
        Self {
            include_work_orders: Some(true),
            include_tasks: Some(true),
            include_execution_logs: Some(false),
        }
    }
}

impl PartStatus {
    pub fn from_work_orders(
        completed: i32,
        in_progress: i32,
        pending: i32,
        cancelled: i32,
        total: i32,
    ) -> Self {
        if total == 0 || (pending == total && in_progress == 0 && completed == 0) {
            Self::NotStarted
        } else if cancelled == total {
            Self::Cancelled
        } else if completed == total {
            Self::Completed
        } else {
            Self::InProgress
        }
    }
}

impl PartCompletionStatus {
    pub fn calculate_completion_percentage(&self) -> f64 {
        if self.total_work_orders == 0 {
            0.0
        } else {
            (self.completed_work_orders as f64 / self.total_work_orders as f64) * 100.0
        }
    }
}

impl WorkOrderStatus {
    pub fn calculate_progress_percentage(&self) -> f64 {
        if self.total_tasks == 0 {
            0.0
        } else {
            (self.completed_tasks as f64 / self.total_tasks as f64) * 100.0
        }
    }
}

impl ProjectProgress {
    pub fn calculate_overall_completion(&self) -> f64 {
        if self.total_parts == 0 {
            0.0
        } else {
            (self.completed_parts as f64 / self.total_parts as f64) * 100.0
        }
    }
}
