use chrono::{DateTime, NaiveDate, NaiveDateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use sqlx::types::BigDecimal;

// 班次模板
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ShiftTemplate {
    pub id: i32,
    pub template_name: String,
    pub schedule_type: String,      // '7x24', '7x12', '5x8', 'custom'
    pub start_hour: i32,
    pub start_minute: i32,
    pub end_hour: i32,
    pub end_minute: i32,
    pub duration_hours: BigDecimal,
    pub work_days: Vec<i32>,        // [1,2,3,4,5,6,7] (周一到周日)
    pub break_periods: Option<serde_json::Value>, // 休息时间段
    pub description: Option<String>,
    pub is_system_template: bool,
    pub is_active: bool,
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
}

// 计划组
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct PlanGroup {
    pub id: i32,
    pub group_name: String,
    pub group_code: String,
    pub description: Option<String>,
    pub priority: i32,
    pub is_active: bool,
    pub created_by: Option<i32>,
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
}

// 计划组班次配置
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct PlanGroupShiftConfig {
    pub id: i32,
    pub plan_group_id: i32,
    pub shift_template_id: i32,
    pub effective_date: NaiveDate,
    pub expiry_date: Option<NaiveDate>,
    pub is_default: bool,
    pub created_by: Option<i32>,
    pub created_at: NaiveDateTime,
}

// 技能组计划分配
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct SkillGroupPlanAssignment {
    pub id: i32,
    pub skill_group_id: i32,
    pub plan_group_id: i32,
    pub assignment_type: String,    // 'primary', 'secondary', 'backup'
    pub effective_date: NaiveDate,
    pub expiry_date: Option<NaiveDate>,
    pub created_by: Option<i32>,
    pub created_at: NaiveDateTime,
}

// 班次实例
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ShiftInstance {
    pub id: i32,
    pub plan_group_id: i32,
    pub shift_template_id: i32,
    pub shift_date: NaiveDate,
    pub actual_start_time: Option<NaiveDateTime>,
    pub actual_end_time: Option<NaiveDateTime>,
    pub planned_start_time: NaiveDateTime,
    pub planned_end_time: NaiveDateTime,
    pub status: String,             // 'planned', 'active', 'completed', 'cancelled'
    pub assigned_users: Option<Vec<i32>>,
    pub notes: Option<String>,
    pub created_by: Option<i32>,
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
}

// 创建班次模板请求
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateShiftTemplateRequest {
    pub template_name: String,
    pub schedule_type: String,
    pub start_hour: i32,
    pub start_minute: Option<i32>,
    pub end_hour: i32,
    pub end_minute: Option<i32>,
    pub work_days: Vec<i32>,
    pub break_periods: Option<serde_json::Value>,
    pub description: Option<String>,
}

// 更新班次模板请求
#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateShiftTemplateRequest {
    pub template_name: Option<String>,
    pub schedule_type: Option<String>,
    pub start_hour: Option<i32>,
    pub start_minute: Option<i32>,
    pub end_hour: Option<i32>,
    pub end_minute: Option<i32>,
    pub work_days: Option<Vec<i32>>,
    pub break_periods: Option<serde_json::Value>,
    pub description: Option<String>,
    pub is_active: Option<bool>,
}

// 创建计划组请求
#[derive(Debug, Serialize, Deserialize)]
pub struct CreatePlanGroupRequest {
    pub group_name: String,
    pub group_code: String,
    pub description: Option<String>,
    pub priority: Option<i32>,
}

// 更新计划组请求
#[derive(Debug, Serialize, Deserialize)]
pub struct UpdatePlanGroupRequest {
    pub group_name: Option<String>,
    pub group_code: Option<String>,
    pub description: Option<String>,
    pub priority: Option<i32>,
    pub is_active: Option<bool>,
}

// 创建计划组班次配置请求
#[derive(Debug, Serialize, Deserialize)]
pub struct CreatePlanGroupShiftConfigRequest {
    pub plan_group_id: i32,
    pub shift_template_id: i32,
    pub effective_date: NaiveDate,
    pub expiry_date: Option<NaiveDate>,
    pub is_default: Option<bool>,
}

// 创建班次实例请求
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateShiftInstanceRequest {
    pub plan_group_id: i32,
    pub shift_template_id: i32,
    pub shift_date: NaiveDate,
    pub assigned_users: Option<Vec<i32>>,
    pub notes: Option<String>,
}

// 班次查询参数
#[derive(Debug, Serialize, Deserialize)]
pub struct ShiftQuery {
    pub plan_group_id: Option<i32>,
    pub schedule_type: Option<String>,
    pub date_from: Option<NaiveDate>,
    pub date_to: Option<NaiveDate>,
    pub status: Option<String>,
    pub limit: Option<i64>,
    pub offset: Option<i64>,
}

// 班次详情视图
#[derive(Debug, Serialize, Deserialize)]
pub struct ShiftInstanceWithDetails {
    pub id: i32,
    pub plan_group_id: i32,
    pub plan_group_name: String,
    pub plan_group_code: String,
    pub shift_template_id: i32,
    pub template_name: String,
    pub schedule_type: String,
    pub shift_date: NaiveDate,
    pub planned_start_time: DateTime<Utc>,
    pub planned_end_time: DateTime<Utc>,
    pub actual_start_time: Option<DateTime<Utc>>,
    pub actual_end_time: Option<DateTime<Utc>>,
    pub status: String,
    pub assigned_users: Option<Vec<i32>>,
    pub duration_hours: f64,
    pub work_days: Vec<i32>,
    pub notes: Option<String>,
}

// 班次统计信息
#[derive(Debug, Serialize, Deserialize)]
pub struct ShiftStatistics {
    pub total_shifts: i64,
    pub active_shifts: i64,
    pub completed_shifts: i64,
    pub planned_shifts: i64,
    pub cancelled_shifts: i64,
    pub average_duration: f64,
    pub utilization_rate: f64,
}

// 班次冲突检测结果
#[derive(Debug, Serialize, Deserialize)]
pub struct ShiftConflictResult {
    pub has_conflict: bool,
    pub conflict_type: Option<String>, // 'time_overlap', 'resource_conflict', 'skill_group_conflict'
    pub conflicting_shifts: Vec<ShiftInstanceWithDetails>,
    pub suggestions: Vec<String>,
}

// 班次分配建议
#[derive(Debug, Serialize, Deserialize)]
pub struct ShiftAssignmentSuggestion {
    pub plan_group_id: i32,
    pub shift_template_id: i32,
    pub suggested_start_time: DateTime<Utc>,
    pub suggested_end_time: DateTime<Utc>,
    pub confidence_score: f64,
    pub reason: String,
}

// 验证班次模板的有效性
impl CreateShiftTemplateRequest {
    pub fn validate(&self) -> Result<(), String> {
        // 验证时间范围
        if self.start_hour < 0 || self.start_hour > 23 {
            return Err("开始小时必须在0-23之间".to_string());
        }
        if self.end_hour < 0 || self.end_hour > 23 {
            return Err("结束小时必须在0-23之间".to_string());
        }
        
        // 验证工作日
        if self.work_days.is_empty() {
            return Err("至少需要选择一个工作日".to_string());
        }
        for day in &self.work_days {
            if *day < 1 || *day > 7 {
                return Err("工作日必须在1-7之间（周一到周日）".to_string());
            }
        }
        
        // 验证班次类型
        match self.schedule_type.as_str() {
            "7x24" | "7x12" | "5x8" | "custom" => {},
            _ => return Err("无效的班次类型".to_string()),
        }
        
        Ok(())
    }
}

// 计算班次时长
impl ShiftTemplate {
    pub fn calculate_duration(&self) -> f64 {
        let start_minutes = self.start_hour as f64 * 60.0 + self.start_minute as f64;
        let end_minutes = self.end_hour as f64 * 60.0 + self.end_minute as f64;
        
        if end_minutes > start_minutes {
            // 同一天内的班次
            (end_minutes - start_minutes) / 60.0
        } else {
            // 跨天班次
            (24.0 * 60.0 - start_minutes + end_minutes) / 60.0
        }
    }
    
    pub fn is_time_within_shift(&self, hour: i32, minute: i32) -> bool {
        let check_minutes = hour * 60 + minute;
        let start_minutes = self.start_hour * 60 + self.start_minute;
        let end_minutes = self.end_hour * 60 + self.end_minute;
        
        if end_minutes > start_minutes {
            // 同一天内的班次
            check_minutes >= start_minutes && check_minutes <= end_minutes
        } else {
            // 跨天班次
            check_minutes >= start_minutes || check_minutes <= end_minutes
        }
    }
}
