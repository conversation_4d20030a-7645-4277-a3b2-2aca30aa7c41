use chrono::{DateTime, NaiveDate, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, Serialize, Deserialize)]
pub struct DashboardOverview {
    pub production_summary: ProductionSummary,
    pub machine_status: MachineStatusSummary,
    pub work_order_status: WorkOrderStatusSummary,
    pub quality_metrics: QualityMetrics,
    pub recent_activities: Vec<RecentActivity>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProductionSummary {
    pub tasks_completed_today: i64,
    pub tasks_in_progress: i64,
    pub tasks_pending: i64,
    pub total_work_orders: i64,
    pub on_time_delivery_rate: f64, // Percentage
    pub overall_efficiency: f64,    // Percentage
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MachineStatusSummary {
    pub total_machines: i64,
    pub available_machines: i64,
    pub in_use_machines: i64,
    pub maintenance_machines: i64,
    pub offline_machines: i64,
    pub utilization_rate: f64, // Percentage
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WorkOrderStatusSummary {
    pub pending_orders: i64,
    pub planned_orders: i64,
    pub in_progress_orders: i64,
    pub completed_orders: i64,
    pub cancelled_orders: i64,
    pub overdue_orders: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct QualityMetrics {
    pub total_inspections: i64,
    pub passed_inspections: i64,
    pub failed_inspections: i64,
    pub quality_rate: f64, // Percentage
    pub defect_rate: f64,  // Percentage
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RecentActivity {
    pub id: i32,
    pub activity_type: String,
    pub description: String,
    pub user_name: String,
    pub timestamp: DateTime<Utc>,
    pub entity_type: String,
    pub entity_id: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProductionReport {
    pub report_period: ReportPeriod,
    pub production_metrics: ProductionMetrics,
    pub machine_utilization: Vec<MachineUtilization>,
    pub work_order_performance: Vec<WorkOrderPerformance>,
    pub skill_group_performance: Vec<SkillGroupPerformance>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ReportPeriod {
    pub start_date: NaiveDate,
    pub end_date: NaiveDate,
    pub period_type: String, // "daily", "weekly", "monthly", "custom"
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProductionMetrics {
    pub total_tasks_completed: i64,
    pub total_production_time: i64, // in minutes
    pub average_task_duration: f64, // in minutes
    pub on_time_completion_rate: f64,
    pub efficiency_rate: f64,
    pub throughput: f64, // tasks per hour
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MachineUtilization {
    pub machine_id: i32,
    pub machine_name: String,
    pub skill_group_name: String,
    pub total_available_time: i64, // in minutes
    pub total_used_time: i64,      // in minutes
    pub utilization_rate: f64,     // percentage
    pub downtime: i64,             // in minutes
    pub maintenance_time: i64,     // in minutes
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WorkOrderPerformance {
    pub work_order_id: i32,
    pub project_name: String,
    pub part_number: String,
    pub planned_quantity: i32,
    pub completed_quantity: i32,
    pub planned_duration: i64, // in minutes
    pub actual_duration: i64,  // in minutes
    pub efficiency: f64,       // percentage
    pub status: String,
    pub due_date: Option<NaiveDate>,
    pub completion_date: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SkillGroupPerformance {
    pub skill_group_id: i32,
    pub skill_group_name: String,
    pub total_tasks: i64,
    pub completed_tasks: i64,
    pub average_task_time: f64,
    pub efficiency_rate: f64,
    pub utilization_rate: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct KPIMetrics {
    pub overall_equipment_effectiveness: f64, // OEE
    pub first_pass_yield: f64,
    pub cycle_time_variance: f64,
    pub schedule_adherence: f64,
    pub resource_utilization: f64,
    pub cost_per_unit: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TrendData {
    pub metric_name: String,
    pub time_series: Vec<TimeSeriesPoint>,
    pub trend_direction: String, // "up", "down", "stable"
    pub percentage_change: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TimeSeriesPoint {
    pub timestamp: DateTime<Utc>,
    pub value: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WeeklyTaskStats {
    pub day_name: String,
    pub date: NaiveDate,
    pub planned_tasks: i64,
    pub completed_tasks: i64,
    pub completion_rate: f64, // percentage
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WeeklyTaskSummary {
    pub week_start: NaiveDate,
    pub week_end: NaiveDate,
    pub daily_stats: Vec<WeeklyTaskStats>,
    pub total_planned: i64,
    pub total_completed: i64,
    pub overall_completion_rate: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DashboardQuery {
    pub start_date: Option<NaiveDate>,
    pub end_date: Option<NaiveDate>,
    pub skill_group_id: Option<i32>,
    pub machine_id: Option<i32>,
    pub project_id: Option<i32>,
    pub include_trends: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ReportRequest {
    pub report_type: String, // "production", "machine_utilization", "quality", "custom"
    pub period: ReportPeriod,
    pub filters: ReportFilters,
    pub format: String, // "json", "csv", "pdf"
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ReportFilters {
    pub skill_group_ids: Option<Vec<i32>>,
    pub machine_ids: Option<Vec<i32>>,
    pub project_ids: Option<Vec<i32>>,
    pub user_ids: Option<Vec<i32>>,
    pub include_cancelled: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AlertMetric {
    pub metric_name: String,
    pub current_value: f64,
    pub threshold_value: f64,
    pub alert_type: String, // "warning", "critical"
    pub description: String,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PerformanceAlert {
    pub id: i32,
    pub alert_type: String,
    pub severity: String,
    pub title: String,
    pub description: String,
    pub entity_type: String,
    pub entity_id: i32,
    pub threshold_value: Option<f64>,
    pub current_value: Option<f64>,
    pub created_at: DateTime<Utc>,
    pub acknowledged: bool,
    pub acknowledged_by: Option<String>,
    pub acknowledged_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CustomDashboard {
    pub dashboard_id: String,
    pub user_id: i32,
    pub dashboard_name: String,
    pub layout: DashboardLayout,
    pub widgets: Vec<DashboardWidget>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DashboardLayout {
    pub columns: i32,
    pub rows: i32,
    pub grid_size: String, // "small", "medium", "large"
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DashboardWidget {
    pub widget_id: String,
    pub widget_type: String, // "chart", "metric", "table", "gauge"
    pub title: String,
    pub position: WidgetPosition,
    pub size: WidgetSize,
    pub configuration: serde_json::Value,
    pub data_source: String,
    pub refresh_interval: i32, // in seconds
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WidgetPosition {
    pub x: i32,
    pub y: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WidgetSize {
    pub width: i32,
    pub height: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ExportRequest {
    pub export_type: String, // "dashboard", "report", "data"
    pub format: String,      // "pdf", "excel", "csv", "json"
    pub data_query: serde_json::Value,
    pub include_charts: bool,
    pub date_range: Option<ReportPeriod>,
}

// 操作员仪表盘相关模型
#[derive(Debug, Serialize, Deserialize)]
pub struct OperatorDashboard {
    pub current_task: Option<CurrentTask>,
    pub daily_stats: OperatorDailyStats,
    pub upcoming_tasks: Vec<UpcomingTask>,
    pub my_machines: Vec<OperatorMachine>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct CurrentTask {
    pub task_id: i32,
    pub work_order_id: i32,
    pub work_order_number: String,
    pub part_name: String,
    pub part_number: String,
    pub progress: i32,
    pub total: i32,
    pub estimated_completion: Option<String>,
    pub priority: String,
    pub project_name: String,
    pub process_name: String,
    pub started_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OperatorDailyStats {
    pub completed_tasks: i64,
    pub quality_rate: f64,
    pub efficiency: f64,
    pub working_hours: f64,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct UpcomingTask {
    pub id: i32,
    pub work_order_number: String,
    pub part_name: String,
    pub part_number: String,
    pub priority: String,
    pub estimated_start: Option<String>,
    pub project_name: String,
    pub process_name: String,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct OperatorMachine {
    pub machine_id: i32,
    pub machine_name: String,
    pub status: String,
    pub skill_group_name: String,
    pub is_primary: bool,
}

// 计划员仪表盘相关模型
#[derive(Debug, Serialize, Deserialize)]
pub struct PlannerDashboard {
    pub today_stats: PlannerStats,
    pub urgent_tasks: Vec<UrgentTask>,
    pub equipment_status: Vec<EquipmentStatus>,
    pub today_milestones: Vec<TodayMilestone>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PlannerStats {
    pub total_plans: i64,
    pub on_schedule: i64,
    pub delayed: i64,
    pub completed: i64,
    pub equipment_utilization: f64,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct UrgentTask {
    pub work_order_id: i32,
    pub work_order_number: String,
    pub part_name: String,
    pub part_number: String,
    pub due_date: Option<DateTime<Utc>>,
    pub progress: f64,
    pub status: String,
    pub assigned_to: Option<String>,
    pub project_name: String,
    pub plan_task_id: Option<i32>,
    pub planned_start: Option<DateTime<Utc>>,
    pub planned_end: Option<DateTime<Utc>>,
    pub skill_group_id: Option<i32>,
    pub skill_group_name: Option<String>,
    pub machine_id: Option<i32>,
    pub machine_name: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct EquipmentStatus {
    pub skill_group_name: String,
    pub total: i64,
    pub running: i64,
    pub idle: i64,
    pub maintenance: i64,
    pub utilization: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TodayMilestone {
    pub time: String,
    pub event: String,
    pub status: String, // pending, scheduled, critical, normal
}
