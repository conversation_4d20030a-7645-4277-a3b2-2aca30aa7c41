use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct PlanTask {
    pub id: i32,
    pub work_order_id: i32,
    pub routing_step_id: i32,
    pub skill_group_id: i32,
    pub machine_id: Option<i32>,
    pub planned_start: DateTime<Utc>,
    pub planned_end: DateTime<Utc>,
    pub status: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PlanTaskWithDetails {
    pub id: i32,
    pub work_order_id: i32,
    pub routing_step_id: i32,
    pub skill_group_id: i32,
    pub machine_id: Option<i32>,
    pub planned_start: DateTime<Utc>,
    pub planned_end: DateTime<Utc>,
    pub status: String,
    // Work order details
    pub work_order_quantity: i32,
    pub work_order_status: String,
    pub work_order_due_date: Option<chrono::NaiveDate>,
    // Project details
    pub project_id: i32,
    pub project_name: String,
    pub customer_name: Option<String>,
    // Part details
    pub part_id: i32,
    pub part_number: String,
    pub part_name: Option<String>,
    pub version: String,
    // Routing step details
    pub step_number: i32,
    pub process_name: String,
    pub work_instructions: Option<String>,
    pub standard_hours: Option<f64>,
    // Skill group details
    pub skill_group_name: String,
    // Machine details (optional)
    pub machine_name: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreatePlanTaskRequest {
    pub work_order_id: i32,
    pub routing_step_id: i32,
    pub skill_group_id: i32,
    pub machine_id: Option<i32>,
    pub planned_start: DateTime<Utc>,
    pub planned_end: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdatePlanTaskRequest {
    pub skill_group_id: Option<i32>,
    pub machine_id: Option<i32>, // None means don't update, Some(None) would be handled by nullable field
    pub planned_start: Option<DateTime<Utc>>,
    pub planned_end: Option<DateTime<Utc>>,
    pub status: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreatePlanTasksFromWorkOrderRequest {
    pub work_order_id: i32,
    pub start_date: DateTime<Utc>,
    pub skill_group_assignments: Option<Vec<SkillGroupAssignment>>,
    pub machine_assignments: Option<Vec<MachineAssignment>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BatchCreatePlanTasksRequest {
    pub creation_type: String, // "part", "project", "work_order"
    pub target_id: i32,        // part_id, project_id, or work_order_id
    pub start_date: DateTime<Utc>,
    pub skill_group_assignments: Option<Vec<SkillGroupAssignment>>,
    pub machine_assignments: Option<Vec<MachineAssignment>>,
    pub auto_schedule: Option<bool>, // 是否自动排程（工序间无缝衔接）
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SkillGroupAssignment {
    pub routing_step_id: i32,
    pub skill_group_id: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MachineAssignment {
    pub routing_step_id: i32,
    pub machine_id: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PlanTaskQuery {
    pub work_order_id: Option<i32>,
    pub skill_group_id: Option<i32>,
    pub machine_id: Option<i32>,
    pub part_id: Option<i32>,
    pub status: Option<String>,
    pub start_date: Option<DateTime<Utc>>,
    pub end_date: Option<DateTime<Utc>>,
    pub limit: Option<i64>,
    pub offset: Option<i64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PlanTaskSearchResult {
    pub plan_tasks: Vec<PlanTaskWithDetails>,
    pub total_count: i64,
    pub limit: i64,
    pub offset: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GanttChartData {
    pub tasks: Vec<GanttTask>,
    pub skill_groups: Vec<GanttSkillGroup>,
    pub time_range: GanttTimeRange,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GanttTask {
    pub id: i32,
    pub name: String,
    pub start: DateTime<Utc>,
    pub end: DateTime<Utc>,
    pub skill_group_id: i32,
    pub machine_id: Option<i32>,
    pub work_order_id: i32,
    pub part_number: String,
    pub process_name: String,
    pub status: String,
    pub progress: f32, // 0.0 to 1.0
    pub machine_name: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GanttSkillGroup {
    pub id: i32,
    pub name: String,
    pub machines: Vec<GanttMachine>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GanttMachine {
    pub id: i32,
    pub name: String,
    pub status: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GanttTimeRange {
    pub start: DateTime<Utc>,
    pub end: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PlanTaskStatusUpdate {
    pub status: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ReschedulePlanTaskRequest {
    pub planned_start: DateTime<Utc>,
    pub planned_end: DateTime<Utc>,
    pub skill_group_id: Option<i32>,
}

// Plan task status constants
pub const PLAN_TASK_STATUS_PLANNED: &str = "planned";
pub const PLAN_TASK_STATUS_SCHEDULED: &str = "scheduled";
pub const PLAN_TASK_STATUS_IN_PROGRESS: &str = "in_progress";
pub const PLAN_TASK_STATUS_COMPLETED: &str = "completed";
pub const PLAN_TASK_STATUS_CANCELLED: &str = "cancelled";
pub const PLAN_TASK_STATUS_ON_HOLD: &str = "on_hold";

pub fn get_valid_plan_task_statuses() -> Vec<&'static str> {
    vec![
        PLAN_TASK_STATUS_PLANNED,
        PLAN_TASK_STATUS_SCHEDULED,
        PLAN_TASK_STATUS_IN_PROGRESS,
        PLAN_TASK_STATUS_COMPLETED,
        PLAN_TASK_STATUS_CANCELLED,
        PLAN_TASK_STATUS_ON_HOLD,
    ]
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CapacityAnalysis {
    pub skill_group_id: i32,
    pub skill_group_name: String,
    pub total_capacity_hours: f64,
    pub scheduled_hours: f64,
    pub available_hours: f64,
    pub utilization_percentage: f64,
    pub overbooked: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ScheduleConflict {
    pub task_id: i32,
    pub conflicting_task_id: i32,
    pub skill_group_id: i32,
    pub overlap_start: DateTime<Utc>,
    pub overlap_end: DateTime<Utc>,
    pub conflict_type: String, // "resource_conflict", "time_overlap", etc.
}
