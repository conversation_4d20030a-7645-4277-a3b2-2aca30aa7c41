use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AutoWorkOrderConfig {
    pub id: i32,
    pub trigger_type: String, // "part_created", "routing_created", "bom_added"
    pub project_id: Option<i32>, // 特定项目，None表示全局
    pub is_enabled: bool,
    pub default_quantity: Option<i32>,
    pub default_due_days: Option<i32>, // 默认交期天数
    pub auto_create_plan_tasks: bool, // 是否自动创建计划任务
    pub created_by: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateAutoWorkOrderConfigRequest {
    pub trigger_type: String,
    pub project_id: Option<i32>,
    pub is_enabled: bool,
    pub default_quantity: Option<i32>,
    pub default_due_days: Option<i32>,
    pub auto_create_plan_tasks: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateAutoWorkOrderConfigRequest {
    pub is_enabled: Option<bool>,
    pub default_quantity: Option<i32>,
    pub default_due_days: Option<i32>,
    pub auto_create_plan_tasks: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AutoWorkOrderTriggerEvent {
    pub trigger_type: String,
    pub project_id: Option<i32>,
    pub part_id: Option<i32>,
    pub bom_id: Option<i32>,
    pub routing_id: Option<i32>,
    pub quantity: Option<i32>,
    pub user_id: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AutoWorkOrderResult {
    pub work_order_id: i32,
    pub plan_task_ids: Vec<i32>,
    pub trigger_config_id: i32,
    pub created_at: DateTime<Utc>,
}

// 触发类型常量
pub const TRIGGER_PART_CREATED: &str = "part_created";
pub const TRIGGER_ROUTING_CREATED: &str = "routing_created";
pub const TRIGGER_BOM_ADDED: &str = "bom_added";

pub fn get_valid_trigger_types() -> Vec<&'static str> {
    vec![
        TRIGGER_PART_CREATED,
        TRIGGER_ROUTING_CREATED,
        TRIGGER_BOM_ADDED,
    ]
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AutoWorkOrderConfigQuery {
    pub project_id: Option<i32>,
    pub trigger_type: Option<String>,
    pub is_enabled: Option<bool>,
    pub limit: Option<i64>,
    pub offset: Option<i64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AutoWorkOrderConfigResponse {
    pub configs: Vec<AutoWorkOrderConfig>,
    pub total_count: i64,
}

// 自动工单创建的策略
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AutoWorkOrderStrategy {
    pub use_bom_quantity: bool, // 是否使用BOM中的数量
    pub quantity_multiplier: f64, // 数量倍数
    pub due_date_strategy: DueDateStrategy, // 交期策略
    pub priority_level: String, // 优先级
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum DueDateStrategy {
    FixedDays(i32), // 固定天数后
    ProjectDueDate, // 使用项目交期
    Manual, // 手动设置
}

impl Default for AutoWorkOrderStrategy {
    fn default() -> Self {
        Self {
            use_bom_quantity: true,
            quantity_multiplier: 1.0,
            due_date_strategy: DueDateStrategy::FixedDays(30),
            priority_level: "normal".to_string(),
        }
    }
}
