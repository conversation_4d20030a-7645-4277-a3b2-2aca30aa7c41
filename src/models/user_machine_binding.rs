use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UserMachineBinding {
    pub id: i32,
    pub user_id: i32,
    pub machine_id: i32,
    pub is_primary: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UserMachineBindingWithDetails {
    pub id: i32,
    pub user_id: i32,
    pub machine_id: i32,
    pub machine_name: String,
    pub skill_group_name: String,
    pub machine_status: String,
    pub is_primary: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Deserialize)]
pub struct CreateUserMachineBindingRequest {
    pub machine_id: i32,
    pub is_primary: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateUserMachineBindingRequest {
    pub is_primary: Option<bool>,
}

#[derive(Debug, Serialize)]
pub struct UserMachineBindingsResponse {
    pub bindings: Vec<UserMachineBindingWithDetails>,
    pub available_machines: Vec<AvailableMachine>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AvailableMachine {
    pub id: i32,
    pub machine_name: String,
    pub skill_group_name: String,
    pub status: String,
    pub is_bound: bool,
}
