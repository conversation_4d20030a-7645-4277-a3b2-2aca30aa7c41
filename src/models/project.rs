use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct Project {
    pub id: i32,
    pub project_name: String,
    pub customer_name: Option<String>,
    pub status: String,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateProjectRequest {
    pub project_name: String,
    pub customer_name: Option<String>,
    pub status: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateProjectRequest {
    pub project_name: Option<String>,
    pub customer_name: Option<String>,
    pub status: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ProjectBom {
    pub id: i32,
    pub project_id: i32,
    pub part_id: i32,
    pub quantity: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectBomWithDetails {
    pub id: i32,
    pub project_id: i32,
    pub part_id: i32,
    pub part_number: String,
    pub part_name: Option<String>,
    pub version: String,
    pub quantity: i32,
    pub specifications: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateProjectBomRequest {
    pub part_id: i32,
    pub quantity: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateProjectBomRequest {
    pub quantity: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectWithBom {
    pub id: i32,
    pub project_name: String,
    pub customer_name: Option<String>,
    pub status: String,
    pub created_at: DateTime<Utc>,
    pub bom_items: Vec<ProjectBomWithDetails>,
}

// Project status constants
pub const PROJECT_STATUS_NORMAL: &str = "Normal";
pub const PROJECT_STATUS_PRIORITY: &str = "Priority";
pub const PROJECT_STATUS_PAUSED: &str = "Paused";

pub fn get_valid_project_statuses() -> Vec<&'static str> {
    vec![
        PROJECT_STATUS_NORMAL,
        PROJECT_STATUS_PRIORITY,
        PROJECT_STATUS_PAUSED,
    ]
}

pub fn is_valid_project_status(status: &str) -> bool {
    get_valid_project_statuses().contains(&status)
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectStatusUpdate {
    pub status: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectQuery {
    pub status: Option<String>,
    pub customer_name: Option<String>,
    pub limit: Option<i64>,
    pub offset: Option<i64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectSearchResult {
    pub projects: Vec<Project>,
    pub total_count: i64,
    pub limit: i64,
    pub offset: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectStatusStats {
    pub status: String,
    pub count: i64,
    pub percentage: f64,
}
