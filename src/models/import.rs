use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ImportJob {
    pub id: i32,
    pub user_id: i32,
    pub module_type: String,
    pub file_name: String,
    pub file_path: String,
    pub status: String,
    pub total_rows: Option<i32>,
    pub processed_rows: i32,
    pub success_rows: i32,
    pub error_rows: i32,
    pub error_details: Option<serde_json::Value>,
    pub created_at: DateTime<Utc>,
    pub started_at: Option<DateTime<Utc>>,
    pub completed_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ImportError {
    pub id: i32,
    pub import_job_id: i32,
    pub row_number: i32,
    pub column_name: Option<String>,
    pub error_type: String,
    pub error_message: String,
    pub row_data: Option<serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateImportJobRequest {
    pub module_type: String,
    pub file_name: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ImportPreviewRequest {
    pub file_id: String,
    pub module_type: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ImportExecuteRequest {
    pub file_id: String,
    pub module_type: String,
    pub options: Option<ImportOptions>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ImportOptions {
    pub skip_duplicates: Option<bool>,
    pub update_existing: Option<bool>,
    pub batch_size: Option<usize>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ImportPreviewResponse {
    pub headers: Vec<String>,
    pub sample_data: Vec<HashMap<String, String>>,
    pub total_rows: usize,
    pub validation_errors: Vec<ValidationError>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ValidationError {
    pub row_number: usize,
    pub column_name: Option<String>,
    pub error_type: String,
    pub error_message: String,
    pub row_data: HashMap<String, String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ImportResult {
    pub import_job_id: i32,
    pub status: String,
    pub total_rows: i32,
    pub processed_rows: i32,
    pub success_rows: i32,
    pub error_rows: i32,
    pub errors: Vec<ImportError>,
    pub duration_ms: Option<i64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ImportStatusResponse {
    pub import_job: ImportJob,
    pub errors: Vec<ImportError>,
    pub progress_percentage: f64,
}

// 导入模块类型常量
pub const MODULE_PROJECTS: &str = "projects";
pub const MODULE_PARTS: &str = "parts";
pub const MODULE_BOM: &str = "bom";
pub const MODULE_ROUTINGS: &str = "routings";
pub const MODULE_MACHINES: &str = "machines";
pub const MODULE_USERS: &str = "users";
pub const MODULE_SKILL_GROUPS: &str = "skill_groups";

// 导入状态常量
pub const STATUS_PENDING: &str = "pending";
pub const STATUS_PROCESSING: &str = "processing";
pub const STATUS_COMPLETED: &str = "completed";
pub const STATUS_FAILED: &str = "failed";
pub const STATUS_CANCELLED: &str = "cancelled";

// 错误类型常量
pub const ERROR_TYPE_VALIDATION: &str = "validation";
pub const ERROR_TYPE_DUPLICATE: &str = "duplicate";
pub const ERROR_TYPE_REFERENCE: &str = "reference";
pub const ERROR_TYPE_FORMAT: &str = "format";
pub const ERROR_TYPE_BUSINESS: &str = "business";

pub fn get_valid_module_types() -> Vec<&'static str> {
    vec![
        MODULE_PROJECTS,
        MODULE_PARTS,
        MODULE_BOM,
        MODULE_ROUTINGS,
        MODULE_MACHINES,
        MODULE_USERS,
        MODULE_SKILL_GROUPS,
    ]
}

pub fn get_valid_import_statuses() -> Vec<&'static str> {
    vec![
        STATUS_PENDING,
        STATUS_PROCESSING,
        STATUS_COMPLETED,
        STATUS_FAILED,
        STATUS_CANCELLED,
    ]
}

// 模块字段映射配置
#[derive(Debug, Serialize, Deserialize)]
pub struct ModuleFieldMapping {
    pub module_type: String,
    pub fields: Vec<FieldMapping>,
    pub required_fields: Vec<String>,
    pub unique_fields: Vec<Vec<String>>, // 复合唯一键
}

#[derive(Debug, Serialize, Deserialize)]
pub struct FieldMapping {
    pub csv_header: String,      // CSV列标题（中文）
    pub db_field: String,        // 数据库字段名
    pub field_type: String,      // 字段类型: string, integer, decimal, date, boolean
    pub required: bool,          // 是否必填
    pub max_length: Option<usize>, // 最大长度
    pub validation_regex: Option<String>, // 验证正则表达式
}

// 获取模块字段映射配置
pub fn get_module_field_mapping(module_type: &str) -> Option<ModuleFieldMapping> {
    match module_type {
        MODULE_PROJECTS => Some(ModuleFieldMapping {
            module_type: MODULE_PROJECTS.to_string(),
            fields: vec![
                FieldMapping {
                    csv_header: "项目名称".to_string(),
                    db_field: "project_name".to_string(),
                    field_type: "string".to_string(),
                    required: true,
                    max_length: Some(255),
                    validation_regex: None,
                },
                FieldMapping {
                    csv_header: "客户名称".to_string(),
                    db_field: "customer_name".to_string(),
                    field_type: "string".to_string(),
                    required: false,
                    max_length: Some(255),
                    validation_regex: None,
                },
            ],
            required_fields: vec!["项目名称".to_string()],
            unique_fields: vec![vec!["项目名称".to_string()]],
        }),
        MODULE_PARTS => Some(ModuleFieldMapping {
            module_type: MODULE_PARTS.to_string(),
            fields: vec![
                FieldMapping {
                    csv_header: "零件编号".to_string(),
                    db_field: "part_number".to_string(),
                    field_type: "string".to_string(),
                    required: true,
                    max_length: Some(255),
                    validation_regex: None,
                },
                FieldMapping {
                    csv_header: "零件名称".to_string(),
                    db_field: "part_name".to_string(),
                    field_type: "string".to_string(),
                    required: false,
                    max_length: Some(255),
                    validation_regex: None,
                },
                FieldMapping {
                    csv_header: "版本".to_string(),
                    db_field: "version".to_string(),
                    field_type: "string".to_string(),
                    required: true,
                    max_length: Some(50),
                    validation_regex: None,
                },
                FieldMapping {
                    csv_header: "规格说明".to_string(),
                    db_field: "specifications".to_string(),
                    field_type: "string".to_string(),
                    required: false,
                    max_length: None,
                    validation_regex: None,
                },
            ],
            required_fields: vec!["零件编号".to_string(), "版本".to_string()],
            unique_fields: vec![vec!["零件编号".to_string(), "版本".to_string()]],
        }),
        MODULE_SKILL_GROUPS => Some(ModuleFieldMapping {
            module_type: MODULE_SKILL_GROUPS.to_string(),
            fields: vec![
                FieldMapping {
                    csv_header: "技能组名称".to_string(),
                    db_field: "group_name".to_string(),
                    field_type: "string".to_string(),
                    required: true,
                    max_length: Some(100),
                    validation_regex: None,
                },
            ],
            required_fields: vec!["技能组名称".to_string()],
            unique_fields: vec![vec!["技能组名称".to_string()]],
        }),
        MODULE_MACHINES => Some(ModuleFieldMapping {
            module_type: MODULE_MACHINES.to_string(),
            fields: vec![
                FieldMapping {
                    csv_header: "设备名称".to_string(),
                    db_field: "machine_name".to_string(),
                    field_type: "string".to_string(),
                    required: true,
                    max_length: Some(255),
                    validation_regex: None,
                },
                FieldMapping {
                    csv_header: "技能组".to_string(),
                    db_field: "skill_group_name".to_string(),
                    field_type: "string".to_string(),
                    required: true,
                    max_length: Some(100),
                    validation_regex: None,
                },
                FieldMapping {
                    csv_header: "状态".to_string(),
                    db_field: "status".to_string(),
                    field_type: "string".to_string(),
                    required: false,
                    max_length: Some(50),
                    validation_regex: None,
                },
            ],
            required_fields: vec!["设备名称".to_string(), "技能组".to_string()],
            unique_fields: vec![vec!["设备名称".to_string()]],
        }),
        MODULE_BOM => Some(ModuleFieldMapping {
            module_type: MODULE_BOM.to_string(),
            fields: vec![
                FieldMapping {
                    csv_header: "项目名称".to_string(),
                    db_field: "project_name".to_string(),
                    field_type: "string".to_string(),
                    required: true,
                    max_length: Some(255),
                    validation_regex: None,
                },
                FieldMapping {
                    csv_header: "零件编号".to_string(),
                    db_field: "part_number".to_string(),
                    field_type: "string".to_string(),
                    required: true,
                    max_length: Some(255),
                    validation_regex: None,
                },
                FieldMapping {
                    csv_header: "零件版本".to_string(),
                    db_field: "part_version".to_string(),
                    field_type: "string".to_string(),
                    required: true,
                    max_length: Some(50),
                    validation_regex: None,
                },
                FieldMapping {
                    csv_header: "数量".to_string(),
                    db_field: "quantity".to_string(),
                    field_type: "integer".to_string(),
                    required: true,
                    max_length: None,
                    validation_regex: None,
                },
            ],
            required_fields: vec!["项目名称".to_string(), "零件编号".to_string(), "零件版本".to_string(), "数量".to_string()],
            unique_fields: vec![vec!["项目名称".to_string(), "零件编号".to_string(), "零件版本".to_string()]],
        }),
        _ => None,
    }
}
