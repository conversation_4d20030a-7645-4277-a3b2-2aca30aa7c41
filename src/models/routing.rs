use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, <PERSON><PERSON>, Serial<PERSON>, Deserialize, FromRow)]
pub struct Routing {
    pub id: i32,
    pub part_id: i32,
    pub step_number: i32,
    pub process_name: String,
    pub work_instructions: Option<String>,
    pub standard_hours: Option<f64>,
    pub skill_group_id: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RoutingWithPartInfo {
    pub id: i32,
    pub part_id: i32,
    pub part_number: String,
    pub part_name: Option<String>,
    pub version: String,
    pub step_number: i32,
    pub process_name: String,
    pub work_instructions: Option<String>,
    pub standard_hours: Option<f64>,
    pub skill_group_id: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateRoutingRequest {
    pub part_id: i32,
    pub step_number: i32,
    pub process_name: String,
    pub work_instructions: Option<String>,
    pub standard_hours: Option<f64>,
    pub skill_group_id: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateRoutingRequest {
    pub process_name: Option<String>,
    pub work_instructions: Option<String>,
    pub standard_hours: Option<f64>,
    pub skill_group_id: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PartRoutingSteps {
    pub part_id: i32,
    pub part_number: String,
    pub part_name: Option<String>,
    pub version: String,
    pub routing_steps: Vec<RoutingStep>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RoutingStep {
    pub id: i32,
    pub step_number: i32,
    pub process_name: String,
    pub work_instructions: Option<String>,
    pub standard_hours: Option<f64>,
    pub skill_group_id: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RoutingQuery {
    pub part_id: Option<i32>,
    pub process_name: Option<String>,
}
