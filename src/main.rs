use axum::{
    Router,
    middleware::from_fn_with_state,
    routing::{get, post, put, delete},
};
use std::net::SocketAddr;
use tower_http::cors::CorsLayer;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
use sqlx::PgPool;

mod handlers;
mod middleware;
mod models;
mod services;
mod utils;

use services::cache_service::CacheService;

#[derive(Clone)]
pub struct AppState {
    pub db: PgPool,
    pub cache: Option<CacheService>,
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // Load environment variables
    dotenvy::dotenv().ok();

    // Initialize tracing
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "mes_system=info,tower_http=info".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    // Initialize health monitoring
    handlers::health::init_health_monitor();

    // Initialize database connection
    tracing::info!("Connecting to database...");
    let pool = utils::database::create_pool()
        .await
        .expect("Failed to create database pool");

    // Initialize cache service (optional)
    let cache_service = match std::env::var("REDIS_URL") {
        Ok(redis_url) => {
            tracing::info!("Connecting to Redis cache...");
            match CacheService::new(&redis_url) {
                Ok(cache) => {
                    if cache.health_check().await {
                        tracing::info!("Redis cache connected successfully");
                        Some(cache)
                    } else {
                        tracing::warn!("Redis health check failed, running without cache");
                        None
                    }
                }
                Err(e) => {
                    tracing::warn!("Failed to connect to Redis: {}, running without cache", e);
                    None
                }
            }
        }
        Err(_) => {
            tracing::info!("REDIS_URL not set, running without cache");
            None
        }
    };

    // Create application state
    let app_state = AppState {
        db: pool.clone(),
        cache: cache_service,
    };

    // Run migrations
    tracing::info!("Running database migrations...");
    // Temporarily skip migrations to resolve version conflict
    // utils::database::run_migrations(&app_state.db)
    //     .await
    //     .expect("Failed to run migrations");

    tracing::info!("Starting MES System...");

    // Start metrics cleanup task
    middleware::metrics_middleware::start_metrics_cleanup_task();

    // Protected routes that require authentication
    let protected_routes = Router::new()
        .route("/api/auth/me", get(handlers::auth::get_current_user))
        .route("/api/auth/refresh", post(handlers::auth::refresh_token))
        .route("/api/auth/users", post(handlers::auth::create_user))
        // User profile routes
        .route("/api/user/profile", put(handlers::users::update_profile))
        .route("/api/user/password", put(handlers::users::change_password))
        // User management routes
        .route("/api/users", get(handlers::users::get_all_users))
        // Debug endpoint - commented out for production
        // .route(
        //     "/api/users/debug/skills",
        //     get(handlers::users::debug_user_skills),
        // )
        .route("/api/users/:id", get(handlers::users::get_user_by_id))
        .route(
            "/api/users/:id/status",
            post(handlers::users::update_user_status),
        )
        .route(
            "/api/users/:id/roles",
            post(handlers::users::update_user_roles),
        )
        .route(
            "/api/users/:id/skills",
            post(handlers::users::update_user_skills),
        )
        .route(
            "/api/users/:id",
            axum::routing::delete(handlers::users::delete_user),
        )
        // User machine bindings routes
        .route(
            "/api/user/machine-bindings",
            get(handlers::user_machine_bindings::get_user_machine_bindings),
        )
        .route(
            "/api/user/machine-bindings",
            post(handlers::user_machine_bindings::create_machine_binding),
        )
        .route(
            "/api/user/machine-bindings/:id",
            put(handlers::user_machine_bindings::update_machine_binding),
        )
        .route(
            "/api/user/machine-bindings/:id",
            delete(handlers::user_machine_bindings::delete_machine_binding),
        )
        .route(
            "/api/user/primary-machine",
            get(handlers::user_machine_bindings::get_user_primary_machine),
        )
        // Auto work order configuration routes
        .route(
            "/api/auto-work-orders/configs",
            get(handlers::auto_work_orders::get_auto_work_order_configs),
        )
        .route(
            "/api/auto-work-orders/configs",
            post(handlers::auto_work_orders::create_auto_work_order_config),
        )
        .route(
            "/api/auto-work-orders/configs/:id",
            put(handlers::auto_work_orders::update_auto_work_order_config),
        )
        .route(
            "/api/auto-work-orders/configs/:id",
            delete(handlers::auto_work_orders::delete_auto_work_order_config),
        )
        .route(
            "/api/auto-work-orders/trigger",
            post(handlers::auto_work_orders::trigger_auto_work_order_creation),
        )
        // Machine management routes
        .route("/api/machines", get(handlers::machines::get_all_machines))
        .route("/api/machines", post(handlers::machines::create_machine))
        .route(
            "/api/machines/:id",
            get(handlers::machines::get_machine_by_id),
        )
        .route(
            "/api/machines/:id",
            axum::routing::put(handlers::machines::update_machine),
        )
        .route(
            "/api/machines/:id",
            axum::routing::delete(handlers::machines::delete_machine),
        )
        // Role management routes
        .route("/api/roles", post(handlers::roles::create_role))
        .route("/api/roles/:id", axum::routing::put(handlers::roles::update_role))
        .route("/api/roles/:id", axum::routing::delete(handlers::roles::delete_role))
        .route("/api/roles/:id/dependencies", get(handlers::roles::check_role_dependencies))
        // Permission management routes
        .route("/api/permissions", get(handlers::permissions::get_all_permissions))
        .route("/api/permissions", post(handlers::permissions::create_permission))
        .route("/api/user/permissions", get(handlers::permissions::get_user_permissions))
        .route("/api/roles/:id/permissions", get(handlers::permissions::get_role_permissions))
        .route("/api/roles/:id/permissions", axum::routing::put(handlers::permissions::update_role_permissions))
        // Skill group management routes
        .route("/api/skill-groups", get(handlers::auth::get_skill_groups))
        .route("/api/skill-groups", post(handlers::skill_groups::create_skill_group))
        .route("/api/skill-groups/:id", axum::routing::put(handlers::skill_groups::update_skill_group))
        .route("/api/skill-groups/:id", axum::routing::delete(handlers::skill_groups::delete_skill_group))
        .route("/api/skill-groups/:id/dependencies", get(handlers::skill_groups::check_skill_group_dependencies))
        .route(
            "/api/machines/:id/status",
            post(handlers::machines::update_machine_status),
        )
        .route(
            "/api/machines/:id/tasks",
            get(handlers::machines::get_machine_tasks),
        )
        // Part management routes
        .route("/api/parts", get(handlers::parts::get_all_parts))
        .route("/api/parts", post(handlers::parts::create_part))
        .route("/api/parts/:id", get(handlers::parts::get_part_by_id))
        .route(
            "/api/parts/:id",
            axum::routing::put(handlers::parts::update_part),
        )
        .route(
            "/api/parts/:id",
            axum::routing::delete(handlers::parts::delete_part),
        )
        .route(
            "/api/parts/:id/projects",
            get(handlers::parts::get_part_projects),
        )
        // Project management routes
        .route("/api/projects", get(handlers::projects::get_all_projects))
        .route("/api/projects", post(handlers::projects::create_project))
        .route(
            "/api/projects/:id",
            get(handlers::projects::get_project_by_id),
        )
        .route(
            "/api/projects/:id/full",
            get(handlers::projects::get_project_with_bom),
        )
        .route(
            "/api/projects/:id/completion-status",
            get(handlers::projects::get_project_completion_status),
        )
        .route(
            "/api/projects/:id",
            axum::routing::put(handlers::projects::update_project),
        )
        .route(
            "/api/projects/:id",
            axum::routing::delete(handlers::projects::delete_project),
        )
        .route(
            "/api/projects/:id/status",
            post(handlers::projects::update_project_status),
        )
        .route(
            "/api/projects/status/stats",
            get(handlers::projects::get_project_status_stats),
        )
        // BOM management routes
        .route(
            "/api/projects/:id/bom",
            get(handlers::projects::get_project_bom),
        )
        .route(
            "/api/projects/:id/bom",
            post(handlers::projects::add_bom_item),
        )
        .route(
            "/api/bom/:id",
            axum::routing::put(handlers::projects::update_bom_item),
        )
        .route(
            "/api/bom/:id",
            axum::routing::delete(handlers::projects::remove_bom_item),
        )
        // Routing management routes
        .route("/api/routings", get(handlers::routings::get_all_routings))
        .route("/api/routings", post(handlers::routings::create_routing))
        .route("/api/routings/:id", get(handlers::routings::get_routing_by_id))
        .route("/api/routings/:id", axum::routing::put(handlers::routings::update_routing))
        .route("/api/routings/:id", axum::routing::delete(handlers::routings::delete_routing))
        .route("/api/parts/:id/routing", get(handlers::routings::get_part_routing))
        .route("/api/parts/:id/routing/reorder", post(handlers::routings::reorder_routing_steps))
        .route("/api/parts/:id/routing/copy", post(handlers::routings::copy_routing))
        // Work order management routes
        .route(
            "/api/work-orders",
            get(handlers::work_orders::get_all_work_orders),
        )
        .route(
            "/api/work-orders",
            post(handlers::work_orders::create_work_order),
        )
        .route(
            "/api/work-orders/:id",
            get(handlers::work_orders::get_work_order_by_id),
        )
        .route(
            "/api/work-orders/:id",
            axum::routing::put(handlers::work_orders::update_work_order),
        )
        .route(
            "/api/work-orders/:id",
            axum::routing::delete(handlers::work_orders::delete_work_order),
        )
        .route(
            "/api/work-orders/:id/status",
            post(handlers::work_orders::update_work_order_status),
        )
        .route(
            "/api/projects/:id/work-orders",
            get(handlers::work_orders::get_work_orders_by_project),
        )
        .route(
            "/api/projects/work-orders/create",
            post(handlers::work_orders::create_work_orders_from_project),
        )
        // Production planning routes
        .route(
            "/api/plan-tasks",
            get(handlers::plan_tasks::get_all_plan_tasks),
        )
        .route(
            "/api/plan-tasks",
            post(handlers::plan_tasks::create_plan_task),
        )
        .route(
            "/api/plan-tasks/:id",
            get(handlers::plan_tasks::get_plan_task_by_id),
        )
        .route(
            "/api/plan-tasks/:id",
            axum::routing::put(handlers::plan_tasks::update_plan_task),
        )
        .route(
            "/api/plan-tasks/:id",
            axum::routing::delete(handlers::plan_tasks::delete_plan_task),
        )
        .route(
            "/api/plan-tasks/:id/status",
            post(handlers::plan_tasks::update_plan_task_status),
        )
        .route(
            "/api/plan-tasks/:id/reschedule",
            post(handlers::plan_tasks::reschedule_plan_task),
        )
        .route(
            "/api/work-orders/:id/plan-tasks",
            post(handlers::plan_tasks::create_plan_tasks_from_work_order),
        )
        .route(
            "/api/plan-tasks/batch-create",
            post(handlers::plan_tasks::batch_create_plan_tasks),
        )
        .route(
            "/api/planning/gantt",
            get(handlers::plan_tasks::get_gantt_chart_data),
        )
        .route(
            "/api/planning/plan-tasks",
            get(handlers::plan_tasks::get_all_plan_tasks),
        )
        // Execution tracking routes
        .route(
            "/api/execution/logs",
            get(handlers::execution::get_execution_logs),
        )
        .route(
            "/api/execution/logs",
            post(handlers::execution::create_execution_log),
        )
        .route(
            "/api/execution/tasks/start",
            post(handlers::execution::start_task),
        )
        .route(
            "/api/execution/tasks/complete",
            post(handlers::execution::complete_task),
        )
        .route(
            "/api/execution/tasks/pause",
            post(handlers::execution::pause_task),
        )
        .route(
            "/api/execution/tasks/resume",
            post(handlers::execution::resume_task),
        )
        .route(
            "/api/execution/barcode/validate",
            post(handlers::execution::validate_barcode),
        )
        .route(
            "/api/execution/tasks/active",
            get(handlers::execution::get_active_tasks),
        )
        .route(
            "/api/execution/dashboard",
            get(handlers::execution::get_shop_floor_dashboard),
        )
        .route(
            "/api/execution/my-tasks",
            get(handlers::execution::get_my_skill_group_tasks),
        )
        // Dashboard and reporting routes
        .route(
            "/api/dashboard/overview",
            get(handlers::dashboard::get_dashboard_overview),
        )
        .route(
            "/api/dashboard/production-summary",
            get(handlers::dashboard::get_production_summary),
        )
        .route(
            "/api/dashboard/work-order-status",
            get(handlers::dashboard::get_work_order_status),
        )
        .route(
            "/api/dashboard/machine-utilization",
            get(handlers::dashboard::get_machine_utilization),
        )
        .route(
            "/api/dashboard/skill-group-performance",
            get(handlers::dashboard::get_skill_group_performance),
        )
        .route(
            "/api/dashboard/recent-activities",
            get(handlers::dashboard::get_recent_activities),
        )
        .route(
            "/api/dashboard/weekly-task-stats",
            get(handlers::dashboard::get_weekly_task_stats),
        )
        .route(
            "/api/dashboard/kpi-metrics",
            get(handlers::dashboard::get_kpi_metrics),
        )
        .route(
            "/api/dashboard/trends",
            get(handlers::dashboard::get_trend_data),
        )
        .route(
            "/api/dashboard/operator/:user_id",
            get(handlers::dashboard::get_operator_dashboard),
        )
        .route(
            "/api/dashboard/planner",
            get(handlers::dashboard::get_planner_dashboard),
        )
        .route(
            "/api/reports/production",
            post(handlers::dashboard::get_production_report),
        )
        // Quality management routes
        .route(
            "/api/quality/inspections",
            get(handlers::quality::get_all_quality_inspections),
        )
        .route(
            "/api/quality/inspections",
            post(handlers::quality::create_quality_inspection),
        )
        .route(
            "/api/quality/inspections/:id",
            get(handlers::quality::get_quality_inspection_by_id),
        )
        .route(
            "/api/quality/inspections/:id",
            axum::routing::put(handlers::quality::update_quality_inspection),
        )
        .route(
            "/api/quality/checkpoints",
            post(handlers::quality::create_quality_checkpoint),
        )
        .route(
            "/api/quality/reports",
            post(handlers::quality::get_quality_report),
        )
        .route(
            "/api/quality/metrics",
            get(handlers::quality::get_quality_metrics),
        )
        .route(
            "/api/quality/pending",
            get(handlers::quality::get_pending_inspections),
        )
        // Audit logging routes
        .route("/api/audit/logs", get(handlers::audit::get_audit_logs))
        .route(
            "/api/audit/trail/:entity_type/:entity_id",
            get(handlers::audit::get_audit_trail),
        )
        .route(
            "/api/audit/summary",
            get(handlers::audit::get_audit_summary),
        )
        .route(
            "/api/audit/statistics",
            get(handlers::audit::get_audit_statistics),
        )
        .route(
            "/api/audit/reports",
            post(handlers::audit::generate_audit_report),
        )
        .route("/api/audit/export", get(handlers::audit::export_audit_logs))
        .route(
            "/api/audit/cleanup",
            post(handlers::audit::cleanup_old_audit_logs),
        )
        // System configuration routes
        .route(
            "/api/system/configs",
            get(handlers::system_config::get_system_configs),
        )
        .route(
            "/api/system/configs",
            post(handlers::system_config::create_config),
        )
        .route(
            "/api/system/configs/categories",
            get(handlers::system_config::get_configs_by_category),
        )
        .route(
            "/api/system/configs/:key",
            get(handlers::system_config::get_config_by_key),
        )
        .route(
            "/api/system/configs/:key",
            axum::routing::put(handlers::system_config::update_config),
        )
        .route(
            "/api/system/plan-assignment-mode",
            get(handlers::system_config::get_plan_assignment_mode),
        )
        .route(
            "/api/system/plan-assignment-mode",
            post(handlers::system_config::set_plan_assignment_mode),
        )
        // Data import routes
        .route(
            "/api/import/upload",
            post(handlers::import::upload_import_file),
        )
        .route(
            "/api/import/:import_job_id/preview",
            get(handlers::import::preview_import_data),
        )
        .route(
            "/api/import/:import_job_id/execute",
            post(handlers::import::execute_import),
        )
        .route(
            "/api/import/:import_job_id/status",
            get(handlers::import::get_import_status),
        )
        .route(
            "/api/import/template",
            get(handlers::import::download_template),
        )
        .route(
            "/api/import/history",
            get(handlers::import::get_import_history),
        )
        // TODO: Shift management routes (需要复杂的类型修复，暂时禁用)
        // .route("/api/shifts/templates", get(handlers::shift::get_shift_templates))
        // .route("/api/shifts/templates", post(handlers::shift::create_shift_template))
        // Health and monitoring routes (admin only)
        .route("/health/detailed", get(handlers::health::detailed_health_check))
        .route("/metrics", get(handlers::health::get_application_metrics))
        .route("/metrics/requests", get(handlers::health::get_recent_request_logs))
        .route("/metrics/dashboard", get(handlers::health::get_performance_dashboard))
        .route("/metrics/system", get(handlers::health::system_metrics))
        .layer(from_fn_with_state(
            pool.clone(),
            middleware::auth::auth_middleware,
        ));

    // Build our application with routes
    let app = Router::new()
        .route("/", get(root))
        .route("/health", get(handlers::health::health_check))
        // Public auth routes
        .route("/api/auth/login", post(handlers::auth::login))
        .route("/api/auth/roles", get(handlers::auth::get_roles))
        .route(
            "/api/auth/skill-groups",
            get(handlers::auth::get_skill_groups),
        )
        // Temporary public plan-tasks routes - commented out for production
        // .route(
        //     "/api/plan-tasks",
        //     get(handlers::plan_tasks::get_all_plan_tasks_public)
        //         .post(handlers::plan_tasks::create_plan_task_public),
        // )
        // Merge protected routes
        .merge(protected_routes)
        .with_state(pool)
        .layer(CorsLayer::permissive());

    // Get server configuration from environment
    let host = std::env::var("SERVER_HOST").unwrap_or_else(|_| "0.0.0.0".to_string());
    let port = std::env::var("SERVER_PORT")
        .unwrap_or_else(|_| "9000".to_string())
        .parse::<u16>()?;
    let enable_ipv6 = std::env::var("ENABLE_IPV6")
        .unwrap_or_else(|_| "true".to_string())
        .parse::<bool>()
        .unwrap_or(true);

    // Try to bind to dual-stack (IPv4 + IPv6) first
    if enable_ipv6 {
        // Try dual-stack binding first
        let dual_stack_addr = SocketAddr::from(([0, 0, 0, 0, 0, 0, 0, 0], port));
        match tokio::net::TcpListener::bind(dual_stack_addr).await {
            Ok(listener) => {
                tracing::info!("Server listening on dual-stack (IPv4 + IPv6): [::]:{}", port);
                axum::serve(listener, app).await?;
                return Ok(());
            }
            Err(e) => {
                tracing::warn!("Failed to bind dual-stack listener: {}. Trying IPv4 only.", e);
            }
        }
    }

    // Fallback to IPv4 only
    let ipv4_addr = SocketAddr::from(([0, 0, 0, 0], port));
    let ipv4_listener = tokio::net::TcpListener::bind(ipv4_addr).await?;
    tracing::info!("Server listening on IPv4 only: {}:{}", host, port);

    // Run the IPv4 server
    axum::serve(ipv4_listener, app).await?;

    Ok(())
}

async fn root() -> &'static str {
    "MES System API - Running"
}

async fn health_check() -> &'static str {
    "OK"
}
