use serde::{Deserialize, Serialize};
use validator::<PERSON>ida<PERSON>;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize, <PERSON><PERSON><PERSON>)]
pub struct LoginRequest {
    #[validate(length(
        min = 3,
        max = 100,
        message = "Username must be between 3 and 100 characters"
    ))]
    pub username: String,

    #[validate(length(min = 6, message = "Password must be at least 6 characters"))]
    pub password: String,
}

#[derive(Debug, Serial<PERSON>, Deserialize, <PERSON>ida<PERSON>)]
pub struct CreateUserRequest {
    #[validate(length(
        min = 3,
        max = 100,
        message = "Username must be between 3 and 100 characters"
    ))]
    pub username: String,

    #[validate(length(min = 6, message = "Password must be at least 6 characters"))]
    pub password: String,

    #[validate(length(max = 100, message = "Full name must not exceed 100 characters"))]
    pub full_name: Option<String>,

    pub role_ids: Vec<i32>,
    pub skill_group_ids: Vec<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LoginResponse {
    pub token: String,
    pub user: UserInfo,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserInfo {
    pub id: i32,
    pub username: String,
    pub full_name: Option<String>,
    pub roles: Vec<String>,
    pub skills: Vec<String>,
    pub is_active: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorResponse {
    pub error: String,
    pub message: String,
}
