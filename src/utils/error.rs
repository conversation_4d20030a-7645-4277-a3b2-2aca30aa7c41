use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde::{Deserialize, Serialize};
use chrono::Utc;
use tracing::{error, warn, info};

#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorResponse {
    pub error: ErrorDetail,
    pub timestamp: String,
    pub path: Option<String>,
    pub request_id: Option<String>,
    pub user_id: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorDetail {
    pub code: String,
    pub message: String,
    pub details: Option<String>,
}

#[derive(Debug)]
pub struct AppError {
    pub code: String,
    pub message: String,
    pub details: Option<String>,
    pub status_code: StatusCode,
}

impl AppError {
    pub fn new(code: String, message: String, status_code: StatusCode) -> Self {
        Self {
            code,
            message,
            details: None,
            status_code,
        }
    }

    pub fn with_details(code: String, message: String, details: String, status_code: StatusCode) -> Self {
        Self {
            code,
            message,
            details: Some(details),
            status_code,
        }
    }

    // 常用错误类型的便捷构造函数

    /// 数据验证错误
    pub fn validation_error(message: String) -> Self {
        Self::new(
            "VALIDATION_ERROR".to_string(),
            message,
            StatusCode::BAD_REQUEST,
        )
    }

    /// 资源未找到错误
    pub fn not_found(resource: &str) -> Self {
        Self::new(
            "RESOURCE_NOT_FOUND".to_string(),
            format!("{} 不存在", resource),
            StatusCode::NOT_FOUND,
        )
    }

    /// 未授权错误
    pub fn unauthorized(message: Option<String>) -> Self {
        Self::new(
            "UNAUTHORIZED".to_string(),
            message.unwrap_or_else(|| "请先登录".to_string()),
            StatusCode::UNAUTHORIZED,
        )
    }

    /// 权限不足错误
    pub fn forbidden(message: Option<String>) -> Self {
        Self::new(
            "FORBIDDEN".to_string(),
            message.unwrap_or_else(|| "权限不足".to_string()),
            StatusCode::FORBIDDEN,
        )
    }

    /// 数据冲突错误
    pub fn conflict(message: String) -> Self {
        Self::new(
            "CONFLICT_ERROR".to_string(),
            message,
            StatusCode::CONFLICT,
        )
    }

    /// 业务逻辑错误
    pub fn business_error(message: String) -> Self {
        Self::new(
            "BUSINESS_LOGIC_ERROR".to_string(),
            message,
            StatusCode::BAD_REQUEST,
        )
    }

    /// 数据库错误
    pub fn database_error(details: String) -> Self {
        Self::with_details(
            "DATABASE_ERROR".to_string(),
            "数据库操作失败".to_string(),
            details,
            StatusCode::INTERNAL_SERVER_ERROR,
        )
    }

    /// 内部服务器错误
    pub fn internal_server_error(message: String) -> Self {
        Self::new(
            "INTERNAL_SERVER_ERROR".to_string(),
            message,
            StatusCode::INTERNAL_SERVER_ERROR,
        )
    }

    /// 请求超时错误
    pub fn timeout_error() -> Self {
        Self::new(
            "TIMEOUT_ERROR".to_string(),
            "请求超时".to_string(),
            StatusCode::REQUEST_TIMEOUT,
        )
    }

    /// 请求过于频繁错误
    pub fn rate_limit_error() -> Self {
        Self::new(
            "RATE_LIMIT_ERROR".to_string(),
            "请求过于频繁，请稍后再试".to_string(),
            StatusCode::TOO_MANY_REQUESTS,
        )
    }

    /// 服务不可用错误
    pub fn service_unavailable() -> Self {
        Self::new(
            "SERVICE_UNAVAILABLE".to_string(),
            "服务暂时不可用".to_string(),
            StatusCode::SERVICE_UNAVAILABLE,
        )
    }
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        // 根据错误级别记录日志
        match self.status_code {
            StatusCode::INTERNAL_SERVER_ERROR => {
                error!(
                    error_code = %self.code,
                    error_message = %self.message,
                    error_details = ?self.details,
                    "Internal server error occurred"
                );
            }
            StatusCode::BAD_REQUEST | StatusCode::CONFLICT => {
                warn!(
                    error_code = %self.code,
                    error_message = %self.message,
                    "Client error occurred"
                );
            }
            _ => {
                info!(
                    error_code = %self.code,
                    error_message = %self.message,
                    "Request error occurred"
                );
            }
        }

        let error_response = ErrorResponse {
            error: ErrorDetail {
                code: self.code.clone(),
                message: self.message.clone(),
                details: self.details.clone(),
            },
            timestamp: Utc::now().to_rfc3339(),
            path: None, // 可以通过中间件添加请求路径
            request_id: None, // 可以通过中间件添加请求ID
            user_id: None, // 可以通过中间件添加用户ID
        };

        let body = Json(error_response);
        (self.status_code, body).into_response()
    }
}

// 从常见错误类型转换为AppError
impl From<sqlx::Error> for AppError {
    fn from(err: sqlx::Error) -> Self {
        match err {
            sqlx::Error::RowNotFound => AppError::not_found("记录"),
            sqlx::Error::Database(db_err) => {
                if db_err.code() == Some(std::borrow::Cow::Borrowed("23505")) {
                    AppError::conflict("数据已存在，违反唯一性约束".to_string())
                } else if db_err.code() == Some(std::borrow::Cow::Borrowed("23503")) {
                    AppError::business_error("违反外键约束，相关数据不存在".to_string())
                } else {
                    AppError::database_error(db_err.to_string())
                }
            }
            _ => AppError::database_error(err.to_string()),
        }
    }
}

impl From<serde_json::Error> for AppError {
    fn from(err: serde_json::Error) -> Self {
        AppError::with_details(
            "VALIDATION_ERROR".to_string(),
            "JSON解析失败".to_string(),
            err.to_string(),
            StatusCode::BAD_REQUEST,
        )
    }
}

impl From<validator::ValidationErrors> for AppError {
    fn from(err: validator::ValidationErrors) -> Self {
        let details = err
            .field_errors()
            .iter()
            .map(|(field, errors)| {
                let messages: Vec<String> = errors
                    .iter()
                    .map(|e| e.message.as_ref().map(|m| m.to_string()).unwrap_or_else(|| "验证失败".to_string()))
                    .collect();
                format!("{}: {}", field, messages.join(", "))
            })
            .collect::<Vec<_>>()
            .join("; ");

        AppError::with_details(
            "VALIDATION_ERROR".to_string(),
            "输入验证失败".to_string(),
            details,
            StatusCode::BAD_REQUEST,
        )
    }
}

// 结果类型别名
pub type AppResult<T> = Result<T, AppError>;

// 便捷宏
#[macro_export]
macro_rules! app_error {
    ($code:expr, $msg:expr) => {
        AppError::new($code.to_string(), $msg.to_string(), StatusCode::BAD_REQUEST)
    };
    ($code:expr, $msg:expr, $status:expr) => {
        AppError::new($code.to_string(), $msg.to_string(), $status)
    };
}

#[macro_export]
macro_rules! validation_error {
    ($msg:expr) => {
        AppError::validation_error($msg.to_string())
    };
    ($msg:expr, $details:expr) => {
        AppError::validation_error_with_details($msg.to_string(), $details.to_string())
    };
}
