use crate::models::project_status::*;
use chrono::Utc;
use sqlx::PgPool;
use std::collections::HashMap;

pub struct ProjectStatusService {
    pool: PgPool,
}

impl ProjectStatusService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn get_project_completion_status(
        &self,
        project_id: i32,
        _query: ProjectStatusQuery,
    ) -> Result<ProjectCompletionStatus, String> {
        // Get project basic info
        let project = sqlx::query!(
            "SELECT id, project_name, customer_name FROM projects WHERE id = $1",
            project_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let project = project.ok_or("Project not found")?;

        // Calculate total planned hours for the project
        let planned_hours_result = sqlx::query!(
            r#"
            SELECT
                COALESCE(SUM(EXTRACT(EPOCH FROM (pt.planned_end - pt.planned_start)) / 3600), 0) as total_planned_hours
            FROM project_boms pb
            JOIN work_orders wo ON wo.project_bom_id = pb.id
            JOIN plan_tasks pt ON pt.work_order_id = wo.id
            WHERE pb.project_id = $1
            "#,
            project_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let total_planned_hours = planned_hours_result.total_planned_hours
            .map(|bd| bd.to_string().parse::<f64>().unwrap_or(0.0))
            .unwrap_or(0.0);

        // Calculate completed hours for the project
        let completed_hours_result = sqlx::query!(
            r#"
            SELECT
                COALESCE(SUM(
                    EXTRACT(EPOCH FROM (end_log.event_time - start_log.event_time)) / 3600
                ), 0) as total_completed_hours
            FROM project_boms pb
            JOIN work_orders wo ON wo.project_bom_id = pb.id
            JOIN plan_tasks pt ON pt.work_order_id = wo.id
            JOIN execution_logs start_log ON start_log.plan_task_id = pt.id AND start_log.event_type = 'task_start'
            JOIN execution_logs end_log ON end_log.plan_task_id = pt.id AND end_log.event_type = 'task_complete'
            WHERE pb.project_id = $1
            "#,
            project_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let total_completed_hours = completed_hours_result.total_completed_hours
            .map(|bd| bd.to_string().parse::<f64>().unwrap_or(0.0))
            .unwrap_or(0.0);

        // Calculate overall progress based on hours
        let overall_completion_percentage = if total_planned_hours > 0.0 {
            (total_completed_hours / total_planned_hours) * 100.0
        } else {
            0.0
        };

        // Get simplified parts status
        let parts_status = self.get_simplified_parts_status(project_id).await?;

        // Get work orders summary
        let work_orders_summary = self.get_work_orders_summary(project_id).await?;

        // Create overall progress
        let overall_progress = ProjectProgress {
            total_parts: parts_status.len() as i32,
            completed_parts: parts_status.iter().filter(|p| matches!(p.status, PartStatus::Completed)).count() as i32,
            in_progress_parts: parts_status.iter().filter(|p| matches!(p.status, PartStatus::InProgress)).count() as i32,
            not_started_parts: parts_status.iter().filter(|p| matches!(p.status, PartStatus::NotStarted)).count() as i32,
            overall_completion_percentage,
            estimated_completion_date: None, // TODO: Calculate based on remaining work
        };

        Ok(ProjectCompletionStatus {
            project_id,
            project_name: project.project_name,
            customer_name: project.customer_name,
            overall_progress,
            parts_status,
            work_orders_summary,
        })
    }

    async fn get_simplified_parts_status(
        &self,
        project_id: i32,
    ) -> Result<Vec<PartCompletionStatus>, String> {
        // Get parts summary with work hours
        let parts_summary = sqlx::query!(
            r#"
            SELECT
                p.id as part_id,
                p.part_number,
                p.part_name,
                p.version,
                p.specifications,
                pb.quantity as bom_quantity,
                COALESCE(SUM(EXTRACT(EPOCH FROM (pt.planned_end - pt.planned_start)) / 3600), 0) as part_planned_hours,
                COALESCE(SUM(
                    CASE WHEN end_log.event_time IS NOT NULL
                    THEN EXTRACT(EPOCH FROM (end_log.event_time - start_log.event_time)) / 3600
                    ELSE 0 END
                ), 0) as part_completed_hours
            FROM project_boms pb
            JOIN parts p ON pb.part_id = p.id
            LEFT JOIN work_orders wo ON wo.project_bom_id = pb.id
            LEFT JOIN plan_tasks pt ON pt.work_order_id = wo.id
            LEFT JOIN execution_logs start_log ON start_log.plan_task_id = pt.id AND start_log.event_type = 'task_start'
            LEFT JOIN execution_logs end_log ON end_log.plan_task_id = pt.id AND end_log.event_type = 'task_complete'
            WHERE pb.project_id = $1
            GROUP BY p.id, p.part_number, p.part_name, p.version, p.specifications, pb.quantity
            ORDER BY p.part_number
            "#,
            project_id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        // Convert to PartCompletionStatus
        let parts: Vec<PartCompletionStatus> = parts_summary.into_iter().map(|part| {
            let part_planned_hours = part.part_planned_hours
                .map(|bd| bd.to_string().parse::<f64>().unwrap_or(0.0))
                .unwrap_or(0.0);
            let part_completed_hours = part.part_completed_hours
                .map(|bd| bd.to_string().parse::<f64>().unwrap_or(0.0))
                .unwrap_or(0.0);

            let completion_percentage = if part_planned_hours > 0.0 {
                (part_completed_hours / part_planned_hours) * 100.0
            } else {
                0.0
            };

            let status = if completion_percentage >= 100.0 {
                PartStatus::Completed
            } else if completion_percentage > 0.0 {
                PartStatus::InProgress
            } else {
                PartStatus::NotStarted
            };

            PartCompletionStatus {
                part_id: part.part_id,
                part_number: part.part_number,
                part_name: part.part_name,
                version: part.version,
                specifications: part.specifications,
                bom_quantity: part.bom_quantity,
                total_work_orders: 0, // TODO: Calculate from work orders
                completed_work_orders: 0,
                in_progress_work_orders: 0,
                pending_work_orders: 0,
                cancelled_work_orders: 0,
                completion_percentage,
                status,
                work_orders: Vec::new(), // Simplified - no detailed work order info
            }
        }).collect();

        Ok(parts)
    }

    async fn get_parts_completion_status(
        &self,
        project_id: i32,
        query: &ProjectStatusQuery,
    ) -> Result<Vec<PartCompletionStatus>, String> {
        // Get parts with their BOM quantities and work order information
        let parts_query = sqlx::query_as!(
            PartStatusQuery,
            r#"
            SELECT
                p.id as part_id,
                p.part_number,
                p.part_name,
                p.version,
                p.specifications,
                pb.quantity as bom_quantity,
                wo.id as "work_order_id?",
                wo.quantity as "work_order_quantity?",
                wo.status as "work_order_status?",
                wo.due_date as "work_order_due_date?",
                wo.created_at as "work_order_created_at?",
                COALESCE(task_counts.total_tasks, 0) as "total_tasks!",
                COALESCE(task_counts.completed_tasks, 0) as "completed_tasks!",
                COALESCE(task_counts.in_progress_tasks, 0) as "in_progress_tasks!",
                COALESCE(task_counts.planned_tasks, 0) as "planned_tasks!"
            FROM project_boms pb
            JOIN parts p ON pb.part_id = p.id
            LEFT JOIN work_orders wo ON wo.project_bom_id = pb.id
            LEFT JOIN (
                SELECT
                    pt.work_order_id,
                    COUNT(*) as total_tasks,
                    COUNT(CASE WHEN pt.status = 'completed' THEN 1 END) as completed_tasks,
                    COUNT(CASE WHEN pt.status = 'in_progress' THEN 1 END) as in_progress_tasks,
                    COUNT(CASE WHEN pt.status IN ('planned', 'scheduled') THEN 1 END) as planned_tasks
                FROM plan_tasks pt
                GROUP BY pt.work_order_id
            ) task_counts ON task_counts.work_order_id = wo.id
            WHERE pb.project_id = $1
            ORDER BY p.part_number, wo.created_at
            "#,
            project_id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;



        // Group by part_id and aggregate work orders
        let mut parts_map: HashMap<i32, Vec<PartStatusQuery>> = HashMap::new();
        for part_query in parts_query {
            parts_map
                .entry(part_query.part_id)
                .or_default()
                .push(part_query);
        }

        let mut parts_status = Vec::new();
        for (part_id, part_queries) in parts_map {
            if let Some(first_part) = part_queries.first() {
                let mut work_orders = Vec::new();
                let mut completed_work_orders = 0;
                let mut in_progress_work_orders = 0;
                let mut pending_work_orders = 0;
                let mut cancelled_work_orders = 0;

                for part_query in &part_queries {
                    if let Some(work_order_id) = part_query.work_order_id {
                        let work_order_status = part_query.work_order_status.as_deref().unwrap_or("pending");
                        
                        match work_order_status {
                            "completed" => completed_work_orders += 1,
                            "in_progress" => in_progress_work_orders += 1,
                            "cancelled" => cancelled_work_orders += 1,
                            _ => pending_work_orders += 1,
                        }

                        let task_details = if query.include_tasks.unwrap_or(true) {
                            self.get_task_details(work_order_id).await?
                        } else {
                            Vec::new()
                        };

                        let total_tasks = part_query.total_tasks as i32;
                        let completed_tasks = part_query.completed_tasks as i32;
                        let in_progress_tasks = part_query.in_progress_tasks as i32;
                        let planned_tasks = part_query.planned_tasks as i32;

                        let progress_percentage = if total_tasks > 0 {
                            (completed_tasks as f64 / total_tasks as f64) * 100.0
                        } else {
                            0.0
                        };

                        work_orders.push(WorkOrderStatus {
                            work_order_id,
                            part_id,
                            part_number: first_part.part_number.clone(),
                            quantity: part_query.work_order_quantity.unwrap_or(0),
                            status: work_order_status.to_string(),
                            due_date: part_query.work_order_due_date,
                            created_at: part_query.work_order_created_at.unwrap_or_else(Utc::now),
                            total_tasks,
                            completed_tasks,
                            in_progress_tasks,
                            planned_tasks,
                            progress_percentage,
                            task_details,
                        });
                    }
                }

                let total_work_orders = work_orders.len() as i32;
                let completion_percentage = if total_work_orders > 0 {
                    (completed_work_orders as f64 / total_work_orders as f64) * 100.0
                } else {
                    0.0
                };

                let status = PartStatus::from_work_orders(
                    completed_work_orders,
                    in_progress_work_orders,
                    pending_work_orders,
                    cancelled_work_orders,
                    total_work_orders,
                );

                parts_status.push(PartCompletionStatus {
                    part_id,
                    part_number: first_part.part_number.clone(),
                    part_name: first_part.part_name.clone(),
                    version: first_part.version.clone(),
                    specifications: first_part.specifications.clone(),
                    bom_quantity: first_part.bom_quantity,
                    total_work_orders,
                    completed_work_orders,
                    in_progress_work_orders,
                    pending_work_orders,
                    cancelled_work_orders,
                    completion_percentage,
                    status,
                    work_orders,
                });
            }
        }

        Ok(parts_status)
    }

    async fn get_task_details(&self, work_order_id: i32) -> Result<Vec<TaskStatus>, String> {
        let tasks = sqlx::query_as!(
            TaskStatusQuery,
            r#"
            SELECT 
                pt.id as plan_task_id,
                pt.work_order_id,
                pt.routing_step_id,
                r.step_number,
                r.process_name,
                pt.status,
                pt.planned_start,
                pt.planned_end,
                sg.group_name as skill_group_name,
                m.machine_name as machine_name,
                start_log.event_time as actual_start,
                end_log.event_time as actual_end
            FROM plan_tasks pt
            JOIN routings r ON pt.routing_step_id = r.id
            JOIN skill_groups sg ON pt.skill_group_id = sg.id
            LEFT JOIN machines m ON pt.machine_id = m.id
            LEFT JOIN execution_logs start_log ON start_log.plan_task_id = pt.id 
                AND start_log.event_type = 'task_start'
            LEFT JOIN execution_logs end_log ON end_log.plan_task_id = pt.id 
                AND end_log.event_type = 'task_complete'
            WHERE pt.work_order_id = $1
            ORDER BY r.step_number
            "#,
            work_order_id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(tasks
            .into_iter()
            .map(|task| TaskStatus {
                plan_task_id: task.plan_task_id,
                routing_step_id: task.routing_step_id,
                step_number: task.step_number,
                process_name: task.process_name,
                status: task.status,
                planned_start: task.planned_start,
                planned_end: task.planned_end,
                actual_start: task.actual_start,
                actual_end: task.actual_end,
                skill_group_name: task.skill_group_name,
                machine_name: task.machine_name,
            })
            .collect())
    }

    async fn calculate_project_progress(
        &self,
        parts_status: &[PartCompletionStatus],
    ) -> Result<ProjectProgress, String> {
        let total_parts = parts_status.len() as i32;
        let completed_parts = parts_status
            .iter()
            .filter(|p| matches!(p.status, PartStatus::Completed))
            .count() as i32;
        let in_progress_parts = parts_status
            .iter()
            .filter(|p| matches!(p.status, PartStatus::InProgress))
            .count() as i32;
        let not_started_parts = parts_status
            .iter()
            .filter(|p| matches!(p.status, PartStatus::NotStarted))
            .count() as i32;

        let overall_completion_percentage = if total_parts > 0 {
            (completed_parts as f64 / total_parts as f64) * 100.0
        } else {
            0.0
        };

        Ok(ProjectProgress {
            total_parts,
            completed_parts,
            in_progress_parts,
            not_started_parts,
            overall_completion_percentage,
            estimated_completion_date: None, // TODO: Calculate based on current progress and remaining work
        })
    }

    async fn get_work_orders_summary(&self, project_id: i32) -> Result<WorkOrdersSummary, String> {
        let summary = sqlx::query!(
            r#"
            SELECT 
                COUNT(*) as total_work_orders,
                COUNT(CASE WHEN wo.status = 'pending' THEN 1 END) as pending_orders,
                COUNT(CASE WHEN wo.status = 'planned' THEN 1 END) as planned_orders,
                COUNT(CASE WHEN wo.status = 'in_progress' THEN 1 END) as in_progress_orders,
                COUNT(CASE WHEN wo.status = 'completed' THEN 1 END) as completed_orders,
                COUNT(CASE WHEN wo.status = 'cancelled' THEN 1 END) as cancelled_orders,
                COUNT(CASE WHEN wo.due_date < CURRENT_DATE AND wo.status != 'completed' THEN 1 END) as overdue_orders
            FROM work_orders wo
            JOIN project_boms pb ON wo.project_bom_id = pb.id
            WHERE pb.project_id = $1
            "#,
            project_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(WorkOrdersSummary {
            total_work_orders: summary.total_work_orders.unwrap_or(0) as i32,
            pending_orders: summary.pending_orders.unwrap_or(0) as i32,
            planned_orders: summary.planned_orders.unwrap_or(0) as i32,
            in_progress_orders: summary.in_progress_orders.unwrap_or(0) as i32,
            completed_orders: summary.completed_orders.unwrap_or(0) as i32,
            cancelled_orders: summary.cancelled_orders.unwrap_or(0) as i32,
            overdue_orders: summary.overdue_orders.unwrap_or(0) as i32,
        })
    }
}
