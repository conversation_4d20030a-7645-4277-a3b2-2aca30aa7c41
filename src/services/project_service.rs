use crate::models::project::{
    CreateProjectBomRequest, CreateProjectRequest, Project, ProjectBom, ProjectBomWithDetails,
    ProjectWithBom, UpdateProjectBomRequest, UpdateProjectRequest, ProjectQuery,
    ProjectSearchResult, ProjectStatusUpdate, ProjectStatusStats, is_valid_project_status,
    PROJECT_STATUS_NORMAL,
};
use sqlx::PgPool;

pub struct ProjectService {
    pool: PgPool,
}

impl ProjectService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn create_project(&self, request: CreateProjectRequest) -> Result<Project, String> {
        // Validate status if provided
        let status = request.status.unwrap_or_else(|| PROJECT_STATUS_NORMAL.to_string());
        if !is_valid_project_status(&status) {
            return Err(format!("Invalid project status: {}", status));
        }

        let project = sqlx::query_as!(
            Project,
            "INSERT INTO projects (project_name, customer_name, status)
             VALUES ($1, $2, $3)
             RETURNING id, project_name, customer_name, status, created_at",
            request.project_name,
            request.customer_name,
            status
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(project)
    }

    pub async fn get_all_projects(&self) -> Result<Vec<Project>, String> {
        let projects = sqlx::query_as!(
            Project,
            "SELECT id, project_name, customer_name, status, created_at
             FROM projects
             ORDER BY created_at DESC"
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(projects)
    }

    pub async fn get_project_by_id(&self, project_id: i32) -> Result<Option<Project>, String> {
        let project = sqlx::query_as!(
            Project,
            "SELECT id, project_name, customer_name, status, created_at
             FROM projects WHERE id = $1",
            project_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(project)
    }

    pub async fn get_project_with_bom(
        &self,
        project_id: i32,
    ) -> Result<Option<ProjectWithBom>, String> {
        let project = self.get_project_by_id(project_id).await?;

        if let Some(project) = project {
            let bom_items = sqlx::query_as!(
                ProjectBomWithDetails,
                "SELECT pb.id, pb.project_id, pb.part_id, p.part_number, p.part_name, 
                        p.version, pb.quantity, p.specifications
                 FROM project_boms pb
                 JOIN parts p ON pb.part_id = p.id
                 WHERE pb.project_id = $1
                 ORDER BY p.part_number, p.version",
                project_id
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

            Ok(Some(ProjectWithBom {
                id: project.id,
                project_name: project.project_name,
                customer_name: project.customer_name,
                status: project.status,
                created_at: project.created_at,
                bom_items,
            }))
        } else {
            Ok(None)
        }
    }

    pub async fn update_project(
        &self,
        project_id: i32,
        request: UpdateProjectRequest,
    ) -> Result<Option<Project>, String> {
        // Validate status if provided
        if let Some(ref status) = request.status {
            if !is_valid_project_status(status) {
                return Err(format!("Invalid project status: {}", status));
            }
        }

        // Check if project exists
        let project_exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM projects WHERE id = $1)",
            project_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(false);

        if !project_exists {
            return Ok(None);
        }

        // Get current values
        let current = sqlx::query_as!(
            Project,
            "SELECT id, project_name, customer_name, status, created_at FROM projects WHERE id = $1",
            project_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let project = sqlx::query_as!(
            Project,
            "UPDATE projects SET project_name = $1, customer_name = $2, status = $3
             WHERE id = $4
             RETURNING id, project_name, customer_name, status, created_at",
            request.project_name.unwrap_or(current.project_name),
            request.customer_name.or(current.customer_name),
            request.status.unwrap_or(current.status),
            project_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(Some(project))
    }

    // Update only the project status
    pub async fn update_project_status(
        &self,
        project_id: i32,
        status_update: ProjectStatusUpdate,
    ) -> Result<Option<Project>, String> {
        // Validate status
        if !is_valid_project_status(&status_update.status) {
            return Err(format!("Invalid project status: {}", status_update.status));
        }

        // Check if project exists
        let project_exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM projects WHERE id = $1)",
            project_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(false);

        if !project_exists {
            return Ok(None);
        }

        // Update only the status
        let project = sqlx::query_as!(
            Project,
            "UPDATE projects SET status = $1
             WHERE id = $2
             RETURNING id, project_name, customer_name, status, created_at",
            status_update.status,
            project_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(Some(project))
    }

    // Search projects with filters
    pub async fn search_projects(&self, query: ProjectQuery) -> Result<ProjectSearchResult, String> {
        // Apply pagination
        let limit = query.limit.unwrap_or(10);
        let offset = query.offset.unwrap_or(0);

        // Build query based on filters
        let (projects, total_count) = if let Some(status) = &query.status {
            if let Some(customer_name) = &query.customer_name {
                // Both status and customer_name filters
                let projects = sqlx::query_as!(
                    Project,
                    "SELECT id, project_name, customer_name, status, created_at
                     FROM projects
                     WHERE status = $1 AND customer_name ILIKE $2
                     ORDER BY created_at DESC
                     LIMIT $3 OFFSET $4",
                    status,
                    format!("%{}%", customer_name),
                    limit,
                    offset
                )
                .fetch_all(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?;

                let total_count = sqlx::query_scalar!(
                    "SELECT COUNT(*) FROM projects WHERE status = $1 AND customer_name ILIKE $2",
                    status,
                    format!("%{}%", customer_name)
                )
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

                (projects, total_count)
            } else {
                // Only status filter
                let projects = sqlx::query_as!(
                    Project,
                    "SELECT id, project_name, customer_name, status, created_at
                     FROM projects
                     WHERE status = $1
                     ORDER BY created_at DESC
                     LIMIT $2 OFFSET $3",
                    status,
                    limit,
                    offset
                )
                .fetch_all(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?;

                let total_count = sqlx::query_scalar!(
                    "SELECT COUNT(*) FROM projects WHERE status = $1",
                    status
                )
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

                (projects, total_count)
            }
        } else if let Some(customer_name) = &query.customer_name {
            // Only customer_name filter
            let projects = sqlx::query_as!(
                Project,
                "SELECT id, project_name, customer_name, status, created_at
                 FROM projects
                 WHERE customer_name ILIKE $1
                 ORDER BY created_at DESC
                 LIMIT $2 OFFSET $3",
                format!("%{}%", customer_name),
                limit,
                offset
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

            let total_count = sqlx::query_scalar!(
                "SELECT COUNT(*) FROM projects WHERE customer_name ILIKE $1",
                format!("%{}%", customer_name)
            )
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?
            .unwrap_or(0);

            (projects, total_count)
        } else {
            // No filters, just pagination
            let projects = sqlx::query_as!(
                Project,
                "SELECT id, project_name, customer_name, status, created_at
                 FROM projects
                 ORDER BY created_at DESC
                 LIMIT $1 OFFSET $2",
                limit,
                offset
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

            let total_count = sqlx::query_scalar!(
                "SELECT COUNT(*) FROM projects"
            )
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?
            .unwrap_or(0);

            (projects, total_count)
        };

        Ok(ProjectSearchResult {
            projects,
            total_count,
            limit,
            offset,
        })
    }

    // Get project status statistics
    pub async fn get_project_status_stats(&self) -> Result<Vec<ProjectStatusStats>, String> {
        let stats_raw = sqlx::query!(
            "SELECT
                status,
                COUNT(*) as count,
                ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
             FROM projects
             GROUP BY status
             ORDER BY
                CASE status
                    WHEN 'priority' THEN 1
                    WHEN 'normal' THEN 2
                    WHEN 'paused' THEN 3
                END"
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let stats = stats_raw
            .into_iter()
            .map(|row| ProjectStatusStats {
                status: row.status,
                count: row.count.unwrap_or(0),
                percentage: row.percentage
                    .map(|bd| bd.to_string().parse::<f64>().unwrap_or(0.0))
                    .unwrap_or(0.0),
            })
            .collect();

        Ok(stats)
    }

    pub async fn delete_project(&self, project_id: i32) -> Result<bool, String> {
        // Start transaction
        let mut tx = self
            .pool
            .begin()
            .await
            .map_err(|e| format!("Transaction error: {}", e))?;

        // Delete project BOMs first
        sqlx::query!("DELETE FROM project_boms WHERE project_id = $1", project_id)
            .execute(&mut *tx)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

        // Delete project
        let result = sqlx::query!("DELETE FROM projects WHERE id = $1", project_id)
            .execute(&mut *tx)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

        // Commit transaction
        tx.commit()
            .await
            .map_err(|e| format!("Transaction commit error: {}", e))?;

        Ok(result.rows_affected() > 0)
    }

    // BOM Management
    pub async fn add_bom_item(
        &self,
        project_id: i32,
        request: CreateProjectBomRequest,
    ) -> Result<ProjectBomWithDetails, String> {
        // Verify project exists
        let project_exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM projects WHERE id = $1)",
            project_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(false);

        if !project_exists {
            return Err("Project not found".to_string());
        }

        // Verify part exists
        let part_exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM parts WHERE id = $1)",
            request.part_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(false);

        if !part_exists {
            return Err("Part not found".to_string());
        }

        // Check if BOM item already exists
        let existing_bom = sqlx::query_scalar!(
            "SELECT id FROM project_boms WHERE project_id = $1 AND part_id = $2",
            project_id,
            request.part_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if existing_bom.is_some() {
            return Err("Part already exists in project BOM".to_string());
        }

        // Create BOM item
        let bom_item = sqlx::query_as!(
            ProjectBom,
            "INSERT INTO project_boms (project_id, part_id, quantity)
             VALUES ($1, $2, $3)
             RETURNING id, project_id, part_id, quantity",
            project_id,
            request.part_id,
            request.quantity
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        // Get BOM item with part details
        let bom_with_details = sqlx::query_as!(
            ProjectBomWithDetails,
            "SELECT pb.id, pb.project_id, pb.part_id, p.part_number, p.part_name, 
                    p.version, pb.quantity, p.specifications
             FROM project_boms pb
             JOIN parts p ON pb.part_id = p.id
             WHERE pb.id = $1",
            bom_item.id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(bom_with_details)
    }

    pub async fn update_bom_item(
        &self,
        bom_id: i32,
        request: UpdateProjectBomRequest,
    ) -> Result<Option<ProjectBomWithDetails>, String> {
        // Check if BOM item exists
        let bom_exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM project_boms WHERE id = $1)",
            bom_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(false);

        if !bom_exists {
            return Ok(None);
        }

        // Get current values
        let current = sqlx::query_as!(
            ProjectBom,
            "SELECT id, project_id, part_id, quantity FROM project_boms WHERE id = $1",
            bom_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        // Update BOM item
        sqlx::query!(
            "UPDATE project_boms SET quantity = $1 WHERE id = $2",
            request.quantity.unwrap_or(current.quantity),
            bom_id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        // Get updated BOM item with part details
        let bom_with_details = sqlx::query_as!(
            ProjectBomWithDetails,
            "SELECT pb.id, pb.project_id, pb.part_id, p.part_number, p.part_name, 
                    p.version, pb.quantity, p.specifications
             FROM project_boms pb
             JOIN parts p ON pb.part_id = p.id
             WHERE pb.id = $1",
            bom_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(Some(bom_with_details))
    }

    pub async fn remove_bom_item(&self, bom_id: i32) -> Result<bool, String> {
        let result = sqlx::query!("DELETE FROM project_boms WHERE id = $1", bom_id)
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

        Ok(result.rows_affected() > 0)
    }

    pub async fn get_project_bom(
        &self,
        project_id: i32,
    ) -> Result<Vec<ProjectBomWithDetails>, String> {
        let bom_items = sqlx::query_as!(
            ProjectBomWithDetails,
            "SELECT pb.id, pb.project_id, pb.part_id, p.part_number, p.part_name, 
                    p.version, pb.quantity, p.specifications
             FROM project_boms pb
             JOIN parts p ON pb.part_id = p.id
             WHERE pb.project_id = $1
             ORDER BY p.part_number, p.version",
            project_id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(bom_items)
    }
}
