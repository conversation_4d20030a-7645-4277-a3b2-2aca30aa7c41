use crate::models::auto_work_order::{
    AutoWorkOrderConfig, AutoWorkOrderConfigQuery, AutoWorkOrderConfigResponse,
    AutoWorkOrderResult, AutoWorkOrderTriggerEvent, CreateAutoWorkOrderConfigRequest,
    UpdateAutoWorkOrderConfigRequest, get_valid_trigger_types,
};
use crate::models::work_order::{CreateWorkOrderRequest, WorkOrder};
use crate::services::work_order_service::WorkOrderService;
use chrono::{Duration, Utc};
use sqlx::PgPool;

pub struct AutoWorkOrderService {
    pool: PgPool,
}

impl AutoWorkOrderService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    // 获取自动工单配置列表
    pub async fn get_configs(
        &self,
        query: AutoWorkOrderConfigQuery,
    ) -> Result<AutoWorkOrderConfigResponse, String> {
        let limit = query.limit.unwrap_or(50);
        let offset = query.offset.unwrap_or(0);

        let mut where_conditions = Vec::new();
        let mut params: Vec<Box<dyn sqlx::Encode<'_, sqlx::Postgres> + Send + Sync>> = Vec::new();
        let mut param_count = 0;

        if let Some(project_id) = query.project_id {
            param_count += 1;
            where_conditions.push(format!("project_id = ${}", param_count));
            params.push(Box::new(project_id));
        }

        if let Some(trigger_type) = query.trigger_type {
            param_count += 1;
            where_conditions.push(format!("trigger_type = ${}", param_count));
            params.push(Box::new(trigger_type));
        }

        if let Some(is_enabled) = query.is_enabled {
            param_count += 1;
            where_conditions.push(format!("is_enabled = ${}", param_count));
            params.push(Box::new(is_enabled));
        }

        // 获取总数
        
        let total_count: i64 = if let Some(project_id) = query.project_id {
            sqlx::query_scalar!(
                "SELECT COUNT(*) FROM auto_work_order_configs WHERE project_id = $1",
                project_id
            )
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?
            .unwrap_or(0i64)
        } else {
            sqlx::query_scalar!("SELECT COUNT(*) FROM auto_work_order_configs")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0i64)
        };

        // 获取配置列表
        let configs = if let Some(project_id) = query.project_id {
            sqlx::query_as!(
                AutoWorkOrderConfig,
                "SELECT id, trigger_type, project_id, is_enabled, default_quantity,
                        default_due_days, auto_create_plan_tasks, created_by, created_at, updated_at
                 FROM auto_work_order_configs
                 WHERE project_id = $1
                 ORDER BY created_at DESC
                 LIMIT $2 OFFSET $3",
                project_id,
                limit,
                offset
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?
        } else {
            sqlx::query_as!(
                AutoWorkOrderConfig,
                "SELECT id, trigger_type, project_id, is_enabled, default_quantity,
                        default_due_days, auto_create_plan_tasks, created_by, created_at, updated_at
                 FROM auto_work_order_configs
                 ORDER BY created_at DESC
                 LIMIT $1 OFFSET $2",
                limit,
                offset
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?
        };

        Ok(AutoWorkOrderConfigResponse {
            configs,
            total_count,
        })
    }

    // 创建自动工单配置
    pub async fn create_config(
        &self,
        request: CreateAutoWorkOrderConfigRequest,
        user_id: i32,
    ) -> Result<AutoWorkOrderConfig, String> {
        // 验证触发类型
        let valid_types = get_valid_trigger_types();
        if !valid_types.contains(&request.trigger_type.as_str()) {
            return Err(format!(
                "Invalid trigger type. Valid types are: {:?}",
                valid_types
            ));
        }

        let config = sqlx::query_as!(
            AutoWorkOrderConfig,
            "INSERT INTO auto_work_order_configs 
             (trigger_type, project_id, is_enabled, default_quantity, default_due_days, 
              auto_create_plan_tasks, created_by)
             VALUES ($1, $2, $3, $4, $5, $6, $7)
             RETURNING id, trigger_type, project_id, is_enabled, default_quantity, 
                       default_due_days, auto_create_plan_tasks, created_by, created_at, updated_at",
            request.trigger_type,
            request.project_id,
            request.is_enabled,
            request.default_quantity,
            request.default_due_days,
            request.auto_create_plan_tasks,
            user_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(config)
    }

    // 更新自动工单配置
    pub async fn update_config(
        &self,
        config_id: i32,
        request: UpdateAutoWorkOrderConfigRequest,
    ) -> Result<Option<AutoWorkOrderConfig>, String> {
        // 检查配置是否存在
        let exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM auto_work_order_configs WHERE id = $1)",
            config_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(false);

        if !exists {
            return Ok(None);
        }

        // 构建更新查询
        let mut set_clauses = Vec::new();
        let mut param_count = 0;

        if request.is_enabled.is_some() {
            param_count += 1;
            set_clauses.push(format!("is_enabled = ${}", param_count));
        }

        if request.default_quantity.is_some() {
            param_count += 1;
            set_clauses.push(format!("default_quantity = ${}", param_count));
        }

        if request.default_due_days.is_some() {
            param_count += 1;
            set_clauses.push(format!("default_due_days = ${}", param_count));
        }

        if request.auto_create_plan_tasks.is_some() {
            param_count += 1;
            set_clauses.push(format!("auto_create_plan_tasks = ${}", param_count));
        }

        if set_clauses.is_empty() {
            return Err("No fields to update".to_string());
        }

        set_clauses.push("updated_at = NOW()".to_string());

        let _query = format!(
            "UPDATE auto_work_order_configs SET {} WHERE id = ${} 
             RETURNING id, trigger_type, project_id, is_enabled, default_quantity, 
                       default_due_days, auto_create_plan_tasks, created_by, created_at, updated_at",
            set_clauses.join(", "),
            param_count + 1
        );

        // 这里需要根据实际的参数构建查询，简化处理
        let config = sqlx::query_as!(
            AutoWorkOrderConfig,
            "UPDATE auto_work_order_configs 
             SET is_enabled = COALESCE($1, is_enabled),
                 default_quantity = COALESCE($2, default_quantity),
                 default_due_days = COALESCE($3, default_due_days),
                 auto_create_plan_tasks = COALESCE($4, auto_create_plan_tasks),
                 updated_at = NOW()
             WHERE id = $5
             RETURNING id, trigger_type, project_id, is_enabled, default_quantity, 
                       default_due_days, auto_create_plan_tasks, created_by, created_at, updated_at",
            request.is_enabled,
            request.default_quantity,
            request.default_due_days,
            request.auto_create_plan_tasks,
            config_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(Some(config))
    }

    // 删除自动工单配置
    pub async fn delete_config(&self, config_id: i32) -> Result<bool, String> {
        let result = sqlx::query!(
            "DELETE FROM auto_work_order_configs WHERE id = $1",
            config_id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(result.rows_affected() > 0)
    }

    // 处理触发事件，自动创建工单
    pub async fn handle_trigger_event(
        &self,
        event: AutoWorkOrderTriggerEvent,
    ) -> Result<Vec<AutoWorkOrderResult>, String> {
        // 查找匹配的配置
        let configs = self.find_matching_configs(&event).await?;
        
        if configs.is_empty() {
            return Ok(Vec::new());
        }

        let mut results = Vec::new();

        for config in configs {
            if let Ok(result) = self.create_work_order_from_config(&config, &event).await {
                results.push(result);
            }
        }

        Ok(results)
    }

    // 查找匹配的配置
    async fn find_matching_configs(
        &self,
        event: &AutoWorkOrderTriggerEvent,
    ) -> Result<Vec<AutoWorkOrderConfig>, String> {
        let configs = sqlx::query_as!(
            AutoWorkOrderConfig,
            "SELECT id, trigger_type, project_id, is_enabled, default_quantity, 
                    default_due_days, auto_create_plan_tasks, created_by, created_at, updated_at
             FROM auto_work_order_configs
             WHERE trigger_type = $1 
               AND is_enabled = true
               AND (project_id IS NULL OR project_id = $2)
             ORDER BY project_id DESC NULLS LAST", // 优先使用项目特定配置
            event.trigger_type,
            event.project_id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(configs)
    }

    // 根据配置创建工单
    async fn create_work_order_from_config(
        &self,
        config: &AutoWorkOrderConfig,
        event: &AutoWorkOrderTriggerEvent,
    ) -> Result<AutoWorkOrderResult, String> {
        // 根据事件类型获取BOM信息
        let bom_info = self.get_bom_info_for_event(event).await?;
        
        if bom_info.is_empty() {
            return Err("No BOM information found for the event".to_string());
        }

        let work_order_service = WorkOrderService::new(self.pool.clone());
        let mut plan_task_ids = Vec::new();

        // 为每个BOM项目创建工单
        for bom in bom_info {
            let quantity = config.default_quantity.unwrap_or(bom.quantity);
            
            let due_date = config.default_due_days.map(|due_days| (Utc::now() + Duration::days(due_days as i64)).date_naive());

            let work_order_request = CreateWorkOrderRequest {
                project_bom_id: bom.id,
                quantity,
                due_date,
            };

            let work_order = work_order_service.create_work_order(work_order_request).await?;

            // 记录自动创建日志
            self.log_auto_creation(config, &work_order, event).await?;

            // 如果配置了自动创建计划任务
            if config.auto_create_plan_tasks {
                let task_ids = self.create_plan_tasks_for_work_order(&work_order).await?;
                plan_task_ids.extend(task_ids);
            }

            return Ok(AutoWorkOrderResult {
                work_order_id: work_order.id,
                plan_task_ids,
                trigger_config_id: config.id,
                created_at: Utc::now(),
            });
        }

        Err("Failed to create work order".to_string())
    }

    // 获取事件相关的BOM信息
    async fn get_bom_info_for_event(
        &self,
        event: &AutoWorkOrderTriggerEvent,
    ) -> Result<Vec<BomInfo>, String> {
        match event.trigger_type.as_str() {
            "bom_added" => {
                if let Some(bom_id) = event.bom_id {
                    let bom = sqlx::query_as!(
                        BomInfo,
                        "SELECT id, project_id, part_id, quantity FROM project_boms WHERE id = $1",
                        bom_id
                    )
                    .fetch_optional(&self.pool)
                    .await
                    .map_err(|e| format!("Database error: {}", e))?;

                    Ok(bom.into_iter().collect())
                } else {
                    Ok(Vec::new())
                }
            }
            "part_created" => {
                if let Some(part_id) = event.part_id {
                    // 查找包含此零件的所有BOM项目
                    let boms = sqlx::query_as!(
                        BomInfo,
                        "SELECT id, project_id, part_id, quantity FROM project_boms WHERE part_id = $1",
                        part_id
                    )
                    .fetch_all(&self.pool)
                    .await
                    .map_err(|e| format!("Database error: {}", e))?;

                    Ok(boms)
                } else {
                    Ok(Vec::new())
                }
            }
            "routing_created" => {
                if let Some(part_id) = event.part_id {
                    // 查找包含此零件的所有BOM项目
                    let boms = sqlx::query_as!(
                        BomInfo,
                        "SELECT id, project_id, part_id, quantity FROM project_boms WHERE part_id = $1",
                        part_id
                    )
                    .fetch_all(&self.pool)
                    .await
                    .map_err(|e| format!("Database error: {}", e))?;

                    Ok(boms)
                } else {
                    Ok(Vec::new())
                }
            }
            _ => Ok(Vec::new()),
        }
    }

    // 记录自动创建日志
    async fn log_auto_creation(
        &self,
        config: &AutoWorkOrderConfig,
        work_order: &WorkOrder,
        event: &AutoWorkOrderTriggerEvent,
    ) -> Result<(), String> {
        let trigger_entity_type = match event.trigger_type.as_str() {
            "part_created" => "part",
            "routing_created" => "routing", 
            "bom_added" => "bom",
            _ => "unknown",
        };

        let trigger_entity_id = event.part_id
            .or(event.routing_id)
            .or(event.bom_id)
            .unwrap_or(0);

        sqlx::query!(
            "INSERT INTO auto_work_order_logs 
             (config_id, work_order_id, trigger_type, trigger_entity_type, trigger_entity_id,
              project_id, part_id, quantity, created_by)
             VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)",
            config.id,
            work_order.id,
            event.trigger_type,
            trigger_entity_type,
            trigger_entity_id,
            event.project_id,
            event.part_id,
            work_order.quantity,
            event.user_id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(())
    }

    // 为工单创建计划任务
    async fn create_plan_tasks_for_work_order(
        &self,
        _work_order: &WorkOrder,
    ) -> Result<Vec<i32>, String> {
        // 这里需要实现根据工单创建计划任务的逻辑
        // 暂时返回空列表
        Ok(Vec::new())
    }
}

#[derive(Debug)]
struct BomInfo {
    id: i32,
    project_id: i32,
    part_id: i32,
    quantity: i32,
}
