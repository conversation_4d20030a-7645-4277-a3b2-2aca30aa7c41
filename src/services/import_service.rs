use crate::models::import::*;
use sqlx::PgPool;
use std::collections::HashMap;
use std::path::Path;
use tokio::fs;
use uuid::Uuid;
use csv::ReaderBuilder;
use chrono::Utc;

pub struct ImportService {
    pool: PgPool,
}

impl ImportService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 创建导入任务
    pub async fn create_import_job(
        &self,
        user_id: i32,
        request: CreateImportJobRequest,
        file_content: Vec<u8>,
    ) -> Result<ImportJob, String> {
        // 验证模块类型
        if !get_valid_module_types().contains(&request.module_type.as_str()) {
            return Err("不支持的模块类型".to_string());
        }

        // 生成唯一文件名
        let file_extension = Path::new(&request.file_name)
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("csv");
        
        let unique_filename = format!("{}_{}.{}", 
            Uuid::new_v4(), 
            request.module_type,
            file_extension
        );
        
        let upload_dir = "uploads/imports";
        let file_path = format!("{}/{}", upload_dir, unique_filename);

        // 确保上传目录存在
        if let Err(e) = fs::create_dir_all(upload_dir).await {
            return Err(format!("创建上传目录失败: {}", e));
        }

        // 保存文件
        if let Err(e) = fs::write(&file_path, file_content).await {
            return Err(format!("保存文件失败: {}", e));
        }

        // 创建数据库记录
        let import_job = sqlx::query_as!(
            ImportJob,
            r#"
            INSERT INTO import_jobs (user_id, module_type, file_name, file_path, status)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING id, user_id, module_type, file_name, file_path, status,
                     total_rows, processed_rows, success_rows, error_rows,
                     error_details, created_at, started_at, completed_at
            "#,
            user_id,
            request.module_type,
            request.file_name,
            file_path,
            STATUS_PENDING
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("创建导入任务失败: {}", e))?;

        Ok(import_job)
    }

    /// 预览导入数据
    pub async fn preview_import_data(
        &self,
        import_job_id: i32,
        user_id: i32,
    ) -> Result<ImportPreviewResponse, String> {
        // 获取导入任务
        let import_job = self.get_import_job_by_id(import_job_id, user_id).await?;
        
        // 读取文件内容
        let file_content = fs::read_to_string(&import_job.file_path)
            .await
            .map_err(|e| format!("读取文件失败: {}", e))?;

        // 解析CSV
        let mut reader = ReaderBuilder::new()
            .has_headers(true)
            .from_reader(file_content.as_bytes());

        // 获取标题行
        let headers = reader.headers()
            .map_err(|e| format!("读取CSV标题失败: {}", e))?
            .iter()
            .map(|h| h.to_string())
            .collect::<Vec<String>>();

        // 读取前10行数据作为预览
        let mut sample_data = Vec::new();
        let mut total_rows = 0;
        
        for (index, result) in reader.records().enumerate() {
            total_rows += 1;
            
            if index < 10 {
                match result {
                    Ok(record) => {
                        let mut row_data = HashMap::new();
                        for (i, field) in record.iter().enumerate() {
                            if let Some(header) = headers.get(i) {
                                row_data.insert(header.clone(), field.to_string());
                            }
                        }
                        sample_data.push(row_data);
                    }
                    Err(e) => {
                        return Err(format!("解析CSV第{}行失败: {}", index + 2, e));
                    }
                }
            }
        }

        // 执行基础验证
        let validation_errors = self.validate_headers(&import_job.module_type, &headers)?;

        // 更新总行数
        sqlx::query!(
            "UPDATE import_jobs SET total_rows = $1 WHERE id = $2",
            total_rows as i32,
            import_job_id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("更新总行数失败: {}", e))?;

        Ok(ImportPreviewResponse {
            headers,
            sample_data,
            total_rows,
            validation_errors,
        })
    }

    /// 执行导入
    pub async fn execute_import(
        &self,
        import_job_id: i32,
        user_id: i32,
        options: Option<ImportOptions>,
    ) -> Result<ImportResult, String> {
        let start_time = Utc::now();
        
        // 获取导入任务
        let import_job = self.get_import_job_by_id(import_job_id, user_id).await?;
        
        if import_job.status != STATUS_PENDING {
            return Err("导入任务状态不正确".to_string());
        }

        // 更新状态为处理中
        sqlx::query!(
            "UPDATE import_jobs SET status = $1, started_at = $2 WHERE id = $3",
            STATUS_PROCESSING,
            start_time,
            import_job_id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("更新任务状态失败: {}", e))?;

        // 执行具体的导入逻辑
        let result = match import_job.module_type.as_str() {
            MODULE_PROJECTS => self.import_projects(import_job_id, &import_job.file_path, options).await,
            MODULE_PARTS => self.import_parts(import_job_id, &import_job.file_path, options).await,
            MODULE_SKILL_GROUPS => self.import_skill_groups(import_job_id, &import_job.file_path, options).await,
            MODULE_MACHINES => self.import_machines(import_job_id, &import_job.file_path, options).await,
            MODULE_BOM => self.import_bom(import_job_id, &import_job.file_path, options).await,
            _ => Err("不支持的模块类型".to_string()),
        };

        let end_time = Utc::now();
        let duration_ms = (end_time - start_time).num_milliseconds();

        // 更新任务完成状态
        let final_status = if result.is_ok() { STATUS_COMPLETED } else { STATUS_FAILED };
        
        sqlx::query!(
            "UPDATE import_jobs SET status = $1, completed_at = $2 WHERE id = $3",
            final_status,
            end_time,
            import_job_id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("更新完成状态失败: {}", e))?;

        // 获取最终结果
        let final_job = self.get_import_job_by_id(import_job_id, user_id).await?;
        let errors = self.get_import_errors(import_job_id).await?;

        Ok(ImportResult {
            import_job_id,
            status: final_job.status,
            total_rows: final_job.total_rows.unwrap_or(0),
            processed_rows: final_job.processed_rows,
            success_rows: final_job.success_rows,
            error_rows: final_job.error_rows,
            errors,
            duration_ms: Some(duration_ms),
        })
    }

    /// 获取导入任务状态
    pub async fn get_import_status(
        &self,
        import_job_id: i32,
        user_id: i32,
    ) -> Result<ImportStatusResponse, String> {
        let import_job = self.get_import_job_by_id(import_job_id, user_id).await?;
        let errors = self.get_import_errors(import_job_id).await?;
        
        let progress_percentage = if let Some(total) = import_job.total_rows {
            if total > 0 {
                (import_job.processed_rows as f64 / total as f64) * 100.0
            } else {
                0.0
            }
        } else {
            0.0
        };

        Ok(ImportStatusResponse {
            import_job,
            errors,
            progress_percentage,
        })
    }

    /// 获取导入任务详情
    async fn get_import_job_by_id(&self, import_job_id: i32, user_id: i32) -> Result<ImportJob, String> {
        sqlx::query_as!(
            ImportJob,
            r#"
            SELECT id, user_id, module_type, file_name, file_path, status,
                   total_rows, processed_rows, success_rows, error_rows,
                   error_details, created_at, started_at, completed_at
            FROM import_jobs 
            WHERE id = $1 AND user_id = $2
            "#,
            import_job_id,
            user_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|_| "导入任务不存在或无权限访问".to_string())
    }

    /// 获取导入错误列表
    async fn get_import_errors(&self, import_job_id: i32) -> Result<Vec<ImportError>, String> {
        sqlx::query_as!(
            ImportError,
            r#"
            SELECT id, import_job_id, row_number, column_name, error_type, error_message, row_data
            FROM import_errors 
            WHERE import_job_id = $1
            ORDER BY row_number, id
            "#,
            import_job_id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("获取错误列表失败: {}", e))
    }

    /// 验证CSV标题
    fn validate_headers(&self, module_type: &str, headers: &[String]) -> Result<Vec<ValidationError>, String> {
        let mut errors = Vec::new();

        if let Some(mapping) = get_module_field_mapping(module_type) {
            // 检查必填字段是否存在
            for required_field in &mapping.required_fields {
                if !headers.contains(required_field) {
                    errors.push(ValidationError {
                        row_number: 0, // 标题行
                        column_name: Some(required_field.clone()),
                        error_type: ERROR_TYPE_VALIDATION.to_string(),
                        error_message: format!("缺少必填列: {}", required_field),
                        row_data: HashMap::new(),
                    });
                }
            }
        }

        Ok(errors)
    }

    /// 导入项目数据
    async fn import_projects(
        &self,
        import_job_id: i32,
        file_path: &str,
        _options: Option<ImportOptions>,
    ) -> Result<(), String> {
        let file_content = fs::read_to_string(file_path)
            .await
            .map_err(|e| format!("读取文件失败: {}", e))?;

        let mut reader = ReaderBuilder::new()
            .has_headers(true)
            .from_reader(file_content.as_bytes());

        let headers = reader.headers()
            .map_err(|e| format!("读取CSV标题失败: {}", e))?
            .iter()
            .map(|h| h.to_string())
            .collect::<Vec<String>>();

        let mut processed = 0;
        let mut success = 0;
        let mut errors = 0;

        for (row_index, result) in reader.records().enumerate() {
            let row_number = row_index + 1;
            processed += 1;

            match result {
                Ok(record) => {
                    let mut row_data = HashMap::new();
                    for (i, field) in record.iter().enumerate() {
                        if let Some(header) = headers.get(i) {
                            row_data.insert(header.clone(), field.to_string());
                        }
                    }

                    // 验证和插入项目数据
                    match self.insert_project_record(&row_data).await {
                        Ok(_) => success += 1,
                        Err(error_msg) => {
                            errors += 1;
                            self.record_import_error(
                                import_job_id,
                                row_number as i32,
                                None,
                                ERROR_TYPE_BUSINESS,
                                &error_msg,
                                &row_data,
                            ).await?;
                        }
                    }
                }
                Err(e) => {
                    errors += 1;
                    self.record_import_error(
                        import_job_id,
                        row_number as i32,
                        None,
                        ERROR_TYPE_FORMAT,
                        &format!("CSV解析错误: {}", e),
                        &HashMap::new(),
                    ).await?;
                }
            }

            // 更新进度
            if processed % 10 == 0 {
                self.update_import_progress(import_job_id, processed, success, errors).await?;
            }
        }

        // 最终更新进度
        self.update_import_progress(import_job_id, processed, success, errors).await?;
        Ok(())
    }

    /// 导入零件数据
    async fn import_parts(
        &self,
        import_job_id: i32,
        file_path: &str,
        _options: Option<ImportOptions>,
    ) -> Result<(), String> {
        let file_content = fs::read_to_string(file_path)
            .await
            .map_err(|e| format!("读取文件失败: {}", e))?;

        let mut reader = ReaderBuilder::new()
            .has_headers(true)
            .from_reader(file_content.as_bytes());

        let headers = reader.headers()
            .map_err(|e| format!("读取CSV标题失败: {}", e))?
            .iter()
            .map(|h| h.to_string())
            .collect::<Vec<String>>();

        let mut processed = 0;
        let mut success = 0;
        let mut errors = 0;

        for (row_index, result) in reader.records().enumerate() {
            let row_number = row_index + 1;
            processed += 1;

            match result {
                Ok(record) => {
                    let mut row_data = HashMap::new();
                    for (i, field) in record.iter().enumerate() {
                        if let Some(header) = headers.get(i) {
                            row_data.insert(header.clone(), field.to_string());
                        }
                    }

                    // 验证和插入零件数据
                    match self.insert_part_record(&row_data).await {
                        Ok(_) => success += 1,
                        Err(error_msg) => {
                            errors += 1;
                            self.record_import_error(
                                import_job_id,
                                row_number as i32,
                                None,
                                ERROR_TYPE_BUSINESS,
                                &error_msg,
                                &row_data,
                            ).await?;
                        }
                    }
                }
                Err(e) => {
                    errors += 1;
                    self.record_import_error(
                        import_job_id,
                        row_number as i32,
                        None,
                        ERROR_TYPE_FORMAT,
                        &format!("CSV解析错误: {}", e),
                        &HashMap::new(),
                    ).await?;
                }
            }

            // 更新进度
            if processed % 10 == 0 {
                self.update_import_progress(import_job_id, processed, success, errors).await?;
            }
        }

        // 最终更新进度
        self.update_import_progress(import_job_id, processed, success, errors).await?;
        Ok(())
    }

    /// 导入技能组数据
    async fn import_skill_groups(
        &self,
        import_job_id: i32,
        file_path: &str,
        _options: Option<ImportOptions>,
    ) -> Result<(), String> {
        let file_content = fs::read_to_string(file_path)
            .await
            .map_err(|e| format!("读取文件失败: {}", e))?;

        let mut reader = ReaderBuilder::new()
            .has_headers(true)
            .from_reader(file_content.as_bytes());

        let headers = reader.headers()
            .map_err(|e| format!("读取CSV标题失败: {}", e))?
            .iter()
            .map(|h| h.to_string())
            .collect::<Vec<String>>();

        let mut processed = 0;
        let mut success = 0;
        let mut errors = 0;

        for (row_index, result) in reader.records().enumerate() {
            let row_number = row_index + 1;
            processed += 1;

            match result {
                Ok(record) => {
                    let mut row_data = HashMap::new();
                    for (i, field) in record.iter().enumerate() {
                        if let Some(header) = headers.get(i) {
                            row_data.insert(header.clone(), field.to_string());
                        }
                    }

                    // 验证和插入技能组数据
                    match self.insert_skill_group_record(&row_data).await {
                        Ok(_) => success += 1,
                        Err(error_msg) => {
                            errors += 1;
                            self.record_import_error(
                                import_job_id,
                                row_number as i32,
                                None,
                                ERROR_TYPE_BUSINESS,
                                &error_msg,
                                &row_data,
                            ).await?;
                        }
                    }
                }
                Err(e) => {
                    errors += 1;
                    self.record_import_error(
                        import_job_id,
                        row_number as i32,
                        None,
                        ERROR_TYPE_FORMAT,
                        &format!("CSV解析错误: {}", e),
                        &HashMap::new(),
                    ).await?;
                }
            }

            // 更新进度
            if processed % 10 == 0 {
                self.update_import_progress(import_job_id, processed, success, errors).await?;
            }
        }

        // 最终更新进度
        self.update_import_progress(import_job_id, processed, success, errors).await?;
        Ok(())
    }

    /// 插入项目记录
    async fn insert_project_record(&self, row_data: &HashMap<String, String>) -> Result<(), String> {
        let project_name = row_data.get("项目名称")
            .ok_or("缺少项目名称")?
            .trim();

        if project_name.is_empty() {
            return Err("项目名称不能为空".to_string());
        }

        let customer_name = row_data.get("客户名称")
            .map(|s| s.trim())
            .filter(|s| !s.is_empty());

        // 检查项目名称是否已存在
        let existing = sqlx::query!(
            "SELECT id FROM projects WHERE project_name = $1",
            project_name
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("检查项目重复失败: {}", e))?;

        if existing.is_some() {
            return Err(format!("项目名称已存在: {}", project_name));
        }

        // 插入新项目
        sqlx::query!(
            "INSERT INTO projects (project_name, customer_name) VALUES ($1, $2)",
            project_name,
            customer_name
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("插入项目失败: {}", e))?;

        Ok(())
    }

    /// 插入零件记录
    async fn insert_part_record(&self, row_data: &HashMap<String, String>) -> Result<(), String> {
        let part_number = row_data.get("零件编号")
            .ok_or("缺少零件编号")?
            .trim();

        let version = row_data.get("版本")
            .ok_or("缺少版本")?
            .trim();

        if part_number.is_empty() {
            return Err("零件编号不能为空".to_string());
        }

        if version.is_empty() {
            return Err("版本不能为空".to_string());
        }

        let part_name = row_data.get("零件名称")
            .map(|s| s.trim())
            .filter(|s| !s.is_empty());

        let specifications = row_data.get("规格说明")
            .map(|s| s.trim())
            .filter(|s| !s.is_empty());

        // 检查零件编号+版本是否已存在
        let existing_part = sqlx::query!(
            "SELECT id FROM parts WHERE part_number = $1 AND version = $2",
            part_number,
            version
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("检查零件重复失败: {}", e))?;

        let part_id = if let Some(existing) = existing_part {
            // 零件已存在，使用现有ID
            existing.id
        } else {
            // 插入新零件
            let new_part = sqlx::query!(
                "INSERT INTO parts (part_number, part_name, version, specifications) VALUES ($1, $2, $3, $4) RETURNING id",
                part_number,
                part_name,
                version,
                specifications
            )
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("插入零件失败: {}", e))?;

            new_part.id
        };

        // 处理项目关联（如果提供了项目名称和BOM数量）
        if let (Some(project_name), Some(bom_quantity_str)) = (
            row_data.get("项目名称").map(|s| s.trim()).filter(|s| !s.is_empty()),
            row_data.get("BOM数量").map(|s| s.trim()).filter(|s| !s.is_empty())
        ) {
            // 解析BOM数量
            let bom_quantity: i32 = bom_quantity_str.parse()
                .map_err(|_| format!("BOM数量格式错误: {}", bom_quantity_str))?;

            if bom_quantity <= 0 {
                return Err("BOM数量必须大于0".to_string());
            }

            // 查找项目ID
            let project = sqlx::query!(
                "SELECT id FROM projects WHERE project_name = $1",
                project_name
            )
            .fetch_optional(&self.pool)
            .await
            .map_err(|e| format!("查找项目失败: {}", e))?;

            if let Some(project) = project {
                // 检查BOM项是否已存在
                let existing_bom = sqlx::query!(
                    "SELECT id FROM project_boms WHERE project_id = $1 AND part_id = $2",
                    project.id,
                    part_id
                )
                .fetch_optional(&self.pool)
                .await
                .map_err(|e| format!("检查BOM重复失败: {}", e))?;

                if existing_bom.is_none() {
                    // 添加到项目BOM
                    sqlx::query!(
                        "INSERT INTO project_boms (project_id, part_id, quantity) VALUES ($1, $2, $3)",
                        project.id,
                        part_id,
                        bom_quantity
                    )
                    .execute(&self.pool)
                    .await
                    .map_err(|e| format!("添加到项目BOM失败: {}", e))?;
                }
            } else {
                return Err(format!("项目不存在: {}", project_name));
            }
        }

        Ok(())
    }

    /// 插入技能组记录
    async fn insert_skill_group_record(&self, row_data: &HashMap<String, String>) -> Result<(), String> {
        let group_name = row_data.get("技能组名称")
            .ok_or("缺少技能组名称")?
            .trim();

        if group_name.is_empty() {
            return Err("技能组名称不能为空".to_string());
        }

        // 检查技能组名称是否已存在
        let existing = sqlx::query!(
            "SELECT id FROM skill_groups WHERE group_name = $1",
            group_name
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("检查技能组重复失败: {}", e))?;

        if existing.is_some() {
            return Err(format!("技能组名称已存在: {}", group_name));
        }

        // 插入新技能组
        sqlx::query!(
            "INSERT INTO skill_groups (group_name) VALUES ($1)",
            group_name
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("插入技能组失败: {}", e))?;

        Ok(())
    }

    /// 记录导入错误
    async fn record_import_error(
        &self,
        import_job_id: i32,
        row_number: i32,
        column_name: Option<&str>,
        error_type: &str,
        error_message: &str,
        row_data: &HashMap<String, String>,
    ) -> Result<(), String> {
        let row_data_json = serde_json::to_value(row_data)
            .map_err(|e| format!("序列化行数据失败: {}", e))?;

        sqlx::query!(
            r#"
            INSERT INTO import_errors (import_job_id, row_number, column_name, error_type, error_message, row_data)
            VALUES ($1, $2, $3, $4, $5, $6)
            "#,
            import_job_id,
            row_number,
            column_name,
            error_type,
            error_message,
            row_data_json
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("记录错误失败: {}", e))?;

        Ok(())
    }

    /// 更新导入进度
    async fn update_import_progress(
        &self,
        import_job_id: i32,
        processed: i32,
        success: i32,
        errors: i32,
    ) -> Result<(), String> {
        sqlx::query!(
            "UPDATE import_jobs SET processed_rows = $1, success_rows = $2, error_rows = $3 WHERE id = $4",
            processed,
            success,
            errors,
            import_job_id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("更新进度失败: {}", e))?;

        Ok(())
    }

    /// 导入设备数据
    async fn import_machines(
        &self,
        import_job_id: i32,
        file_path: &str,
        _options: Option<ImportOptions>,
    ) -> Result<(), String> {
        let file_content = fs::read_to_string(file_path)
            .await
            .map_err(|e| format!("读取文件失败: {}", e))?;

        let mut reader = ReaderBuilder::new()
            .has_headers(true)
            .from_reader(file_content.as_bytes());

        let headers = reader.headers()
            .map_err(|e| format!("读取CSV标题失败: {}", e))?
            .iter()
            .map(|h| h.to_string())
            .collect::<Vec<String>>();

        let mut processed = 0;
        let mut success = 0;
        let mut errors = 0;

        for (row_index, result) in reader.records().enumerate() {
            let row_number = row_index + 1;
            processed += 1;

            match result {
                Ok(record) => {
                    let mut row_data = HashMap::new();
                    for (i, field) in record.iter().enumerate() {
                        if let Some(header) = headers.get(i) {
                            row_data.insert(header.clone(), field.to_string());
                        }
                    }

                    // 验证和插入设备数据
                    match self.insert_machine_record(&row_data).await {
                        Ok(_) => success += 1,
                        Err(error_msg) => {
                            errors += 1;
                            self.record_import_error(
                                import_job_id,
                                row_number as i32,
                                None,
                                ERROR_TYPE_BUSINESS,
                                &error_msg,
                                &row_data,
                            ).await?;
                        }
                    }
                }
                Err(e) => {
                    errors += 1;
                    self.record_import_error(
                        import_job_id,
                        row_number as i32,
                        None,
                        ERROR_TYPE_FORMAT,
                        &format!("CSV解析错误: {}", e),
                        &HashMap::new(),
                    ).await?;
                }
            }

            // 更新进度
            if processed % 10 == 0 {
                self.update_import_progress(import_job_id, processed, success, errors).await?;
            }
        }

        // 最终更新进度
        self.update_import_progress(import_job_id, processed, success, errors).await?;
        Ok(())
    }

    /// 导入BOM数据
    async fn import_bom(
        &self,
        import_job_id: i32,
        file_path: &str,
        _options: Option<ImportOptions>,
    ) -> Result<(), String> {
        let file_content = fs::read_to_string(file_path)
            .await
            .map_err(|e| format!("读取文件失败: {}", e))?;

        let mut reader = ReaderBuilder::new()
            .has_headers(true)
            .from_reader(file_content.as_bytes());

        let headers = reader.headers()
            .map_err(|e| format!("读取CSV标题失败: {}", e))?
            .iter()
            .map(|h| h.to_string())
            .collect::<Vec<String>>();

        let mut processed = 0;
        let mut success = 0;
        let mut errors = 0;

        for (row_index, result) in reader.records().enumerate() {
            let row_number = row_index + 1;
            processed += 1;

            match result {
                Ok(record) => {
                    let mut row_data = HashMap::new();
                    for (i, field) in record.iter().enumerate() {
                        if let Some(header) = headers.get(i) {
                            row_data.insert(header.clone(), field.to_string());
                        }
                    }

                    // 验证和插入BOM数据
                    match self.insert_bom_record(&row_data).await {
                        Ok(_) => success += 1,
                        Err(error_msg) => {
                            errors += 1;
                            self.record_import_error(
                                import_job_id,
                                row_number as i32,
                                None,
                                ERROR_TYPE_BUSINESS,
                                &error_msg,
                                &row_data,
                            ).await?;
                        }
                    }
                }
                Err(e) => {
                    errors += 1;
                    self.record_import_error(
                        import_job_id,
                        row_number as i32,
                        None,
                        ERROR_TYPE_FORMAT,
                        &format!("CSV解析错误: {}", e),
                        &HashMap::new(),
                    ).await?;
                }
            }

            // 更新进度
            if processed % 10 == 0 {
                self.update_import_progress(import_job_id, processed, success, errors).await?;
            }
        }

        // 最终更新进度
        self.update_import_progress(import_job_id, processed, success, errors).await?;
        Ok(())
    }

    /// 插入设备记录
    async fn insert_machine_record(&self, row_data: &HashMap<String, String>) -> Result<(), String> {
        let machine_name = row_data.get("设备名称")
            .ok_or("缺少设备名称")?
            .trim();

        let skill_group_name = row_data.get("技能组")
            .ok_or("缺少技能组")?
            .trim();

        if machine_name.is_empty() {
            return Err("设备名称不能为空".to_string());
        }

        if skill_group_name.is_empty() {
            return Err("技能组不能为空".to_string());
        }

        let status = row_data.get("状态")
            .map(|s| s.trim())
            .filter(|s| !s.is_empty())
            .unwrap_or("available");

        // 检查设备名称是否已存在
        let existing = sqlx::query!(
            "SELECT id FROM machines WHERE machine_name = $1",
            machine_name
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("检查设备重复失败: {}", e))?;

        if existing.is_some() {
            return Err(format!("设备名称已存在: {}", machine_name));
        }

        // 查找技能组ID
        let skill_group = sqlx::query!(
            "SELECT id FROM skill_groups WHERE group_name = $1",
            skill_group_name
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("查询技能组失败: {}", e))?;

        let skill_group_id = skill_group
            .ok_or_else(|| format!("技能组不存在: {}", skill_group_name))?
            .id;

        // 插入新设备
        sqlx::query!(
            "INSERT INTO machines (machine_name, skill_group_id, status) VALUES ($1, $2, $3)",
            machine_name,
            skill_group_id,
            status
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("插入设备失败: {}", e))?;

        Ok(())
    }

    /// 插入BOM记录
    async fn insert_bom_record(&self, row_data: &HashMap<String, String>) -> Result<(), String> {
        let project_name = row_data.get("项目名称")
            .ok_or("缺少项目名称")?
            .trim();

        let part_number = row_data.get("零件编号")
            .ok_or("缺少零件编号")?
            .trim();

        let part_version = row_data.get("零件版本")
            .ok_or("缺少零件版本")?
            .trim();

        let quantity_str = row_data.get("数量")
            .ok_or("缺少数量")?
            .trim();

        if project_name.is_empty() {
            return Err("项目名称不能为空".to_string());
        }

        if part_number.is_empty() {
            return Err("零件编号不能为空".to_string());
        }

        if part_version.is_empty() {
            return Err("零件版本不能为空".to_string());
        }

        let quantity: i32 = quantity_str.parse()
            .map_err(|_| format!("数量格式错误: {}", quantity_str))?;

        if quantity <= 0 {
            return Err("数量必须大于0".to_string());
        }

        // 查找项目ID
        let project = sqlx::query!(
            "SELECT id FROM projects WHERE project_name = $1",
            project_name
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("查询项目失败: {}", e))?;

        let project_id = project
            .ok_or_else(|| format!("项目不存在: {}", project_name))?
            .id;

        // 查找零件ID
        let part = sqlx::query!(
            "SELECT id FROM parts WHERE part_number = $1 AND version = $2",
            part_number,
            part_version
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("查询零件失败: {}", e))?;

        let part_id = part
            .ok_or_else(|| format!("零件不存在: {} v{}", part_number, part_version))?
            .id;

        // 检查BOM项是否已存在
        let existing = sqlx::query!(
            "SELECT id FROM project_boms WHERE project_id = $1 AND part_id = $2",
            project_id,
            part_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("检查BOM重复失败: {}", e))?;

        if existing.is_some() {
            return Err(format!("BOM项已存在: {} - {} v{}", project_name, part_number, part_version));
        }

        // 插入新BOM项
        sqlx::query!(
            "INSERT INTO project_boms (project_id, part_id, quantity) VALUES ($1, $2, $3)",
            project_id,
            part_id,
            quantity
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("插入BOM失败: {}", e))?;

        Ok(())
    }
}
