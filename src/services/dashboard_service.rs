use crate::models::dashboard::{
    DashboardOverview, DashboardQuery, KPIMetrics, MachineStatusSummary, MachineUtilization,
    ProductionMetrics, ProductionReport, ProductionSummary, QualityMetrics, RecentActivity,
    ReportPeriod, ReportRequest, SkillGroupPerformance, TimeSeriesPoint, TrendData,
    WeeklyTaskStats, WeeklyTaskSummary, WorkOrderPerformance, WorkOrderStatusSummary,
    OperatorDashboard, CurrentTask, OperatorDailyStats, UpcomingTask, OperatorMachine,
    PlannerDashboard, PlannerStats, UrgentTask, EquipmentStatus, TodayMilestone,
};
use chrono::{Duration, Utc};
use sqlx::PgPool;

pub struct DashboardService {
    pool: PgPool,
}

impl DashboardService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn get_dashboard_overview(
        &self,
        query: DashboardQuery,
    ) -> Result<DashboardOverview, String> {
        let production_summary = self.get_production_summary(&query).await?;
        let machine_status = self.get_machine_status_summary(&query).await?;
        let work_order_status = self.get_work_order_status_summary(&query).await?;
        let quality_metrics = self.get_quality_metrics(&query).await?;
        let recent_activities = self.get_recent_activities(&query).await?;

        Ok(DashboardOverview {
            production_summary,
            machine_status,
            work_order_status,
            quality_metrics,
            recent_activities,
        })
    }

    async fn get_production_summary(
        &self,
        _query: &DashboardQuery,
    ) -> Result<ProductionSummary, String> {
        // TODO: Implement query filtering
        let today = Utc::now().date_naive();
        let start_of_day = today.and_hms_opt(0, 0, 0).unwrap().and_utc();

        // Tasks completed today
        let tasks_completed_today = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM execution_logs 
             WHERE event_type = 'task_complete' 
             AND event_time >= $1",
            start_of_day
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(0);

        // Tasks in progress
        let tasks_in_progress =
            sqlx::query_scalar!("SELECT COUNT(*) FROM plan_tasks WHERE status = 'in_progress'")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

        // Tasks pending
        let tasks_pending = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM plan_tasks WHERE status IN ('planned', 'scheduled')"
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(0);

        // Total work orders
        let total_work_orders = sqlx::query_scalar!("SELECT COUNT(*) FROM work_orders")
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?
            .unwrap_or(0);

        // Calculate on-time delivery rate (simplified)
        let completed_on_time = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM work_orders wo
             JOIN plan_tasks pt ON wo.id = pt.work_order_id
             WHERE wo.status = 'completed' 
             AND wo.due_date IS NOT NULL
             AND pt.planned_end <= wo.due_date::timestamptz"
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(0);

        let total_completed =
            sqlx::query_scalar!("SELECT COUNT(*) FROM work_orders WHERE status = 'completed'")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

        let on_time_delivery_rate = if total_completed > 0 {
            (completed_on_time as f64 / total_completed as f64) * 100.0
        } else {
            0.0
        };

        // Overall efficiency (simplified calculation)
        let overall_efficiency = 85.0; // Placeholder - would calculate based on actual vs planned times

        Ok(ProductionSummary {
            tasks_completed_today,
            tasks_in_progress,
            tasks_pending,
            total_work_orders,
            on_time_delivery_rate,
            overall_efficiency,
        })
    }

    async fn get_machine_status_summary(
        &self,
        _query: &DashboardQuery,
    ) -> Result<MachineStatusSummary, String> {
        let total_machines = sqlx::query_scalar!("SELECT COUNT(*) FROM machines")
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?
            .unwrap_or(0);

        let available_machines =
            sqlx::query_scalar!("SELECT COUNT(*) FROM machines WHERE status = 'available'")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

        let in_use_machines =
            sqlx::query_scalar!("SELECT COUNT(*) FROM machines WHERE status = 'in_use'")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

        let maintenance_machines =
            sqlx::query_scalar!("SELECT COUNT(*) FROM machines WHERE status = 'maintenance'")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

        let offline_machines =
            sqlx::query_scalar!("SELECT COUNT(*) FROM machines WHERE status = 'offline'")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

        let utilization_rate = if total_machines > 0 {
            (in_use_machines as f64 / total_machines as f64) * 100.0
        } else {
            0.0
        };

        Ok(MachineStatusSummary {
            total_machines,
            available_machines,
            in_use_machines,
            maintenance_machines,
            offline_machines,
            utilization_rate,
        })
    }

    async fn get_work_order_status_summary(
        &self,
        _query: &DashboardQuery,
    ) -> Result<WorkOrderStatusSummary, String> {
        let pending_orders =
            sqlx::query_scalar!("SELECT COUNT(*) FROM work_orders WHERE status = 'pending'")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

        let planned_orders =
            sqlx::query_scalar!("SELECT COUNT(*) FROM work_orders WHERE status = 'planned'")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

        let in_progress_orders =
            sqlx::query_scalar!("SELECT COUNT(*) FROM work_orders WHERE status = 'in_progress'")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

        let completed_orders =
            sqlx::query_scalar!("SELECT COUNT(*) FROM work_orders WHERE status = 'completed'")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

        let cancelled_orders =
            sqlx::query_scalar!("SELECT COUNT(*) FROM work_orders WHERE status = 'cancelled'")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

        let overdue_orders = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM work_orders 
             WHERE due_date < CURRENT_DATE 
             AND status NOT IN ('completed', 'cancelled')"
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(0);

        Ok(WorkOrderStatusSummary {
            pending_orders,
            planned_orders,
            in_progress_orders,
            completed_orders,
            cancelled_orders,
            overdue_orders,
        })
    }

    async fn get_quality_metrics(&self, _query: &DashboardQuery) -> Result<QualityMetrics, String> {
        // Placeholder implementation - in a real system, this would query quality inspection data
        Ok(QualityMetrics {
            total_inspections: 100,
            passed_inspections: 95,
            failed_inspections: 5,
            quality_rate: 95.0,
            defect_rate: 5.0,
        })
    }

    async fn get_recent_activities(
        &self,
        _query: &DashboardQuery,
    ) -> Result<Vec<RecentActivity>, String> {
        let activities = sqlx::query!(
            "SELECT el.id, el.event_type, el.event_time, el.notes,
                    u.username, u.full_name,
                    'plan_task' as entity_type, el.plan_task_id as entity_id
             FROM execution_logs el
             JOIN users u ON el.user_id = u.id
             ORDER BY el.event_time DESC
             LIMIT 10"
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let recent_activities = activities
            .into_iter()
            .map(|activity| {
                let description = match activity.event_type.as_str() {
                    "task_start" => "Started task execution".to_string(),
                    "task_complete" => "Completed task".to_string(),
                    "task_pause" => "Paused task".to_string(),
                    "task_resume" => "Resumed task".to_string(),
                    _ => format!("Performed {}", activity.event_type),
                };

                RecentActivity {
                    id: activity.id,
                    activity_type: activity.event_type,
                    description,
                    user_name: activity.full_name.unwrap_or(activity.username),
                    timestamp: activity.event_time,
                    entity_type: "plan_task".to_string(),
                    entity_id: activity.entity_id,
                }
            })
            .collect();

        Ok(recent_activities)
    }

    pub async fn get_production_report(
        &self,
        request: ReportRequest,
    ) -> Result<ProductionReport, String> {
        let production_metrics = self.calculate_production_metrics(&request.period).await?;
        let machine_utilization = self.calculate_machine_utilization(&request.period).await?;
        let work_order_performance = self
            .calculate_work_order_performance(&request.period)
            .await?;
        let skill_group_performance = self
            .calculate_skill_group_performance(&request.period)
            .await?;

        Ok(ProductionReport {
            report_period: request.period,
            production_metrics,
            machine_utilization,
            work_order_performance,
            skill_group_performance,
        })
    }

    async fn calculate_production_metrics(
        &self,
        period: &ReportPeriod,
    ) -> Result<ProductionMetrics, String> {
        let start_time = period.start_date.and_hms_opt(0, 0, 0).unwrap().and_utc();
        let end_time = period.end_date.and_hms_opt(23, 59, 59).unwrap().and_utc();

        let total_tasks_completed = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM execution_logs 
             WHERE event_type = 'task_complete' 
             AND event_time BETWEEN $1 AND $2",
            start_time,
            end_time
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(0);

        // Simplified calculations - in a real system, these would be more sophisticated
        let total_production_time = total_tasks_completed * 60; // Assume 60 minutes per task
        let average_task_duration = if total_tasks_completed > 0 {
            total_production_time as f64 / total_tasks_completed as f64
        } else {
            0.0
        };

        Ok(ProductionMetrics {
            total_tasks_completed,
            total_production_time,
            average_task_duration,
            on_time_completion_rate: 90.0, // Placeholder
            efficiency_rate: 85.0,         // Placeholder
            throughput: if total_production_time > 0 {
                (total_tasks_completed as f64 * 60.0) / (total_production_time as f64 / 60.0)
            } else {
                0.0
            },
        })
    }

    async fn calculate_machine_utilization(
        &self,
        _period: &ReportPeriod,
    ) -> Result<Vec<MachineUtilization>, String> {
        let machines = sqlx::query!(
            "SELECT m.id, m.machine_name, sg.group_name as skill_group_name
             FROM machines m
             JOIN skill_groups sg ON m.skill_group_id = sg.id"
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let utilization_data = machines
            .into_iter()
            .map(|machine| MachineUtilization {
                machine_id: machine.id,
                machine_name: machine.machine_name,
                skill_group_name: machine.skill_group_name,
                total_available_time: 480, // 8 hours in minutes
                total_used_time: 360,      // Placeholder
                utilization_rate: 75.0,    // Placeholder
                downtime: 60,              // Placeholder
                maintenance_time: 60,      // Placeholder
            })
            .collect();

        Ok(utilization_data)
    }

    async fn calculate_work_order_performance(
        &self,
        _period: &ReportPeriod,
    ) -> Result<Vec<WorkOrderPerformance>, String> {
        // Simplified implementation
        Ok(Vec::new())
    }

    async fn calculate_skill_group_performance(
        &self,
        _period: &ReportPeriod,
    ) -> Result<Vec<SkillGroupPerformance>, String> {
        let skill_groups = sqlx::query!(
            "SELECT sg.id, sg.group_name,
                    COUNT(pt.id) as total_tasks,
                    COUNT(CASE WHEN pt.status = 'completed' THEN 1 END) as completed_tasks
             FROM skill_groups sg
             LEFT JOIN plan_tasks pt ON sg.id = pt.skill_group_id
             GROUP BY sg.id, sg.group_name"
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let performance_data = skill_groups
            .into_iter()
            .map(|sg| {
                let total_tasks = sg.total_tasks.unwrap_or(0);
                let completed_tasks = sg.completed_tasks.unwrap_or(0);

                let efficiency_rate = if total_tasks > 0 {
                    (completed_tasks as f64 / total_tasks as f64) * 100.0
                } else {
                    0.0
                };

                SkillGroupPerformance {
                    skill_group_id: sg.id,
                    skill_group_name: sg.group_name,
                    total_tasks,
                    completed_tasks,
                    average_task_time: 60.0, // Placeholder
                    efficiency_rate,
                    utilization_rate: 80.0, // Placeholder
                }
            })
            .collect();

        Ok(performance_data)
    }

    pub async fn get_kpi_metrics(&self) -> Result<KPIMetrics, String> {
        // Placeholder implementation - in a real system, these would be calculated from actual data
        Ok(KPIMetrics {
            overall_equipment_effectiveness: 85.0,
            first_pass_yield: 95.0,
            cycle_time_variance: 5.0,
            schedule_adherence: 90.0,
            resource_utilization: 80.0,
            cost_per_unit: 25.50,
        })
    }

    pub async fn get_trend_data(&self, metric_name: &str, days: i32) -> Result<TrendData, String> {
        let end_date = Utc::now();
        let start_date = end_date - Duration::days(days as i64);

        // Simplified implementation - generate sample trend data
        let mut time_series = Vec::new();
        for i in 0..days {
            let timestamp = start_date + Duration::days(i as i64);
            let value = 80.0 + (i as f64 * 0.5) + (i as f64 % 7.0) * 2.0; // Sample trending data
            time_series.push(TimeSeriesPoint { timestamp, value });
        }

        let first_value = time_series.first().map(|p| p.value).unwrap_or(0.0);
        let last_value = time_series.last().map(|p| p.value).unwrap_or(0.0);
        let percentage_change = if first_value > 0.0 {
            ((last_value - first_value) / first_value) * 100.0
        } else {
            0.0
        };

        let trend_direction = if percentage_change > 1.0 {
            "up"
        } else if percentage_change < -1.0 {
            "down"
        } else {
            "stable"
        };

        Ok(TrendData {
            metric_name: metric_name.to_string(),
            time_series,
            trend_direction: trend_direction.to_string(),
            percentage_change,
        })
    }

    // 获取本周任务统计数据
    pub async fn get_weekly_task_stats(&self) -> Result<WeeklyTaskSummary, String> {
        use chrono::{Datelike, Weekday};

        // 获取本周的开始和结束日期（周一到周日）
        let today = chrono::Utc::now().date_naive();
        let days_from_monday = today.weekday().num_days_from_monday();
        let week_start = today - chrono::Duration::days(days_from_monday as i64);
        let week_end = week_start + chrono::Duration::days(6);

        let mut daily_stats = Vec::new();
        let mut total_planned = 0i64;
        let mut total_completed = 0i64;

        // 为本周的每一天获取统计数据
        for i in 0..7 {
            let current_date = week_start + chrono::Duration::days(i);
            let day_name = match current_date.weekday() {
                Weekday::Mon => "周一",
                Weekday::Tue => "周二",
                Weekday::Wed => "周三",
                Weekday::Thu => "周四",
                Weekday::Fri => "周五",
                Weekday::Sat => "周六",
                Weekday::Sun => "周日",
            };

            // 获取当天计划的任务数（从plan_tasks表）
            let day_start = current_date.and_hms_opt(0, 0, 0).unwrap().and_utc();
            let day_end = current_date.and_hms_opt(23, 59, 59).unwrap().and_utc();

            let planned_tasks = sqlx::query_scalar!(
                "SELECT COUNT(*) FROM plan_tasks
                 WHERE planned_start >= $1 AND planned_start <= $2",
                day_start,
                day_end
            )
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?
            .unwrap_or(0);

            // 获取当天完成的任务数（从execution_logs表）
            let completed_tasks = sqlx::query_scalar!(
                "SELECT COUNT(*) FROM execution_logs
                 WHERE event_type = 'task_complete'
                 AND event_time >= $1 AND event_time <= $2",
                day_start,
                day_end
            )
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?
            .unwrap_or(0);

            let completion_rate = if planned_tasks > 0 {
                (completed_tasks as f64 / planned_tasks as f64) * 100.0
            } else {
                0.0
            };

            daily_stats.push(WeeklyTaskStats {
                day_name: day_name.to_string(),
                date: current_date,
                planned_tasks,
                completed_tasks,
                completion_rate,
            });

            total_planned += planned_tasks;
            total_completed += completed_tasks;
        }

        let overall_completion_rate = if total_planned > 0 {
            (total_completed as f64 / total_planned as f64) * 100.0
        } else {
            0.0
        };

        Ok(WeeklyTaskSummary {
            week_start,
            week_end,
            daily_stats,
            total_planned,
            total_completed,
            overall_completion_rate,
        })
    }

    pub async fn get_operator_dashboard(&self, user_id: i32) -> Result<OperatorDashboard, String> {
        let current_task = self.get_current_task(user_id).await?;
        let daily_stats = self.get_operator_daily_stats(user_id).await?;
        let upcoming_tasks = self.get_upcoming_tasks(user_id).await?;
        let my_machines = self.get_operator_machines(user_id).await?;

        Ok(OperatorDashboard {
            current_task,
            daily_stats,
            upcoming_tasks,
            my_machines,
        })
    }

    async fn get_current_task(&self, user_id: i32) -> Result<Option<CurrentTask>, String> {
        // 只获取已经开始但未结束的任务（有start执行日志，但没有complete执行日志）
        let current_task = sqlx::query_as::<_, CurrentTask>(
            "SELECT
                pt.id as task_id,
                wo.id as work_order_id,
                CONCAT('WO', LPAD(wo.id::text, 6, '0')) as work_order_number,
                COALESCE(p.part_name, 'Unknown Part') as part_name,
                COALESCE(p.part_number, 'N/A') as part_number,
                0 as progress,
                wo.quantity as total,
                TO_CHAR(pt.planned_end, 'MM-DD HH24:MI') as estimated_completion,
                'high' as priority,
                proj.project_name,
                r.process_name,
                el_start.event_time as started_at
             FROM plan_tasks pt
             JOIN work_orders wo ON pt.work_order_id = wo.id
             JOIN project_boms pb ON wo.project_bom_id = pb.id
             JOIN parts p ON pb.part_id = p.id
             JOIN projects proj ON pb.project_id = proj.id
             JOIN routings r ON pt.routing_step_id = r.id
             JOIN execution_logs el_start ON pt.id = el_start.plan_task_id
             WHERE el_start.user_id = $1
               AND el_start.event_type = 'start'
               AND NOT EXISTS (
                   SELECT 1 FROM execution_logs el_complete
                   WHERE el_complete.plan_task_id = pt.id
                   AND el_complete.user_id = $1
                   AND el_complete.event_type = 'complete'
                   AND el_complete.event_time > el_start.event_time
               )
             ORDER BY el_start.event_time DESC
             LIMIT 1"
        )
        .bind(user_id)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Failed to get current task: {}", e))?;

        Ok(current_task)
    }

    async fn get_operator_daily_stats(&self, user_id: i32) -> Result<OperatorDailyStats, String> {
        let today = chrono::Utc::now().date_naive();

        // 获取今日完成任务数（基于execution_logs）
        let completed_tasks: i64 = sqlx::query_scalar(
            "SELECT COUNT(DISTINCT pt.id)
             FROM plan_tasks pt
             JOIN execution_logs el ON pt.id = el.plan_task_id
             WHERE el.user_id = $1
               AND pt.status = 'completed'
               AND DATE(el.event_time) = $2
               AND el.event_type = 'complete'"
        )
        .bind(user_id)
        .bind(today)
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Failed to get completed tasks: {}", e))?;

        // 获取质量合格率
        let quality_stats: (i64, i64) = sqlx::query_as(
            "SELECT
                COUNT(*) as total_inspections,
                COUNT(CASE WHEN qi.result = 'pass' THEN 1 END) as passed_inspections
             FROM quality_inspections qi
             JOIN plan_tasks pt ON qi.plan_task_id = pt.id
             JOIN execution_logs el ON pt.id = el.plan_task_id
             WHERE el.user_id = $1
               AND DATE(qi.inspection_date) = $2"
        )
        .bind(user_id)
        .bind(today)
        .fetch_one(&self.pool)
        .await
        .unwrap_or((0, 0));

        let quality_rate = if quality_stats.0 > 0 {
            (quality_stats.1 as f64 / quality_stats.0 as f64) * 100.0
        } else {
            100.0 // 如果没有检验记录，默认100%
        };

        // 计算工作效率（基于计划时间vs实际时间）
        let efficiency_data: Option<(Option<f64>, Option<f64>)> = sqlx::query_as(
            "SELECT
                AVG(EXTRACT(EPOCH FROM (pt.planned_end - pt.planned_start))/60.0) as avg_planned_minutes,
                AVG(EXTRACT(EPOCH FROM (el_end.event_time - el_start.event_time))/60.0) as avg_actual_minutes
             FROM plan_tasks pt
             JOIN execution_logs el_start ON pt.id = el_start.plan_task_id AND el_start.event_type = 'start'
             JOIN execution_logs el_end ON pt.id = el_end.plan_task_id AND el_end.event_type = 'complete'
             WHERE el_start.user_id = $1
               AND pt.status = 'completed'
               AND DATE(el_end.event_time) = $2"
        )
        .bind(user_id)
        .bind(today)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Failed to get efficiency data: {}", e))?;

        let efficiency = if let Some((Some(planned), Some(actual))) = efficiency_data {
            if actual > 0.0 {
                (planned / actual) * 100.0
            } else {
                100.0
            }
        } else {
            100.0
        };

        // 计算工作时长
        let working_hours: Option<Option<f64>> = sqlx::query_scalar(
            "SELECT
                SUM(EXTRACT(EPOCH FROM (el_end.event_time - el_start.event_time))/3600.0) as total_hours
             FROM execution_logs el_start
             JOIN execution_logs el_end ON el_start.plan_task_id = el_end.plan_task_id
                AND el_end.event_type = 'complete' AND el_start.event_type = 'start'
             WHERE el_start.user_id = $1
               AND DATE(el_start.event_time) = $2"
        )
        .bind(user_id)
        .bind(today)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Failed to get working hours: {}", e))?;

        Ok(OperatorDailyStats {
            completed_tasks,
            quality_rate,
            efficiency: efficiency.min(150.0), // 限制最大效率为150%
            working_hours: working_hours.flatten().unwrap_or(0.0),
        })
    }

    async fn get_upcoming_tasks(&self, user_id: i32) -> Result<Vec<UpcomingTask>, String> {
        // 获取当天分配给用户的任务（基于技能组匹配）
        let today = chrono::Utc::now().date_naive();
        let start_of_day = today.and_hms_opt(0, 0, 0).unwrap().and_utc();
        let end_of_day = today.and_hms_opt(23, 59, 59).unwrap().and_utc();

        let tasks = sqlx::query_as::<_, UpcomingTask>(
            "SELECT
                pt.id,
                CONCAT('WO', LPAD(wo.id::text, 6, '0')) as work_order_number,
                COALESCE(p.part_name, 'Unknown Part') as part_name,
                COALESCE(p.part_number, 'N/A') as part_number,
                CASE
                    WHEN pt.planned_start < NOW() THEN 'high'
                    WHEN pt.planned_start < NOW() + INTERVAL '2 hours' THEN 'medium'
                    ELSE 'low'
                END as priority,
                TO_CHAR(pt.planned_start, 'HH24:MI') as estimated_start,
                proj.project_name,
                r.process_name
             FROM plan_tasks pt
             JOIN work_orders wo ON pt.work_order_id = wo.id
             JOIN project_boms pb ON wo.project_bom_id = pb.id
             JOIN parts p ON pb.part_id = p.id
             JOIN projects proj ON pb.project_id = proj.id
             JOIN routings r ON pt.routing_step_id = r.id
             JOIN skill_groups sg ON pt.skill_group_id = sg.id
             JOIN user_skills us ON sg.id = us.skill_group_id
             WHERE us.user_id = $1
               AND pt.status IN ('planned', 'scheduled')
               AND pt.planned_start >= $2
               AND pt.planned_start <= $3
               AND NOT EXISTS (
                   SELECT 1 FROM execution_logs el
                   WHERE el.plan_task_id = pt.id
                   AND el.user_id = $1
                   AND el.event_type = 'start'
               )
             ORDER BY pt.planned_start ASC
             LIMIT 10"
        )
        .bind(user_id)
        .bind(start_of_day)
        .bind(end_of_day)
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Failed to get upcoming tasks: {}", e))?;

        Ok(tasks)
    }

    async fn get_operator_machines(&self, user_id: i32) -> Result<Vec<OperatorMachine>, String> {
        let machines = sqlx::query_as::<_, OperatorMachine>(
            "SELECT
                m.id as machine_id,
                m.machine_name,
                COALESCE(m.status, 'available') as status,
                sg.group_name as skill_group_name,
                COALESCE(umb.is_primary, false) as is_primary
             FROM user_machine_bindings umb
             JOIN machines m ON umb.machine_id = m.id
             JOIN skill_groups sg ON m.skill_group_id = sg.id
             WHERE umb.user_id = $1
             ORDER BY umb.is_primary DESC, m.machine_name ASC"
        )
        .bind(user_id)
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Failed to get operator machines: {}", e))?;

        Ok(machines)
    }

    pub async fn get_planner_dashboard(&self) -> Result<PlannerDashboard, String> {
        // 获取今日统计
        let today_stats = self.get_planner_stats().await?;

        // 获取紧急任务
        let urgent_tasks = self.get_urgent_tasks().await?;

        // 获取设备状态
        let equipment_status = self.get_equipment_status().await?;

        // 获取今日里程碑
        let today_milestones = self.get_today_milestones().await?;

        Ok(PlannerDashboard {
            today_stats,
            urgent_tasks,
            equipment_status,
            today_milestones,
        })
    }

    async fn get_planner_stats(&self) -> Result<PlannerStats, String> {
        // 获取总计划数
        let total_plans = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM plan_tasks"
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(0);

        // 获取按时完成的任务数
        let on_schedule = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM plan_tasks
             WHERE status = 'completed' AND planned_end >= NOW()"
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(0);

        // 获取延期任务数
        let delayed = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM plan_tasks
             WHERE status IN ('planned', 'in_progress') AND planned_end < NOW()"
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(0);

        // 获取已完成任务数
        let completed = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM plan_tasks WHERE status = 'completed'"
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(0);

        // 计算设备利用率
        let equipment_utilization = self.calculate_equipment_utilization().await?;

        Ok(PlannerStats {
            total_plans,
            on_schedule,
            delayed,
            completed,
            equipment_utilization,
        })
    }

    async fn get_urgent_tasks(&self) -> Result<Vec<UrgentTask>, String> {
        let tasks = sqlx::query!(
            r#"SELECT
                wo.id as work_order_id,
                CONCAT('WO', LPAD(wo.id::text, 6, '0')) as work_order_number,
                COALESCE(parts.part_name, 'Unknown Part') as part_name,
                parts.part_number,
                wo.due_date,
                CASE
                    WHEN wo.status = 'completed' THEN 100.0
                    WHEN wo.status = 'in_progress' THEN 50.0
                    ELSE 0.0
                END as progress,
                wo.status,
                CAST(NULL AS TEXT) as assigned_to,
                p.project_name,
                pt.id as "plan_task_id: Option<i32>",
                pt.planned_start as "planned_start: Option<chrono::DateTime<chrono::Utc>>",
                pt.planned_end as "planned_end: Option<chrono::DateTime<chrono::Utc>>",
                pt.skill_group_id as "skill_group_id: Option<i32>",
                sg.group_name as "skill_group_name: Option<String>",
                pt.machine_id as "machine_id: Option<i32>",
                m.machine_name as "machine_name: Option<String>"
             FROM work_orders wo
             JOIN project_boms pb ON wo.project_bom_id = pb.id
             JOIN projects p ON pb.project_id = p.id
             JOIN parts ON pb.part_id = parts.id
             LEFT JOIN plan_tasks pt ON wo.id = pt.work_order_id
             LEFT JOIN skill_groups sg ON pt.skill_group_id = sg.id
             LEFT JOIN machines m ON pt.machine_id = m.id
             WHERE wo.due_date <= NOW() + INTERVAL '3 days'
               AND wo.status != 'completed'
             ORDER BY wo.due_date ASC
             LIMIT 10"#
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let urgent_tasks = tasks.into_iter().map(|task| UrgentTask {
            work_order_id: task.work_order_id,
            work_order_number: task.work_order_number.unwrap_or_else(|| format!("WO{:06}", task.work_order_id)),
            part_name: task.part_name.unwrap_or_else(|| "Unknown Part".to_string()),
            part_number: task.part_number,
            due_date: task.due_date.map(|d| d.and_hms_opt(0, 0, 0).unwrap().and_utc()),
            progress: task.progress.map(|p| p.to_string().parse::<f64>().unwrap_or(0.0)).unwrap_or(0.0),
            status: task.status,
            assigned_to: task.assigned_to,
            project_name: task.project_name,
            plan_task_id: task.plan_task_id,
            planned_start: task.planned_start,
            planned_end: task.planned_end,
            skill_group_id: task.skill_group_id,
            skill_group_name: task.skill_group_name,
            machine_id: task.machine_id.flatten(),
            machine_name: task.machine_name,
        }).collect();

        Ok(urgent_tasks)
    }

    async fn get_equipment_status(&self) -> Result<Vec<EquipmentStatus>, String> {
        let status = sqlx::query!(
            "SELECT
                sg.group_name as skill_group_name,
                COUNT(m.id) as total,
                COUNT(CASE WHEN m.status = 'available' THEN 1 END) as running,
                COUNT(CASE WHEN m.status = 'idle' THEN 1 END) as idle,
                COUNT(CASE WHEN m.status = 'maintenance' THEN 1 END) as maintenance,
                CASE
                    WHEN COUNT(m.id) > 0 THEN
                        (COUNT(CASE WHEN m.status = 'available' THEN 1 END)::float / COUNT(m.id)::float * 100.0)
                    ELSE 0.0
                END as utilization
             FROM skill_groups sg
             LEFT JOIN machines m ON sg.id = m.skill_group_id
             GROUP BY sg.id, sg.group_name
             ORDER BY sg.group_name"
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let equipment_status = status.into_iter().map(|row| EquipmentStatus {
            skill_group_name: row.skill_group_name,
            total: row.total.unwrap_or(0),
            running: row.running.unwrap_or(0),
            idle: row.idle.unwrap_or(0),
            maintenance: row.maintenance.unwrap_or(0),
            utilization: row.utilization.unwrap_or(0.0),
        }).collect();

        Ok(equipment_status)
    }

    async fn get_today_milestones(&self) -> Result<Vec<TodayMilestone>, String> {
        // 基于实际数据生成今日里程碑
        let mut milestones = Vec::new();

        // 获取今日计划开始的任务
        let planned_starts = sqlx::query!(
            "SELECT
                pt.planned_start,
                wo.id as work_order_id,
                parts.part_number
             FROM plan_tasks pt
             JOIN work_orders wo ON pt.work_order_id = wo.id
             JOIN project_boms pb ON wo.project_bom_id = pb.id
             JOIN parts ON pb.part_id = parts.id
             WHERE DATE(pt.planned_start) = CURRENT_DATE
               AND pt.status = 'planned'
             ORDER BY pt.planned_start
             LIMIT 5"
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        for task in planned_starts {
            milestones.push(TodayMilestone {
                time: task.planned_start.format("%H:%M").to_string(),
                event: format!("批次{}开始", task.part_number),
                status: "scheduled".to_string(),
            });
        }

        // 获取今日计划完成的任务
        let planned_ends = sqlx::query!(
            "SELECT
                pt.planned_end,
                wo.id as work_order_id,
                parts.part_number
             FROM plan_tasks pt
             JOIN work_orders wo ON pt.work_order_id = wo.id
             JOIN project_boms pb ON wo.project_bom_id = pb.id
             JOIN parts ON pb.part_id = parts.id
             WHERE DATE(pt.planned_end) = CURRENT_DATE
               AND pt.status = 'in_progress'
             ORDER BY pt.planned_end
             LIMIT 5"
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        for task in planned_ends {
            milestones.push(TodayMilestone {
                time: task.planned_end.format("%H:%M").to_string(),
                event: format!("工单WO{}交付", task.work_order_id),
                status: "normal".to_string(),
            });
        }

        // 如果没有实际数据，添加一些示例里程碑
        if milestones.is_empty() {
            milestones.push(TodayMilestone {
                time: "16:00".to_string(),
                event: "批次20241206完成".to_string(),
                status: "normal".to_string(),
            });
            milestones.push(TodayMilestone {
                time: "17:30".to_string(),
                event: "设备CNC-001维护".to_string(),
                status: "scheduled".to_string(),
            });
            milestones.push(TodayMilestone {
                time: "18:00".to_string(),
                event: "工单WO001交付".to_string(),
                status: "critical".to_string(),
            });
            milestones.push(TodayMilestone {
                time: "19:00".to_string(),
                event: "夜班交接".to_string(),
                status: "normal".to_string(),
            });
        }

        // 按时间排序
        milestones.sort_by(|a, b| a.time.cmp(&b.time));

        Ok(milestones)
    }

    async fn calculate_equipment_utilization(&self) -> Result<f64, String> {
        let utilization = sqlx::query_scalar!(
            "SELECT
                CASE
                    WHEN COUNT(m.id) > 0 THEN
                        (COUNT(CASE WHEN m.status = 'available' THEN 1 END)::float / COUNT(m.id)::float * 100.0)
                    ELSE 0.0
                END as utilization
             FROM machines m"
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(0.0);

        Ok(utilization)
    }
}
