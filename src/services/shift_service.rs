use crate::models::shift::{
    ShiftTemplate, PlanGroup, PlanGroupShiftConfig, ShiftInstance, ShiftInstanceWithDetails,
    CreateShiftTemplateRequest, UpdateShiftTemplateRequest, CreatePlanGroupRequest, 
    UpdatePlanGroupRequest, CreatePlanGroupShiftConfigRequest, CreateShiftInstanceRequest,
    ShiftQuery, ShiftStatistics, ShiftConflictResult, ShiftAssignmentSuggestion,
    SkillGroupPlanAssignment,
};
use chrono::{DateTime, NaiveDate, NaiveDateTime, Utc, Datelike};
use sqlx::PgPool;
use sqlx::types::BigDecimal;

pub struct ShiftService {
    pool: PgPool,
}

impl ShiftService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    // ==================== 班次模板管理 ====================
    
    pub async fn create_shift_template(
        &self,
        request: CreateShiftTemplateRequest,
    ) -> Result<ShiftTemplate, String> {
        // 验证请求
        request.validate()?;
        
        // 计算班次时长
        let duration = self.calculate_shift_duration(
            request.start_hour,
            request.start_minute.unwrap_or(0),
            request.end_hour,
            request.end_minute.unwrap_or(0),
        );
        
        let shift_template = sqlx::query_as!(
            ShiftTemplate,
            r#"
            INSERT INTO shift_templates (
                template_name, schedule_type, start_hour, start_minute, 
                end_hour, end_minute, duration_hours, work_days, 
                break_periods, description
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING id, template_name, schedule_type, start_hour, start_minute,
                      end_hour, end_minute, duration_hours, work_days, break_periods,
                      description, is_system_template, is_active, created_at, updated_at
            "#,
            request.template_name,
            request.schedule_type,
            request.start_hour,
            request.start_minute.unwrap_or(0),
            request.end_hour,
            request.end_minute.unwrap_or(0),
            duration,
            &request.work_days,
            request.break_periods,
            request.description
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("创建班次模板失败: {}", e))?;

        Ok(shift_template)
    }

    pub async fn get_shift_templates(&self, active_only: bool) -> Result<Vec<ShiftTemplate>, String> {
        let templates = if active_only {
            sqlx::query_as!(
                ShiftTemplate,
                "SELECT * FROM shift_templates WHERE is_active = true ORDER BY schedule_type, template_name"
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("获取班次模板失败: {}", e))?
        } else {
            sqlx::query_as!(
                ShiftTemplate,
                "SELECT * FROM shift_templates ORDER BY schedule_type, template_name"
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("获取班次模板失败: {}", e))?
        };

        Ok(templates)
    }

    pub async fn get_shift_template_by_id(&self, id: i32) -> Result<Option<ShiftTemplate>, String> {
        let template = sqlx::query_as!(
            ShiftTemplate,
            "SELECT * FROM shift_templates WHERE id = $1",
            id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("获取班次模板失败: {}", e))?;

        Ok(template)
    }

    pub async fn update_shift_template(
        &self,
        id: i32,
        request: UpdateShiftTemplateRequest,
    ) -> Result<ShiftTemplate, String> {
        // 检查模板是否存在
        let existing = self.get_shift_template_by_id(id).await?
            .ok_or("班次模板不存在")?;

        // 检查是否为系统模板
        if existing.is_system_template {
            return Err("系统预设模板不能修改".to_string());
        }

        // 构建更新查询
        let mut query_parts = Vec::new();
        let mut param_count = 1;

        if request.template_name.is_some() {
            query_parts.push(format!("template_name = ${}", param_count));
            param_count += 1;
        }
        if request.schedule_type.is_some() {
            query_parts.push(format!("schedule_type = ${}", param_count));
            param_count += 1;
        }
        // ... 其他字段的更新逻辑

        if query_parts.is_empty() {
            return Ok(existing);
        }

        query_parts.push("updated_at = CURRENT_TIMESTAMP".to_string());
        let query = format!(
            "UPDATE shift_templates SET {} WHERE id = ${} RETURNING *",
            query_parts.join(", "),
            param_count
        );

        // 这里需要根据实际的字段动态构建查询
        // 为了简化，我们使用一个更直接的方法
        let updated = sqlx::query_as!(
            ShiftTemplate,
            r#"
            UPDATE shift_templates 
            SET template_name = COALESCE($2, template_name),
                schedule_type = COALESCE($3, schedule_type),
                start_hour = COALESCE($4, start_hour),
                start_minute = COALESCE($5, start_minute),
                end_hour = COALESCE($6, end_hour),
                end_minute = COALESCE($7, end_minute),
                work_days = COALESCE($8, work_days),
                break_periods = COALESCE($9, break_periods),
                description = COALESCE($10, description),
                is_active = COALESCE($11, is_active),
                updated_at = CURRENT_TIMESTAMP
            WHERE id = $1
            RETURNING id, template_name, schedule_type, start_hour, start_minute,
                      end_hour, end_minute, duration_hours, work_days, break_periods,
                      description, is_system_template, is_active, created_at, updated_at
            "#,
            id,
            request.template_name,
            request.schedule_type,
            request.start_hour,
            request.start_minute,
            request.end_hour,
            request.end_minute,
            request.work_days.as_deref(),
            request.break_periods,
            request.description,
            request.is_active
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("更新班次模板失败: {}", e))?;

        Ok(updated)
    }

    // ==================== 计划组管理 ====================
    
    pub async fn create_plan_group(
        &self,
        request: CreatePlanGroupRequest,
        created_by: i32,
    ) -> Result<PlanGroup, String> {
        // 检查组名和代码是否已存在
        let existing = sqlx::query!(
            "SELECT id FROM plan_groups WHERE group_name = $1 OR group_code = $2",
            request.group_name,
            request.group_code
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("数据库查询失败: {}", e))?;

        if existing.is_some() {
            return Err("计划组名称或代码已存在".to_string());
        }

        let plan_group = sqlx::query_as!(
            PlanGroup,
            r#"
            INSERT INTO plan_groups (group_name, group_code, description, priority, created_by)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING id, group_name, group_code, description, priority, is_active,
                      created_by, created_at, updated_at
            "#,
            request.group_name,
            request.group_code,
            request.description,
            request.priority.unwrap_or(1),
            created_by
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("创建计划组失败: {}", e))?;

        Ok(plan_group)
    }

    pub async fn get_plan_groups(&self, active_only: bool) -> Result<Vec<PlanGroup>, String> {
        let groups = if active_only {
            sqlx::query_as!(
                PlanGroup,
                "SELECT * FROM plan_groups WHERE is_active = true ORDER BY priority, group_name"
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("获取计划组失败: {}", e))?
        } else {
            sqlx::query_as!(
                PlanGroup,
                "SELECT * FROM plan_groups ORDER BY priority, group_name"
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("获取计划组失败: {}", e))?
        };

        Ok(groups)
    }

    // ==================== 班次实例管理 ====================
    
    pub async fn create_shift_instance(
        &self,
        request: CreateShiftInstanceRequest,
        created_by: i32,
    ) -> Result<ShiftInstance, String> {
        // 获取班次模板信息
        let template = self.get_shift_template_by_id(request.shift_template_id).await?
            .ok_or("班次模板不存在")?;

        // 计算计划开始和结束时间
        let (planned_start, planned_end) = self.calculate_shift_times(
            &request.shift_date,
            &template,
        )?;

        // 检查时间冲突
        let conflict = self.check_shift_conflicts(
            request.plan_group_id,
            planned_start,
            planned_end,
            None,
        ).await?;

        if conflict.has_conflict {
            return Err(format!("班次时间冲突: {}", 
                conflict.conflicting_shifts.len()));
        }

        let shift_instance = sqlx::query_as!(
            ShiftInstance,
            r#"
            INSERT INTO shift_instances (
                plan_group_id, shift_template_id, shift_date,
                planned_start_time, planned_end_time, assigned_users, notes, created_by
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING id, plan_group_id, shift_template_id, shift_date,
                      actual_start_time, actual_end_time, planned_start_time, planned_end_time,
                      status, assigned_users, notes, created_by, created_at, updated_at
            "#,
            request.plan_group_id,
            request.shift_template_id,
            request.shift_date,
            planned_start.naive_utc(),
            planned_end.naive_utc(),
            request.assigned_users.as_deref(),
            request.notes,
            created_by
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("创建班次实例失败: {}", e))?;

        Ok(shift_instance)
    }

    // ==================== 辅助方法 ====================
    
    fn calculate_shift_duration(&self, start_hour: i32, start_minute: i32, end_hour: i32, end_minute: i32) -> BigDecimal {
        let start_minutes = start_hour as f64 * 60.0 + start_minute as f64;
        let end_minutes = end_hour as f64 * 60.0 + end_minute as f64;

        let duration_hours = if end_minutes > start_minutes {
            (end_minutes - start_minutes) / 60.0
        } else {
            (24.0 * 60.0 - start_minutes + end_minutes) / 60.0
        };

        BigDecimal::from_f64(duration_hours).unwrap_or_default()
    }

    fn calculate_shift_times(
        &self,
        shift_date: &NaiveDate,
        template: &ShiftTemplate,
    ) -> Result<(DateTime<Utc>, DateTime<Utc>), String> {
        let start_time = shift_date
            .and_hms_opt(template.start_hour as u32, template.start_minute as u32, 0)
            .ok_or("无效的开始时间")?
            .and_utc();

        let end_time = if template.end_hour < template.start_hour {
            // 跨天班次
            shift_date
                .succ_opt()
                .ok_or("日期计算错误")?
                .and_hms_opt(template.end_hour as u32, template.end_minute as u32, 0)
                .ok_or("无效的结束时间")?
                .and_utc()
        } else {
            shift_date
                .and_hms_opt(template.end_hour as u32, template.end_minute as u32, 0)
                .ok_or("无效的结束时间")?
                .and_utc()
        };

        Ok((start_time, end_time))
    }

    pub async fn check_shift_conflicts(
        &self,
        plan_group_id: i32,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        exclude_shift_id: Option<i32>,
    ) -> Result<ShiftConflictResult, String> {
        // 查询冲突的班次实例
        let conflicting_instances = if let Some(exclude_id) = exclude_shift_id {
            sqlx::query!(
                r#"
                SELECT si.id, si.planned_start_time, si.planned_end_time, si.status,
                       pg.group_name, pg.group_code, st.template_name, st.schedule_type
                FROM shift_instances si
                JOIN plan_groups pg ON si.plan_group_id = pg.id
                JOIN shift_templates st ON si.shift_template_id = st.id
                WHERE si.plan_group_id = $1
                  AND si.status != 'cancelled'
                  AND si.id != $4
                  AND (si.planned_start_time, si.planned_end_time) OVERLAPS ($2, $3)
                "#,
                plan_group_id,
                start_time.naive_utc(),
                end_time.naive_utc(),
                exclude_id
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("查询班次冲突失败: {}", e))?
        } else {
            sqlx::query!(
                r#"
                SELECT si.id, si.planned_start_time, si.planned_end_time, si.status,
                       pg.group_name, pg.group_code, st.template_name, st.schedule_type
                FROM shift_instances si
                JOIN plan_groups pg ON si.plan_group_id = pg.id
                JOIN shift_templates st ON si.shift_template_id = st.id
                WHERE si.plan_group_id = $1
                  AND si.status != 'cancelled'
                  AND (si.planned_start_time, si.planned_end_time) OVERLAPS ($2, $3)
                "#,
                plan_group_id,
                start_time.naive_utc(),
                end_time.naive_utc()
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("查询班次冲突失败: {}", e))?
        };

        let has_conflict = !conflicting_instances.is_empty();
        let conflict_type = if has_conflict {
            Some("time_overlap".to_string())
        } else {
            None
        };

        // 构建冲突班次详情
        let conflicting_shifts: Vec<ShiftInstanceWithDetails> = conflicting_instances
            .into_iter()
            .map(|row| ShiftInstanceWithDetails {
                id: row.id,
                plan_group_id,
                plan_group_name: row.group_name,
                plan_group_code: row.group_code,
                shift_template_id: 0, // 这里需要从查询中获取
                template_name: row.template_name,
                schedule_type: row.schedule_type,
                shift_date: row.planned_start_time.date(),
                planned_start_time: row.planned_start_time.and_utc(),
                planned_end_time: row.planned_end_time.and_utc(),
                actual_start_time: None,
                actual_end_time: None,
                status: row.status,
                assigned_users: None,
                duration_hours: 0.0, // 需要计算
                work_days: vec![],
                notes: None,
            })
            .collect();

        // 生成建议
        let suggestions = if has_conflict {
            self.generate_conflict_suggestions(start_time, end_time, &conflicting_shifts).await
        } else {
            vec![]
        };

        Ok(ShiftConflictResult {
            has_conflict,
            conflict_type,
            conflicting_shifts,
            suggestions,
        })
    }

    // 生成冲突解决建议
    async fn generate_conflict_suggestions(
        &self,
        requested_start: DateTime<Utc>,
        requested_end: DateTime<Utc>,
        conflicting_shifts: &[ShiftInstanceWithDetails],
    ) -> Vec<String> {
        let mut suggestions = Vec::new();

        if conflicting_shifts.is_empty() {
            return suggestions;
        }

        // 建议1：调整时间到最早可用时段
        if let Some(earliest_conflict) = conflicting_shifts.iter().min_by_key(|s| s.planned_start_time) {
            let duration = requested_end - requested_start;
            let suggested_start = earliest_conflict.planned_end_time;
            suggestions.push(format!(
                "建议将班次调整到 {} 开始，避开与 {} 的冲突",
                suggested_start.format("%Y-%m-%d %H:%M"),
                earliest_conflict.template_name
            ));
        }

        // 建议2：调整到前一个时段
        if let Some(latest_conflict) = conflicting_shifts.iter().max_by_key(|s| s.planned_end_time) {
            let duration = requested_end - requested_start;
            let suggested_end = latest_conflict.planned_start_time;
            let suggested_start = suggested_end - duration;
            suggestions.push(format!(
                "建议将班次调整到 {} - {} 时段",
                suggested_start.format("%Y-%m-%d %H:%M"),
                suggested_end.format("%Y-%m-%d %H:%M")
            ));
        }

        // 建议3：分割班次
        if conflicting_shifts.len() == 1 {
            suggestions.push("考虑将班次分割为多个较短的时段".to_string());
        }

        // 建议4：使用其他计划组
        suggestions.push("考虑使用其他可用的计划组".to_string());

        suggestions
    }

    // 智能班次分配建议
    pub async fn suggest_shift_assignment(
        &self,
        plan_group_id: i32,
        preferred_date: chrono::NaiveDate,
        duration_hours: f64,
        skill_group_id: Option<i32>,
    ) -> Result<Vec<ShiftAssignmentSuggestion>, String> {
        // 获取计划组的可用班次模板
        let available_templates = sqlx::query!(
            r#"
            SELECT st.id, st.template_name, st.schedule_type, st.start_hour, st.start_minute,
                   st.end_hour, st.end_minute, st.duration_hours, st.work_days
            FROM shift_templates st
            JOIN plan_group_shift_configs pgsc ON st.id = pgsc.shift_template_id
            WHERE pgsc.plan_group_id = $1
              AND st.is_active = true
              AND pgsc.effective_date <= $2
              AND (pgsc.expiry_date IS NULL OR pgsc.expiry_date >= $2)
            ORDER BY pgsc.is_default DESC, st.duration_hours DESC
            "#,
            plan_group_id,
            preferred_date
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("获取可用班次模板失败: {}", e))?;

        let mut suggestions = Vec::new();

        for template in available_templates {
            // 检查工作日是否匹配
            let weekday = preferred_date.weekday().num_days_from_monday() + 1;
            if !template.work_days.contains(&(weekday as i32)) {
                continue;
            }

            // 计算建议的开始和结束时间
            let suggested_start = preferred_date
                .and_hms_opt(template.start_hour as u32, template.start_minute as u32, 0)
                .ok_or("无效的开始时间")?
                .and_utc();

            let suggested_end = if template.end_hour < template.start_hour {
                // 跨天班次
                preferred_date
                    .succ_opt()
                    .ok_or("日期计算错误")?
                    .and_hms_opt(template.end_hour as u32, template.end_minute as u32, 0)
                    .ok_or("无效的结束时间")?
                    .and_utc()
            } else {
                preferred_date
                    .and_hms_opt(template.end_hour as u32, template.end_minute as u32, 0)
                    .ok_or("无效的结束时间")?
                    .and_utc()
            };

            // 检查是否有冲突
            let conflict_result = self.check_shift_conflicts(
                plan_group_id,
                suggested_start,
                suggested_end,
                None,
            ).await?;

            // 计算置信度分数
            let mut confidence_score: f64 = 1.0;

            // 如果有冲突，降低置信度
            if conflict_result.has_conflict {
                confidence_score *= 0.3;
            }

            // 如果班次时长匹配需求，提高置信度
            let template_duration = template.duration_hours.to_f64().unwrap_or(0.0);
            if (template_duration - duration_hours).abs() < 1.0 {
                confidence_score *= 1.2;
            }

            // 7x24模式优先级更高
            if template.schedule_type == "7x24" {
                confidence_score *= 1.1;
            }

            let reason = if conflict_result.has_conflict {
                format!("班次模板 {} 可用，但存在时间冲突", template.template_name)
            } else {
                format!("班次模板 {} 完全可用，无冲突", template.template_name)
            };

            suggestions.push(ShiftAssignmentSuggestion {
                plan_group_id,
                shift_template_id: template.id,
                suggested_start_time: suggested_start,
                suggested_end_time: suggested_end,
                confidence_score: confidence_score.min(1.0),
                reason,
            });
        }

        // 按置信度排序
        suggestions.sort_by(|a, b| b.confidence_score.partial_cmp(&a.confidence_score).unwrap());

        Ok(suggestions)
    }
}
