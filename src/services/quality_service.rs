use crate::models::quality::{
    CreateQualityCheckpointRequest, CreateQualityInspectionRequest, QualityCheckpoint,
    QualityInspection, QualityInspectionQuery, QualityInspectionSearchResult,
    QualityInspectionWithDetails, QualityOverallMetrics, QualityReport, QualityReportPeriod,
    UpdateQualityInspectionRequest,
};
use sqlx::{PgPool, Row};

pub struct QualityService {
    #[allow(dead_code)]
    pool: PgPool,
}

impl QualityService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn create_quality_inspection(
        &self,
        inspector_user_id: i32,
        request: CreateQualityInspectionRequest,
    ) -> Result<QualityInspection, String> {
        // Validate that the plan task exists
        let plan_task_exists: bool = sqlx::query_scalar(
            "SELECT EXISTS(SELECT 1 FROM plan_tasks WHERE id = $1)"
        )
        .bind(request.plan_task_id)
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Failed to validate plan task: {}", e))?;

        if !plan_task_exists {
            return Err("Plan task not found".to_string());
        }

        // Insert the quality inspection
        let inspection = sqlx::query_as::<_, QualityInspection>(
            "INSERT INTO quality_inspections
             (plan_task_id, inspector_user_id, inspection_type, status, result, notes, inspection_date, created_at)
             VALUES ($1, $2, $3, 'pending', 'pending', $4, NOW(), NOW())
             RETURNING id, plan_task_id, inspector_user_id, inspection_type, status, result, notes, inspection_date, created_at"
        )
        .bind(request.plan_task_id)
        .bind(inspector_user_id)
        .bind(request.inspection_type)
        .bind(request.notes)
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Failed to create quality inspection: {}", e))?;

        Ok(inspection)
    }

    pub async fn get_quality_inspection_by_id(
        &self,
        inspection_id: i32,
    ) -> Result<Option<QualityInspectionWithDetails>, String> {
        let inspection = sqlx::query_as::<_, QualityInspectionWithDetails>(
            "SELECT
                qi.id, qi.plan_task_id, qi.inspector_user_id, qi.inspection_type,
                qi.status, qi.result, qi.notes, qi.inspection_date, qi.created_at,
                COALESCE(pt.work_order_id, 0) as work_order_id,
                COALESCE(pt.routing_step_id, 0) as routing_step_id,
                COALESCE(pt.skill_group_id, 0) as skill_group_id,
                COALESCE(wo.quantity, 0) as work_order_quantity,
                COALESCE(wo.status, '') as work_order_status,
                COALESCE(p.id, 0) as project_id,
                COALESCE(p.project_name, '') as project_name,
                p.customer_name,
                COALESCE(parts.id, 0) as part_id,
                COALESCE(parts.part_number, '') as part_number,
                parts.part_name,
                COALESCE(parts.version, '') as version,
                COALESCE(r.step_number, 0) as step_number,
                COALESCE(r.process_name, '') as process_name,
                COALESCE(u.username, '') as inspector_username,
                u.full_name as inspector_full_name
             FROM quality_inspections qi
             LEFT JOIN plan_tasks pt ON qi.plan_task_id = pt.id
             LEFT JOIN work_orders wo ON pt.work_order_id = wo.id
             LEFT JOIN project_boms pb ON wo.project_bom_id = pb.id
             LEFT JOIN projects p ON pb.project_id = p.id
             LEFT JOIN parts ON pb.part_id = parts.id
             LEFT JOIN routings r ON pt.routing_step_id = r.id
             LEFT JOIN users u ON qi.inspector_user_id = u.id
             WHERE qi.id = $1"
        )
        .bind(inspection_id)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Failed to fetch inspection by id: {}", e))?;

        Ok(inspection)
    }

    pub async fn update_quality_inspection(
        &self,
        inspection_id: i32,
        request: UpdateQualityInspectionRequest,
    ) -> Result<Option<QualityInspection>, String> {
        // Build dynamic update query
        let mut set_clauses = Vec::new();
        let mut param_count = 0;

        if request.status.is_some() {
            param_count += 1;
            set_clauses.push(format!("status = ${}", param_count));
        }

        if request.result.is_some() {
            param_count += 1;
            set_clauses.push(format!("result = ${}", param_count));
        }

        if request.notes.is_some() {
            param_count += 1;
            set_clauses.push(format!("notes = ${}", param_count));
        }

        if set_clauses.is_empty() {
            return Err("No fields to update".to_string());
        }

        param_count += 1;
        let id_param = param_count;

        let update_query = format!(
            "UPDATE quality_inspections
             SET {}
             WHERE id = ${}
             RETURNING id, plan_task_id, inspector_user_id, inspection_type, status, result, notes, inspection_date, created_at",
            set_clauses.join(", "),
            id_param
        );

        let mut query = sqlx::query_as::<_, QualityInspection>(&update_query);

        // Bind parameters in the same order as set_clauses
        if let Some(status) = request.status {
            query = query.bind(status);
        }
        if let Some(result) = request.result {
            query = query.bind(result);
        }
        if let Some(notes) = request.notes {
            query = query.bind(notes);
        }
        query = query.bind(inspection_id);

        let inspection = query
            .fetch_optional(&self.pool)
            .await
            .map_err(|e| format!("Failed to update quality inspection: {}", e))?;

        Ok(inspection)
    }

    pub async fn get_all_quality_inspections(
        &self,
        query: QualityInspectionQuery,
    ) -> Result<QualityInspectionSearchResult, String> {
        let limit = query.limit.unwrap_or(50).min(100);
        let offset = query.offset.unwrap_or(0);

        // For simplicity, let's start with a basic query without complex filtering
        // We can enhance this later with proper parameter binding

        // Query for total count
        let total_count: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM quality_inspections"
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Failed to get inspection count: {}", e))?;

        // Query for inspections with details
        let inspections = sqlx::query_as::<_, QualityInspectionWithDetails>(
            "SELECT
                qi.id, qi.plan_task_id, qi.inspector_user_id, qi.inspection_type,
                qi.status, qi.result, qi.notes, qi.inspection_date, qi.created_at,
                COALESCE(pt.work_order_id, 0) as work_order_id,
                COALESCE(pt.routing_step_id, 0) as routing_step_id,
                COALESCE(pt.skill_group_id, 0) as skill_group_id,
                COALESCE(wo.quantity, 0) as work_order_quantity,
                COALESCE(wo.status, '') as work_order_status,
                COALESCE(p.id, 0) as project_id,
                COALESCE(p.project_name, '') as project_name,
                p.customer_name,
                COALESCE(parts.id, 0) as part_id,
                COALESCE(parts.part_number, '') as part_number,
                parts.part_name,
                COALESCE(parts.version, '') as version,
                COALESCE(r.step_number, 0) as step_number,
                COALESCE(r.process_name, '') as process_name,
                COALESCE(u.username, '') as inspector_username,
                u.full_name as inspector_full_name
             FROM quality_inspections qi
             LEFT JOIN plan_tasks pt ON qi.plan_task_id = pt.id
             LEFT JOIN work_orders wo ON pt.work_order_id = wo.id
             LEFT JOIN project_boms pb ON wo.project_bom_id = pb.id
             LEFT JOIN projects p ON pb.project_id = p.id
             LEFT JOIN parts ON pb.part_id = parts.id
             LEFT JOIN routings r ON pt.routing_step_id = r.id
             LEFT JOIN users u ON qi.inspector_user_id = u.id
             ORDER BY qi.inspection_date DESC
             LIMIT $1 OFFSET $2"
        )
        .bind(limit)
        .bind(offset)
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Failed to fetch inspections: {}", e))?;

        Ok(QualityInspectionSearchResult {
            inspections,
            total_count,
            limit,
            offset,
        })
    }

    pub async fn create_quality_checkpoint(
        &self,
        request: CreateQualityCheckpointRequest,
    ) -> Result<QualityCheckpoint, String> {
        // Validate that the routing step exists
        let routing_step_exists: bool = sqlx::query_scalar(
            "SELECT EXISTS(SELECT 1 FROM routings WHERE id = $1)"
        )
        .bind(request.routing_step_id)
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Failed to validate routing step: {}", e))?;

        if !routing_step_exists {
            return Err("Routing step not found".to_string());
        }

        // Insert the quality checkpoint
        let checkpoint = sqlx::query_as::<_, QualityCheckpoint>(
            "INSERT INTO quality_checkpoints
             (routing_step_id, checkpoint_name, description, required, inspection_criteria,
              measurement_type, tolerance_min, tolerance_max, unit_of_measure, created_at)
             VALUES ($1, $2, $3, $4, $5, $6,
                     CASE WHEN $7 IS NOT NULL THEN $7::numeric ELSE NULL END,
                     CASE WHEN $8 IS NOT NULL THEN $8::numeric ELSE NULL END,
                     $9, NOW())
             RETURNING id, routing_step_id, checkpoint_name, description, required,
                       inspection_criteria, measurement_type, tolerance_min::text, tolerance_max::text,
                       unit_of_measure, created_at"
        )
        .bind(request.routing_step_id)
        .bind(request.checkpoint_name)
        .bind(request.description)
        .bind(request.required)
        .bind(request.inspection_criteria)
        .bind(request.measurement_type)
        .bind(request.tolerance_min)
        .bind(request.tolerance_max)
        .bind(request.unit_of_measure)
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Failed to create quality checkpoint: {}", e))?;

        Ok(checkpoint)
    }

    pub async fn get_quality_report(
        &self,
        period: QualityReportPeriod,
    ) -> Result<QualityReport, String> {
        // Get overall metrics with date filtering
        let metrics_query = "SELECT
            COUNT(*) as total_inspections,
            COUNT(CASE WHEN result = 'pass' THEN 1 END) as passed_inspections,
            COUNT(CASE WHEN result = 'fail' THEN 1 END) as failed_inspections,
            COUNT(CASE WHEN status = 'pending' OR status = 'in_progress' THEN 1 END) as pending_inspections
         FROM quality_inspections
         WHERE inspection_date >= $1 AND inspection_date <= $2";

        let metrics_row = sqlx::query(metrics_query)
            .bind(period.start_date)
            .bind(period.end_date)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Failed to fetch quality metrics: {}", e))?;

        let total_inspections: i64 = metrics_row.get("total_inspections");
        let passed_inspections: i64 = metrics_row.get("passed_inspections");
        let failed_inspections: i64 = metrics_row.get("failed_inspections");
        let pending_inspections: i64 = metrics_row.get("pending_inspections");

        let completed_inspections = passed_inspections + failed_inspections;
        let pass_rate = if completed_inspections > 0 {
            (passed_inspections as f64 / completed_inspections as f64) * 100.0
        } else {
            0.0
        };

        let first_pass_yield = pass_rate; // Simplified - in reality this would be more complex
        let defect_rate = 100.0 - pass_rate;

        let overall_metrics = QualityOverallMetrics {
            total_inspections,
            passed_inspections,
            failed_inspections,
            pending_inspections,
            pass_rate,
            first_pass_yield,
            defect_rate,
        };

        Ok(QualityReport {
            report_period: period,
            overall_metrics,
            inspection_summary: Vec::new(), // TODO: Implement detailed summaries
            defect_analysis: Vec::new(),    // TODO: Implement defect analysis
            trend_data: Vec::new(),         // TODO: Implement trend data
        })
    }
}
