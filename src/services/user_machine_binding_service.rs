use crate::models::user_machine_binding::{
    AvailableMachine, CreateUserMachineBindingRequest, UpdateUserMachineBindingRequest,
    UserMachineBinding, UserMachineBindingWithDetails, UserMachineBindingsResponse,
};
use sqlx::PgPool;

pub struct UserMachineBindingService {
    pool: PgPool,
}

impl UserMachineBindingService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    // 获取用户的设备绑定列表
    pub async fn get_user_machine_bindings(
        &self,
        user_id: i32,
    ) -> Result<UserMachineBindingsResponse, String> {
        // 获取用户已绑定的设备
        let bindings = sqlx::query_as!(
            UserMachineBindingWithDetails,
            "SELECT 
                umb.id,
                umb.user_id,
                umb.machine_id,
                m.machine_name,
                sg.group_name as skill_group_name,
                m.status as machine_status,
                umb.is_primary,
                umb.created_at,
                umb.updated_at
             FROM user_machine_bindings umb
             JOIN machines m ON umb.machine_id = m.id
             JOIN skill_groups sg ON m.skill_group_id = sg.id
             WHERE umb.user_id = $1
             ORDER BY umb.is_primary DESC, umb.created_at ASC",
            user_id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        // 获取用户技能组对应的可用设备（排除已绑定的）
        let bound_machine_ids: Vec<i32> = bindings.iter().map(|b| b.machine_id).collect();
        
        let available_machines = if bound_machine_ids.is_empty() {
            let machines = sqlx::query!(
                "SELECT
                    m.id,
                    m.machine_name,
                    sg.group_name as skill_group_name,
                    m.status
                 FROM machines m
                 JOIN skill_groups sg ON m.skill_group_id = sg.id
                 WHERE m.skill_group_id IN (
                     SELECT skill_group_id FROM user_skills WHERE user_id = $1
                 )
                 ORDER BY m.machine_name",
                user_id
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

            machines.into_iter().map(|m| AvailableMachine {
                id: m.id,
                machine_name: m.machine_name,
                skill_group_name: m.skill_group_name,
                status: m.status,
                is_bound: false,
            }).collect()
        } else {
            let machines = sqlx::query!(
                "SELECT
                    m.id,
                    m.machine_name,
                    sg.group_name as skill_group_name,
                    m.status
                 FROM machines m
                 JOIN skill_groups sg ON m.skill_group_id = sg.id
                 WHERE m.skill_group_id IN (
                     SELECT skill_group_id FROM user_skills WHERE user_id = $1
                 )
                 AND m.id != ALL($2)
                 ORDER BY m.machine_name",
                user_id,
                &bound_machine_ids
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

            machines.into_iter().map(|m| AvailableMachine {
                id: m.id,
                machine_name: m.machine_name,
                skill_group_name: m.skill_group_name,
                status: m.status,
                is_bound: false,
            }).collect()
        };

        Ok(UserMachineBindingsResponse {
            bindings,
            available_machines,
        })
    }

    // 创建设备绑定
    pub async fn create_machine_binding(
        &self,
        user_id: i32,
        request: CreateUserMachineBindingRequest,
    ) -> Result<UserMachineBinding, String> {
        // 验证设备是否存在且用户有权限绑定（通过技能组）
        let machine_valid = sqlx::query_scalar!(
            "SELECT EXISTS(
                SELECT 1 FROM machines m
                JOIN skill_groups sg ON m.skill_group_id = sg.id
                WHERE m.id = $1 
                AND m.skill_group_id IN (
                    SELECT skill_group_id FROM user_skills WHERE user_id = $2
                )
            )",
            request.machine_id,
            user_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(false);

        if !machine_valid {
            return Err("设备不存在或您没有权限绑定此设备".to_string());
        }

        // 检查是否已经绑定
        let already_bound = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM user_machine_bindings WHERE user_id = $1 AND machine_id = $2)",
            user_id,
            request.machine_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(false);

        if already_bound {
            return Err("您已经绑定了此设备".to_string());
        }

        let is_primary = request.is_primary.unwrap_or(false);

        // 如果设置为主要设备，先取消其他主要设备
        if is_primary {
            sqlx::query!(
                "UPDATE user_machine_bindings SET is_primary = false WHERE user_id = $1",
                user_id
            )
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;
        }

        // 创建绑定
        let binding = sqlx::query_as!(
            UserMachineBinding,
            "INSERT INTO user_machine_bindings (user_id, machine_id, is_primary)
             VALUES ($1, $2, $3)
             RETURNING id, user_id, machine_id, is_primary, created_at, updated_at",
            user_id,
            request.machine_id,
            is_primary
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(binding)
    }

    // 更新设备绑定
    pub async fn update_machine_binding(
        &self,
        user_id: i32,
        binding_id: i32,
        request: UpdateUserMachineBindingRequest,
    ) -> Result<UserMachineBinding, String> {
        // 验证绑定是否属于当前用户
        let binding_exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM user_machine_bindings WHERE id = $1 AND user_id = $2)",
            binding_id,
            user_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(false);

        if !binding_exists {
            return Err("绑定不存在或您没有权限修改".to_string());
        }

        if let Some(is_primary) = request.is_primary {
            if is_primary {
                // 如果设置为主要设备，先取消其他主要设备
                sqlx::query!(
                    "UPDATE user_machine_bindings SET is_primary = false WHERE user_id = $1 AND id != $2",
                    user_id,
                    binding_id
                )
                .execute(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?;
            }

            let binding = sqlx::query_as!(
                UserMachineBinding,
                "UPDATE user_machine_bindings 
                 SET is_primary = $1, updated_at = NOW()
                 WHERE id = $2 AND user_id = $3
                 RETURNING id, user_id, machine_id, is_primary, created_at, updated_at",
                is_primary,
                binding_id,
                user_id
            )
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

            Ok(binding)
        } else {
            Err("没有提供要更新的字段".to_string())
        }
    }

    // 删除设备绑定
    pub async fn delete_machine_binding(&self, user_id: i32, binding_id: i32) -> Result<bool, String> {
        let result = sqlx::query!(
            "DELETE FROM user_machine_bindings WHERE id = $1 AND user_id = $2",
            binding_id,
            user_id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(result.rows_affected() > 0)
    }

    // 获取用户的主要设备
    pub async fn get_user_primary_machine(
        &self,
        user_id: i32,
    ) -> Result<Option<UserMachineBindingWithDetails>, String> {
        let binding = sqlx::query_as!(
            UserMachineBindingWithDetails,
            "SELECT 
                umb.id,
                umb.user_id,
                umb.machine_id,
                m.machine_name,
                sg.group_name as skill_group_name,
                m.status as machine_status,
                umb.is_primary,
                umb.created_at,
                umb.updated_at
             FROM user_machine_bindings umb
             JOIN machines m ON umb.machine_id = m.id
             JOIN skill_groups sg ON m.skill_group_id = sg.id
             WHERE umb.user_id = $1 AND umb.is_primary = true",
            user_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(binding)
    }
}
