use redis::{AsyncCommands, Client, RedisResult};
use serde::{Deserialize, Serialize};
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::time::sleep;
use tracing::{info, warn, error};
use std::collections::HashMap;

#[derive(Clone)]
pub struct CacheService {
    client: Client,
}

impl CacheService {
    pub fn new(redis_url: &str) -> RedisResult<Self> {
        let client = Client::open(redis_url)?;
        Ok(Self { client })
    }

    pub async fn get_connection(&self) -> RedisResult<redis::aio::Connection> {
        self.client.get_async_connection().await
    }

    // 通用缓存操作
    pub async fn get<T>(&self, key: &str) -> RedisResult<Option<T>>
    where
        T: for<'de> Deserialize<'de>,
    {
        let mut conn = self.get_connection().await?;
        let value: Option<String> = conn.get(key).await?;
        
        match value {
            Some(json_str) => {
                match serde_json::from_str(&json_str) {
                    Ok(data) => Ok(Some(data)),
                    Err(_) => Ok(None),
                }
            }
            None => Ok(None),
        }
    }

    pub async fn set<T>(&self, key: &str, value: &T, ttl_seconds: Option<u64>) -> RedisResult<()>
    where
        T: Serialize,
    {
        let mut conn = self.get_connection().await?;
        let json_str = serde_json::to_string(value).map_err(|_| {
            redis::RedisError::from((redis::ErrorKind::TypeError, "序列化失败"))
        })?;

        if let Some(ttl) = ttl_seconds {
            conn.set_ex(key, json_str, ttl).await
        } else {
            conn.set(key, json_str).await
        }
    }

    pub async fn delete(&self, key: &str) -> RedisResult<()> {
        let mut conn = self.get_connection().await?;
        conn.del(key).await
    }

    pub async fn exists(&self, key: &str) -> RedisResult<bool> {
        let mut conn = self.get_connection().await?;
        conn.exists(key).await
    }

    // 批量操作
    pub async fn delete_pattern(&self, pattern: &str) -> RedisResult<u64> {
        let mut conn = self.get_connection().await?;
        let keys: Vec<String> = conn.keys(pattern).await?;
        if keys.is_empty() {
            return Ok(0);
        }
        conn.del(&keys).await
    }

    // 缓存穿透保护 - 使用空值缓存
    pub async fn get_or_set_null<T, F, Fut>(&self, key: &str, fetch_fn: F, ttl_seconds: u64) -> RedisResult<Option<T>>
    where
        T: Serialize + for<'de> Deserialize<'de>,
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = Option<T>>,
    {
        // 先尝试从缓存获取
        if let Some(cached) = self.get::<Option<T>>(key).await? {
            return Ok(cached);
        }

        // 缓存未命中，执行获取函数
        let result = fetch_fn().await;
        
        // 无论结果是否为None都缓存，防止缓存穿透
        self.set(key, &result, Some(ttl_seconds)).await?;
        
        Ok(result)
    }

    // 缓存击穿保护 - 使用分布式锁
    pub async fn get_or_set_with_lock<T, F, Fut>(&self, 
        key: &str, 
        lock_key: &str,
        fetch_fn: F, 
        ttl_seconds: u64,
        lock_timeout_seconds: u64
    ) -> RedisResult<Option<T>>
    where
        T: Serialize + for<'de> Deserialize<'de>,
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = Option<T>>,
    {
        // 先尝试从缓存获取
        if let Some(cached) = self.get::<T>(key).await? {
            return Ok(Some(cached));
        }

        // 尝试获取分布式锁
        let mut conn = self.get_connection().await?;
        let lock_acquired: bool = conn.set_nx(lock_key, "locked").await?;
        
        if lock_acquired {
            // 设置锁的过期时间
            let _: () = conn.expire(lock_key, lock_timeout_seconds as i64).await?;
            
            // 再次检查缓存（双重检查）
            if let Some(cached) = self.get::<T>(key).await? {
                // 释放锁
                let _: () = conn.del(lock_key).await?;
                return Ok(Some(cached));
            }

            // 执行获取函数
            let result = fetch_fn().await;
            
            // 缓存结果
            if let Some(ref data) = result {
                self.set(key, data, Some(ttl_seconds)).await?;
            }
            
            // 释放锁
            let _: () = conn.del(lock_key).await?;
            
            Ok(result)
        } else {
            // 未获取到锁，等待一段时间后重试
            sleep(Duration::from_millis(100)).await;
            
            // 重试获取缓存
            if let Some(cached) = self.get::<T>(key).await? {
                Ok(Some(cached))
            } else {
                // 如果还是没有，返回None（避免无限等待）
                Ok(None)
            }
        }
    }

    // 健康检查
    pub async fn health_check(&self) -> bool {
        match self.get_connection().await {
            Ok(mut conn) => {
                match redis::cmd("PING").query_async::<_, String>(&mut conn).await {
                    Ok(response) => response == "PONG",
                    Err(_) => false,
                }
            }
            Err(_) => false,
        }
    }

    // 获取缓存统计信息
    pub async fn get_stats(&self) -> RedisResult<CacheStats> {
        let mut conn = self.get_connection().await?;
        let info: String = redis::cmd("INFO").arg("stats").query_async(&mut conn).await?;
        
        // 解析INFO命令的输出
        let mut hits = 0u64;
        let mut misses = 0u64;
        
        for line in info.lines() {
            if line.starts_with("keyspace_hits:") {
                hits = line.split(':').nth(1).unwrap_or("0").parse().unwrap_or(0);
            } else if line.starts_with("keyspace_misses:") {
                misses = line.split(':').nth(1).unwrap_or("0").parse().unwrap_or(0);
            }
        }
        
        let total = hits + misses;
        let hit_rate = if total > 0 { hits as f64 / total as f64 } else { 0.0 };
        
        Ok(CacheStats {
            hits,
            misses,
            hit_rate,
        })
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CacheStats {
    pub hits: u64,
    pub misses: u64,
    pub hit_rate: f64,
}

// 缓存键生成器
pub struct CacheKeyBuilder;

impl CacheKeyBuilder {
    pub fn projects_list(page: Option<i32>, limit: Option<i32>) -> String {
        match (page, limit) {
            (Some(p), Some(l)) => format!("projects:list:{}:{}", p, l),
            _ => "projects:list:all".to_string(),
        }
    }

    pub fn project_detail(id: i32) -> String {
        format!("project:{}", id)
    }

    pub fn work_orders_list(project_id: Option<i32>) -> String {
        match project_id {
            Some(id) => format!("work_orders:project:{}", id),
            None => "work_orders:list:all".to_string(),
        }
    }

    pub fn plan_tasks_list(project_id: Option<i32>) -> String {
        match project_id {
            Some(id) => format!("plan_tasks:project:{}", id),
            None => "plan_tasks:list:all".to_string(),
        }
    }

    pub fn user_permissions(user_id: i32) -> String {
        format!("user:{}:permissions", user_id)
    }

    pub fn system_config(key: &str) -> String {
        format!("config:{}", key)
    }

    pub fn lock_key(base_key: &str) -> String {
        format!("lock:{}", base_key)
    }
}

// 缓存TTL常量（秒）
pub struct CacheTTL;

impl CacheTTL {
    pub const SHORT: u64 = 300;      // 5分钟
    pub const MEDIUM: u64 = 1800;    // 30分钟
    pub const LONG: u64 = 3600;      // 1小时
    pub const VERY_LONG: u64 = 86400; // 24小时
}

// 缓存策略枚举
#[derive(Debug, Clone)]
pub enum CacheStrategy {
    /// 简单缓存，到期后删除
    Simple { ttl: u64 },
    /// 写穿透缓存，更新时同时更新缓存和数据库
    WriteThrough { ttl: u64 },
    /// 写回缓存，延迟写入数据库
    WriteBack { ttl: u64, sync_interval: u64 },
    /// 只读缓存，数据变更时失效
    ReadOnly { ttl: u64 },
    /// 热点数据缓存，访问频率高的数据延长TTL
    HotData { base_ttl: u64, max_ttl: u64, access_threshold: u32 },
}

// 扩展缓存统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExtendedCacheStats {
    pub hits: u64,
    pub misses: u64,
    pub sets: u64,
    pub deletes: u64,
    pub hit_rate: f64,
    pub last_updated: u64,
}

impl ExtendedCacheStats {
    pub fn new() -> Self {
        Self {
            hits: 0,
            misses: 0,
            sets: 0,
            deletes: 0,
            hit_rate: 0.0,
            last_updated: SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs(),
        }
    }

    pub fn record_hit(&mut self) {
        self.hits += 1;
        self.update_hit_rate();
    }

    pub fn record_miss(&mut self) {
        self.misses += 1;
        self.update_hit_rate();
    }

    pub fn record_set(&mut self) {
        self.sets += 1;
    }

    pub fn record_delete(&mut self) {
        self.deletes += 1;
    }

    fn update_hit_rate(&mut self) {
        let total = self.hits + self.misses;
        self.hit_rate = if total > 0 {
            self.hits as f64 / total as f64
        } else {
            0.0
        };
        self.last_updated = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs();
    }
}

// 智能缓存管理器
pub struct SmartCacheManager {
    cache_service: CacheService,
    stats: ExtendedCacheStats,
    strategies: HashMap<String, CacheStrategy>,
    access_counts: HashMap<String, u32>,
}

impl SmartCacheManager {
    pub fn new(cache_service: CacheService) -> Self {
        Self {
            cache_service,
            stats: ExtendedCacheStats::new(),
            strategies: HashMap::new(),
            access_counts: HashMap::new(),
        }
    }

    /// 设置缓存策略
    pub fn set_strategy(&mut self, pattern: String, strategy: CacheStrategy) {
        info!("设置缓存策略: {} -> {:?}", pattern, strategy);
        self.strategies.insert(pattern, strategy);
    }

    /// 智能获取缓存
    pub async fn smart_get<T>(&mut self, key: &str) -> RedisResult<Option<T>>
    where
        T: for<'de> Deserialize<'de>,
    {
        // 记录访问次数
        *self.access_counts.entry(key.to_string()).or_insert(0) += 1;

        match self.cache_service.get::<T>(key).await {
            Ok(Some(value)) => {
                self.stats.record_hit();
                info!("缓存命中: {}", key);
                Ok(Some(value))
            }
            Ok(None) => {
                self.stats.record_miss();
                info!("缓存未命中: {}", key);
                Ok(None)
            }
            Err(e) => {
                self.stats.record_miss();
                warn!("缓存获取失败: {} - {}", key, e);
                Err(e)
            }
        }
    }

    /// 智能设置缓存
    pub async fn smart_set<T>(&mut self, key: &str, value: &T) -> RedisResult<()>
    where
        T: Serialize,
    {
        let ttl = self.calculate_smart_ttl(key);

        match self.cache_service.set(key, value, Some(ttl)).await {
            Ok(_) => {
                self.stats.record_set();
                info!("缓存设置成功: {} (TTL: {}s)", key, ttl);
                Ok(())
            }
            Err(e) => {
                error!("缓存设置失败: {} - {}", key, e);
                Err(e)
            }
        }
    }

    /// 计算智能TTL
    fn calculate_smart_ttl(&self, key: &str) -> u64 {
        // 查找匹配的策略
        for (pattern, strategy) in &self.strategies {
            if key.contains(pattern) {
                return match strategy {
                    CacheStrategy::Simple { ttl } => *ttl,
                    CacheStrategy::WriteThrough { ttl } => *ttl,
                    CacheStrategy::WriteBack { ttl, .. } => *ttl,
                    CacheStrategy::ReadOnly { ttl } => *ttl,
                    CacheStrategy::HotData { base_ttl, max_ttl, access_threshold } => {
                        let access_count = self.access_counts.get(key).unwrap_or(&0);
                        if *access_count >= *access_threshold {
                            *max_ttl
                        } else {
                            *base_ttl
                        }
                    }
                };
            }
        }

        // 默认TTL
        CacheTTL::MEDIUM
    }

    /// 批量失效相关缓存
    pub async fn invalidate_related(&mut self, patterns: &[&str]) -> RedisResult<u64> {
        let mut total_deleted = 0;

        for pattern in patterns {
            match self.cache_service.delete_pattern(pattern).await {
                Ok(deleted) => {
                    total_deleted += deleted;
                    self.stats.record_delete();
                    info!("失效缓存模式: {} (删除 {} 个键)", pattern, deleted);
                }
                Err(e) => {
                    error!("失效缓存失败: {} - {}", pattern, e);
                }
            }
        }

        Ok(total_deleted)
    }

    /// 获取缓存统计信息
    pub fn get_stats(&self) -> &ExtendedCacheStats {
        &self.stats
    }

    /// 重置统计信息
    pub fn reset_stats(&mut self) {
        self.stats = ExtendedCacheStats::new();
        self.access_counts.clear();
        info!("缓存统计信息已重置");
    }

    /// 获取热点数据
    pub fn get_hot_keys(&self, limit: usize) -> Vec<(String, u32)> {
        let mut sorted_keys: Vec<_> = self.access_counts.iter().collect();
        sorted_keys.sort_by(|a, b| b.1.cmp(a.1));
        sorted_keys
            .into_iter()
            .take(limit)
            .map(|(k, v)| (k.clone(), *v))
            .collect()
    }
}
