@echo off
setlocal enabledelayedexpansion

REM MES系统Windows启动脚本
REM 版本: 1.0

echo ========================================
echo MES系统启动脚本 (Windows)
echo ========================================
echo.

REM 设置变量
set SCRIPT_DIR=%~dp0
set MES_BINARY=%SCRIPT_DIR%target\release\mes-system.exe
set FRONTEND_DIST=%SCRIPT_DIR%frontend\dist

REM 默认配置
set DEFAULT_PORT=9001
set DEFAULT_HOST=0.0.0.0

REM 从环境变量或使用默认值
if "%MES_PORT%"=="" set MES_PORT=%DEFAULT_PORT%
if "%MES_HOST%"=="" set MES_HOST=%DEFAULT_HOST%
if "%RUST_LOG%"=="" set RUST_LOG=info

REM 检查二进制文件
if not exist "%MES_BINARY%" (
    echo [错误] MES系统二进制文件不存在: %MES_BINARY%
    echo.
    echo 请先构建发布版本:
    echo   cargo build --release
    echo.
    pause
    exit /b 1
)

REM 检查前端文件
if not exist "%FRONTEND_DIST%" (
    echo [警告] 前端构建文件不存在: %FRONTEND_DIST%
    echo.
    echo 请先构建前端:
    echo   cd frontend
    echo   npm run build
    echo.
    pause
    exit /b 1
)

REM 检查环境变量文件
if exist "%SCRIPT_DIR%.env" (
    echo [信息] 发现环境变量文件，请手动设置环境变量
    echo.
)

echo [信息] 启动配置:
echo   - 主机: %MES_HOST%
echo   - 端口: %MES_PORT%
echo   - 日志级别: %RUST_LOG%
echo   - 二进制文件: %MES_BINARY%
echo   - 前端文件: %FRONTEND_DIST%
echo.

echo [信息] 启动MES系统...
echo [信息] 访问地址: http://localhost:%MES_PORT%
echo [信息] 按 Ctrl+C 停止服务
echo.

REM 启动系统
"%MES_BINARY%"

pause
