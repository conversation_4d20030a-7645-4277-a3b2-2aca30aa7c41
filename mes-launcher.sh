#!/bin/bash

# MES系统统一启动器
# 整合所有启动脚本的交互式入口
# 版本: 2.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m'

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 默认端口配置 (避免使用受限端口)
DEFAULT_BACKEND_PORT=9001
DEFAULT_FRONTEND_PROD_PORT=3080
DEFAULT_FRONTEND_DEV_PORT=3000

# 加载端口配置文件
if [ -f ".env.ports" ]; then
    source .env.ports
fi

# 从环境变量获取端口配置
BACKEND_PORT=${MES_PORT:-$DEFAULT_BACKEND_PORT}
FRONTEND_PROD_PORT=${FRONTEND_PORT:-$DEFAULT_FRONTEND_PROD_PORT}
FRONTEND_DEV_PORT=${VITE_PORT:-$DEFAULT_FRONTEND_DEV_PORT}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}$1${NC}"
}

# 显示横幅
show_banner() {
    clear
    echo -e "${CYAN}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                    MES 制造执行系统                          ║
║                   统一启动控制台                             ║
║                                                              ║
║              Manufacturing Execution System                 ║
║                   Unified Launcher                          ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
    echo -e "${WHITE}版本: 2.0${NC} | ${WHITE}构建日期: $(date '+%Y-%m-%d')${NC}"
    echo ""
}

# 检查系统状态
check_system_status() {
    log_info "检查系统状态..."
    
    # 检查后端
    if [ -f "target/release/mes-system" ]; then
        echo -e "  ${GREEN}✓${NC} 后端二进制文件: 已构建"
    else
        echo -e "  ${RED}✗${NC} 后端二进制文件: 未构建"
    fi
    
    # 检查前端
    if [ -d "frontend/dist" ] && [ -f "frontend/dist/index.html" ]; then
        echo -e "  ${GREEN}✓${NC} 前端构建文件: 已构建"
    else
        echo -e "  ${RED}✗${NC} 前端构建文件: 未构建"
    fi
    
    # 检查依赖
    if command -v cargo >/dev/null 2>&1; then
        echo -e "  ${GREEN}✓${NC} Rust/Cargo: 已安装"
    else
        echo -e "  ${RED}✗${NC} Rust/Cargo: 未安装"
    fi
    
    if command -v node >/dev/null 2>&1; then
        echo -e "  ${GREEN}✓${NC} Node.js: 已安装 ($(node --version))"
    else
        echo -e "  ${RED}✗${NC} Node.js: 未安装"
    fi
    
    if command -v npm >/dev/null 2>&1; then
        echo -e "  ${GREEN}✓${NC} npm: 已安装 ($(npm --version))"
    else
        echo -e "  ${RED}✗${NC} npm: 未安装"
    fi
    
    # 检查运行状态和端口
    # 优先检查PID文件
    local backend_pid=""
    if [ -f "logs/backend.pid" ]; then
        local pid_from_file=$(cat logs/backend.pid 2>/dev/null)
        if ps -p "$pid_from_file" > /dev/null 2>&1; then
            backend_pid=$pid_from_file
        fi
    fi

    # 如果PID文件不存在或无效，按进程名查找
    if [ -z "$backend_pid" ]; then
        backend_pid=$(pgrep -f "target/release/mes-system" 2>/dev/null | head -1)
    fi

    if [ -n "$backend_pid" ]; then
        echo -e "  ${YELLOW}⚡${NC} 后端服务: 正在运行 (PID: $backend_pid, 端口: $BACKEND_PORT)"
    else
        echo -e "  ${BLUE}○${NC} 后端服务: 未运行 (配置端口: $BACKEND_PORT)"
    fi

    # 检查前端服务
    local frontend_pid=""
    local frontend_mode=""

    # 优先检查PID文件
    if [ -f "logs/frontend.pid" ]; then
        local pid_from_file=$(cat logs/frontend.pid 2>/dev/null)
        if ps -p "$pid_from_file" > /dev/null 2>&1; then
            frontend_pid=$pid_from_file
            frontend_mode="生产模式"
        fi
    fi

    # 检查开发模式
    if [ -z "$frontend_pid" ]; then
        local dev_pid=$(pgrep -f "vite.*dev\|npm.*dev" 2>/dev/null | head -1)
        if [ -n "$dev_pid" ]; then
            frontend_pid=$dev_pid
            frontend_mode="开发模式"
        fi
    fi

    # 检查生产模式Python服务器
    if [ -z "$frontend_pid" ]; then
        local prod_pid=$(pgrep -f "python.*http.server.*$FRONTEND_PROD_PORT" 2>/dev/null | head -1)
        if [ -n "$prod_pid" ]; then
            frontend_pid=$prod_pid
            frontend_mode="生产模式"
        fi
    fi

    if [ -n "$frontend_pid" ]; then
        if [ "$frontend_mode" = "开发模式" ]; then
            echo -e "  ${YELLOW}⚡${NC} 前端服务: $frontend_mode运行 (PID: $frontend_pid, 端口: $FRONTEND_DEV_PORT)"
        else
            echo -e "  ${YELLOW}⚡${NC} 前端服务: $frontend_mode运行 (PID: $frontend_pid, 端口: $FRONTEND_PROD_PORT)"
        fi
    else
        echo -e "  ${BLUE}○${NC} 前端服务: 未运行 (开发端口: $FRONTEND_DEV_PORT, 生产端口: $FRONTEND_PROD_PORT)"
    fi

    # 检查端口占用
    echo ""
    log_info "端口占用检查:"
    for port in $BACKEND_PORT $FRONTEND_PROD_PORT $FRONTEND_DEV_PORT; do
        if command -v netstat >/dev/null 2>&1; then
            local port_info=$(netstat -tlnp 2>/dev/null | grep ":$port ")
            if [ -n "$port_info" ]; then
                echo -e "  ${YELLOW}⚡${NC} 端口 $port: 已占用"
            else
                echo -e "  ${GREEN}✓${NC} 端口 $port: 可用"
            fi
        elif command -v ss >/dev/null 2>&1; then
            local port_info=$(ss -tlnp 2>/dev/null | grep ":$port ")
            if [ -n "$port_info" ]; then
                echo -e "  ${YELLOW}⚡${NC} 端口 $port: 已占用"
            else
                echo -e "  ${GREEN}✓${NC} 端口 $port: 可用"
            fi
        fi
    done
    
    echo ""
}

# 显示主菜单
show_main_menu() {
    log_header "═══════════════ 主菜单 ═══════════════"
    echo ""
    echo -e "${WHITE}🚀 快速启动${NC}"
    echo "  1) 完整系统启动 (前台运行)"
    echo "  2) 完整系统启动 (后台运行)"
    echo "  3) 仅启动后端服务 (前台)"
    echo "  4) 仅启动后端服务 (后台)"
    echo "  5) 仅启动前端服务 (前台)"
    echo "  6) 仅启动前端服务 (后台)"
    echo ""
    echo -e "${WHITE}🔧 开发模式${NC}"
    echo "  7) 后端开发模式 (cargo run)"
    echo "  8) 前端开发模式 (vite dev)"
    echo "  9) 开发模式 (前台运行)"
    echo " 10) 开发模式 (后台运行)"
    echo ""
    echo -e "${WHITE}📦 构建管理${NC}"
    echo " 11) 构建后端 (release)"
    echo " 12) 构建前端 (production)"
    echo " 13) 构建完整系统"
    echo ""
    echo -e "${WHITE}🐳 Docker部署${NC}"
    echo " 14) Docker构建"
    echo " 15) Docker运行"
    echo " 16) Docker完整部署"
    echo ""
    echo -e "${WHITE}🛠️  系统管理${NC}"
    echo " 17) 停止所有服务"
    echo " 18) 系统状态检查"
    echo " 19) 清理缓存"
    echo " 20) 生产环境安装"
    echo " 21) 端口配置"
    echo ""
    echo -e "${WHITE}📚 帮助信息${NC}"
    echo " 22) 查看文档"
    echo " 23) 故障排除"
    echo ""
    echo "  0) 退出"
    echo ""
    echo -e "${CYAN}═══════════════════════════════════════${NC}"
    echo -e "${YELLOW}当前端口配置: 后端=$BACKEND_PORT, 前端生产=$FRONTEND_PROD_PORT, 前端开发=$FRONTEND_DEV_PORT${NC}"
}

# 等待用户输入
wait_for_input() {
    echo ""
    echo -e "${YELLOW}按任意键继续...${NC}"
    read -n 1 -s
}

# 完整系统启动 (前台)
start_full_system_foreground() {
    log_header "启动完整MES系统 (前台模式)"

    # 检查构建文件
    if [ ! -f "target/release/mes-system" ]; then
        log_warning "后端未构建，正在构建..."
        cargo build --release
    fi

    if [ ! -d "frontend/dist" ]; then
        log_warning "前端未构建，正在构建..."
        cd frontend && npm run build && cd ..
    fi

    # 设置端口环境变量
    export MES_PORT=$BACKEND_PORT
    export FRONTEND_PORT=$FRONTEND_PROD_PORT

    log_info "启动后端服务 (端口$BACKEND_PORT)..."
    ./quick-start.sh &
    BACKEND_PID=$!

    sleep 3

    log_info "启动前端服务 (端口$FRONTEND_PROD_PORT)..."
    cd frontend
    python3 -m http.server $FRONTEND_PROD_PORT --directory dist &
    FRONTEND_PID=$!
    cd ..

    log_success "系统启动完成！"
    log_info "后端地址: http://localhost:$BACKEND_PORT"
    log_info "前端地址: http://localhost:$FRONTEND_PROD_PORT"
    log_info "按 Ctrl+C 停止所有服务"

    # 等待用户中断
    trap 'kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; log_info "正在停止服务..."; exit 0' INT
    wait
}

# 完整系统启动 (后台)
start_full_system_background() {
    log_header "启动完整MES系统 (后台模式)"

    # 检查构建文件
    if [ ! -f "target/release/mes-system" ]; then
        log_warning "后端未构建，正在构建..."
        cargo build --release
    fi

    if [ ! -d "frontend/dist" ]; then
        log_warning "前端未构建，正在构建..."
        cd frontend && npm run build && cd ..
    fi

    log_info "使用进程管理器启动服务..."
    ./mes-process-manager.sh start-all $BACKEND_PORT $FRONTEND_PROD_PORT

    log_success "系统启动完成！"
    log_info "后端地址: http://localhost:$BACKEND_PORT"
    log_info "前端地址: http://localhost:$FRONTEND_PROD_PORT"
    log_info "使用选项16停止服务或选项17查看状态"

    wait_for_input
}

# 仅启动后端 (前台)
start_backend_foreground() {
    log_header "启动后端服务 (前台模式)"

    if [ ! -f "target/release/mes-system" ]; then
        log_warning "后端未构建，正在构建..."
        cargo build --release
    fi

    export MES_PORT=$BACKEND_PORT
    log_info "启动后端服务 (端口$BACKEND_PORT)..."
    exec ./quick-start.sh
}

# 仅启动后端 (后台)
start_backend_background() {
    log_header "启动后端服务 (后台模式)"

    if [ ! -f "target/release/mes-system" ]; then
        log_warning "后端未构建，正在构建..."
        cargo build --release
    fi

    log_info "使用进程管理器启动后端服务..."
    ./mes-process-manager.sh start-backend $BACKEND_PORT

    log_info "使用选项16停止服务或选项17查看状态"
    wait_for_input
}

# 仅启动前端 (前台)
start_frontend_foreground() {
    log_header "启动前端服务 (前台模式)"

    if [ ! -d "frontend/dist" ]; then
        log_warning "前端未构建，正在构建..."
        cd frontend && npm run build && cd ..
    fi

    log_info "启动前端服务 (端口$FRONTEND_PROD_PORT)..."
    cd frontend
    exec python3 -m http.server $FRONTEND_PROD_PORT --directory dist
}

# 仅启动前端 (后台)
start_frontend_background() {
    log_header "启动前端服务 (后台模式)"

    if [ ! -d "frontend/dist" ]; then
        log_warning "前端未构建，正在构建..."
        cd frontend && npm run build && cd ..
    fi

    log_info "使用进程管理器启动前端服务..."
    ./mes-process-manager.sh start-frontend $FRONTEND_PROD_PORT

    log_info "使用选项16停止服务或选项17查看状态"
    wait_for_input
}

# 后端开发模式
start_backend_dev() {
    log_header "后端开发模式"
    log_info "启动Rust开发服务器..."
    exec cargo run
}

# 前端开发模式
start_frontend_dev() {
    log_header "前端开发模式"
    export VITE_PORT=$FRONTEND_DEV_PORT
    export VITE_API_PROXY_TARGET="http://localhost:$BACKEND_PORT"
    log_info "启动Vite开发服务器 (端口$FRONTEND_DEV_PORT)..."
    log_info "API代理目标: http://localhost:$BACKEND_PORT"
    cd frontend
    exec npm run dev
}

# 开发模式 (前后端)
start_dev_mode() {
    log_header "开发模式 (前后端)"

    export MES_PORT=$BACKEND_PORT
    export VITE_PORT=$FRONTEND_DEV_PORT
    export VITE_API_PROXY_TARGET="http://localhost:$BACKEND_PORT"

    log_info "启动后端开发服务器 (端口$BACKEND_PORT)..."
    cargo run &
    BACKEND_PID=$!

    sleep 3

    log_info "启动前端开发服务器 (端口$FRONTEND_DEV_PORT)..."
    log_info "API代理目标: http://localhost:$BACKEND_PORT"
    cd frontend
    npm run dev &
    FRONTEND_PID=$!
    cd ..

    log_success "开发环境启动完成！"
    log_info "后端地址: http://localhost:$BACKEND_PORT"
    log_info "前端地址: http://localhost:$FRONTEND_DEV_PORT"
    log_info "按 Ctrl+C 停止所有服务"

    trap 'kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; log_info "正在停止服务..."; exit 0' INT
    wait
}

# 开发模式 (前后端) - 后台运行
start_dev_mode_background() {
    log_header "开发模式 (后台运行)"

    # 检查是否已经有开发模式后台启动脚本
    if [ ! -f "start-dev-background.sh" ]; then
        log_error "开发模式后台启动脚本不存在"
        log_info "请确保 start-dev-background.sh 文件存在"
        wait_for_input
        return 1
    fi

    # 使用专门的后台启动脚本
    log_info "使用后台启动脚本启动开发环境..."
    ./start-dev-background.sh start

    if [ $? -eq 0 ]; then
        log_success "开发环境后台启动完成！"
        echo ""
        log_info "管理命令:"
        log_info "  查看状态: ./start-dev-background.sh status"
        log_info "  查看日志: ./start-dev-background.sh logs"
        log_info "  停止服务: ./start-dev-background.sh stop"
        echo ""
        wait_for_input
    else
        log_error "开发环境启动失败"
        wait_for_input
    fi
}

# 构建后端
build_backend() {
    log_header "构建后端 (Release模式)"
    log_info "开始构建..."
    cargo build --release
    log_success "后端构建完成！"
    wait_for_input
}

# 构建前端
build_frontend() {
    log_header "构建前端 (Production模式)"
    log_info "开始构建..."
    cd frontend

    # 设置生产环境API URL
    export VITE_API_URL="http://localhost:$BACKEND_PORT/api"
    log_info "API URL: $VITE_API_URL"

    npm run build
    cd ..
    log_success "前端构建完成！"
    wait_for_input
}

# 构建完整系统
build_full_system() {
    log_header "构建完整系统"

    log_info "构建后端..."
    cargo build --release

    log_info "构建前端..."
    cd frontend

    # 设置生产环境API URL
    export VITE_API_URL="http://localhost:$BACKEND_PORT/api"
    log_info "API URL: $VITE_API_URL"

    npm run build
    cd ..

    log_success "完整系统构建完成！"
    wait_for_input
}

# Docker构建
docker_build() {
    log_header "Docker构建"
    
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker未安装"
        wait_for_input
        return
    fi
    
    log_info "构建Docker镜像..."
    docker build -t mes-system .
    log_success "Docker镜像构建完成！"
    wait_for_input
}

# Docker运行
docker_run() {
    log_header "Docker运行"
    
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker未安装"
        wait_for_input
        return
    fi
    
    log_info "启动Docker容器..."
    docker run -p 9001:9001 -p 8080:80 mes-system
}

# Docker完整部署
docker_deploy() {
    log_header "Docker完整部署"
    
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker未安装"
        wait_for_input
        return
    fi
    
    log_info "构建Docker镜像..."
    docker build -t mes-system .
    
    log_info "启动Docker容器..."
    docker run -d -p 9001:9001 -p 8080:80 --name mes-system mes-system
    
    log_success "Docker部署完成！"
    log_info "访问地址: http://localhost:8080"
    wait_for_input
}

# 停止所有服务
stop_all_services() {
    log_header "停止所有服务"

    # 停止开发模式后台服务
    if [ -f "start-dev-background.sh" ]; then
        log_info "停止开发模式后台服务..."
        ./start-dev-background.sh stop 2>/dev/null || true
    fi

    # 停止其他服务
    log_info "使用安全的进程管理器停止服务..."
    ./mes-process-manager.sh stop-all

    log_success "服务停止完成"
    wait_for_input
}

# 系统状态检查
system_status_check() {
    log_header "系统状态检查"
    check_system_status
    
    log_info "端口占用情况:"
    if command -v netstat >/dev/null 2>&1; then
        netstat -tlnp 2>/dev/null | grep -E ":9001|:8080|:3000" || echo "  无相关端口占用"
    else
        log_warning "netstat未安装，无法检查端口"
    fi
    
    wait_for_input
}

# 清理缓存
clean_cache() {
    log_header "清理缓存"
    
    log_info "清理Rust缓存..."
    cargo clean
    
    log_info "清理前端缓存..."
    cd frontend
    rm -rf node_modules/.vite dist 2>/dev/null || true
    cd ..
    
    log_success "缓存清理完成"
    wait_for_input
}

# 生产环境安装
production_install() {
    log_header "生产环境安装"
    
    if [ "$EUID" -ne 0 ]; then
        log_error "生产环境安装需要root权限"
        log_info "请使用: sudo $0"
        wait_for_input
        return
    fi
    
    log_info "执行生产环境安装脚本..."
    ./install-production.sh
}

# 查看文档
show_documentation() {
    log_header "文档列表"
    echo ""
    echo "📖 主要文档:"
    echo "  - README.md              项目说明"
    echo "  - DEPLOYMENT.md          部署指南"
    echo "  - frontend/FRONTEND-DEPLOYMENT.md  前端部署"
    echo ""
    echo "📋 功能文档:"
    echo "  - docs/                  详细文档目录"
    echo "  - QUICK_START.md         快速开始"
    echo ""
    wait_for_input
}

# 端口配置
configure_ports() {
    log_header "端口配置"
    echo ""
    echo -e "${BLUE}当前端口配置:${NC}"
    echo "  后端服务: $BACKEND_PORT"
    echo "  前端生产: $FRONTEND_PROD_PORT"
    echo "  前端开发: $FRONTEND_DEV_PORT"
    echo ""
    echo -e "${YELLOW}注意: 避免使用受限端口 80, 8080, 443, 8443${NC}"
    echo ""

    echo -n "是否要修改端口配置? (y/N): "
    read -r confirm

    if [[ $confirm =~ ^[Yy]$ ]]; then
        echo -n "后端端口 (当前: $BACKEND_PORT): "
        read -r new_backend_port
        if [[ $new_backend_port =~ ^[0-9]+$ ]] && [ $new_backend_port -gt 1024 ] && [ $new_backend_port -lt 65536 ]; then
            BACKEND_PORT=$new_backend_port
        fi

        echo -n "前端生产端口 (当前: $FRONTEND_PROD_PORT): "
        read -r new_frontend_prod_port
        if [[ $new_frontend_prod_port =~ ^[0-9]+$ ]] && [ $new_frontend_prod_port -gt 1024 ] && [ $new_frontend_prod_port -lt 65536 ]; then
            FRONTEND_PROD_PORT=$new_frontend_prod_port
        fi

        echo -n "前端开发端口 (当前: $FRONTEND_DEV_PORT): "
        read -r new_frontend_dev_port
        if [[ $new_frontend_dev_port =~ ^[0-9]+$ ]] && [ $new_frontend_dev_port -gt 1024 ] && [ $new_frontend_dev_port -lt 65536 ]; then
            FRONTEND_DEV_PORT=$new_frontend_dev_port
        fi

        # 保存到环境变量文件
        cat > .env.ports << EOF
# MES系统端口配置
export MES_PORT=$BACKEND_PORT
export FRONTEND_PORT=$FRONTEND_PROD_PORT
export VITE_PORT=$FRONTEND_DEV_PORT
EOF

        log_success "端口配置已保存到 .env.ports"
        log_info "新的端口配置:"
        log_info "  后端服务: $BACKEND_PORT"
        log_info "  前端生产: $FRONTEND_PROD_PORT"
        log_info "  前端开发: $FRONTEND_DEV_PORT"
    fi

    wait_for_input
}

# 故障排除
troubleshooting() {
    log_header "故障排除"
    echo ""
    echo "🔧 常见问题:"
    echo ""
    echo "1. 端口被占用:"
    echo "   使用选项16停止所有服务"
    echo "   或手动: pkill -f mes-system && pkill -f vite"
    echo ""
    echo "2. 端口冲突:"
    echo "   使用选项20配置端口"
    echo "   避免使用: 80, 8080, 443, 8443"
    echo ""
    echo "3. 构建失败:"
    echo "   使用选项18清理缓存"
    echo "   或手动: cargo clean && cd frontend && rm -rf node_modules"
    echo ""
    echo "4. 数据库连接失败:"
    echo "   检查PostgreSQL服务状态"
    echo "   验证DATABASE_URL环境变量"
    echo ""
    echo "5. 权限问题:"
    echo "   chmod +x *.sh && chmod +x frontend/*.sh"
    echo ""
    echo "6. 后台服务管理:"
    echo "   查看PID文件: logs/*.pid"
    echo "   查看日志: logs/*.log"
    echo ""
    wait_for_input
}

# 主循环
main_loop() {
    while true; do
        show_banner
        check_system_status
        show_main_menu
        
        echo -n -e "${CYAN}请选择操作 [0-23]: ${NC}"
        read -r choice
        
        case $choice in
            1) start_full_system_foreground ;;
            2) start_full_system_background ;;
            3) start_backend_foreground ;;
            4) start_backend_background ;;
            5) start_frontend_foreground ;;
            6) start_frontend_background ;;
            7) start_backend_dev ;;
            8) start_frontend_dev ;;
            9) start_dev_mode ;;
            10) start_dev_mode_background ;;
            11) build_backend ;;
            12) build_frontend ;;
            13) build_full_system ;;
            14) docker_build ;;
            15) docker_run ;;
            16) docker_deploy ;;
            17) stop_all_services ;;
            18) system_status_check ;;
            19) clean_cache ;;
            20) production_install ;;
            21) configure_ports ;;
            22) show_documentation ;;
            23) troubleshooting ;;
            0)
                log_info "感谢使用MES系统！"
                exit 0
                ;;
            *)
                log_error "无效选择，请重新输入 (0-23)"
                sleep 2
                ;;
        esac
    done
}

# 检查参数
if [ $# -gt 0 ]; then
    case $1 in
        --help|-h)
            show_banner
            echo "MES系统统一启动器"
            echo ""
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --help, -h     显示帮助信息"
            echo "  --status       显示系统状态"
            echo "  --stop         停止所有服务"
            echo ""
            echo "无参数运行将进入交互模式"
            exit 0
            ;;
        --status)
            show_banner
            check_system_status
            exit 0
            ;;
        --stop)
            stop_all_services
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            log_info "使用 --help 查看帮助"
            exit 1
            ;;
    esac
fi

# 启动主循环
main_loop
