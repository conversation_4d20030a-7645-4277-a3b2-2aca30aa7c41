# 工序拖拽调整功能

## 🎯 功能概述

为MES系统的工艺路径管理添加了拖拽调整工序顺序的功能，用户可以通过拖拽工序卡片来重新排列工序顺序。

## ✨ 功能特性

### 1. 直观的拖拽操作
- **拖拽手柄**: 每个工序卡片标题栏左侧显示拖拽图标 (⋮⋮)
- **视觉反馈**: 拖拽时卡片变为半透明，提供清晰的视觉反馈
- **实时预览**: 拖拽过程中可以看到工序的新位置

### 2. 智能排序
- **自动重编号**: 拖拽完成后自动重新分配步骤号 (1, 2, 3...)
- **保持连续性**: 确保工序步骤号连续，无跳跃
- **即时保存**: 拖拽完成后立即保存到数据库

### 3. 权限控制
- **角色限制**: 只有管理员和工艺工程师可以调整工序顺序
- **编辑模式**: 只有在编辑模式下才显示拖拽手柄

## 🛠️ 技术实现

### 前端技术栈
- **@dnd-kit/core**: 现代化的拖拽核心库
- **@dnd-kit/sortable**: 排序功能支持
- **@dnd-kit/utilities**: 工具函数
- **React**: 组件化实现

### 后端API支持
- **重排序接口**: `POST /api/parts/{part_id}/routing/reorder`
- **权限验证**: 确保只有授权用户可以调整顺序
- **事务处理**: 保证数据一致性

## 📱 使用方法

### 1. 访问工艺路径页面
1. 登录MES系统
2. 导航到 **工艺路径** 页面
3. 选择要编辑的零件

### 2. 调整工序顺序
1. 在工艺流程图中找到要移动的工序卡片
2. 将鼠标悬停在工序标题栏的拖拽图标 (⋮⋮) 上
3. 按住鼠标左键开始拖拽
4. 将工序拖拽到目标位置
5. 释放鼠标完成调整

### 3. 确认结果
- 系统会自动重新分配步骤号
- 显示成功提示消息
- 工艺流程图实时更新

## 🎨 界面设计

### 工序卡片布局
```
┌─────────────────────────────────┐
│ ⋮⋮ 工序 1              ✏️ 🗑️ │
├─────────────────────────────────┤
│ [Grinding]                      │
│                                 │
│ 工作指导: g                     │
│ 标准工时: 4h                    │
└─────────────────────────────────┘
```

### 拖拽状态
- **正常状态**: 完全不透明，蓝色边框
- **拖拽状态**: 50% 透明度，保持蓝色边框
- **拖拽手柄**: 蓝色图标，鼠标悬停时显示抓取光标

## 🔧 配置选项

### 拖拽敏感度
- **激活距离**: 8px (避免误触)
- **拖拽策略**: 水平列表排序
- **碰撞检测**: 最近中心点算法

### 权限配置
```typescript
// 只有以下角色可以拖拽调整工序
const allowedRoles = ['admin', 'process_engineer'];
```

## 🚀 性能优化

### 1. 防抖处理
- 拖拽操作有8px的激活阈值
- 避免意外触发拖拽

### 2. 批量更新
- 一次拖拽操作只发送一个API请求
- 使用事务确保数据一致性

### 3. 缓存刷新
- 拖拽完成后智能刷新相关缓存
- 避免不必要的数据重新加载

## 🐛 故障排除

### 常见问题

#### 1. 拖拽图标不显示
- **原因**: 用户权限不足或未开启编辑模式
- **解决**: 确保用户有工艺工程师或管理员权限

#### 2. 拖拽无响应
- **原因**: 浏览器兼容性问题
- **解决**: 使用现代浏览器 (Chrome 88+, Firefox 85+, Safari 14+)

#### 3. 顺序调整失败
- **原因**: 网络问题或后端权限验证失败
- **解决**: 检查网络连接和用户权限

### 调试信息
- 打开浏览器开发者工具查看控制台错误
- 检查网络请求是否成功发送
- 确认后端API响应状态

## 📈 未来改进

### 计划功能
1. **批量拖拽**: 支持选择多个工序同时移动
2. **拖拽预览**: 显示拖拽目标位置的预览线
3. **撤销功能**: 支持撤销最近的拖拽操作
4. **键盘支持**: 使用键盘快捷键调整工序顺序

### 性能优化
1. **虚拟滚动**: 支持大量工序的高效渲染
2. **增量更新**: 只更新变化的工序步骤号
3. **离线支持**: 支持离线状态下的拖拽操作

## 🔗 相关文档

- [工艺路径管理用户手册](./ROUTING_MANAGEMENT.md)
- [权限系统配置指南](./PERMISSION_SYSTEM.md)
- [API接口文档](./API_DOCUMENTATION.md)

---

**版本**: v1.0.0  
**更新时间**: 2025-01-05  
**维护者**: MES开发团队
