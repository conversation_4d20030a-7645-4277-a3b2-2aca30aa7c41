-- 自动工单创建配置表
-- 用于配置在什么条件下自动创建工单

-- 自动工单配置表
CREATE TABLE auto_work_order_configs (
    id SERIAL PRIMARY KEY,
    trigger_type VARCHAR(50) NOT NULL, -- 触发类型：part_created, routing_created, bom_added
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE, -- 特定项目，NULL表示全局配置
    is_enabled BOOLEAN NOT NULL DEFAULT true,
    default_quantity INTEGER, -- 默认数量，NULL表示使用BOM数量
    default_due_days INTEGER, -- 默认交期天数
    auto_create_plan_tasks BOOLEAN NOT NULL DEFAULT false, -- 是否自动创建计划任务
    created_by INTEGER NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(trigger_type, project_id) -- 每个项目的每种触发类型只能有一个配置
);

-- 自动工单创建记录表
CREATE TABLE auto_work_order_logs (
    id SERIAL PRIMARY KEY,
    config_id INTEGER NOT NULL REFERENCES auto_work_order_configs(id) ON DELETE CASCADE,
    work_order_id INTEGER NOT NULL REFERENCES work_orders(id) ON DELETE CASCADE,
    trigger_type VARCHAR(50) NOT NULL,
    trigger_entity_type VARCHAR(50) NOT NULL, -- part, routing, bom
    trigger_entity_id INTEGER NOT NULL,
    project_id INTEGER REFERENCES projects(id),
    part_id INTEGER REFERENCES parts(id),
    quantity INTEGER NOT NULL,
    created_by INTEGER NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_auto_work_order_configs_trigger_type ON auto_work_order_configs(trigger_type);
CREATE INDEX idx_auto_work_order_configs_project_id ON auto_work_order_configs(project_id);
CREATE INDEX idx_auto_work_order_configs_is_enabled ON auto_work_order_configs(is_enabled);
CREATE INDEX idx_auto_work_order_logs_config_id ON auto_work_order_logs(config_id);
CREATE INDEX idx_auto_work_order_logs_work_order_id ON auto_work_order_logs(work_order_id);
CREATE INDEX idx_auto_work_order_logs_trigger_type ON auto_work_order_logs(trigger_type);
CREATE INDEX idx_auto_work_order_logs_created_at ON auto_work_order_logs(created_at);

-- 添加注释
COMMENT ON TABLE auto_work_order_configs IS '自动工单创建配置表';
COMMENT ON COLUMN auto_work_order_configs.trigger_type IS '触发类型：part_created(零件创建), routing_created(工艺创建), bom_added(BOM添加)';
COMMENT ON COLUMN auto_work_order_configs.project_id IS '特定项目ID，NULL表示全局配置';
COMMENT ON COLUMN auto_work_order_configs.default_quantity IS '默认工单数量，NULL表示使用BOM数量';
COMMENT ON COLUMN auto_work_order_configs.default_due_days IS '默认交期天数，从当前日期开始计算';
COMMENT ON COLUMN auto_work_order_configs.auto_create_plan_tasks IS '是否自动创建计划任务';

COMMENT ON TABLE auto_work_order_logs IS '自动工单创建记录表';
COMMENT ON COLUMN auto_work_order_logs.trigger_entity_type IS '触发实体类型：part(零件), routing(工艺), bom(BOM项目)';
COMMENT ON COLUMN auto_work_order_logs.trigger_entity_id IS '触发实体的ID';

-- 创建触发器自动更新updated_at字段
CREATE OR REPLACE FUNCTION update_auto_work_order_configs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_update_auto_work_order_configs_updated_at
    BEFORE UPDATE ON auto_work_order_configs
    FOR EACH ROW
    EXECUTE FUNCTION update_auto_work_order_configs_updated_at();

-- 插入默认配置
INSERT INTO auto_work_order_configs (
    trigger_type, 
    project_id, 
    is_enabled, 
    default_quantity, 
    default_due_days, 
    auto_create_plan_tasks, 
    created_by
) VALUES 
-- 全局配置：BOM添加时自动创建工单（默认启用）
('bom_added', NULL, true, NULL, 30, false, 1),
-- 全局配置：零件创建时自动创建工单（默认禁用）
('part_created', NULL, false, 1, 30, false, 1),
-- 全局配置：工艺创建时自动创建工单（默认禁用）
('routing_created', NULL, false, 1, 30, true, 1)
ON CONFLICT (trigger_type, project_id) DO NOTHING;
