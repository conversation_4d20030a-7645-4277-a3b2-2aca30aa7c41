-- 为工艺路径表添加技能组字段
-- 这样可以在工艺路径中预设技能组，避免在计划任务创建时手动选择

-- 1. 添加技能组字段到routings表
ALTER TABLE routings ADD COLUMN skill_group_id INTEGER REFERENCES skill_groups(id);

-- 2. 为现有的工艺路径自动分配技能组
-- 根据工艺名称智能匹配技能组

-- 更新CNC相关工艺
UPDATE routings 
SET skill_group_id = (SELECT id FROM skill_groups WHERE group_name ILIKE '%CNC%' OR group_name ILIKE '%数控%' LIMIT 1)
WHERE skill_group_id IS NULL 
AND (process_name ILIKE '%CNC%' OR process_name ILIKE '%数控%' OR process_name ILIKE '%machining%');

-- 更新铣削相关工艺
UPDATE routings 
SET skill_group_id = (SELECT id FROM skill_groups WHERE group_name ILIKE '%mill%' OR group_name ILIKE '%铣%' LIMIT 1)
WHERE skill_group_id IS NULL 
AND (process_name ILIKE '%mill%' OR process_name ILIKE '%铣%');

-- 更新车削相关工艺
UPDATE routings 
SET skill_group_id = (SELECT id FROM skill_groups WHERE group_name ILIKE '%turn%' OR group_name ILIKE '%车%' OR group_name ILIKE '%lathe%' LIMIT 1)
WHERE skill_group_id IS NULL 
AND (process_name ILIKE '%turn%' OR process_name ILIKE '%车%' OR process_name ILIKE '%lathe%');

-- 更新磨削相关工艺
UPDATE routings 
SET skill_group_id = (SELECT id FROM skill_groups WHERE group_name ILIKE '%grind%' OR group_name ILIKE '%磨%' LIMIT 1)
WHERE skill_group_id IS NULL 
AND (process_name ILIKE '%grind%' OR process_name ILIKE '%磨%');

-- 更新装配相关工艺
UPDATE routings 
SET skill_group_id = (SELECT id FROM skill_groups WHERE group_name ILIKE '%assembl%' OR group_name ILIKE '%装配%' OR group_name ILIKE '%组装%' LIMIT 1)
WHERE skill_group_id IS NULL 
AND (process_name ILIKE '%assembl%' OR process_name ILIKE '%装配%' OR process_name ILIKE '%组装%');

-- 更新质检相关工艺
UPDATE routings 
SET skill_group_id = (SELECT id FROM skill_groups WHERE group_name ILIKE '%quality%' OR group_name ILIKE '%inspect%' OR group_name ILIKE '%质检%' OR group_name ILIKE '%检验%' LIMIT 1)
WHERE skill_group_id IS NULL 
AND (process_name ILIKE '%quality%' OR process_name ILIKE '%inspect%' OR process_name ILIKE '%质检%' OR process_name ILIKE '%检验%' OR process_name ILIKE '%测试%');

-- 更新包装相关工艺
UPDATE routings 
SET skill_group_id = (SELECT id FROM skill_groups WHERE group_name ILIKE '%pack%' OR group_name ILIKE '%包装%' LIMIT 1)
WHERE skill_group_id IS NULL 
AND (process_name ILIKE '%pack%' OR process_name ILIKE '%包装%');

-- 为剩余未匹配的工艺分配默认技能组（第一个技能组）
UPDATE routings 
SET skill_group_id = (SELECT id FROM skill_groups ORDER BY id LIMIT 1)
WHERE skill_group_id IS NULL;

-- 3. 添加注释
COMMENT ON COLUMN routings.skill_group_id IS '工艺步骤对应的技能组ID，用于自动分配计划任务';

-- 4. 创建索引以提高查询性能
CREATE INDEX idx_routings_skill_group_id ON routings(skill_group_id);
CREATE INDEX idx_routings_part_skill_group ON routings(part_id, skill_group_id);
