-- 增强角色和技能组表结构
-- 添加显示名称和描述字段，支持多语言显示

-- 为角色表添加显示名称和描述字段
ALTER TABLE roles ADD COLUMN IF NOT EXISTS display_name VARCHAR(100);
ALTER TABLE roles ADD COLUMN IF NOT EXISTS description TEXT;

-- 为技能组表添加显示名称和描述字段
ALTER TABLE skill_groups ADD COLUMN IF NOT EXISTS display_name VARCHAR(100);
ALTER TABLE skill_groups ADD COLUMN IF NOT EXISTS description TEXT;

-- 更新现有角色的显示名称和描述
UPDATE roles SET 
    display_name = '系统管理员',
    description = '拥有系统所有权限，可以管理用户、角色和系统配置'
WHERE role_name = 'admin';

UPDATE roles SET 
    display_name = '工艺工程师',
    description = '负责工艺设计、路径规划和技术文档管理'
WHERE role_name = 'process_engineer';

UPDATE roles SET 
    display_name = '生产计划员',
    description = '负责生产计划制定、任务调度和资源分配'
WHERE role_name = 'planner';

UPDATE roles SET 
    display_name = '操作员',
    description = '负责具体生产执行，只能开始/暂停/完成分配给自己的任务'
WHERE role_name = 'operator';

UPDATE roles SET 
    display_name = '质检员',
    description = '负责质量检验和质量数据管理'
WHERE role_name = 'quality_inspector';

-- 更新现有技能组的显示名称和描述
UPDATE skill_groups SET 
    display_name = 'CNC加工',
    description = 'CNC机床操作和编程，包括铣削、车削等精密加工'
WHERE group_name = 'CNC Machining';

UPDATE skill_groups SET 
    display_name = '铣削加工',
    description = '铣床操作，进行各种铣削加工作业'
WHERE group_name = 'Milling';

UPDATE skill_groups SET 
    display_name = '车削加工',
    description = '车床操作，进行各种车削加工作业'
WHERE group_name = 'Turning';

UPDATE skill_groups SET 
    display_name = '磨削加工',
    description = '磨床操作，进行精密磨削加工作业'
WHERE group_name = 'Grinding';

UPDATE skill_groups SET 
    display_name = '装配',
    description = '产品装配和调试，包括机械装配和电气装配'
WHERE group_name = 'Assembly';

UPDATE skill_groups SET 
    display_name = '质量控制',
    description = '质量检验和测试，确保产品质量符合标准'
WHERE group_name = 'Quality Control';

UPDATE skill_groups SET 
    display_name = '包装',
    description = '产品包装和出货准备'
WHERE group_name = 'Packaging';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_roles_display_name ON roles(display_name);
CREATE INDEX IF NOT EXISTS idx_skill_groups_display_name ON skill_groups(display_name);

-- 添加约束确保显示名称不为空（对于新记录）
-- 注意：不对现有记录强制要求，因为可能有历史数据
