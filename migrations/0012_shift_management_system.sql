-- 班次管理系统数据库迁移
-- 支持7*12小时和7*24小时工作模式

-- 1. 班次模板表 - 定义基础班次类型
CREATE TABLE shift_templates (
    id SERIAL PRIMARY KEY,
    template_name VARCHAR(100) NOT NULL UNIQUE, -- '全天班', '白班', '夜班', '早班', '晚班'
    schedule_type VARCHAR(20) NOT NULL,         -- '7x24', '7x12', '5x8', 'custom'
    start_hour INTEGER NOT NULL CHECK (start_hour >= 0 AND start_hour <= 23),
    start_minute INTEGER DEFAULT 0 CHECK (start_minute >= 0 AND start_minute <= 59),
    end_hour INTEGER NOT NULL CHECK (end_hour >= 0 AND end_hour <= 23),
    end_minute INTEGER DEFAULT 0 CHECK (end_minute >= 0 AND end_minute <= 59),
    duration_hours DECIMAL(4,2) NOT NULL,      -- 班次时长（小时）
    work_days INTEGER[] NOT NULL,              -- 工作日 [1,2,3,4,5,6,7] (周一到周日)
    break_periods JSONB,                       -- 休息时间段 [{"start": "12:00", "end": "13:00", "name": "午休"}]
    description TEXT,
    is_system_template BOOLEAN DEFAULT false,  -- 是否为系统预设模板
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 计划组表 - 管理不同的计划组
CREATE TABLE plan_groups (
    id SERIAL PRIMARY KEY,
    group_name VARCHAR(100) NOT NULL UNIQUE,   -- '生产计划组', '维护计划组', '质检计划组'
    group_code VARCHAR(20) NOT NULL UNIQUE,    -- 'PROD', 'MAINT', 'QC'
    description TEXT,
    priority INTEGER DEFAULT 1,                -- 优先级，数字越小优先级越高
    is_active BOOLEAN DEFAULT true,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. 计划组班次配置表 - 计划组与班次模板的关联
CREATE TABLE plan_group_shift_configs (
    id SERIAL PRIMARY KEY,
    plan_group_id INTEGER NOT NULL REFERENCES plan_groups(id) ON DELETE CASCADE,
    shift_template_id INTEGER NOT NULL REFERENCES shift_templates(id),
    effective_date DATE NOT NULL,              -- 生效日期
    expiry_date DATE,                          -- 失效日期（NULL表示永久有效）
    is_default BOOLEAN DEFAULT false,          -- 是否为该计划组的默认班次
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 确保同一计划组在同一时间段内只有一个默认班次
    UNIQUE (plan_group_id, effective_date, is_default)
);

-- 4. 技能组班次关联表 - 技能组与计划组的关联
CREATE TABLE skill_group_plan_assignments (
    id SERIAL PRIMARY KEY,
    skill_group_id INTEGER NOT NULL REFERENCES skill_groups(id) ON DELETE CASCADE,
    plan_group_id INTEGER NOT NULL REFERENCES plan_groups(id) ON DELETE CASCADE,
    assignment_type VARCHAR(20) DEFAULT 'primary', -- 'primary', 'secondary', 'backup'
    effective_date DATE NOT NULL,
    expiry_date DATE,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 确保技能组在同一时间段内只有一个主要计划组
    UNIQUE (skill_group_id, effective_date, assignment_type)
);

-- 5. 班次实例表 - 具体的班次安排
CREATE TABLE shift_instances (
    id SERIAL PRIMARY KEY,
    plan_group_id INTEGER NOT NULL REFERENCES plan_groups(id),
    shift_template_id INTEGER NOT NULL REFERENCES shift_templates(id),
    shift_date DATE NOT NULL,                  -- 班次日期
    actual_start_time TIMESTAMP,               -- 实际开始时间
    actual_end_time TIMESTAMP,                 -- 实际结束时间
    planned_start_time TIMESTAMP NOT NULL,     -- 计划开始时间
    planned_end_time TIMESTAMP NOT NULL,       -- 计划结束时间
    status VARCHAR(20) DEFAULT 'planned',      -- 'planned', 'active', 'completed', 'cancelled'
    assigned_users INTEGER[],                  -- 分配的用户ID数组
    notes TEXT,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 确保同一计划组在同一时间段内不会有重叠的班次
    UNIQUE (plan_group_id, planned_start_time)
);

-- 6. 扩展现有的计划任务表，添加班次相关字段
ALTER TABLE plan_tasks 
ADD COLUMN shift_instance_id INTEGER REFERENCES shift_instances(id),
ADD COLUMN plan_group_id INTEGER REFERENCES plan_groups(id),
ADD COLUMN shift_constraints JSONB; -- 班次约束条件，如 {"must_within_shift": true, "allow_cross_shift": false}

-- 7. 创建索引以提高查询性能
CREATE INDEX idx_shift_templates_schedule_type ON shift_templates(schedule_type);
CREATE INDEX idx_shift_templates_active ON shift_templates(is_active) WHERE is_active = true;
CREATE INDEX idx_plan_groups_active ON plan_groups(is_active) WHERE is_active = true;
CREATE INDEX idx_plan_group_shift_configs_effective ON plan_group_shift_configs(effective_date, expiry_date);
CREATE INDEX idx_skill_group_plan_assignments_effective ON skill_group_plan_assignments(effective_date, expiry_date);
CREATE INDEX idx_shift_instances_date_status ON shift_instances(shift_date, status);
CREATE INDEX idx_shift_instances_plan_group_date ON shift_instances(plan_group_id, shift_date);
CREATE INDEX idx_plan_tasks_shift_instance ON plan_tasks(shift_instance_id) WHERE shift_instance_id IS NOT NULL;
CREATE INDEX idx_plan_tasks_plan_group ON plan_tasks(plan_group_id) WHERE plan_group_id IS NOT NULL;

-- 8. 插入系统预设的班次模板
INSERT INTO shift_templates (template_name, schedule_type, start_hour, start_minute, end_hour, end_minute, duration_hours, work_days, description, is_system_template) VALUES
-- 7×24小时连续班次
('全天连续班', '7x24', 0, 0, 23, 59, 24.0, ARRAY[1,2,3,4,5,6,7], '7天×24小时连续工作制，适用于连续生产线', true),

-- 7×12小时两班制
('白班(7×12)', '7x12', 8, 0, 20, 0, 12.0, ARRAY[1,2,3,4,5,6,7], '白班：8:00-20:00，7天×12小时工作制', true),
('夜班(7×12)', '7x12', 20, 0, 8, 0, 12.0, ARRAY[1,2,3,4,5,6,7], '夜班：20:00-次日8:00，7天×12小时工作制', true),

-- 5×8小时标准班次
('标准白班(5×8)', '5x8', 9, 0, 17, 0, 8.0, ARRAY[1,2,3,4,5], '标准白班：9:00-17:00，周一至周五', true),
('早班(5×8)', '5x8', 6, 0, 14, 0, 8.0, ARRAY[1,2,3,4,5], '早班：6:00-14:00，周一至周五', true),
('晚班(5×8)', '5x8', 14, 0, 22, 0, 8.0, ARRAY[1,2,3,4,5], '晚班：14:00-22:00，周一至周五', true),

-- 特殊班次
('维护班次', 'custom', 22, 0, 6, 0, 8.0, ARRAY[6,7], '设备维护专用班次：22:00-次日6:00，周末执行', true);

-- 9. 插入默认计划组
INSERT INTO plan_groups (group_name, group_code, description, priority, created_by) VALUES
('生产计划组', 'PROD', '负责生产任务的计划和调度，支持7×24小时连续生产', 1, 1),
('维护计划组', 'MAINT', '负责设备维护和保养任务的计划，通常在非生产时间执行', 2, 1),
('质检计划组', 'QC', '负责质量检验任务的计划，配合生产进度进行', 3, 1),
('紧急计划组', 'URGENT', '处理紧急生产任务和故障维修，优先级最高', 0, 1);

-- 10. 创建视图以简化查询
CREATE VIEW v_active_shift_configs AS
SELECT
    pgsc.id,
    pg.group_name,
    pg.group_code,
    st.template_name,
    st.schedule_type,
    st.start_hour,
    st.start_minute,
    st.end_hour,
    st.end_minute,
    st.duration_hours,
    st.work_days,
    pgsc.effective_date,
    pgsc.expiry_date,
    pgsc.is_default
FROM plan_group_shift_configs pgsc
JOIN plan_groups pg ON pgsc.plan_group_id = pg.id
JOIN shift_templates st ON pgsc.shift_template_id = st.id
WHERE pg.is_active = true
  AND st.is_active = true
  AND pgsc.effective_date <= CURRENT_DATE
  AND (pgsc.expiry_date IS NULL OR pgsc.expiry_date >= CURRENT_DATE);

-- 11. 创建函数以获取指定日期的班次安排
CREATE OR REPLACE FUNCTION get_shift_schedule(
    p_plan_group_id INTEGER,
    p_date DATE
) RETURNS TABLE (
    shift_instance_id INTEGER,
    template_name VARCHAR(100),
    schedule_type VARCHAR(20),
    planned_start_time TIMESTAMP,
    planned_end_time TIMESTAMP,
    status VARCHAR(20)
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        si.id,
        st.template_name,
        st.schedule_type,
        si.planned_start_time,
        si.planned_end_time,
        si.status
    FROM shift_instances si
    JOIN shift_templates st ON si.shift_template_id = st.id
    WHERE si.plan_group_id = p_plan_group_id
      AND si.shift_date = p_date
    ORDER BY si.planned_start_time;
END;
$$ LANGUAGE plpgsql;
