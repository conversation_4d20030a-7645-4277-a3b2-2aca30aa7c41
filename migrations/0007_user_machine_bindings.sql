-- 用户设备绑定表
-- 允许操作员绑定自己常用的设备，用于个性化仪表板显示

-- 创建用户设备绑定表
CREATE TABLE user_machine_bindings (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    machine_id INTEGER NOT NULL REFERENCES machines(id) ON DELETE CASCADE,
    is_primary BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(user_id, machine_id)
);

-- 创建索引
CREATE INDEX idx_user_machine_bindings_user_id ON user_machine_bindings(user_id);
CREATE INDEX idx_user_machine_bindings_machine_id ON user_machine_bindings(machine_id);
CREATE INDEX idx_user_machine_bindings_is_primary ON user_machine_bindings(is_primary);

-- 添加注释
COMMENT ON TABLE user_machine_bindings IS '用户设备绑定表，记录操作员与设备的关联关系';
COMMENT ON COLUMN user_machine_bindings.user_id IS '用户ID';
COMMENT ON COLUMN user_machine_bindings.machine_id IS '设备ID';
COMMENT ON COLUMN user_machine_bindings.is_primary IS '是否为主要设备（用户可以设置一个主要设备）';
COMMENT ON COLUMN user_machine_bindings.created_at IS '绑定创建时间';
COMMENT ON COLUMN user_machine_bindings.updated_at IS '绑定更新时间';

-- 创建触发器自动更新updated_at字段
CREATE OR REPLACE FUNCTION update_user_machine_bindings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_update_user_machine_bindings_updated_at
    BEFORE UPDATE ON user_machine_bindings
    FOR EACH ROW
    EXECUTE FUNCTION update_user_machine_bindings_updated_at();

-- 确保每个用户只能有一个主要设备的约束
-- 这个约束通过应用层逻辑来保证，因为PostgreSQL的部分唯一索引语法较复杂
