-- Migration: Add project status functionality
-- Description: Add status field to projects table with constraints
-- Date: 2025-07-14

-- Add status column to projects table
ALTER TABLE projects 
ADD COLUMN status VARCHAR(20) NOT NULL DEFAULT 'normal';

-- Add check constraint for valid status values
ALTER TABLE projects 
ADD CONSTRAINT projects_status_check 
CHECK (status IN ('normal', 'priority', 'paused'));

-- Create index for better query performance on status
CREATE INDEX idx_projects_status ON projects(status);

-- Update existing projects to have 'normal' status (already set by DEFAULT)
-- This is just for documentation - the DEFAULT clause handles this

-- Add comment to document the status field
COMMENT ON COLUMN projects.status IS 'Project status: normal (default), priority (high priority), paused (temporarily stopped)';

-- Optional: Create a view for project status statistics
CREATE OR REPLACE VIEW project_status_stats AS
SELECT 
    status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
FROM projects 
GROUP BY status
ORDER BY 
    CASE status 
        WHEN 'priority' THEN 1 
        WHEN 'normal' THEN 2 
        WHEN 'paused' THEN 3 
    END;

-- Add comment to the view
COMMENT ON VIEW project_status_stats IS 'Statistics view showing project count and percentage by status';
