-- 数据导入系统数据库设计
-- 支持批量数据导入和错误跟踪

-- 导入任务表
CREATE TABLE IF NOT EXISTS import_jobs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    module_type VARCHAR(50) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    total_rows INTEGER,
    processed_rows INTEGER NOT NULL DEFAULT 0,
    success_rows INTEGER NOT NULL DEFAULT 0,
    error_rows INTEGER NOT NULL DEFAULT 0,
    error_details JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    
    CONSTRAINT valid_status CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    CONSTRAINT valid_module_type CHECK (module_type IN ('projects', 'parts', 'bom', 'routings', 'machines', 'users', 'skill_groups')),
    CONSTRAINT valid_row_counts CHECK (
        processed_rows >= 0 AND 
        success_rows >= 0 AND 
        error_rows >= 0 AND
        processed_rows = success_rows + error_rows
    )
);

-- 导入错误详情表
CREATE TABLE IF NOT EXISTS import_errors (
    id SERIAL PRIMARY KEY,
    import_job_id INTEGER NOT NULL REFERENCES import_jobs(id) ON DELETE CASCADE,
    row_number INTEGER NOT NULL,
    column_name VARCHAR(100),
    error_type VARCHAR(50) NOT NULL,
    error_message TEXT NOT NULL,
    row_data JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    CONSTRAINT valid_error_type CHECK (error_type IN ('validation', 'duplicate', 'reference', 'format', 'business')),
    CONSTRAINT valid_row_number CHECK (row_number > 0)
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_import_jobs_user_id ON import_jobs(user_id);
CREATE INDEX IF NOT EXISTS idx_import_jobs_module_type ON import_jobs(module_type);
CREATE INDEX IF NOT EXISTS idx_import_jobs_status ON import_jobs(status);
CREATE INDEX IF NOT EXISTS idx_import_jobs_created_at ON import_jobs(created_at);

CREATE INDEX IF NOT EXISTS idx_import_errors_job_id ON import_errors(import_job_id);
CREATE INDEX IF NOT EXISTS idx_import_errors_row_number ON import_errors(import_job_id, row_number);
CREATE INDEX IF NOT EXISTS idx_import_errors_error_type ON import_errors(error_type);

-- 添加注释
COMMENT ON TABLE import_jobs IS '数据导入任务表';
COMMENT ON COLUMN import_jobs.user_id IS '执行导入的用户ID';
COMMENT ON COLUMN import_jobs.module_type IS '导入的模块类型';
COMMENT ON COLUMN import_jobs.file_name IS '原始文件名';
COMMENT ON COLUMN import_jobs.file_path IS '服务器上的文件路径';
COMMENT ON COLUMN import_jobs.status IS '导入状态: pending, processing, completed, failed, cancelled';
COMMENT ON COLUMN import_jobs.total_rows IS '文件总行数（不包括标题行）';
COMMENT ON COLUMN import_jobs.processed_rows IS '已处理行数';
COMMENT ON COLUMN import_jobs.success_rows IS '成功导入行数';
COMMENT ON COLUMN import_jobs.error_rows IS '错误行数';
COMMENT ON COLUMN import_jobs.error_details IS '错误汇总信息（JSON格式）';

COMMENT ON TABLE import_errors IS '导入错误详情表';
COMMENT ON COLUMN import_errors.import_job_id IS '关联的导入任务ID';
COMMENT ON COLUMN import_errors.row_number IS '错误所在行号（从1开始，不包括标题行）';
COMMENT ON COLUMN import_errors.column_name IS '错误所在列名';
COMMENT ON COLUMN import_errors.error_type IS '错误类型: validation, duplicate, reference, format, business';
COMMENT ON COLUMN import_errors.error_message IS '错误描述信息';
COMMENT ON COLUMN import_errors.row_data IS '错误行的原始数据（JSON格式）';

-- 插入一些示例数据（可选，用于测试）
-- INSERT INTO import_jobs (user_id, module_type, file_name, file_path, status, total_rows, processed_rows, success_rows, error_rows)
-- VALUES 
--     (1, 'projects', 'projects_sample.csv', '/uploads/projects_sample.csv', 'completed', 10, 10, 8, 2),
--     (1, 'parts', 'parts_sample.csv', '/uploads/parts_sample.csv', 'processing', 50, 25, 20, 5);
