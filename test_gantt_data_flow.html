<!DOCTYPE html>
<html>
<head>
    <title>甘特图数据流测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .error { color: red; }
        .success { color: green; }
        .info { color: blue; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; max-height: 300px; border-radius: 4px; }
        button { padding: 8px 16px; margin: 5px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #40a9ff; }
        .status { padding: 8px 12px; border-radius: 4px; margin: 5px 0; }
        .status.success { background: #f6ffed; border: 1px solid #b7eb8f; }
        .status.error { background: #fff2f0; border: 1px solid #ffccc7; }
        .status.info { background: #f0f9ff; border: 1px solid #91d5ff; }
        .status.warning { background: #fffbe6; border: 1px solid #ffe58f; }
    </style>
</head>
<body>
    <h1>甘特图数据流测试</h1>
    
    <div class="test-section">
        <h2>🎯 测试目标</h2>
        <p>验证从API到甘特图组件的完整数据流</p>
        <ol>
            <li>API数据获取</li>
            <li>React Query缓存</li>
            <li>组件props传递</li>
            <li>数据转换处理</li>
            <li>甘特图渲染</li>
        </ol>
    </div>
    
    <div class="test-section">
        <h2>1. API数据测试</h2>
        <button onclick="testAPIData()">测试API数据</button>
        <div id="api-test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 数据格式验证</h2>
        <button onclick="validateDataFormat()">验证数据格式</button>
        <div id="format-test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 日期解析测试</h2>
        <button onclick="testDateParsing()">测试日期解析</button>
        <div id="date-test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 甘特图组件调试</h2>
        <p>请访问 <a href="http://localhost:3000/plan-tasks" target="_blank">计划任务页面</a> 并打开浏览器控制台查看调试信息</p>
        <button onclick="openConsoleInstructions()">显示控制台调试指南</button>
        <div id="console-instructions"></div>
    </div>
    
    <div class="test-section">
        <h2>5. 问题诊断</h2>
        <button onclick="diagnoseProblem()">诊断问题</button>
        <div id="diagnosis-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:9000/api';
        let authToken = '';
        
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function appendStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML += `<div class="status ${type}">${message}</div>`;
        }
        
        async function login() {
            if (authToken) return true;
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'admin', password: 'admin123' })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    authToken = data.token;
                    return true;
                } else {
                    throw new Error(`登录失败: ${response.status}`);
                }
            } catch (error) {
                throw new Error(`登录请求失败: ${error.message}`);
            }
        }
        
        async function testAPIData() {
            showStatus('api-test-result', '🔍 测试API数据...', 'info');
            
            try {
                await login();
                appendStatus('api-test-result', '✅ 登录成功', 'success');
                
                const response = await fetch(`${API_BASE}/plan-tasks`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                if (!response.ok) {
                    throw new Error(`API请求失败: ${response.status}`);
                }
                
                const data = await response.json();
                const tasks = data.plan_tasks || [];
                
                appendStatus('api-test-result', 
                    `✅ API数据获取成功<br>
                    📊 任务数量: ${tasks.length}<br>
                    📋 数据结构: ${JSON.stringify(Object.keys(data), null, 2)}<br>
                    ${tasks.length > 0 ? 
                        `<details><summary>查看第一个任务</summary><pre>${JSON.stringify(tasks[0], null, 2)}</pre></details>` : 
                        ''
                    }`, 
                    'success'
                );
                
                // 保存数据供其他测试使用
                window.testData = { tasks, rawData: data };
                
            } catch (error) {
                appendStatus('api-test-result', `❌ API测试失败: ${error.message}`, 'error');
            }
        }
        
        async function validateDataFormat() {
            showStatus('format-test-result', '🔍 验证数据格式...', 'info');
            
            if (!window.testData) {
                appendStatus('format-test-result', '⚠️ 请先运行API数据测试', 'warning');
                return;
            }
            
            const { tasks } = window.testData;
            
            if (tasks.length === 0) {
                appendStatus('format-test-result', '❌ 没有任务数据可验证', 'error');
                return;
            }
            
            const requiredFields = ['id', 'process_name', 'part_number', 'planned_start', 'planned_end', 'status', 'skill_group_name'];
            const validTasks = [];
            const invalidTasks = [];
            
            tasks.forEach((task, index) => {
                const missingFields = requiredFields.filter(field => !task.hasOwnProperty(field) || task[field] === null || task[field] === undefined);
                
                if (missingFields.length === 0) {
                    validTasks.push(task);
                } else {
                    invalidTasks.push({ index, task, missingFields });
                }
            });
            
            appendStatus('format-test-result', 
                `📊 数据格式验证结果:<br>
                ✅ 有效任务: ${validTasks.length}<br>
                ❌ 无效任务: ${invalidTasks.length}<br>
                📋 必需字段: ${requiredFields.join(', ')}<br>
                ${invalidTasks.length > 0 ? 
                    `<details><summary>查看无效任务</summary><pre>${JSON.stringify(invalidTasks, null, 2)}</pre></details>` : 
                    ''
                }`, 
                validTasks.length > 0 ? 'success' : 'error'
            );
        }
        
        async function testDateParsing() {
            showStatus('date-test-result', '🔍 测试日期解析...', 'info');
            
            if (!window.testData) {
                appendStatus('date-test-result', '⚠️ 请先运行API数据测试', 'warning');
                return;
            }
            
            const { tasks } = window.testData;
            
            if (tasks.length === 0) {
                appendStatus('date-test-result', '❌ 没有任务数据可测试', 'error');
                return;
            }
            
            const dateResults = tasks.map((task, index) => {
                try {
                    const startDate = new Date(task.planned_start);
                    const endDate = new Date(task.planned_end);
                    
                    return {
                        index,
                        taskId: task.id,
                        planned_start: task.planned_start,
                        planned_end: task.planned_end,
                        startDateValid: !isNaN(startDate.getTime()),
                        endDateValid: !isNaN(endDate.getTime()),
                        startDateParsed: startDate.toISOString(),
                        endDateParsed: endDate.toISOString(),
                        duration: !isNaN(startDate.getTime()) && !isNaN(endDate.getTime()) ? 
                            Math.round((endDate - startDate) / (1000 * 60 * 60)) + '小时' : 'N/A'
                    };
                } catch (error) {
                    return {
                        index,
                        taskId: task.id,
                        error: error.message,
                        startDateValid: false,
                        endDateValid: false
                    };
                }
            });
            
            const validDates = dateResults.filter(r => r.startDateValid && r.endDateValid);
            const invalidDates = dateResults.filter(r => !r.startDateValid || !r.endDateValid);
            
            appendStatus('date-test-result', 
                `📅 日期解析结果:<br>
                ✅ 有效日期: ${validDates.length}<br>
                ❌ 无效日期: ${invalidDates.length}<br>
                ${validDates.length > 0 ? 
                    `<details><summary>查看有效日期示例</summary><pre>${JSON.stringify(validDates.slice(0, 3), null, 2)}</pre></details>` : 
                    ''
                }
                ${invalidDates.length > 0 ? 
                    `<details><summary>查看无效日期</summary><pre>${JSON.stringify(invalidDates, null, 2)}</pre></details>` : 
                    ''
                }`, 
                validDates.length > 0 ? 'success' : 'error'
            );
        }
        
        function openConsoleInstructions() {
            showStatus('console-instructions', 
                `📋 控制台调试指南:<br><br>
                1. 访问 <a href="http://localhost:3000/plan-tasks" target="_blank">计划任务页面</a><br>
                2. 打开浏览器开发者工具 (F12)<br>
                3. 切换到"控制台"标签<br>
                4. 点击"甘特图"标签页<br>
                5. 查看控制台输出的调试信息<br><br>
                
                <strong>关键调试信息:</strong><br>
                • 🔍 甘特图数据转换 - 原始任务数量<br>
                • 🔍 甘特图数据转换 - 原始任务数据<br>
                • ✅ 甘特图数据转换完成 - 有效任务数量<br>
                • ❌ 甘特图显示空数据页面 (如果出现此信息说明有问题)<br><br>
                
                <strong>如果看到"暂无计划任务数据":</strong><br>
                检查页面上显示的调试信息，包括原始任务数量和处理后任务数量`, 
                'info'
            );
        }
        
        async function diagnoseProblem() {
            showStatus('diagnosis-result', '🔍 诊断问题...', 'info');
            
            try {
                // 检查API数据
                if (!window.testData) {
                    await testAPIData();
                }
                
                if (!window.testData || !window.testData.tasks) {
                    appendStatus('diagnosis-result', '❌ 无法获取API数据', 'error');
                    return;
                }
                
                const { tasks } = window.testData;
                
                // 诊断步骤
                let diagnosis = [];
                
                if (tasks.length === 0) {
                    diagnosis.push('❌ API返回的任务数量为0');
                } else {
                    diagnosis.push(`✅ API返回 ${tasks.length} 个任务`);
                }
                
                // 检查必需字段
                const requiredFields = ['id', 'process_name', 'part_number', 'planned_start', 'planned_end', 'status', 'skill_group_name'];
                const validTasks = tasks.filter(task => 
                    requiredFields.every(field => task.hasOwnProperty(field) && task[field] !== null && task[field] !== undefined)
                );
                
                if (validTasks.length === 0) {
                    diagnosis.push('❌ 没有包含所有必需字段的任务');
                } else {
                    diagnosis.push(`✅ ${validTasks.length} 个任务包含所有必需字段`);
                }
                
                // 检查日期格式
                const validDateTasks = validTasks.filter(task => {
                    const startDate = new Date(task.planned_start);
                    const endDate = new Date(task.planned_end);
                    return !isNaN(startDate.getTime()) && !isNaN(endDate.getTime());
                });
                
                if (validDateTasks.length === 0) {
                    diagnosis.push('❌ 没有任务具有有效的日期格式');
                } else {
                    diagnosis.push(`✅ ${validDateTasks.length} 个任务具有有效的日期格式`);
                }
                
                // 最终诊断
                if (validDateTasks.length > 0) {
                    diagnosis.push('🎯 数据应该能正常显示在甘特图中');
                    diagnosis.push('🔍 如果甘特图仍然显示"暂无数据"，问题可能在于:');
                    diagnosis.push('   • React组件状态更新');
                    diagnosis.push('   • React Query缓存');
                    diagnosis.push('   • 组件渲染逻辑');
                } else {
                    diagnosis.push('🎯 数据问题导致甘特图无法显示');
                }
                
                appendStatus('diagnosis-result', 
                    `📋 问题诊断结果:<br><br>
                    ${diagnosis.join('<br>')}<br><br>
                    <strong>建议操作:</strong><br>
                    1. 访问计划任务页面并检查控制台调试信息<br>
                    2. 如果控制台显示有数据但甘特图为空，问题在前端组件<br>
                    3. 如果控制台显示无数据，问题在API或数据传递`, 
                    validDateTasks.length > 0 ? 'success' : 'error'
                );
                
            } catch (error) {
                appendStatus('diagnosis-result', `❌ 诊断失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动运行API测试
        window.onload = function() {
            testAPIData();
        };
    </script>
</body>
</html>
