# Rust
/target/
**/*.rs.bk

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Frontend build
frontend/dist/
frontend/build/
frontend/.next/
frontend/out/

# Database
*.db
*.sqlite
*.sqlite3

# Backup files
*.backup
*.bak
backups/

# Temporary files
*.tmp
*.temp
.cache/

# Docker
.dockerignore
docker-compose.override.yml

# Test coverage
coverage/
*.lcov

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Build artifacts
build/
dist/

# System files
.DS_Store
Thumbs.db

# Custom
.roo/
.roomodes
