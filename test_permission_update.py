#!/usr/bin/env python3
"""
测试权限更新功能的脚本
"""

import requests
import json
import sys

# 配置
BASE_URL = "http://localhost:9001/api"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "admin123"

def login():
    """登录获取token"""
    login_data = {
        "username": ADMIN_USERNAME,
        "password": ADMIN_PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        data = response.json()
        return data.get("token")
    else:
        print(f"登录失败: {response.status_code} - {response.text}")
        return None

def get_roles(token):
    """获取所有角色"""
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/auth/roles", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        return data.get("roles", [])
    else:
        print(f"获取角色失败: {response.status_code} - {response.text}")
        return []

def get_role_permissions(token, role_id):
    """获取角色权限"""
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/roles/{role_id}/permissions", headers=headers)
    
    if response.status_code == 200:
        return response.json()
    else:
        print(f"获取角色权限失败: {response.status_code} - {response.text}")
        return None

def update_role_permissions(token, role_id, permissions):
    """更新角色权限"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    data = {"permissions": permissions}
    response = requests.put(f"{BASE_URL}/roles/{role_id}/permissions", 
                          headers=headers, json=data)
    
    if response.status_code == 200:
        print(f"权限更新成功: {response.json()}")
        return True
    else:
        print(f"权限更新失败: {response.status_code} - {response.text}")
        return False

def main():
    print("开始测试权限更新功能...")
    
    # 1. 登录
    print("1. 登录...")
    token = login()
    if not token:
        sys.exit(1)
    print(f"登录成功，token: {token[:20]}...")
    
    # 2. 获取角色列表
    print("\n2. 获取角色列表...")
    roles = get_roles(token)
    print(f"找到 {len(roles)} 个角色:")
    for role in roles:
        print(f"  - {role['role_name']} (ID: {role['id']})")
    
    # 3. 找到operator角色
    operator_role = None
    for role in roles:
        if role['role_name'] == 'operator':
            operator_role = role
            break
    
    if not operator_role:
        print("未找到operator角色")
        sys.exit(1)
    
    print(f"\n3. 测试operator角色 (ID: {operator_role['id']})...")
    
    # 4. 获取当前权限
    print("4. 获取当前权限...")
    role_permissions = get_role_permissions(token, operator_role['id'])
    if not role_permissions:
        sys.exit(1)
    
    print(f"角色: {role_permissions['role_name']}")
    print(f"当前权限数量: {len(role_permissions['permissions'])}")
    
    # 显示当前已授权的权限
    granted_permissions = [p for p in role_permissions['permissions'] if p['granted']]
    print(f"已授权权限 ({len(granted_permissions)}):")
    for perm in granted_permissions:
        print(f"  - {perm['permission_name']} ({perm['permission_code']})")
    
    # 5. 测试权限修改 - 添加一个新权限
    print("\n5. 测试权限修改...")
    
    # 找到一个未授权的权限来测试
    ungranted_permissions = [p for p in role_permissions['permissions'] if not p['granted']]
    if not ungranted_permissions:
        print("所有权限都已授权，无法测试添加权限")
        return
    
    test_permission = ungranted_permissions[0]
    print(f"将为operator角色添加权限: {test_permission['permission_name']} ({test_permission['permission_code']})")
    
    # 构建更新请求
    updated_permissions = []
    for perm in role_permissions['permissions']:
        if perm['id'] == test_permission['id']:
            # 授权这个权限
            updated_permissions.append({
                "permission_id": perm['id'],
                "granted": True
            })
        else:
            # 保持原有状态
            updated_permissions.append({
                "permission_id": perm['id'],
                "granted": perm['granted']
            })
    
    # 6. 执行更新
    print("6. 执行权限更新...")
    success = update_role_permissions(token, operator_role['id'], updated_permissions)
    
    if success:
        # 7. 验证更新结果
        print("\n7. 验证更新结果...")
        updated_role_permissions = get_role_permissions(token, operator_role['id'])
        if updated_role_permissions:
            updated_granted = [p for p in updated_role_permissions['permissions'] if p['granted']]
            print(f"更新后已授权权限数量: {len(updated_granted)}")
            
            # 检查测试权限是否已授权
            test_perm_granted = False
            for perm in updated_role_permissions['permissions']:
                if perm['id'] == test_permission['id'] and perm['granted']:
                    test_perm_granted = True
                    break
            
            if test_perm_granted:
                print(f"✅ 权限更新成功！{test_permission['permission_name']} 已被授权")
            else:
                print(f"❌ 权限更新失败！{test_permission['permission_name']} 未被授权")
        else:
            print("❌ 无法验证更新结果")
    else:
        print("❌ 权限更新失败")

if __name__ == "__main__":
    main()
