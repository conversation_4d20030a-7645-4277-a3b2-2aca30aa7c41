@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo MES系统 - 优化部署脚本 v2.0
echo ========================================
echo.

:: 显示系统要求
echo 📋 系统要求检查
echo ========================================
echo 本脚本将自动检查并安装以下组件:
echo.
echo ✅ 自动安装组件:
echo   • Rust (编程语言和工具链)
echo   • Node.js (JavaScript运行时)
echo   • 项目依赖包
echo.
echo ⚠️  需要手动安装的组件:
echo   • PostgreSQL 数据库 (15+ 版本)
echo   • Windows 10/11 (推荐)
echo   • 至少 4GB 可用内存
echo   • 至少 2GB 可用磁盘空间
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  权限提醒: 建议以管理员身份运行此脚本
    echo    这将确保所有组件能够正确安装
    echo.
)

:: PostgreSQL 安装检查和提醒
echo 🔍 检查 PostgreSQL 数据库...
where psql >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ❌ PostgreSQL 未安装或未添加到系统PATH
    echo.
    echo 📥 PostgreSQL 安装指南:
    echo ========================================
    echo 1. 访问官方下载页面:
    echo    https://www.postgresql.org/download/windows/
    echo.
    echo 2. 下载 PostgreSQL 15 或更高版本
    echo    推荐下载 "Windows x86-64" 版本
    echo.
    echo 3. 运行安装程序，安装过程中请注意:
    echo    • 记住设置的超级用户(postgres)密码
    echo    • 保持默认端口 5432
    echo    • 勾选 "Add to PATH" 选项
    echo    • 安装 pgAdmin (可选，用于图形化管理)
    echo.
    echo 4. 安装完成后，重新运行此脚本
    echo.
    echo ⏸️  按任意键打开下载页面，或关闭窗口手动安装...
    pause >nul
    start https://www.postgresql.org/download/windows/
    echo.
    echo 安装完成后请重新运行此脚本
    pause
    exit /b 1
) else (
    echo ✅ PostgreSQL 已安装
    psql --version
)

echo.
set /p "confirm=🚀 是否继续自动安装其他组件? [y/N]: "
if /i not "!confirm!"=="y" (
    echo 部署已取消
    pause
    exit /b 0
)

echo.
echo 🔧 开始自动安装和配置...
echo ========================================

:: 检查系统信息
echo.
echo [1/7] 📊 系统环境检查...
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo Windows版本: %VERSION%

if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    set "ARCH=x64"
) else if "%PROCESSOR_ARCHITECTURE%"=="x86" (
    set "ARCH=x86"
) else (
    set "ARCH=%PROCESSOR_ARCHITECTURE%"
)
echo 系统架构: %ARCH%
echo ✅ 系统环境检查完成

:: Rust 安装
echo.
echo [2/7] 🦀 检查并安装 Rust...
where rustc >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Rust 已安装
    rustc --version
) else (
    echo 📥 正在下载并安装 Rust...
    echo    这可能需要几分钟时间，请耐心等待...
    
    powershell -Command "Invoke-WebRequest -Uri 'https://win.rustup.rs/x86_64' -OutFile 'rustup-init.exe'"
    if !errorlevel! neq 0 (
        echo ❌ 下载 Rust 安装程序失败
        echo 请检查网络连接或手动访问 https://rustup.rs/ 下载安装
        pause
        exit /b 1
    )
    
    rustup-init.exe -y --default-toolchain stable
    if !errorlevel! neq 0 (
        echo ❌ Rust 安装失败
        pause
        exit /b 1
    )
    
    call "%USERPROFILE%\.cargo\env.bat"
    del rustup-init.exe
    echo ✅ Rust 安装完成
)

:: Node.js 安装
echo.
echo [3/7] 🟢 检查并安装 Node.js...
where node >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js 已安装
    node --version
    npm --version
) else (
    echo 📥 正在下载并安装 Node.js LTS...
    
    set "NODE_VERSION=20.10.0"
    set "NODE_URL=https://nodejs.org/dist/v%NODE_VERSION%/node-v%NODE_VERSION%-x64.msi"
    
    powershell -Command "Invoke-WebRequest -Uri '%NODE_URL%' -OutFile 'nodejs.msi'"
    if !errorlevel! neq 0 (
        echo ❌ 下载 Node.js 失败
        echo 请检查网络连接或手动访问 https://nodejs.org/ 下载安装
        pause
        exit /b 1
    )
    
    msiexec /i nodejs.msi /quiet /norestart
    if !errorlevel! neq 0 (
        echo ❌ Node.js 安装失败
        pause
        exit /b 1
    )
    
    del nodejs.msi
    set "PATH=%PATH%;%ProgramFiles%\nodejs"
    echo ✅ Node.js 安装完成
)

:: 数据库初始化
echo.
echo [4/7] 🗄️  数据库初始化...
if not exist ".env" (
    echo 📝 正在配置数据库连接...
    call init-database.bat
    if !errorlevel! neq 0 (
        echo ❌ 数据库初始化失败
        pause
        exit /b 1
    )
) else (
    echo ✅ 数据库配置已存在，跳过初始化
)

:: 后端构建
echo.
echo [5/7] ⚙️  构建后端服务...
echo 📦 正在编译 Rust 后端，这可能需要几分钟...
cargo build --release
if %errorlevel% neq 0 (
    echo ❌ 后端编译失败
    echo 请检查 Rust 环境和网络连接
    pause
    exit /b 1
)
echo ✅ 后端构建完成

:: 前端构建
echo.
echo [6/7] 🎨 构建前端应用...
cd frontend
echo 📦 正在安装前端依赖...
npm install
if %errorlevel% neq 0 (
    echo ❌ 前端依赖安装失败
    echo 请检查 Node.js 环境和网络连接
    pause
    exit /b 1
)

echo 🏗️  正在构建前端应用...
npm run build
if %errorlevel% neq 0 (
    echo ❌ 前端构建失败
    pause
    exit /b 1
)
echo ✅ 前端构建完成
cd ..

:: 创建启动脚本和服务
echo.
echo [7/7] 📜 创建启动脚本...
echo ✅ 启动脚本已就绪

echo.
echo ========================================
echo 🎉 MES系统部署完成！
echo ========================================
echo.
echo 📊 部署摘要:
echo   ✅ Rust 后端服务已构建
echo   ✅ React 前端应用已构建  
echo   ✅ PostgreSQL 数据库已配置
echo   ✅ 环境配置文件已创建
echo.
echo 🚀 下一步操作:
echo   1. 运行 start_all.bat 启动所有服务
echo   2. 或运行 quick_start.bat 快速启动
echo.
echo 🌐 系统访问地址:
echo   前端界面: http://localhost:3000
echo   后端API:  http://localhost:8080
echo.
echo 🔑 默认管理员账户:
echo   用户名: admin  
echo   密码: admin123
echo.
echo 💡 有用的命令:
echo   start_all.bat     - 启动所有服务
echo   stop_all.bat      - 停止所有服务  
echo   check-system.bat  - 检查系统状态
echo   quick_start.bat   - 快速启动(推荐)
echo.

set /p "start_now=🚀 是否立即启动系统? [y/N]: "
if /i "!start_now!"=="y" (
    echo.
    echo 🔄 正在启动系统...
    call quick_start.bat
) else (
    echo.
    echo 📝 部署完成！请手动运行启动脚本来启动系统
    pause
)
