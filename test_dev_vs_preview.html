<!DOCTYPE html>
<html>
<head>
    <title>开发版本 vs 预览版本对比测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .error { color: red; }
        .success { color: green; }
        .info { color: blue; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; max-height: 200px; border-radius: 4px; }
        button { padding: 8px 16px; margin: 5px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #40a9ff; }
        .status { padding: 8px 12px; border-radius: 4px; margin: 5px 0; }
        .status.success { background: #f6ffed; border: 1px solid #b7eb8f; }
        .status.error { background: #fff2f0; border: 1px solid #ffccc7; }
        .status.info { background: #f0f9ff; border: 1px solid #91d5ff; }
        .status.warning { background: #fffbe6; border: 1px solid #ffe58f; }
        .comparison { display: flex; gap: 20px; }
        .comparison > div { flex: 1; }
        h3 { color: #1890ff; }
    </style>
</head>
<body>
    <h1>开发版本 vs 预览版本甘特图对比测试</h1>
    
    <div class="test-section">
        <h2>🔍 问题分析</h2>
        <p><strong>用户反馈</strong>: 开发版本甘特图正常，预览版本甘特图不显示任务</p>
        <p><strong>可能原因</strong>:</p>
        <ul>
            <li>构建过程中的代码转换问题</li>
            <li>环境变量差异</li>
            <li>模块解析差异</li>
            <li>热重载 vs 静态文件的差异</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>1. 当前环境检测</h2>
        <button onclick="detectEnvironment()">检测当前环境</button>
        <div id="env-status"></div>
    </div>
    
    <div class="test-section">
        <h2>2. API数据对比测试</h2>
        <button onclick="testAPIs()">测试所有API</button>
        <div id="api-status"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 前端数据处理测试</h2>
        <button onclick="testDataProcessing()">测试数据处理</button>
        <div id="processing-status"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 甘特图组件调试</h2>
        <button onclick="debugGanttComponent()">调试甘特图组件</button>
        <div id="gantt-debug-status"></div>
    </div>
    
    <div class="test-section">
        <h2>5. 手动验证</h2>
        <div class="comparison">
            <div>
                <h3>开发版本 (当前)</h3>
                <a href="http://localhost:3000/plan-tasks" target="_blank" style="color: #1890ff;">
                    访问计划任务页面 (开发版本)
                </a>
                <p style="font-size: 14px; color: #666;">
                    点击链接，查看甘特图标签页是否显示任务
                </p>
            </div>
            <div>
                <h3>预览版本 (需要构建)</h3>
                <button onclick="buildAndTestPreview()">构建并测试预览版本</button>
                <div id="preview-build-status"></div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:9000/api';
        let authToken = '';
        
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function appendStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML += `<div class="status ${type}">${message}</div>`;
        }
        
        async function detectEnvironment() {
            showStatus('env-status', '🔍 检测环境中...', 'info');
            
            try {
                // 检测是否是开发模式
                const isDev = window.location.port === '3000' && 
                             (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');
                
                // 检测Vite开发服务器特征
                const response = await fetch('http://localhost:3000/@vite/client', { method: 'HEAD' });
                const isViteDev = response.ok;
                
                let envType = 'unknown';
                if (isViteDev) {
                    envType = 'development (Vite Dev Server)';
                } else if (isDev) {
                    envType = 'preview (Vite Preview)';
                } else {
                    envType = 'production';
                }
                
                showStatus('env-status', 
                    `✅ 环境检测完成<br>
                    🌍 当前环境: ${envType}<br>
                    🔗 访问地址: ${window.location.origin}<br>
                    🔧 Vite开发服务器: ${isViteDev ? '是' : '否'}<br>
                    📱 用户代理: ${navigator.userAgent.substring(0, 50)}...`, 
                    'success'
                );
            } catch (error) {
                showStatus('env-status', `❌ 环境检测失败: ${error.message}`, 'error');
            }
        }
        
        async function login() {
            if (authToken) return true;
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'admin', password: 'admin123' })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    authToken = data.token;
                    return true;
                } else {
                    throw new Error(`登录失败: ${response.status}`);
                }
            } catch (error) {
                throw new Error(`登录请求失败: ${error.message}`);
            }
        }
        
        async function testAPIs() {
            showStatus('api-status', '🔍 测试API中...', 'info');
            
            try {
                await login();
                appendStatus('api-status', '✅ 登录成功', 'success');
                
                // 测试计划任务API
                const planTasksResponse = await fetch(`${API_BASE}/plan-tasks`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                if (planTasksResponse.ok) {
                    const planTasksData = await planTasksResponse.json();
                    const taskCount = planTasksData.plan_tasks ? planTasksData.plan_tasks.length : 0;
                    appendStatus('api-status', `✅ 计划任务API: ${taskCount} 个任务`, 'success');
                } else {
                    appendStatus('api-status', `❌ 计划任务API失败: ${planTasksResponse.status}`, 'error');
                }
                
                // 测试甘特图API
                const now = new Date();
                const startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                const endDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
                
                const ganttResponse = await fetch(`${API_BASE}/planning/gantt?start_date=${startDate.toISOString()}&end_date=${endDate.toISOString()}`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                if (ganttResponse.ok) {
                    const ganttData = await ganttResponse.json();
                    const ganttTaskCount = ganttData.tasks ? ganttData.tasks.length : 0;
                    appendStatus('api-status', `✅ 甘特图API: ${ganttTaskCount} 个任务`, 'success');
                } else {
                    appendStatus('api-status', `❌ 甘特图API失败: ${ganttResponse.status}`, 'error');
                }
                
            } catch (error) {
                appendStatus('api-status', `❌ API测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testDataProcessing() {
            showStatus('processing-status', '🔍 测试数据处理中...', 'info');
            
            try {
                await login();
                
                // 获取计划任务数据
                const response = await fetch(`${API_BASE}/plan-tasks`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                if (!response.ok) {
                    throw new Error(`API调用失败: ${response.status}`);
                }
                
                const data = await response.json();
                const tasks = data.plan_tasks || [];
                
                if (tasks.length === 0) {
                    appendStatus('processing-status', '⚠️ 没有计划任务数据', 'warning');
                    return;
                }
                
                // 模拟甘特图组件的数据处理
                const processedTasks = tasks.map(task => {
                    try {
                        return {
                            id: task.id,
                            name: `${task.process_name} - ${task.part_number}`,
                            startDate: new Date(task.planned_start),
                            endDate: new Date(task.planned_end),
                            status: task.status,
                            progress: 0,
                            assignee: task.skill_group_name,
                            isValid: !isNaN(new Date(task.planned_start).getTime()) && 
                                    !isNaN(new Date(task.planned_end).getTime())
                        };
                    } catch (error) {
                        return {
                            id: task.id,
                            error: error.message,
                            isValid: false
                        };
                    }
                });
                
                const validTasks = processedTasks.filter(task => task.isValid);
                const invalidTasks = processedTasks.filter(task => !task.isValid);
                
                appendStatus('processing-status', 
                    `✅ 数据处理完成<br>
                    📊 原始任务: ${tasks.length}<br>
                    ✅ 有效任务: ${validTasks.length}<br>
                    ❌ 无效任务: ${invalidTasks.length}<br>
                    ${invalidTasks.length > 0 ? 
                        `<details><summary>查看无效任务</summary><pre>${JSON.stringify(invalidTasks, null, 2)}</pre></details>` : 
                        ''
                    }`, 
                    validTasks.length > 0 ? 'success' : 'error'
                );
                
            } catch (error) {
                appendStatus('processing-status', `❌ 数据处理失败: ${error.message}`, 'error');
            }
        }
        
        async function debugGanttComponent() {
            showStatus('gantt-debug-status', '🔍 调试甘特图组件...', 'info');
            
            // 检查甘特图相关的DOM元素
            setTimeout(() => {
                const ganttElements = document.querySelectorAll('[class*="gantt"], [id*="gantt"]');
                const hasGanttCSS = Array.from(document.styleSheets).some(sheet => {
                    try {
                        return Array.from(sheet.cssRules).some(rule => 
                            rule.selectorText && rule.selectorText.includes('gantt')
                        );
                    } catch (e) {
                        return false;
                    }
                });
                
                appendStatus('gantt-debug-status', 
                    `🔍 DOM检查结果:<br>
                    📋 甘特图相关元素: ${ganttElements.length}<br>
                    🎨 甘特图CSS规则: ${hasGanttCSS ? '存在' : '不存在'}<br>
                    📱 当前页面: ${window.location.pathname}`, 
                    'info'
                );
                
                if (window.location.pathname === '/plan-tasks') {
                    appendStatus('gantt-debug-status', 
                        '✅ 当前在计划任务页面，请检查甘特图标签页', 
                        'success'
                    );
                } else {
                    appendStatus('gantt-debug-status', 
                        '⚠️ 请访问计划任务页面进行测试', 
                        'warning'
                    );
                }
            }, 1000);
        }
        
        async function buildAndTestPreview() {
            showStatus('preview-build-status', '🔨 构建预览版本中...', 'info');
            
            appendStatus('preview-build-status', 
                `⚠️ 需要手动操作:<br>
                1. 在终端运行: <code>cd frontend && npm run build-skip-check</code><br>
                2. 停止开发服务器: <code>pkill -f 'vite'</code><br>
                3. 启动预览服务器: <code>npm run preview</code><br>
                4. 访问: <a href="http://localhost:3000/plan-tasks" target="_blank">http://localhost:3000/plan-tasks</a>`, 
                'warning'
            );
        }
        
        // 页面加载时自动检测环境
        window.onload = function() {
            detectEnvironment();
        };
    </script>
</body>
</html>
