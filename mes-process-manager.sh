#!/bin/bash

# MES系统进程管理工具
# 安全的进程启动、停止和监控

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOGS_DIR="$SCRIPT_DIR/logs"
PIDS_DIR="$LOGS_DIR"

# 确保目录存在
mkdir -p "$LOGS_DIR"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查进程是否运行
is_process_running() {
    local pid=$1
    if [ -n "$pid" ] && ps -p "$pid" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 获取进程PID
get_backend_pid() {
    if [ -f "$PIDS_DIR/backend.pid" ]; then
        local pid=$(cat "$PIDS_DIR/backend.pid" 2>/dev/null)
        if is_process_running "$pid"; then
            echo "$pid"
            return 0
        else
            rm -f "$PIDS_DIR/backend.pid"
        fi
    fi
    
    # 按进程名查找
    local pid=$(pgrep -f "target/release/mes-system" 2>/dev/null | head -1)
    if [ -n "$pid" ]; then
        echo "$pid"
        return 0
    fi
    
    return 1
}

get_frontend_pid() {
    if [ -f "$PIDS_DIR/frontend.pid" ]; then
        local pid=$(cat "$PIDS_DIR/frontend.pid" 2>/dev/null)
        if is_process_running "$pid"; then
            echo "$pid"
            return 0
        else
            rm -f "$PIDS_DIR/frontend.pid"
        fi
    fi
    
    # 按进程名查找
    local pid=$(pgrep -f "python.*http.server.*3080\|vite.*dev" 2>/dev/null | head -1)
    if [ -n "$pid" ]; then
        echo "$pid"
        return 0
    fi
    
    return 1
}

# 安全停止进程
safe_kill_process() {
    local pid=$1
    local name=$2
    local timeout=${3:-10}
    
    if ! is_process_running "$pid"; then
        log_warning "$name 进程 (PID: $pid) 已经停止"
        return 0
    fi
    
    log_info "正在停止 $name 进程 (PID: $pid)..."
    
    # 发送TERM信号
    kill -TERM "$pid" 2>/dev/null || {
        log_error "无法发送TERM信号到进程 $pid"
        return 1
    }
    
    # 等待进程优雅退出
    local count=0
    while [ $count -lt $timeout ] && is_process_running "$pid"; do
        sleep 1
        count=$((count + 1))
    done
    
    # 如果还在运行，发送KILL信号
    if is_process_running "$pid"; then
        log_warning "$name 进程未响应TERM信号，发送KILL信号..."
        kill -KILL "$pid" 2>/dev/null || {
            log_error "无法发送KILL信号到进程 $pid"
            return 1
        }
        sleep 2
    fi
    
    if is_process_running "$pid"; then
        log_error "无法停止 $name 进程 (PID: $pid)"
        return 1
    else
        log_success "$name 进程已停止"
        return 0
    fi
}

# 启动后端 (后台)
start_backend() {
    local port=${1:-9001}
    
    # 检查是否已经运行
    if get_backend_pid >/dev/null 2>&1; then
        local pid=$(get_backend_pid)
        log_warning "后端服务已在运行 (PID: $pid)"
        return 1
    fi
    
    # 检查二进制文件
    if [ ! -f "$SCRIPT_DIR/target/release/mes-system" ]; then
        log_error "后端二进制文件不存在: $SCRIPT_DIR/target/release/mes-system"
        return 1
    fi
    
    log_info "启动后端服务 (端口: $port)..."
    
    # 设置环境变量
    export MES_PORT=$port
    export RUST_LOG=${RUST_LOG:-info}
    
    # 启动服务
    cd "$SCRIPT_DIR"
    nohup ./target/release/mes-system > "$LOGS_DIR/backend.log" 2>&1 &
    local pid=$!
    
    # 保存PID
    echo "$pid" > "$PIDS_DIR/backend.pid"
    
    # 等待启动
    sleep 3
    
    if is_process_running "$pid"; then
        log_success "后端服务已启动 (PID: $pid, 端口: $port)"
        log_info "日志文件: $LOGS_DIR/backend.log"
        return 0
    else
        log_error "后端服务启动失败"
        rm -f "$PIDS_DIR/backend.pid"
        return 1
    fi
}

# 启动前端 (后台)
start_frontend() {
    local port=${1:-3080}
    
    # 检查是否已经运行
    if get_frontend_pid >/dev/null 2>&1; then
        local pid=$(get_frontend_pid)
        log_warning "前端服务已在运行 (PID: $pid)"
        return 1
    fi
    
    # 检查构建文件
    if [ ! -d "$SCRIPT_DIR/frontend/dist" ]; then
        log_error "前端构建文件不存在: $SCRIPT_DIR/frontend/dist"
        return 1
    fi
    
    log_info "启动前端服务 (端口: $port)..."
    
    # 启动Python HTTP服务器
    cd "$SCRIPT_DIR/frontend"
    nohup python3 -m http.server "$port" --directory dist > "$LOGS_DIR/frontend.log" 2>&1 &
    local pid=$!
    
    # 保存PID
    echo "$pid" > "$PIDS_DIR/frontend.pid"
    
    # 等待启动
    sleep 2
    
    if is_process_running "$pid"; then
        log_success "前端服务已启动 (PID: $pid, 端口: $port)"
        log_info "日志文件: $LOGS_DIR/frontend.log"
        return 0
    else
        log_error "前端服务启动失败"
        rm -f "$PIDS_DIR/frontend.pid"
        return 1
    fi
}

# 停止后端
stop_backend() {
    local pid
    if pid=$(get_backend_pid); then
        safe_kill_process "$pid" "后端服务"
        rm -f "$PIDS_DIR/backend.pid"
        return $?
    else
        log_info "后端服务未运行"
        return 0
    fi
}

# 停止前端
stop_frontend() {
    local pid
    if pid=$(get_frontend_pid); then
        safe_kill_process "$pid" "前端服务"
        rm -f "$PIDS_DIR/frontend.pid"
        return $?
    else
        log_info "前端服务未运行"
        return 0
    fi
}

# 显示状态
show_status() {
    echo "=== MES系统进程状态 ==="
    echo ""
    
    # 后端状态
    if pid=$(get_backend_pid); then
        echo -e "${GREEN}✓${NC} 后端服务: 运行中 (PID: $pid)"
        if [ -f "$LOGS_DIR/backend.log" ]; then
            echo "  日志文件: $LOGS_DIR/backend.log"
        fi
    else
        echo -e "${RED}✗${NC} 后端服务: 未运行"
    fi
    
    # 前端状态
    if pid=$(get_frontend_pid); then
        echo -e "${GREEN}✓${NC} 前端服务: 运行中 (PID: $pid)"
        if [ -f "$LOGS_DIR/frontend.log" ]; then
            echo "  日志文件: $LOGS_DIR/frontend.log"
        fi
    else
        echo -e "${RED}✗${NC} 前端服务: 未运行"
    fi
    
    echo ""
}

# 清理无效的PID文件
cleanup_pids() {
    log_info "清理无效的PID文件..."
    
    for pidfile in "$PIDS_DIR"/*.pid; do
        if [ -f "$pidfile" ]; then
            local pid=$(cat "$pidfile" 2>/dev/null)
            if ! is_process_running "$pid"; then
                log_info "清理无效PID文件: $(basename "$pidfile")"
                rm -f "$pidfile"
            fi
        fi
    done
}

# 主函数
case "${1:-status}" in
    start-backend)
        start_backend "${2:-9001}"
        ;;
    start-frontend)
        start_frontend "${2:-3080}"
        ;;
    start-all)
        start_backend "${2:-9001}"
        start_frontend "${3:-3080}"
        ;;
    stop-backend)
        stop_backend
        ;;
    stop-frontend)
        stop_frontend
        ;;
    stop-all)
        stop_backend
        stop_frontend
        ;;
    status)
        show_status
        ;;
    cleanup)
        cleanup_pids
        ;;
    *)
        echo "用法: $0 {start-backend|start-frontend|start-all|stop-backend|stop-frontend|stop-all|status|cleanup} [端口]"
        echo ""
        echo "示例:"
        echo "  $0 start-backend 9001    # 启动后端服务"
        echo "  $0 start-frontend 3080   # 启动前端服务"
        echo "  $0 start-all 9001 3080   # 启动所有服务"
        echo "  $0 stop-all              # 停止所有服务"
        echo "  $0 status                # 显示状态"
        exit 1
        ;;
esac
