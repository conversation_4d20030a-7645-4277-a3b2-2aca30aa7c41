<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端认证状态测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button { margin: 5px; padding: 10px 15px; }
    </style>
</head>
<body>
    <h1>前端认证状态测试</h1>
    
    <div>
        <button onclick="checkLocalStorage()">检查本地存储</button>
        <button onclick="testLogin()">测试登录</button>
        <button onclick="testPartsAPI()">测试Parts API</button>
        <button onclick="clearStorage()">清除存储</button>
    </div>
    
    <div id="results"></div>

    <script>
        const API_BASE = 'http://localhost:9001/api';
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        function checkLocalStorage() {
            clearResults();
            addResult('<h3>检查本地存储状态</h3>');
            
            // 检查localStorage
            const authStorage = localStorage.getItem('auth-storage');
            if (authStorage) {
                try {
                    const parsed = JSON.parse(authStorage);
                    addResult(`认证存储: ${JSON.stringify(parsed, null, 2)}`, 'info');
                } catch (e) {
                    addResult(`认证存储解析错误: ${e.message}`, 'error');
                }
            } else {
                addResult('未找到认证存储', 'warning');
            }
            
            // 检查token
            const token = localStorage.getItem('token');
            if (token) {
                addResult(`Token: ${token.substring(0, 50)}...`, 'info');
                
                // 解析token
                try {
                    const parts = token.split('.');
                    if (parts.length === 3) {
                        const payload = JSON.parse(atob(parts[1]));
                        addResult(`Token payload: ${JSON.stringify(payload, null, 2)}`, 'info');
                        
                        const now = Math.floor(Date.now() / 1000);
                        const isExpired = payload.exp < now;
                        addResult(`Token过期状态: ${isExpired ? '已过期' : '有效'} (过期时间: ${new Date(payload.exp * 1000).toLocaleString()})`, isExpired ? 'error' : 'success');
                    }
                } catch (e) {
                    addResult(`Token解析错误: ${e.message}`, 'error');
                }
            } else {
                addResult('未找到Token', 'warning');
            }
        }
        
        async function testLogin() {
            clearResults();
            addResult('<h3>测试登录</h3>');
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`登录成功: ${JSON.stringify(data, null, 2)}`, 'success');
                    
                    // 存储token
                    localStorage.setItem('token', data.token);
                    addResult('Token已存储到localStorage', 'success');
                } else {
                    const error = await response.text();
                    addResult(`登录失败: ${response.status} - ${error}`, 'error');
                }
            } catch (error) {
                addResult(`登录请求错误: ${error.message}`, 'error');
            }
        }
        
        async function testPartsAPI() {
            clearResults();
            addResult('<h3>测试Parts API</h3>');
            
            const token = localStorage.getItem('token');
            if (!token) {
                addResult('未找到Token，请先登录', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/parts`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    }
                });
                
                addResult(`API响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`Parts数据: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    const error = await response.text();
                    addResult(`API错误: ${error}`, 'error');
                }
            } catch (error) {
                addResult(`API请求错误: ${error.message}`, 'error');
            }
        }
        
        function clearStorage() {
            localStorage.clear();
            sessionStorage.clear();
            addResult('所有存储已清除', 'info');
        }
        
        // 页面加载时自动检查
        window.onload = function() {
            checkLocalStorage();
        };
    </script>
</body>
</html>
