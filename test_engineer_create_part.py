#!/usr/bin/env python3
"""
测试工艺员创建零件功能
"""

import requests
import json
import time

BASE_URL = "http://localhost:9001/api"

def main():
    print("🔍 测试工艺员创建零件功能...")
    
    # 1. 工艺员登录
    login_response = requests.post(f"{BASE_URL}/auth/login", json={
        "username": "engineer1",
        "password": "gongyi"
    })
    
    if login_response.status_code != 200:
        print(f"❌ 工艺员登录失败: {login_response.status_code}")
        return 1
    
    token = login_response.json()["token"]
    print("✅ 工艺员登录成功")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 2. 检查用户权限
    me_response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
    if me_response.status_code == 200:
        user_info = me_response.json()
        print(f"当前用户: {user_info['username']}")
        print(f"角色: {user_info['roles']}")
        print(f"技能: {user_info.get('skills', [])}")
    
    # 3. 测试访问零件列表
    parts_response = requests.get(f"{BASE_URL}/parts", headers=headers)
    print(f"零件列表访问状态: {parts_response.status_code}")
    
    if parts_response.status_code == 200:
        parts_data = parts_response.json()
        print(f"✅ 可以访问零件列表 (共 {parts_data.get('data', {}).get('total_count', 0)} 个零件)")
    elif parts_response.status_code == 403:
        print("❌ 没有访问零件列表的权限")
        print(f"错误信息: {parts_response.text}")
        return 1
    else:
        print(f"❌ 访问零件列表失败: {parts_response.status_code}")
        print(f"错误信息: {parts_response.text}")
    
    # 4. 测试创建零件
    part_data = {
        "part_number": f"PE-TEST-{int(time.time())}",
        "part_name": "工艺员测试零件",
        "version": "1.0",
        "specifications": "这是工艺员创建的测试零件"
    }
    
    print(f"\n创建零件数据:")
    print(json.dumps(part_data, ensure_ascii=False, indent=2))
    
    create_response = requests.post(f"{BASE_URL}/parts", 
                                  headers=headers, 
                                  json=part_data)
    
    print(f"\n零件创建响应状态: {create_response.status_code}")
    print(f"响应头: {dict(create_response.headers)}")
    
    if create_response.status_code in [200, 201]:
        result = create_response.json()
        print("✅ 零件创建成功!")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        return 0
    else:
        print("❌ 零件创建失败!")
        print(f"错误响应: {create_response.text}")
        
        # 检查是否是权限问题
        if create_response.status_code == 403:
            print("\n🔍 权限问题分析:")
            print("1. 检查工艺员是否有CREATE_PART权限")
            print("2. 检查权限验证逻辑")
            print("3. 检查后端日志")
        
        return 1

if __name__ == "__main__":
    exit(main())
