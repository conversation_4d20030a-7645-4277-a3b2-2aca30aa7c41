#!/bin/bash

# MES系统自动安装脚本
# 支持Ubuntu/Debian、CentOS/RHEL、macOS

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warning "检测到root用户，建议使用普通用户安装"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 检测操作系统
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if [ -f /etc/debian_version ]; then
            OS="debian"
            log_info "检测到Debian/Ubuntu系统"
        elif [ -f /etc/redhat-release ]; then
            OS="redhat"
            log_info "检测到CentOS/RHEL系统"
        else
            OS="linux"
            log_info "检测到Linux系统"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        log_info "检测到macOS系统"
    else
        log_error "不支持的操作系统: $OSTYPE"
        exit 1
    fi
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查内存
    if [[ "$OS" == "macos" ]]; then
        MEMORY=$(sysctl -n hw.memsize | awk '{print int($1/1024/1024/1024)}')
    else
        MEMORY=$(free -g | awk '/^Mem:/{print $2}')
    fi
    
    if [ "$MEMORY" -lt 4 ]; then
        log_warning "系统内存少于4GB，可能影响性能"
    fi
    
    # 检查磁盘空间
    DISK_SPACE=$(df -BG . | awk 'NR==2 {print int($4)}')
    if [ "$DISK_SPACE" -lt 20 ]; then
        log_error "可用磁盘空间少于20GB，无法继续安装"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 安装系统依赖
install_system_deps() {
    log_info "安装系统依赖..."
    
    case $OS in
        "debian")
            sudo apt update
            sudo apt install -y curl wget git build-essential pkg-config libssl-dev
            ;;
        "redhat")
            sudo yum update -y
            sudo yum groupinstall -y "Development Tools"
            sudo yum install -y curl wget git openssl-devel
            ;;
        "macos")
            if ! command -v brew &> /dev/null; then
                log_info "安装Homebrew..."
                /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
            fi
            brew install curl wget git
            ;;
    esac
    
    log_success "系统依赖安装完成"
}

# 安装Rust
install_rust() {
    if command -v rustc &> /dev/null; then
        log_info "Rust已安装，版本: $(rustc --version)"
        return
    fi
    
    log_info "安装Rust..."
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
    source ~/.cargo/env
    
    # 验证安装
    if command -v rustc &> /dev/null; then
        log_success "Rust安装成功，版本: $(rustc --version)"
    else
        log_error "Rust安装失败"
        exit 1
    fi
}

# 安装Node.js
install_nodejs() {
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        if [ "$NODE_VERSION" -ge 18 ]; then
            log_info "Node.js已安装，版本: $(node --version)"
            return
        fi
    fi
    
    log_info "安装Node.js 18..."
    
    case $OS in
        "debian")
            curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
            sudo apt-get install -y nodejs
            ;;
        "redhat")
            curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
            sudo yum install -y nodejs
            ;;
        "macos")
            brew install node@18
            ;;
    esac
    
    # 验证安装
    if command -v node &> /dev/null; then
        log_success "Node.js安装成功，版本: $(node --version)"
    else
        log_error "Node.js安装失败"
        exit 1
    fi
}

# 安装PostgreSQL
install_postgresql() {
    if command -v psql &> /dev/null; then
        log_info "PostgreSQL已安装，版本: $(psql --version)"
        return
    fi
    
    log_info "安装PostgreSQL..."
    
    case $OS in
        "debian")
            sudo apt install -y postgresql postgresql-contrib
            sudo systemctl start postgresql
            sudo systemctl enable postgresql
            ;;
        "redhat")
            sudo yum install -y postgresql-server postgresql-contrib
            sudo postgresql-setup initdb
            sudo systemctl start postgresql
            sudo systemctl enable postgresql
            ;;
        "macos")
            brew install postgresql
            brew services start postgresql
            ;;
    esac
    
    log_success "PostgreSQL安装完成"
}

# 配置数据库
setup_database() {
    log_info "配置数据库..."
    
    # 生成随机密码
    DB_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    
    # 创建数据库用户和数据库
    sudo -u postgres psql << EOF
CREATE USER mes_user WITH PASSWORD '$DB_PASSWORD';
CREATE DATABASE mes_db OWNER mes_user;
GRANT ALL PRIVILEGES ON DATABASE mes_db TO mes_user;
\q
EOF
    
    # 创建.env文件
    cat > .env << EOF
DATABASE_URL=postgresql://mes_user:$DB_PASSWORD@localhost:5432/mes_db
JWT_SECRET=$(openssl rand -base64 64 | tr -d "=+/" | cut -c1-50)
RUST_LOG=info
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
EOF
    
    log_success "数据库配置完成"
    log_info "数据库密码已保存到.env文件中"
}

# 构建后端
build_backend() {
    log_info "构建后端应用..."
    
    # 加载环境变量
    source ~/.cargo/env
    
    # 构建应用
    cargo build --release
    
    # 运行数据库迁移
    log_info "运行数据库迁移..."
    cargo run --release
    
    log_success "后端构建完成"
}

# 安装前端依赖
setup_frontend() {
    log_info "安装前端依赖..."
    
    cd frontend
    npm install
    
    # 创建前端环境配置
    cat > .env.local << EOF
VITE_API_BASE_URL=http://localhost:8080/api
EOF
    
    cd ..
    log_success "前端依赖安装完成"
}

# 创建启动脚本
create_startup_scripts() {
    log_info "创建启动脚本..."
    
    # 后端启动脚本
    cat > start_backend.sh << 'EOF'
#!/bin/bash
source ~/.cargo/env
export $(cat .env | xargs)
./target/release/mes-system
EOF
    
    # 前端启动脚本（已存在，检查并更新）
    if [ ! -f "start_frontend.sh" ]; then
        cat > start_frontend.sh << 'EOF'
#!/bin/bash
cd frontend
npm run dev
EOF
    fi
    
    # 系统服务启动脚本
    cat > start_all.sh << 'EOF'
#!/bin/bash
echo "启动MES系统..."

# 启动后端
echo "启动后端服务..."
./start_backend.sh &
BACKEND_PID=$!

# 等待后端启动
sleep 5

# 启动前端
echo "启动前端服务..."
./start_frontend.sh &
FRONTEND_PID=$!

echo "MES系统启动完成!"
echo "后端API: http://localhost:9000"
echo "前端界面: http://localhost:3000"
echo "默认登录: admin/admin123"

# 等待用户输入停止
read -p "按Enter键停止服务..."

# 停止服务
kill $BACKEND_PID $FRONTEND_PID
echo "服务已停止"
EOF
    
    chmod +x start_backend.sh start_frontend.sh start_all.sh
    log_success "启动脚本创建完成"
}

# 验证安装
verify_installation() {
    log_info "验证安装..."
    
    # 检查后端可执行文件
    if [ -f "./target/release/mes-system" ]; then
        log_success "后端构建成功"
    else
        log_error "后端构建失败"
        return 1
    fi
    
    # 检查前端依赖
    if [ -d "./frontend/node_modules" ]; then
        log_success "前端依赖安装成功"
    else
        log_error "前端依赖安装失败"
        return 1
    fi
    
    # 检查数据库连接
    if psql -h localhost -U mes_user -d mes_db -c "SELECT 1;" > /dev/null 2>&1; then
        log_success "数据库连接正常"
    else
        log_warning "数据库连接测试失败，请检查配置"
    fi
    
    log_success "安装验证完成"
}

# 显示安装结果
show_results() {
    echo
    echo "=================================="
    echo "🎉 MES系统安装完成！"
    echo "=================================="
    echo
    echo "📋 安装信息:"
    echo "  - 后端API: http://localhost:8080"
    echo "  - 前端界面: http://localhost:3000"
    echo "  - 数据库: PostgreSQL (mes_db)"
    echo
    echo "🔑 默认登录信息:"
    echo "  - 用户名: admin"
    echo "  - 密码: admin123"
    echo
    echo "🚀 启动命令:"
    echo "  - 启动全部服务: ./start_all.sh"
    echo "  - 仅启动后端: ./start_backend.sh"
    echo "  - 仅启动前端: ./start_frontend.sh"
    echo
    echo "📚 更多信息请查看:"
    echo "  - 安装指南: INSTALLATION_GUIDE.md"
    echo "  - API文档: API_DOCUMENTATION.md"
    echo "  - 用户手册: README.md"
    echo
    echo "⚠️  重要提醒:"
    echo "  - 生产环境请修改默认密码"
    echo "  - 数据库密码保存在.env文件中"
    echo "  - 定期备份数据库数据"
    echo
}

# 主安装流程
main() {
    echo "=================================="
    echo "🏭 MES系统自动安装程序"
    echo "=================================="
    echo
    
    check_root
    detect_os
    check_requirements
    
    log_info "开始安装MES系统..."
    
    install_system_deps
    install_rust
    install_nodejs
    install_postgresql
    setup_database
    build_backend
    setup_frontend
    create_startup_scripts
    
    if verify_installation; then
        show_results
    else
        log_error "安装验证失败，请检查错误信息"
        exit 1
    fi
}

# 运行主程序
main "$@"
