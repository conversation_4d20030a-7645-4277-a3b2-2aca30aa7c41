# 班次管理系统修复计划

## 🎯 问题总结

在优化分支 `optimization-phase1` 中，我们尝试修复班次管理系统时遇到了复杂的类型兼容性问题：

### 主要问题

1. **BigDecimal版本冲突**
   - SQLx 0.7.4 使用 `sqlx::types::BigDecimal` (基于 bigdecimal 0.3.x)
   - 项目中使用 `bigdecimal 0.4.8`
   - 两个版本的API不兼容

2. **数据库字段类型不匹配**
   - 数据库中的字段有默认值，不是NULL
   - Rust模型中错误地定义为 `Option<T>` 类型
   - 导致 `sqlx::query_as!` 宏类型推断失败

3. **时间字段处理不一致**
   - 数据库使用 `TIMESTAMP` (无时区)
   - 部分代码使用 `DateTime<Utc>` (有时区)
   - 应该统一使用 `NaiveDateTime`

## 🔧 详细修复方案

### 阶段1：依赖版本统一

#### 1.1 更新 Cargo.toml
```toml
[dependencies]
# 移除直接的 bigdecimal 依赖，使用 sqlx 提供的版本
# bigdecimal = { version = "0.3.1", features = ["serde"] }  # 删除这行

# 确保 sqlx 版本正确
sqlx = { version = "0.7", features = [
    "runtime-tokio-rustls", 
    "postgres", 
    "chrono", 
    "uuid", 
    "bigdecimal",  # 使用 sqlx 内置的 bigdecimal 支持
    "json"
] }

# 如果需要额外的数值处理功能，添加 num-traits
num-traits = "0.2"
```

#### 1.2 更新导入语句
```rust
// src/models/shift.rs 和 src/services/shift_service.rs
use sqlx::types::BigDecimal;  // 使用 sqlx 提供的 BigDecimal
use num_traits::{ToPrimitive, FromPrimitive};  // 用于类型转换
```

### 阶段2：数据库模型修复

#### 2.1 修复 ShiftTemplate 模型
```rust
// src/models/shift.rs
#[derive(FromRow, Serialize, Deserialize, Debug, Clone)]
pub struct ShiftTemplate {
    pub id: i32,
    pub template_name: String,
    pub schedule_type: String,
    pub start_hour: i32,
    pub start_minute: i32,        // 非可选，数据库默认值0
    pub end_hour: i32,
    pub end_minute: i32,          // 非可选，数据库默认值0
    pub duration_hours: BigDecimal, // 使用 sqlx::types::BigDecimal
    pub description: Option<String>,
    pub is_active: bool,          // 非可选，数据库默认值true
    pub created_at: NaiveDateTime,  // 使用 NaiveDateTime
    pub updated_at: NaiveDateTime,  // 使用 NaiveDateTime
    pub created_by: i32,
}
```

#### 2.2 修复 PlanGroup 模型
```rust
#[derive(FromRow, Serialize, Deserialize, Debug, Clone)]
pub struct PlanGroup {
    pub id: i32,
    pub group_name: String,
    pub group_code: String,
    pub description: Option<String>,
    pub priority: i32,            // 非可选，数据库默认值0
    pub is_active: bool,          // 非可选，数据库默认值true
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
    pub created_by: i32,
}
```

#### 2.3 修复 ShiftInstance 模型
```rust
#[derive(FromRow, Serialize, Deserialize, Debug, Clone)]
pub struct ShiftInstance {
    pub id: i32,
    pub template_id: i32,
    pub plan_group_id: i32,
    pub shift_date: NaiveDate,
    pub actual_start_time: Option<NaiveDateTime>,
    pub actual_end_time: Option<NaiveDateTime>,
    pub planned_start_time: NaiveDateTime,
    pub planned_end_time: NaiveDateTime,
    pub status: String,
    pub notes: Option<String>,
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
    pub created_by: i32,
}
```

### 阶段3：服务层修复

#### 3.1 修复 BigDecimal 相关方法
```rust
// src/services/shift_service.rs
use sqlx::types::BigDecimal;
use num_traits::{ToPrimitive, FromPrimitive};

impl ShiftService {
    fn calculate_shift_duration(&self, start_hour: i32, start_minute: i32, end_hour: i32, end_minute: i32) -> BigDecimal {
        let start_minutes = start_hour as f64 * 60.0 + start_minute as f64;
        let end_minutes = end_hour as f64 * 60.0 + end_minute as f64;
        
        let duration_hours = if end_minutes > start_minutes {
            (end_minutes - start_minutes) / 60.0
        } else {
            (24.0 * 60.0 - start_minutes + end_minutes) / 60.0
        };
        
        // 使用 num-traits 进行类型转换
        BigDecimal::from_f64(duration_hours).unwrap_or_else(|| BigDecimal::from(0))
    }
    
    // 修复 BigDecimal 到 f64 的转换
    fn bigdecimal_to_f64(&self, value: &BigDecimal) -> f64 {
        value.to_f64().unwrap_or(0.0)
    }
}
```

#### 3.2 修复数据库查询
```rust
// 使用显式类型注解避免类型推断问题
pub async fn create_shift_template(&self, request: CreateShiftTemplateRequest) -> Result<ShiftTemplate, String> {
    let duration = self.calculate_shift_duration(
        request.start_hour,
        request.start_minute.unwrap_or(0),
        request.end_hour,
        request.end_minute.unwrap_or(0),
    );

    let shift_template = sqlx::query_as!(
        ShiftTemplate,
        r#"
        INSERT INTO shift_templates (
            template_name, schedule_type, start_hour, start_minute,
            end_hour, end_minute, duration_hours, description,
            is_active, created_by
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING *
        "#,
        request.template_name,
        request.schedule_type,
        request.start_hour,
        request.start_minute.unwrap_or(0),
        request.end_hour,
        request.end_minute.unwrap_or(0),
        duration,
        request.description,
        true, // is_active 默认为 true
        request.created_by
    )
    .fetch_one(&self.pool)
    .await
    .map_err(|e| format!("创建班次模板失败: {}", e))?;

    Ok(shift_template)
}
```

### 阶段4：请求/响应模型修复

#### 4.1 修复请求模型
```rust
// 确保请求模型与数据库字段类型匹配
#[derive(Deserialize, Debug)]
pub struct CreateShiftTemplateRequest {
    pub template_name: String,
    pub schedule_type: String,
    pub start_hour: i32,
    pub start_minute: Option<i32>,  // 请求中可选，服务层处理默认值
    pub end_hour: i32,
    pub end_minute: Option<i32>,    // 请求中可选，服务层处理默认值
    pub description: Option<String>,
    pub created_by: i32,
}
```

### 阶段5：测试和验证

#### 5.1 单元测试
```rust
#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_calculate_shift_duration() {
        let service = ShiftService::new(/* mock pool */);
        
        // 测试正常班次 (8:00 - 17:00)
        let duration = service.calculate_shift_duration(8, 0, 17, 0);
        assert_eq!(duration.to_f64().unwrap(), 9.0);
        
        // 测试跨天班次 (22:00 - 06:00)
        let duration = service.calculate_shift_duration(22, 0, 6, 0);
        assert_eq!(duration.to_f64().unwrap(), 8.0);
    }
}
```

#### 5.2 集成测试
```rust
#[tokio::test]
async fn test_create_shift_template() {
    let pool = setup_test_db().await;
    let service = ShiftService::new(pool);
    
    let request = CreateShiftTemplateRequest {
        template_name: "早班".to_string(),
        schedule_type: "fixed".to_string(),
        start_hour: 8,
        start_minute: Some(0),
        end_hour: 17,
        end_minute: Some(0),
        description: Some("标准早班".to_string()),
        created_by: 1,
    };
    
    let result = service.create_shift_template(request).await;
    assert!(result.is_ok());
}
```

## 📋 实施步骤

### 第1步：创建专门的修复分支
```bash
git checkout -b fix-shift-management
```

### 第2步：按阶段实施修复
1. 更新依赖和导入
2. 修复数据库模型
3. 修复服务层逻辑
4. 修复请求/响应模型
5. 编写和运行测试

### 第3步：验证修复
```bash
# 编译检查
cargo check

# 运行测试
cargo test shift

# 启动服务验证
cargo run
```

### 第4步：合并到优化分支
```bash
git checkout optimization-phase1
git merge fix-shift-management
```

## 🚨 注意事项

1. **数据库兼容性**：确保修复不会破坏现有数据
2. **API兼容性**：保持前端API接口不变
3. **性能影响**：BigDecimal操作比f64慢，但精度更高
4. **测试覆盖**：确保所有修复都有对应的测试

## 📊 预期结果

修复完成后，班次管理系统应该能够：
- ✅ 正常编译，无类型错误
- ✅ 创建和管理班次模板
- ✅ 创建和管理计划组
- ✅ 生成班次实例
- ✅ 检测班次冲突
- ✅ 支持班次感知的任务调度

## 🎯 成功指标

- [ ] 所有班次相关代码编译通过
- [ ] 单元测试覆盖率 > 80%
- [ ] 集成测试全部通过
- [ ] API接口正常响应
- [ ] 前端界面正常显示班次数据
