[Unit]
Description=MES Manufacturing Execution System
Documentation=https://github.com/your-org/mes-system
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=simple
User=mes
Group=mes
WorkingDirectory=/opt/mes-system
ExecStart=/opt/mes-system/target/release/mes-system
ExecReload=/bin/kill -HUP $MAINPID
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30
Restart=always
RestartSec=5

# 环境变量
Environment=RUST_LOG=info
Environment=MES_PORT=9001
Environment=MES_HOST=0.0.0.0
EnvironmentFile=-/opt/mes-system/.env

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/mes-system/logs
ReadOnlyPaths=/opt/mes-system

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 日志设置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=mes-system

[Install]
WantedBy=multi-user.target
