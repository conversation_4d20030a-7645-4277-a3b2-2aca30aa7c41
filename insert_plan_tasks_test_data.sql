-- 插入计划任务测试数据
-- 用于测试甘特图显示

-- 首先检查并插入基础数据（如果不存在）

-- 插入技能组（如果不存在）
INSERT INTO skill_groups (group_name, description) VALUES
('CNC加工组', 'CNC机床操作和编程')
ON CONFLICT (group_name) DO NOTHING;

INSERT INTO skill_groups (group_name, description) VALUES
('装配组', '产品装配和调试')
ON CONFLICT (group_name) DO NOTHING;

INSERT INTO skill_groups (group_name, description) VALUES
('质检组', '质量检验和测试')
ON CONFLICT (group_name) DO NOTHING;

-- 插入设备（如果不存在）
INSERT INTO machines (machine_name, machine_type, status, skill_group_id) VALUES
('CNC-001', 'CNC铣床', 'active', (SELECT id FROM skill_groups WHERE group_name = 'CNC加工组' LIMIT 1))
ON CONFLICT (machine_name) DO NOTHING;

INSERT INTO machines (machine_name, machine_type, status, skill_group_id) VALUES
('CNC-002', 'CNC车床', 'active', (SELECT id FROM skill_groups WHERE group_name = 'CNC加工组' LIMIT 1))
ON CONFLICT (machine_name) DO NOTHING;

INSERT INTO machines (machine_name, machine_type, status, skill_group_id) VALUES
('ASM-001', '装配台', 'active', (SELECT id FROM skill_groups WHERE group_name = '装配组' LIMIT 1))
ON CONFLICT (machine_name) DO NOTHING;

-- 插入项目（如果不存在）
INSERT INTO projects (project_name, customer_name, description, status, start_date, due_date) VALUES
('测试项目A', '客户A', '用于测试甘特图的项目', 'active', CURRENT_DATE, CURRENT_DATE + INTERVAL '30 days')
ON CONFLICT (project_name) DO NOTHING;

-- 插入零件（如果不存在）
INSERT INTO parts (part_number, part_name, version, description) VALUES
('P001', '测试零件1', 'v1.0', '用于测试的零件1')
ON CONFLICT (part_number, version) DO NOTHING;

INSERT INTO parts (part_number, part_name, version, description) VALUES
('P002', '测试零件2', 'v1.0', '用于测试的零件2')
ON CONFLICT (part_number, version) DO NOTHING;

-- 插入项目BOM（如果不存在）
INSERT INTO project_boms (project_id, part_id, quantity, unit_cost) VALUES
((SELECT id FROM projects WHERE project_name = '测试项目A' LIMIT 1),
 (SELECT id FROM parts WHERE part_number = 'P001' LIMIT 1),
 10, 100.00)
ON CONFLICT (project_id, part_id) DO NOTHING;

INSERT INTO project_boms (project_id, part_id, quantity, unit_cost) VALUES
((SELECT id FROM projects WHERE project_name = '测试项目A' LIMIT 1),
 (SELECT id FROM parts WHERE part_number = 'P002' LIMIT 1),
 5, 200.00)
ON CONFLICT (project_id, part_id) DO NOTHING;

-- 插入工艺路线（如果不存在）
INSERT INTO routings (part_id, step_number, process_name, work_instructions, standard_hours, skill_group_id) VALUES
((SELECT id FROM parts WHERE part_number = 'P001' LIMIT 1), 1, 'CNC铣削', '使用CNC铣床进行粗加工', 4.0, 
 (SELECT id FROM skill_groups WHERE group_name = 'CNC加工组' LIMIT 1))
ON CONFLICT (part_id, step_number) DO NOTHING;

INSERT INTO routings (part_id, step_number, process_name, work_instructions, standard_hours, skill_group_id) VALUES
((SELECT id FROM parts WHERE part_number = 'P001' LIMIT 1), 2, 'CNC精加工', '使用CNC铣床进行精加工', 2.0,
 (SELECT id FROM skill_groups WHERE group_name = 'CNC加工组' LIMIT 1))
ON CONFLICT (part_id, step_number) DO NOTHING;

INSERT INTO routings (part_id, step_number, process_name, work_instructions, standard_hours, skill_group_id) VALUES
((SELECT id FROM parts WHERE part_number = 'P001' LIMIT 1), 3, '质量检验', '进行尺寸和外观检验', 1.0,
 (SELECT id FROM skill_groups WHERE group_name = '质检组' LIMIT 1))
ON CONFLICT (part_id, step_number) DO NOTHING;

INSERT INTO routings (part_id, step_number, process_name, work_instructions, standard_hours, skill_group_id) VALUES
((SELECT id FROM parts WHERE part_number = 'P002' LIMIT 1), 1, 'CNC车削', '使用CNC车床进行加工', 3.0,
 (SELECT id FROM skill_groups WHERE group_name = 'CNC加工组' LIMIT 1))
ON CONFLICT (part_id, step_number) DO NOTHING;

INSERT INTO routings (part_id, step_number, process_name, work_instructions, standard_hours, skill_group_id) VALUES
((SELECT id FROM parts WHERE part_number = 'P002' LIMIT 1), 2, '装配', '进行产品装配', 2.0,
 (SELECT id FROM skill_groups WHERE group_name = '装配组' LIMIT 1))
ON CONFLICT (part_id, step_number) DO NOTHING;

-- 插入工单（如果不存在）
INSERT INTO work_orders (project_bom_id, quantity, status, due_date, priority) VALUES
((SELECT id FROM project_boms WHERE project_id = (SELECT id FROM projects WHERE project_name = '测试项目A' LIMIT 1) 
  AND part_id = (SELECT id FROM parts WHERE part_number = 'P001' LIMIT 1) LIMIT 1),
 10, 'active', CURRENT_DATE + INTERVAL '15 days', 'high')
ON CONFLICT DO NOTHING;

INSERT INTO work_orders (project_bom_id, quantity, status, due_date, priority) VALUES
((SELECT id FROM project_boms WHERE project_id = (SELECT id FROM projects WHERE project_name = '测试项目A' LIMIT 1) 
  AND part_id = (SELECT id FROM parts WHERE part_number = 'P002' LIMIT 1) LIMIT 1),
 5, 'active', CURRENT_DATE + INTERVAL '20 days', 'medium')
ON CONFLICT DO NOTHING;

-- 插入计划任务
-- 为P001零件的工艺路线创建计划任务
INSERT INTO plan_tasks (work_order_id, routing_step_id, skill_group_id, machine_id, planned_start, planned_end, status) VALUES
-- P001 - CNC铣削
((SELECT id FROM work_orders WHERE project_bom_id = 
  (SELECT id FROM project_boms WHERE part_id = (SELECT id FROM parts WHERE part_number = 'P001' LIMIT 1) LIMIT 1) LIMIT 1),
 (SELECT id FROM routings WHERE part_id = (SELECT id FROM parts WHERE part_number = 'P001' LIMIT 1) AND step_number = 1 LIMIT 1),
 (SELECT id FROM skill_groups WHERE group_name = 'CNC加工组' LIMIT 1),
 (SELECT id FROM machines WHERE machine_name = 'CNC-001' LIMIT 1),
 CURRENT_TIMESTAMP,
 CURRENT_TIMESTAMP + INTERVAL '4 hours',
 'pending'),

-- P001 - CNC精加工
((SELECT id FROM work_orders WHERE project_bom_id = 
  (SELECT id FROM project_boms WHERE part_id = (SELECT id FROM parts WHERE part_number = 'P001' LIMIT 1) LIMIT 1) LIMIT 1),
 (SELECT id FROM routings WHERE part_id = (SELECT id FROM parts WHERE part_number = 'P001' LIMIT 1) AND step_number = 2 LIMIT 1),
 (SELECT id FROM skill_groups WHERE group_name = 'CNC加工组' LIMIT 1),
 (SELECT id FROM machines WHERE machine_name = 'CNC-002' LIMIT 1),
 CURRENT_TIMESTAMP + INTERVAL '4 hours',
 CURRENT_TIMESTAMP + INTERVAL '6 hours',
 'pending'),

-- P001 - 质量检验
((SELECT id FROM work_orders WHERE project_bom_id = 
  (SELECT id FROM project_boms WHERE part_id = (SELECT id FROM parts WHERE part_number = 'P001' LIMIT 1) LIMIT 1) LIMIT 1),
 (SELECT id FROM routings WHERE part_id = (SELECT id FROM parts WHERE part_number = 'P001' LIMIT 1) AND step_number = 3 LIMIT 1),
 (SELECT id FROM skill_groups WHERE group_name = '质检组' LIMIT 1),
 NULL,
 CURRENT_TIMESTAMP + INTERVAL '6 hours',
 CURRENT_TIMESTAMP + INTERVAL '7 hours',
 'pending'),

-- P002 - CNC车削
((SELECT id FROM work_orders WHERE project_bom_id = 
  (SELECT id FROM project_boms WHERE part_id = (SELECT id FROM parts WHERE part_number = 'P002' LIMIT 1) LIMIT 1) LIMIT 1),
 (SELECT id FROM routings WHERE part_id = (SELECT id FROM parts WHERE part_number = 'P002' LIMIT 1) AND step_number = 1 LIMIT 1),
 (SELECT id FROM skill_groups WHERE group_name = 'CNC加工组' LIMIT 1),
 (SELECT id FROM machines WHERE machine_name = 'CNC-002' LIMIT 1),
 CURRENT_TIMESTAMP + INTERVAL '1 day',
 CURRENT_TIMESTAMP + INTERVAL '1 day 3 hours',
 'pending'),

-- P002 - 装配
((SELECT id FROM work_orders WHERE project_bom_id = 
  (SELECT id FROM project_boms WHERE part_id = (SELECT id FROM parts WHERE part_number = 'P002' LIMIT 1) LIMIT 1) LIMIT 1),
 (SELECT id FROM routings WHERE part_id = (SELECT id FROM parts WHERE part_number = 'P002' LIMIT 1) AND step_number = 2 LIMIT 1),
 (SELECT id FROM skill_groups WHERE group_name = '装配组' LIMIT 1),
 (SELECT id FROM machines WHERE machine_name = 'ASM-001' LIMIT 1),
 CURRENT_TIMESTAMP + INTERVAL '1 day 3 hours',
 CURRENT_TIMESTAMP + INTERVAL '1 day 5 hours',
 'pending');

-- 查询插入的数据进行验证
SELECT 
    pt.id,
    pt.planned_start,
    pt.planned_end,
    pt.status,
    r.process_name,
    p.part_number,
    sg.group_name as skill_group_name,
    m.machine_name
FROM plan_tasks pt
JOIN routings r ON pt.routing_step_id = r.id
JOIN parts p ON r.part_id = p.id
JOIN skill_groups sg ON pt.skill_group_id = sg.id
LEFT JOIN machines m ON pt.machine_id = m.id
ORDER BY pt.planned_start;
