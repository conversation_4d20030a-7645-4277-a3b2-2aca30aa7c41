#!/usr/bin/env python3
"""
测试工艺员登录的不同密码
"""

import requests
import json

BASE_URL = "http://localhost:9001/api"

def test_login(username, password):
    """测试登录"""
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json={
            "username": username,
            "password": password
        })
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 登录成功: {username} / {password}")
            print(f"   Token: {data['token'][:50]}...")
            return data['token']
        else:
            print(f"❌ 登录失败: {username} / {password} - {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 登录异常: {username} / {password} - {e}")
        return None

def main():
    print("🔍 测试工艺员登录...")
    
    username = "engineer1"
    passwords = [
        "gongyi",
        "123456",
        "password",
        "engineer1",
        "admin123",
        "gongyi123",
        "engineer",
        "123"
    ]
    
    for password in passwords:
        token = test_login(username, password)
        if token:
            # 验证token有效性
            try:
                me_response = requests.get(f"{BASE_URL}/auth/me", 
                                         headers={"Authorization": f"Bearer {token}"})
                if me_response.status_code == 200:
                    user_info = me_response.json()
                    print(f"   用户信息: {user_info['username']} - 角色: {user_info['roles']}")
                    return token
            except Exception as e:
                print(f"   Token验证失败: {e}")
    
    print("\n❌ 所有密码都尝试失败")
    return None

if __name__ == "__main__":
    main()
