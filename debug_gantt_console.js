// 甘特图调试脚本 - 在浏览器控制台中运行

console.log('🔍 开始甘特图调试...');

// 1. 检查React Query缓存
function checkReactQueryCache() {
    console.log('\n📊 检查React Query缓存:');
    
    // 尝试访问React Query的缓存
    const queryClient = window.__REACT_QUERY_CLIENT__;
    if (queryClient) {
        const cache = queryClient.getQueryCache();
        const queries = cache.getAll();
        
        console.log(`总查询数: ${queries.length}`);
        
        queries.forEach(query => {
            if (query.queryKey.includes('plan-tasks') || query.queryKey.includes('gantt')) {
                console.log(`查询: ${JSON.stringify(query.queryKey)}`);
                console.log(`状态: ${query.state.status}`);
                console.log(`数据:`, query.state.data);
                console.log(`错误:`, query.state.error);
                console.log('---');
            }
        });
    } else {
        console.log('❌ 无法访问React Query客户端');
    }
}

// 2. 检查甘特图组件DOM
function checkGanttDOM() {
    console.log('\n🎨 检查甘特图DOM:');
    
    // 查找甘特图相关元素
    const ganttElements = document.querySelectorAll('[class*="gantt"], [id*="gantt"]');
    console.log(`甘特图元素数量: ${ganttElements.length}`);
    
    ganttElements.forEach((el, index) => {
        console.log(`元素 ${index + 1}:`, el);
        console.log(`类名: ${el.className}`);
        console.log(`内容长度: ${el.textContent.length}`);
    });
    
    // 查找任务相关元素
    const taskElements = document.querySelectorAll('[class*="task"], [data-task-id]');
    console.log(`任务元素数量: ${taskElements.length}`);
    
    // 查找空闲状态元素
    const idleElements = document.querySelectorAll('*');
    let idleCount = 0;
    idleElements.forEach(el => {
        if (el.textContent && el.textContent.includes('空闲')) {
            idleCount++;
        }
    });
    console.log(`包含"空闲"的元素数量: ${idleCount}`);
}

// 3. 检查网络请求
function checkNetworkRequests() {
    console.log('\n🌐 检查网络请求:');
    
    // 监听fetch请求
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        const url = args[0];
        if (typeof url === 'string' && (url.includes('plan-tasks') || url.includes('gantt'))) {
            console.log(`📡 API请求: ${url}`);
        }
        return originalFetch.apply(this, args).then(response => {
            if (typeof url === 'string' && (url.includes('plan-tasks') || url.includes('gantt'))) {
                console.log(`📡 API响应: ${url} - ${response.status}`);
                if (response.ok) {
                    response.clone().json().then(data => {
                        console.log(`📡 API数据:`, data);
                    }).catch(() => {
                        console.log(`📡 API数据: 非JSON格式`);
                    });
                }
            }
            return response;
        });
    };
    
    console.log('✅ 网络请求监听已启用');
}

// 4. 检查localStorage和sessionStorage
function checkStorage() {
    console.log('\n💾 检查存储:');
    
    console.log('localStorage:');
    for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key.includes('token') || key.includes('auth') || key.includes('user')) {
            console.log(`${key}: ${localStorage.getItem(key).substring(0, 50)}...`);
        }
    }
    
    console.log('sessionStorage:');
    for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        console.log(`${key}: ${sessionStorage.getItem(key).substring(0, 50)}...`);
    }
}

// 5. 检查错误
function checkErrors() {
    console.log('\n❌ 检查错误:');
    
    // 监听错误
    window.addEventListener('error', (event) => {
        console.log('JavaScript错误:', event.error);
    });
    
    window.addEventListener('unhandledrejection', (event) => {
        console.log('未处理的Promise拒绝:', event.reason);
    });
    
    console.log('✅ 错误监听已启用');
}

// 6. 模拟甘特图数据处理
async function simulateGanttProcessing() {
    console.log('\n🔧 模拟甘特图数据处理:');
    
    try {
        // 获取token
        const token = localStorage.getItem('token');
        if (!token) {
            console.log('❌ 未找到认证token');
            return;
        }
        
        // 获取计划任务数据
        const response = await fetch('http://localhost:9000/api/plan-tasks', {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            console.log(`❌ API请求失败: ${response.status}`);
            return;
        }
        
        const data = await response.json();
        const tasks = data.plan_tasks || [];
        
        console.log(`📊 获取到 ${tasks.length} 个计划任务`);
        
        if (tasks.length > 0) {
            console.log('第一个任务:', tasks[0]);
            
            // 模拟甘特图组件的数据转换
            const ganttTask = {
                id: tasks[0].id,
                name: `${tasks[0].process_name} - ${tasks[0].part_number}`,
                startDate: new Date(tasks[0].planned_start),
                endDate: new Date(tasks[0].planned_end),
                status: tasks[0].status,
                progress: 0,
                assignee: tasks[0].skill_group_name,
            };
            
            console.log('转换后的甘特图任务:', ganttTask);
            console.log('开始时间有效:', !isNaN(ganttTask.startDate.getTime()));
            console.log('结束时间有效:', !isNaN(ganttTask.endDate.getTime()));
        }
        
    } catch (error) {
        console.log('❌ 模拟处理失败:', error);
    }
}

// 执行所有检查
async function runAllChecks() {
    console.log('🚀 执行所有检查...\n');
    
    checkStorage();
    checkGanttDOM();
    checkNetworkRequests();
    checkErrors();
    
    // 延迟执行需要网络请求的检查
    setTimeout(() => {
        checkReactQueryCache();
        simulateGanttProcessing();
    }, 2000);
}

// 提供手动执行函数
window.debugGantt = {
    checkReactQueryCache,
    checkGanttDOM,
    checkNetworkRequests,
    checkStorage,
    checkErrors,
    simulateGanttProcessing,
    runAllChecks
};

console.log('✅ 甘特图调试工具已加载');
console.log('使用方法:');
console.log('- debugGantt.runAllChecks() - 执行所有检查');
console.log('- debugGantt.checkGanttDOM() - 检查DOM');
console.log('- debugGantt.simulateGanttProcessing() - 模拟数据处理');

// 自动执行检查
runAllChecks();
