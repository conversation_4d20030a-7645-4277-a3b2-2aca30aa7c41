# MES系统一键管理脚本使用指南

## 概述

MES系统提供了多个管理脚本，用于不同场景下的启动和管理需求：

1. **mes-launcher.sh** - 完整的系统管理工具（主要脚本）
2. **mes-quick-manager.sh** - 快速启动工具（专用于前后端组合）
3. **mes-process-manager.sh** - 进程管理工具（后台服务管理）
4. **start-dev-background.sh** - 开发模式后台启动

## 脚本功能对比

| 脚本 | 主要用途 | 特点 |
|------|----------|------|
| mes-launcher.sh | 全功能管理 | 包含所有功能，适合完整管理 |
| mes-quick-manager.sh | 快速启动 | 专注于前后端组合模式 |
| mes-process-manager.sh | 进程管理 | 后台服务的启停管理 |
| start-dev-background.sh | 开发模式 | 开发环境后台运行 |

## 快速启动指南

### 1. 使用快速管理工具（推荐）

```bash
# 启动快速管理工具
./mes-quick-manager.sh

# 或查看帮助
./mes-quick-manager.sh --help

# 快速查看状态
./mes-quick-manager.sh --status

# 快速停止所有服务
./mes-quick-manager.sh --stop
```

**快速管理工具提供的组合模式：**

- **生产模式组合**
  - 后端生产 + 前端生产（前台/后台）
  
- **开发模式组合**
  - 后端开发 + 前端开发（前台/后台）
  
- **混合模式组合**
  - 后端生产 + 前端开发（前台/后台）
  - 后端开发 + 前端生产（前台/后台）

### 2. 使用完整管理工具

```bash
# 启动完整管理工具
./mes-launcher.sh

# 查看系统状态
./mes-launcher.sh --status

# 停止所有服务
./mes-launcher.sh --stop
```

## 常用启动组合

### 开发场景

1. **纯开发模式**（推荐开发时使用）
   - 后端：cargo run（热重载）
   - 前端：vite dev（热重载）
   - 选择：快速管理工具选项3或4

2. **后端生产 + 前端开发**（测试生产后端）
   - 后端：预编译二进制（稳定）
   - 前端：vite dev（可调试）
   - 选择：快速管理工具选项5或6

3. **后端开发 + 前端生产**（测试前端构建）
   - 后端：cargo run（可调试）
   - 前端：静态文件服务（测试构建结果）
   - 选择：快速管理工具选项7或8

### 生产场景

1. **完整生产模式**
   - 后端：预编译二进制
   - 前端：静态文件服务
   - 选择：快速管理工具选项1或2

## 端口配置

默认端口配置：
- 后端服务：9001
- 前端生产：3080
- 前端开发：3000

### 修改端口

1. **通过环境变量**：
```bash
export MES_PORT=9002
export FRONTEND_PORT=3081
export VITE_PORT=3001
```

2. **通过配置文件**：
创建 `.env.ports` 文件：
```bash
export MES_PORT=9002
export FRONTEND_PORT=3081
export VITE_PORT=3001
```

3. **通过管理工具**：
使用 `mes-launcher.sh` 选项21进行交互式配置

## 日志管理

### 日志文件位置
- 后端生产：`logs/backend.log`
- 前端生产：`logs/frontend.log`
- 后端开发：`logs/backend-dev.log`
- 前端开发：`logs/frontend-dev.log`

### 查看日志
```bash
# 使用快速管理工具查看
./mes-quick-manager.sh
# 选择选项11

# 或直接查看文件
tail -f logs/backend.log
tail -f logs/frontend-dev.log
```

## 进程管理

### PID文件位置
- 后端生产：`logs/backend.pid`
- 前端生产：`logs/frontend.pid`
- 后端开发：`logs/backend-dev.pid`
- 前端开发：`logs/frontend-dev.pid`

### 手动进程管理
```bash
# 使用进程管理器
./mes-process-manager.sh status
./mes-process-manager.sh start-backend 9001
./mes-process-manager.sh start-frontend 3080
./mes-process-manager.sh stop-all

# 使用开发模式管理器
./start-dev-background.sh status
./start-dev-background.sh start
./start-dev-background.sh stop
```

## 故障排除

### 常见问题

1. **端口被占用**
```bash
# 查看端口占用
netstat -tlnp | grep -E ":9001|:3080|:3000"

# 停止所有服务
./mes-quick-manager.sh --stop
```

2. **构建文件缺失**
```bash
# 使用快速管理工具自动检查和构建
./mes-quick-manager.sh
# 选择选项12进行构建
```

3. **进程残留**
```bash
# 清理所有相关进程
pkill -f mes-system
pkill -f vite
pkill -f "npm.*dev"
pkill -f "python.*http.server"
```

4. **权限问题**
```bash
# 添加执行权限
chmod +x *.sh
chmod +x frontend/*.sh
```

### 重置环境
```bash
# 停止所有服务
./mes-quick-manager.sh --stop

# 清理构建缓存
cargo clean
cd frontend && rm -rf node_modules/.vite dist && cd ..

# 重新构建
./mes-quick-manager.sh
# 选择选项12
```

## 最佳实践

1. **开发时**：使用快速管理工具的开发模式组合（选项3或4）
2. **测试时**：使用混合模式验证不同组合
3. **生产时**：使用生产模式组合（选项1或2）
4. **调试时**：使用前台模式便于查看实时日志
5. **部署时**：使用后台模式保持服务运行

## 脚本更新

如需更新脚本功能，主要文件：
- `mes-launcher.sh` - 主管理脚本
- `mes-quick-manager.sh` - 快速管理脚本
- `mes-process-manager.sh` - 进程管理脚本

建议在修改前备份原文件。
