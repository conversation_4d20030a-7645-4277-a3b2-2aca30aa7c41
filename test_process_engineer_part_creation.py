#!/usr/bin/env python3
"""
测试工艺员角色添加零件功能
"""

import requests
import json
import sys
import time

# 配置
BASE_URL = "http://localhost:9001/api"

def login_as_process_engineer():
    """以工艺员身份登录"""
    print("🔍 尝试以工艺员身份登录...")
    
    # 首先获取所有用户，找到工艺员用户
    try:
        admin_login = requests.post(f"{BASE_URL}/auth/login", json={
            "username": "admin",
            "password": "admin123"
        })
        
        if admin_login.status_code != 200:
            print("❌ 管理员登录失败")
            return None
            
        admin_token = admin_login.json()["token"]
        
        # 获取用户列表
        users_response = requests.get(f"{BASE_URL}/users", 
                                    headers={"Authorization": f"Bearer {admin_token}"})
        
        if users_response.status_code != 200:
            print("❌ 获取用户列表失败")
            return None
            
        users = users_response.json()["users"]
        
        # 查找工艺员用户
        process_engineer_user = None
        for user in users:
            if "process_engineer" in user.get("roles", []):
                process_engineer_user = user
                break
        
        if not process_engineer_user:
            print("❌ 未找到工艺员用户")
            return None
            
        print(f"✅ 找到工艺员用户: {process_engineer_user['username']}")
        
        # 尝试以工艺员身份登录
        pe_login = requests.post(f"{BASE_URL}/auth/login", json={
            "username": process_engineer_user["username"],
            "password": "gongyi"  # 用户创建的工艺员密码
        })
        
        if pe_login.status_code == 200:
            token = pe_login.json()["token"]
            print(f"✅ 工艺员登录成功")
            return token
        else:
            print(f"❌ 工艺员登录失败: {pe_login.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 登录过程出错: {e}")
        return None

def test_part_creation(token):
    """测试零件创建"""
    print("\n🔍 测试零件创建...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 测试数据
    part_data = {
        "part_number": f"TEST-{int(time.time())}",
        "part_name": "测试零件",
        "version": "1.0",
        "specifications": "测试规格说明"
    }
    
    print(f"创建零件数据: {json.dumps(part_data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(f"{BASE_URL}/parts", 
                               headers=headers, 
                               json=part_data)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200 or response.status_code == 201:
            result = response.json()
            print(f"✅ 零件创建成功: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return True
        else:
            print(f"❌ 零件创建失败")
            print(f"错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_user_permissions(token):
    """测试用户权限"""
    print("\n🔍 检查用户权限...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # 获取当前用户信息
        me_response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
        
        if me_response.status_code == 200:
            user_info = me_response.json()
            print(f"当前用户信息:")
            print(f"  用户名: {user_info.get('username')}")
            print(f"  角色: {user_info.get('roles', [])}")
            print(f"  技能: {user_info.get('skills', [])}")
            
            # 检查是否有process_engineer角色
            if "process_engineer" in user_info.get("roles", []):
                print("✅ 用户具有工艺员角色")
                return True
            else:
                print("❌ 用户没有工艺员角色")
                return False
        else:
            print(f"❌ 获取用户信息失败: {me_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 权限检查异常: {e}")
        return False

def test_parts_api_access(token):
    """测试零件API访问权限"""
    print("\n🔍 测试零件API访问权限...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # 测试获取零件列表
        response = requests.get(f"{BASE_URL}/parts", headers=headers)
        
        print(f"GET /api/parts 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            parts_data = response.json()
            print(f"✅ 可以访问零件列表 (共 {parts_data.get('data', {}).get('total_count', 0)} 个零件)")
            return True
        elif response.status_code == 403:
            print("❌ 没有访问零件列表的权限")
            return False
        else:
            print(f"❌ 访问零件列表失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API访问测试异常: {e}")
        return False

def check_backend_logs():
    """检查后端日志"""
    print("\n🔍 检查后端日志...")
    print("请查看后端控制台输出，寻找相关错误信息")

def main():
    print("🚀 开始测试工艺员角色添加零件功能...\n")
    
    # 1. 登录
    token = login_as_process_engineer()
    if not token:
        print("\n❌ 无法获取工艺员token，测试终止")
        return 1
    
    # 2. 检查用户权限
    if not test_user_permissions(token):
        print("\n❌ 用户权限检查失败")
        return 1
    
    # 3. 测试API访问权限
    if not test_parts_api_access(token):
        print("\n❌ API访问权限检查失败")
        return 1
    
    # 4. 测试零件创建
    if test_part_creation(token):
        print("\n✅ 零件创建测试成功！")
        return 0
    else:
        print("\n❌ 零件创建测试失败！")
        check_backend_logs()
        return 1

if __name__ == "__main__":
    sys.exit(main())
