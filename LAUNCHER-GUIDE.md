# MES系统统一启动器使用指南

## 🚀 快速开始

### 启动统一启动器
```bash
./mes-launcher.sh
```

这将打开交互式菜单，提供所有常用功能。

### 命令行选项
```bash
./mes-launcher.sh --help      # 显示帮助
./mes-launcher.sh --status    # 显示系统状态
./mes-launcher.sh --stop      # 停止所有服务
```

## 📋 主要功能

### 🚀 快速启动
1. **完整系统启动 (前台)** - 同时启动前后端，前台运行
2. **完整系统启动 (后台)** - 同时启动前后端，后台运行 (推荐)
3. **仅启动后端 (前台)** - 只启动后端服务，前台运行
4. **仅启动后端 (后台)** - 只启动后端服务，后台运行
5. **仅启动前端 (前台)** - 只启动前端服务，前台运行
6. **仅启动前端 (后台)** - 只启动前端服务，后台运行

### 🔧 开发模式
7. **后端开发模式** - 使用 `cargo run` 启动
8. **前端开发模式** - 使用 `vite dev` 启动 (端口3000)
9. **同时启动开发模式** - 前后端开发环境

### 📦 构建管理
10. **构建后端** - Release模式构建
11. **构建前端** - Production模式构建
12. **构建完整系统** - 前后端一起构建

### 🐳 Docker部署
13. **Docker构建** - 构建Docker镜像
14. **Docker运行** - 运行Docker容器
15. **Docker完整部署** - 构建并运行

### 🛠️ 系统管理
16. **停止所有服务** - 安全停止所有MES相关进程
17. **系统状态检查** - 检查构建状态和运行状态
18. **清理缓存** - 清理Rust和前端缓存
19. **生产环境安装** - 安装到生产服务器
20. **端口配置** - 配置服务端口 (避免受限端口)

### 📚 帮助信息
21. **查看文档** - 显示文档列表
22. **故障排除** - 常见问题解决方案

## 🎯 使用场景

### 开发人员
```bash
# 日常开发
./mes-launcher.sh
# 选择 6) 同时启动开发模式

# 测试生产构建
./mes-launcher.sh
# 选择 9) 构建完整系统
# 然后选择 1) 完整系统启动
```

### 部署人员
```bash
# 生产环境部署
sudo ./mes-launcher.sh
# 选择 16) 生产环境安装

# 或者使用Docker
./mes-launcher.sh
# 选择 12) Docker完整部署
```

### 运维人员
```bash
# 检查系统状态
./mes-launcher.sh --status

# 停止所有服务
./mes-launcher.sh --stop

# 重启服务
./mes-launcher.sh
# 选择 13) 停止所有服务
# 然后选择 1) 完整系统启动
```

## 📁 脚本整理说明

### 保留的核心脚本
- `mes-launcher.sh` - **统一启动器** (主要入口)
- `start-mes-system.sh` - 完整的后端启动脚本
- `quick-start.sh` - 后端快速启动
- `install-production.sh` - 生产环境安装
- `frontend/start-frontend.sh` - 前端完整启动脚本
- `frontend/quick-start-frontend.sh` - 前端快速启动

### 整理后的目录结构
```
scripts/
├── archived/     - 已归档的重复脚本
├── windows/      - Windows批处理脚本
├── docker/       - Docker相关脚本
├── testing/      - 测试脚本
├── database/     - 数据库脚本
└── README.md     - 脚本说明文档
```

### 脚本迁移对照表
| 旧脚本 | 新功能 | 说明 |
|--------|--------|------|
| `start.sh` | `mes-launcher.sh` 选项1 | 完整系统启动 |
| `start_all.sh` | `mes-launcher.sh` 选项1 | 完整系统启动 |
| `stop.sh` | `mes-launcher.sh` 选项13 | 停止所有服务 |
| `status.sh` | `mes-launcher.sh` 选项14 | 系统状态检查 |
| `start_frontend.sh` | `frontend/start-frontend.sh` | 前端启动脚本 |

## 🔧 高级用法

### 端口配置
```bash
# 使用端口配置功能
./mes-launcher.sh
# 选择 20) 端口配置

# 或直接设置环境变量
export MES_PORT=9001          # 后端端口
export FRONTEND_PORT=3080     # 前端生产端口
export VITE_PORT=3000         # 前端开发端口

# 避免使用受限端口: 80, 8080, 443, 8443
```

### 安全进程管理
```bash
# 使用独立的进程管理器
./mes-process-manager.sh status           # 查看状态
./mes-process-manager.sh start-all        # 启动所有服务
./mes-process-manager.sh stop-all         # 安全停止所有服务

# 单独管理服务
./mes-process-manager.sh start-backend 9001
./mes-process-manager.sh start-frontend 3080
```

### 环境变量配置
```bash
# 设置日志级别
export RUST_LOG=debug

# 数据库配置
export DATABASE_URL="postgresql://user:pass@localhost:5432/mes_db"

# 然后启动
./mes-launcher.sh
```

### 批量操作
```bash
# 停止 -> 清理 -> 构建 -> 启动
./mes-launcher.sh --stop
./mes-launcher.sh
# 选择 15) 清理缓存
# 选择 9) 构建完整系统
# 选择 1) 完整系统启动
```

### Docker开发环境
```bash
# 构建开发镜像
./mes-launcher.sh
# 选择 10) Docker构建

# 运行开发容器
./mes-launcher.sh
# 选择 11) Docker运行
```

## 🚨 故障排除

### 常见问题

1. **端口被占用**
   - 使用选项16安全停止所有服务
   - 使用选项20配置端口，避免冲突
   - 避免使用受限端口: 80, 8080, 443, 8443

2. **进程管理问题**
   - 使用 `./mes-process-manager.sh status` 查看进程状态
   - 使用 `./mes-process-manager.sh cleanup` 清理无效PID文件
   - 后台服务日志位置: `logs/backend.log`, `logs/frontend.log`

3. **构建失败**
   - 使用选项18清理缓存
   - 然后重新构建

4. **权限问题**
   ```bash
   chmod +x mes-launcher.sh
   chmod +x mes-process-manager.sh
   chmod +x *.sh
   chmod +x frontend/*.sh
   ```

5. **Docker问题**
   - 确保Docker服务运行: `sudo systemctl start docker`
   - 检查Docker权限: `sudo usermod -aG docker $USER`

6. **SSH连接中断问题**
   - 新版本使用安全的进程管理，不会影响SSH连接
   - 停止服务只会终止MES相关进程，不影响系统进程

### 日志查看
- 开发模式: 直接在终端查看
- 生产模式: 查看 `logs/mes-system.log`
- Docker模式: `docker logs mes-system`

## 📞 支持

### 获取帮助
```bash
./mes-launcher.sh --help
./mes-launcher.sh
# 选择 17) 查看文档
# 选择 18) 故障排除
```

### 文档资源
- `README.md` - 项目总览
- `DEPLOYMENT.md` - 详细部署指南
- `frontend/FRONTEND-DEPLOYMENT.md` - 前端专用指南
- `scripts/README.md` - 脚本说明

### 系统要求
- **后端**: Rust 1.70+, PostgreSQL 12+
- **前端**: Node.js 16+, npm 8+
- **Docker**: Docker 20.10+, Docker Compose 2.0+

## 🎉 总结

统一启动器 `mes-launcher.sh` 提供了：
- ✅ 交互式菜单，操作简单
- ✅ 完整的功能覆盖
- ✅ 智能状态检测
- ✅ 详细的错误提示
- ✅ 统一的操作体验

现在您只需要记住一个命令：`./mes-launcher.sh` 🚀
