#!/usr/bin/env python3
"""
测试权限配置修复后的功能
"""

import requests
import json
import sys
import time

# 配置
BASE_URL = "http://localhost:9001/api"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "admin123"

def login():
    """登录获取token"""
    login_data = {
        "username": ADMIN_USERNAME,
        "password": ADMIN_PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        data = response.json()
        return data.get("token")
    else:
        print(f"登录失败: {response.status_code} - {response.text}")
        return None

def get_roles(token):
    """获取所有角色"""
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/auth/roles", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        return data.get("roles", [])
    else:
        print(f"获取角色失败: {response.status_code} - {response.text}")
        return []

def get_role_permissions(token, role_id):
    """获取角色权限"""
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/roles/{role_id}/permissions", headers=headers)
    
    if response.status_code == 200:
        return response.json()
    else:
        print(f"获取角色权限失败: {response.status_code} - {response.text}")
        return None

def update_role_permissions(token, role_id, permissions):
    """更新角色权限"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    data = {"permissions": permissions}
    response = requests.put(f"{BASE_URL}/roles/{role_id}/permissions", 
                          headers=headers, json=data)
    
    if response.status_code == 200:
        return response.json()
    else:
        print(f"权限更新失败: {response.status_code} - {response.text}")
        return None

def test_rapid_permission_changes(token, role_id):
    """测试快速权限变更（模拟前端快速点击）"""
    print("测试快速权限变更...")
    
    # 获取当前权限
    role_permissions = get_role_permissions(token, role_id)
    if not role_permissions:
        return False
    
    # 找到几个可以切换的权限
    test_permissions = []
    for perm in role_permissions['permissions']:
        if perm['permission_code'] in ['CREATE_WORK_ORDER', 'EDIT_WORK_ORDER', 'DELETE_WORK_ORDER']:
            test_permissions.append(perm)
    
    if len(test_permissions) < 2:
        print("没有足够的测试权限")
        return False
    
    print(f"将测试 {len(test_permissions)} 个权限的快速切换")
    
    # 记录初始状态
    initial_states = {p['id']: p['granted'] for p in test_permissions}
    
    # 快速切换权限状态（模拟用户快速点击）
    for i in range(3):  # 进行3轮快速切换
        print(f"第 {i+1} 轮快速切换...")
        
        for perm in test_permissions:
            # 切换权限状态
            new_granted = not perm['granted']
            
            # 构建更新请求
            updated_permissions = []
            for p in role_permissions['permissions']:
                if p['id'] == perm['id']:
                    updated_permissions.append({
                        "permission_id": p['id'],
                        "granted": new_granted
                    })
                else:
                    updated_permissions.append({
                        "permission_id": p['id'],
                        "granted": p['granted']
                    })
            
            # 发送更新请求
            result = update_role_permissions(token, role_id, updated_permissions)
            if result:
                print(f"  ✅ {perm['permission_name']}: {perm['granted']} -> {new_granted}")
                perm['granted'] = new_granted
            else:
                print(f"  ❌ {perm['permission_name']}: 更新失败")
                return False
            
            # 短暂延迟模拟用户操作间隔
            time.sleep(0.1)
    
    # 验证最终状态
    print("验证最终状态...")
    final_role_permissions = get_role_permissions(token, role_id)
    if not final_role_permissions:
        return False
    
    success = True
    for perm in test_permissions:
        final_perm = next((p for p in final_role_permissions['permissions'] if p['id'] == perm['id']), None)
        if final_perm:
            if final_perm['granted'] == perm['granted']:
                print(f"  ✅ {perm['permission_name']}: 状态一致 ({final_perm['granted']})")
            else:
                print(f"  ❌ {perm['permission_name']}: 状态不一致 (期望: {perm['granted']}, 实际: {final_perm['granted']})")
                success = False
        else:
            print(f"  ❌ {perm['permission_name']}: 未找到权限")
            success = False
    
    return success

def main():
    print("开始测试权限配置修复后的功能...")
    
    # 1. 登录
    print("1. 登录...")
    token = login()
    if not token:
        sys.exit(1)
    print("登录成功")
    
    # 2. 获取角色列表
    print("\n2. 获取角色列表...")
    roles = get_roles(token)
    
    # 3. 找到operator角色
    operator_role = None
    for role in roles:
        if role['role_name'] == 'operator':
            operator_role = role
            break
    
    if not operator_role:
        print("未找到operator角色")
        sys.exit(1)
    
    print(f"找到operator角色 (ID: {operator_role['id']})")
    
    # 4. 测试快速权限变更
    print("\n3. 测试快速权限变更...")
    success = test_rapid_permission_changes(token, operator_role['id'])
    
    if success:
        print("\n✅ 权限配置功能测试通过！")
        print("修复内容:")
        print("1. ✅ 添加了防抖机制，避免快速点击导致的请求冲突")
        print("2. ✅ 实现了本地状态管理，提供即时的UI反馈")
        print("3. ✅ 改进了错误处理和状态同步")
        print("4. ✅ 添加了加载状态指示")
    else:
        print("\n❌ 权限配置功能测试失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
