# 设备/技能组调度弹窗时间选择更新

## 📋 更新概述

根据用户反馈，设备/技能组调度弹窗中的时间选择仍然是"按周"模式。现已更新为支持"按天"和"按周"两种模式，默认使用"按天"模式。

## 🎯 主要更新

### 1. 新增时间模式选择

#### 更新前
- 只支持按周选择
- 固定显示7天（周一到周日）
- 使用周选择器

#### 更新后
- **支持按天和按周两种模式**
- **默认使用按天模式**
- 按天模式：使用日期范围选择器，可选择任意日期范围
- 按周模式：保持原有的周选择器功能

### 2. 界面布局优化

#### 新的控制面板布局
```
┌─────────────────────────────────────────────────────┐
│ 调度模式: [7×24小时] 时间模式: [按天]                │
│ 选择日期范围: [2025-07-14] 至 [2025-07-20]          │
│ 显示范围: 2025-07-14 至 2025-07-20                  │
└─────────────────────────────────────────────────────┘
```

### 3. 动态表格列生成

#### 按天模式
- 表格列标题显示具体日期（如：07-14, 07-15, 07-16...）
- 支持任意天数的日期范围
- 自动适应选择的日期范围

#### 按周模式
- 表格列标题显示星期（周一, 周二, 周三...）
- 固定7天显示
- 保持原有的周选择逻辑

## 🛠️ 技术实现

### 1. 状态管理更新

```typescript
// 新增状态
const [dateRangeMode, setDateRangeMode] = useState<'day' | 'week'>('day');
const [selectedDateRange, setSelectedDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
  dayjs().startOf('day'),
  dayjs().add(6, 'day').endOf('day')
]);
```

### 2. 数据查询逻辑

```typescript
// 根据模式动态计算日期范围
let startDate: string, endDate: string;

if (dateRangeMode === 'day') {
  startDate = selectedDateRange[0].toISOString();
  endDate = selectedDateRange[1].toISOString();
} else {
  startDate = selectedDate.startOf('week').toISOString();
  endDate = selectedDate.endOf('week').toISOString();
}
```

### 3. 动态列生成

```typescript
// 动态生成天数标题
const getDayTitles = () => {
  if (dateRangeMode === 'day') {
    const startDate = selectedDateRange[0];
    const endDate = selectedDateRange[1];
    const dayCount = endDate.diff(startDate, 'day') + 1;
    
    const titles = [];
    for (let i = 0; i < dayCount; i++) {
      const currentDate = startDate.add(i, 'day');
      titles.push(currentDate.format('MM-DD'));
    }
    return titles;
  } else {
    return ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
  }
};
```

### 4. 时间表数据生成

```typescript
// 支持两种模式的时间表生成
const generateScheduleData = () => {
  let days: string[];
  let dateRange: dayjs.Dayjs[];
  
  if (dateRangeMode === 'day') {
    // 按天模式：生成选定日期范围内的天数
    const startDate = selectedDateRange[0];
    const endDate = selectedDateRange[1];
    const dayCount = endDate.diff(startDate, 'day') + 1;
    
    days = [];
    dateRange = [];
    for (let i = 0; i < dayCount; i++) {
      const currentDate = startDate.add(i, 'day');
      days.push(currentDate.format('MM-DD'));
      dateRange.push(currentDate);
    }
  } else {
    // 按周模式：使用固定的7天
    days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    dateRange = [];
    for (let i = 0; i < 7; i++) {
      dateRange.push(selectedDate.startOf('week').add(i, 'day'));
    }
  }
  
  // ... 其余逻辑保持不变
};
```

## 🎮 使用方法

### 按天模式（默认）
1. 打开设备/技能组调度弹窗
2. 确认"时间模式"选择为"按天"
3. 使用"选择日期范围"选择开始和结束日期
4. 查看表格中显示具体日期的列标题
5. 进行任务拖拽调度

### 按周模式
1. 在控制面板中将"时间模式"切换为"按周"
2. 使用"选择周"选择器选择目标周
3. 查看表格中显示星期的列标题
4. 进行任务拖拽调度

## 📊 改进效果

### 用户体验提升
- **精确控制**：按天模式提供更精确的时间控制
- **灵活选择**：可以选择任意日期范围，不限于固定的7天
- **模式切换**：保留原有按周功能，用户可根据需要选择
- **直观显示**：表格列标题清楚显示具体日期或星期

### 功能兼容性
- **向后兼容**：保留原有的按周功能
- **数据一致**：两种模式使用相同的数据查询和处理逻辑
- **界面统一**：保持相同的界面风格和交互方式

## 🔗 相关文件

### 主要修改文件
- `frontend/src/components/MachineScheduleModal.tsx` - 设备/技能组调度弹窗组件

### 测试访问
- 前端：http://localhost:3001
- 进入"计划任务"页面
- 点击甘特图中的技能组或设备名称
- 查看调度弹窗中的新时间选择功能

---

*此更新解决了用户反馈的时间选择问题，提供了更灵活和精确的时间控制功能。*
