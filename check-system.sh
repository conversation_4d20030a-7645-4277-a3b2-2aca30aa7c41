#!/bin/bash

# MES系统状态检查脚本
# 检查系统环境、依赖、服务状态等

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# 检查结果统计
CHECKS_TOTAL=0
CHECKS_PASSED=0
CHECKS_FAILED=0
CHECKS_WARNING=0

# 执行检查并记录结果
run_check() {
    local check_name="$1"
    local check_command="$2"
    local is_critical="${3:-false}"
    
    CHECKS_TOTAL=$((CHECKS_TOTAL + 1))
    
    if eval "$check_command" > /dev/null 2>&1; then
        log_success "$check_name"
        CHECKS_PASSED=$((CHECKS_PASSED + 1))
        return 0
    else
        if [ "$is_critical" = "true" ]; then
            log_error "$check_name"
            CHECKS_FAILED=$((CHECKS_FAILED + 1))
        else
            log_warning "$check_name"
            CHECKS_WARNING=$((CHECKS_WARNING + 1))
        fi
        return 1
    fi
}

# 检查系统信息
check_system_info() {
    echo "=================================="
    echo "🖥️  系统信息"
    echo "=================================="
    
    echo "操作系统: $(uname -s) $(uname -r)"
    echo "架构: $(uname -m)"
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if [ -f /etc/os-release ]; then
            . /etc/os-release
            echo "发行版: $NAME $VERSION"
        fi
        
        # 内存信息
        MEMORY_TOTAL=$(free -h | awk '/^Mem:/ {print $2}')
        MEMORY_USED=$(free -h | awk '/^Mem:/ {print $3}')
        MEMORY_FREE=$(free -h | awk '/^Mem:/ {print $4}')
        echo "内存: $MEMORY_USED / $MEMORY_TOTAL (可用: $MEMORY_FREE)"
        
        # 磁盘信息
        DISK_INFO=$(df -h . | awk 'NR==2 {print $3 " / " $2 " (可用: " $4 ")"}')
        echo "磁盘: $DISK_INFO"
        
        # CPU信息
        CPU_COUNT=$(nproc)
        echo "CPU核心数: $CPU_COUNT"
        
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS系统信息
        MEMORY_TOTAL=$(sysctl -n hw.memsize | awk '{print int($1/1024/1024/1024) "GB"}')
        echo "内存: $MEMORY_TOTAL"
        
        CPU_COUNT=$(sysctl -n hw.ncpu)
        echo "CPU核心数: $CPU_COUNT"
    fi
    
    echo
}

# 检查基础依赖
check_basic_dependencies() {
    echo "=================================="
    echo "📦 基础依赖检查"
    echo "=================================="
    
    run_check "Git" "command -v git" true
    run_check "Curl" "command -v curl" true
    run_check "Wget" "command -v wget" false
    
    if command -v git > /dev/null; then
        echo "Git版本: $(git --version)"
    fi
    
    echo
}

# 检查Rust环境
check_rust_environment() {
    echo "=================================="
    echo "🦀 Rust环境检查"
    echo "=================================="
    
    run_check "Rust编译器" "command -v rustc" true
    run_check "Cargo包管理器" "command -v cargo" true
    
    if command -v rustc > /dev/null; then
        echo "Rust版本: $(rustc --version)"
        echo "Cargo版本: $(cargo --version)"
        
        # 检查Rust版本是否满足要求
        RUST_VERSION=$(rustc --version | awk '{print $2}' | cut -d'.' -f1-2)
        if [ "$(echo "$RUST_VERSION >= 1.70" | bc -l 2>/dev/null || echo "0")" = "1" ]; then
            log_success "Rust版本满足要求 (>= 1.70)"
        else
            log_warning "Rust版本可能过低，建议升级到1.70+"
        fi
    fi
    
    echo
}

# 检查Node.js环境
check_nodejs_environment() {
    echo "=================================="
    echo "📗 Node.js环境检查"
    echo "=================================="
    
    run_check "Node.js" "command -v node" true
    run_check "NPM包管理器" "command -v npm" true
    
    if command -v node > /dev/null; then
        NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        echo "Node.js版本: $(node --version)"
        echo "NPM版本: $(npm --version)"
        
        # 检查Node.js版本是否满足要求
        if [ "$NODE_VERSION" -ge 18 ]; then
            log_success "Node.js版本满足要求 (>= 18)"
        else
            log_warning "Node.js版本过低，建议升级到18+"
        fi
    fi
    
    echo
}

# 检查PostgreSQL
check_postgresql() {
    echo "=================================="
    echo "🐘 PostgreSQL检查"
    echo "=================================="
    
    run_check "PostgreSQL客户端" "command -v psql" true
    
    if command -v psql > /dev/null; then
        echo "PostgreSQL版本: $(psql --version)"
    fi
    
    # 检查PostgreSQL服务状态
    if systemctl is-active postgresql > /dev/null 2>&1; then
        log_success "PostgreSQL服务运行中"
    elif brew services list | grep postgresql | grep started > /dev/null 2>&1; then
        log_success "PostgreSQL服务运行中 (Homebrew)"
    else
        log_warning "PostgreSQL服务未运行"
    fi
    
    # 检查数据库连接
    if [ -f ".env" ]; then
        source .env
        if [ -n "$DATABASE_URL" ]; then
            if psql "$DATABASE_URL" -c "SELECT 1;" > /dev/null 2>&1; then
                log_success "数据库连接正常"
                
                # 检查表数量
                TABLE_COUNT=$(psql "$DATABASE_URL" -tAc "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null || echo "0")
                echo "数据库表数量: $TABLE_COUNT"
                
                # 检查用户数量
                USER_COUNT=$(psql "$DATABASE_URL" -tAc "SELECT COUNT(*) FROM users;" 2>/dev/null || echo "未知")
                echo "系统用户数量: $USER_COUNT"
            else
                log_error "数据库连接失败"
            fi
        else
            log_warning "未找到DATABASE_URL配置"
        fi
    else
        log_warning "未找到.env配置文件"
    fi
    
    echo
}

# 检查项目文件
check_project_files() {
    echo "=================================="
    echo "📁 项目文件检查"
    echo "=================================="
    
    run_check "Cargo.toml" "test -f Cargo.toml" true
    run_check "源代码目录" "test -d src" true
    run_check "迁移文件目录" "test -d migrations" true
    run_check "前端目录" "test -d frontend" true
    run_check "前端package.json" "test -f frontend/package.json" true
    
    # 检查构建产物
    run_check "后端可执行文件" "test -f target/release/mes-system" false
    run_check "前端依赖" "test -d frontend/node_modules" false
    run_check "前端构建产物" "test -d frontend/dist" false
    
    echo
}

# 检查端口占用
check_ports() {
    echo "=================================="
    echo "🔌 端口检查"
    echo "=================================="
    
    # 检查9000端口（后端API）
    if lsof -i :9000 > /dev/null 2>&1; then
        log_warning "端口9000已被占用"
        echo "占用进程: $(lsof -i :9000 | tail -n +2 | awk '{print $1 " (PID: " $2 ")"}')"
    else
        log_success "端口9000可用"
    fi
    
    # 检查3000端口（前端）
    if lsof -i :3000 > /dev/null 2>&1; then
        log_warning "端口3000已被占用"
        echo "占用进程: $(lsof -i :3000 | tail -n +2 | awk '{print $1 " (PID: " $2 ")"}')"
    else
        log_success "端口3000可用"
    fi
    
    # 检查5432端口（PostgreSQL）
    if lsof -i :5432 > /dev/null 2>&1; then
        log_success "端口5432已被占用 (PostgreSQL)"
    else
        log_warning "端口5432未被占用 (PostgreSQL可能未运行)"
    fi
    
    echo
}

# 检查服务状态
check_services() {
    echo "=================================="
    echo "🚀 服务状态检查"
    echo "=================================="
    
    # 检查后端API
    if curl -f http://localhost:8080/api/health > /dev/null 2>&1; then
        log_success "后端API服务响应正常"
    else
        log_warning "后端API服务无响应"
    fi
    
    # 检查前端服务
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        log_success "前端服务响应正常"
    else
        log_warning "前端服务无响应"
    fi
    
    echo
}

# 检查Docker环境（如果存在）
check_docker() {
    echo "=================================="
    echo "🐳 Docker环境检查"
    echo "=================================="
    
    if command -v docker > /dev/null; then
        log_success "Docker已安装"
        echo "Docker版本: $(docker --version)"
        
        # 检查Docker服务状态
        if docker info > /dev/null 2>&1; then
            log_success "Docker服务运行正常"
        else
            log_warning "Docker服务未运行"
        fi
        
        # 检查Docker Compose
        if command -v docker-compose > /dev/null || docker compose version > /dev/null 2>&1; then
            log_success "Docker Compose可用"
        else
            log_warning "Docker Compose未安装"
        fi
        
        # 检查Docker容器状态
        if [ -f "docker-compose.yml" ]; then
            if docker-compose ps | grep -q "Up"; then
                log_success "Docker容器运行中"
                echo "容器状态:"
                docker-compose ps
            else
                log_warning "Docker容器未运行"
            fi
        fi
    else
        log_warning "Docker未安装"
    fi
    
    echo
}

# 性能检查
check_performance() {
    echo "=================================="
    echo "⚡ 性能检查"
    echo "=================================="
    
    # 检查内存使用
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        MEMORY_USAGE=$(free | awk '/^Mem:/ {printf "%.1f", $3/$2 * 100}')
        echo "内存使用率: ${MEMORY_USAGE}%"
        
        if [ "$(echo "$MEMORY_USAGE > 80" | bc -l 2>/dev/null || echo "0")" = "1" ]; then
            log_warning "内存使用率较高"
        else
            log_success "内存使用率正常"
        fi
        
        # 检查磁盘使用
        DISK_USAGE=$(df . | awk 'NR==2 {print $5}' | sed 's/%//')
        echo "磁盘使用率: ${DISK_USAGE}%"
        
        if [ "$DISK_USAGE" -gt 80 ]; then
            log_warning "磁盘使用率较高"
        else
            log_success "磁盘使用率正常"
        fi
        
        # 检查负载
        LOAD_AVG=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
        echo "系统负载: $LOAD_AVG"
    fi
    
    echo
}

# 生成检查报告
generate_report() {
    echo "=================================="
    echo "📊 检查报告"
    echo "=================================="
    echo
    echo "总检查项: $CHECKS_TOTAL"
    echo "通过: $CHECKS_PASSED"
    echo "警告: $CHECKS_WARNING"
    echo "失败: $CHECKS_FAILED"
    echo
    
    if [ "$CHECKS_FAILED" -eq 0 ]; then
        if [ "$CHECKS_WARNING" -eq 0 ]; then
            log_success "所有检查项通过！系统状态良好"
        else
            log_warning "存在 $CHECKS_WARNING 个警告项，建议检查"
        fi
    else
        log_error "存在 $CHECKS_FAILED 个失败项，需要修复"
    fi
    
    echo
    echo "建议操作:"
    if [ "$CHECKS_FAILED" -gt 0 ]; then
        echo "  1. 修复失败的检查项"
        echo "  2. 重新运行检查脚本"
    fi
    if [ "$CHECKS_WARNING" -gt 0 ]; then
        echo "  3. 查看警告项并根据需要处理"
    fi
    echo "  4. 查看安装指南: INSTALLATION_GUIDE.md"
    echo "  5. 如需帮助，请查看文档或提交Issue"
    echo
}

# 主程序
main() {
    echo "=================================="
    echo "🔍 MES系统状态检查"
    echo "=================================="
    echo
    
    check_system_info
    check_basic_dependencies
    check_rust_environment
    check_nodejs_environment
    check_postgresql
    check_project_files
    check_ports
    check_services
    check_docker
    check_performance
    generate_report
}

# 运行主程序
main "$@"
