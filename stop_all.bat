@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo MES系统 - Windows停止服务脚本
echo ========================================
echo.

echo 正在停止MES系统服务...
echo.

:: 停止MES后端进程
echo [1/3] 停止后端服务...
tasklist | findstr "mes-system.exe" >nul
if %errorlevel% equ 0 (
    echo [停止] 正在停止MES后端进程...
    taskkill /f /im "mes-system.exe" >nul 2>&1
    if !errorlevel! equ 0 (
        echo [✓] 后端服务已停止
    ) else (
        echo [警告] 停止后端服务时出现问题
    )
) else (
    echo [信息] 后端服务未运行
)

:: 停止Node.js进程 (前端)
echo [2/3] 停止前端服务...
tasklist | findstr "node.exe" >nul
if %errorlevel% equ 0 (
    echo [停止] 正在停止前端进程...
    
    :: 查找运行在3000端口的Node.js进程
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":3000"') do (
        taskkill /f /pid %%a >nul 2>&1
    )
    
    :: 停止所有npm相关进程
    taskkill /f /im "node.exe" /fi "WINDOWTITLE eq MES前端*" >nul 2>&1
    
    echo [✓] 前端服务已停止
) else (
    echo [信息] 前端服务未运行
)

:: 清理端口占用
echo [3/3] 清理端口占用...

:: 检查9000端口
netstat -ano | findstr ":9000" >nul
if %errorlevel% equ 0 (
    echo [清理] 清理9000端口占用...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":9000"') do (
        taskkill /f /pid %%a >nul 2>&1
    )
)

:: 检查3000端口
netstat -ano | findstr ":3000" >nul
if %errorlevel% equ 0 (
    echo [清理] 清理3000端口占用...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":3000"') do (
        taskkill /f /pid %%a >nul 2>&1
    )
)

echo [✓] 端口清理完成

echo.
echo ========================================
echo MES系统服务已停止
echo ========================================
echo.
echo 所有MES相关进程已终止
echo 端口9000和3000已释放
echo.
echo 要重新启动系统，请运行 start_all.bat
echo.
pause
