# 📸 MES系统截图说明

## 🎯 截图采集说明

由于技术限制，系统截图需要手动采集。以下是建议的截图列表和采集方法：

---

## 📋 截图清单

### 🔐 1. 用户认证
- **文件名**: `01_login_page.png`
- **页面**: http://localhost:3000/login
- **说明**: 用户登录界面，展示JWT认证系统

### 📊 2. 生产仪表板
- **文件名**: `02_dashboard_overview.png`
- **页面**: http://localhost:3000/dashboard
- **说明**: 实时生产监控面板，KPI指标展示

### 🏗️ 3. 项目管理
- **文件名**: `03_project_management.png`
- **页面**: http://localhost:3000/projects
- **说明**: 项目列表和管理功能

### 🎯 4. 生产执行中心
- **文件名**: `04_production_center.png`
- **页面**: http://localhost:3000/production-center
- **说明**: 任务分配和执行跟踪

### 🔍 5. 质量管理
- **文件名**: `05_quality_management.png`
- **页面**: http://localhost:3000/quality
- **说明**: 质量检验和控制流程

### 👥 6. 用户管理
- **文件名**: `06_user_management.png`
- **页面**: http://localhost:3000/users
- **说明**: 用户、角色、技能组管理

### 🏭 7. 设备管理
- **文件名**: `07_machine_management.png`
- **页面**: http://localhost:3000/machines
- **说明**: 设备分组和状态监控

### 📋 8. BOM管理
- **文件名**: `08_bom_management.png`
- **页面**: http://localhost:3000/bom
- **说明**: 物料清单管理

### ⚙️ 9. 系统配置
- **文件名**: `09_system_config.png`
- **页面**: http://localhost:3000/system-config
- **说明**: 系统参数配置

---

## 🛠️ 手动截图方法

### 方法一：浏览器截图
1. 打开浏览器访问 http://localhost:3000
2. 登录系统 (admin/admin123)
3. 导航到各个功能页面
4. 使用浏览器开发者工具截图
5. 保存到 `screenshots/` 目录

### 方法二：系统截图工具
1. 使用Windows截图工具 (Win + Shift + S)
2. 或使用第三方截图软件
3. 确保截图清晰完整
4. 按照文件名规范保存

### 方法三：自动化工具
```powershell
# 使用PowerShell + Selenium
# 或其他自动化截图工具
```

---

## 📁 文件结构

```
screenshots/
├── README.md                    # 本说明文件
├── 01_login_page.png           # 登录页面
├── 02_dashboard_overview.png   # 仪表板概览
├── 03_project_management.png   # 项目管理
├── 04_production_center.png    # 生产执行中心
├── 05_quality_management.png   # 质量管理
├── 06_user_management.png      # 用户管理
├── 07_machine_management.png   # 设备管理
├── 08_bom_management.png       # BOM管理
└── 09_system_config.png        # 系统配置
```

---

## 🎨 截图要求

### 📐 尺寸规格
- **分辨率**: 1920x1080 或更高
- **格式**: PNG (推荐) 或 JPG
- **质量**: 高清，文字清晰可读

### 🎯 内容要求
- 包含完整的页面内容
- 显示系统导航菜单
- 展示主要功能区域
- 避免敏感信息泄露

### 📝 命名规范
- 使用数字前缀排序 (01, 02, 03...)
- 使用英文下划线命名
- 文件名简洁明了
- 统一使用小写字母

---

## 🔄 更新说明

截图采集完成后，请：

1. **更新文档引用**
   - 检查所有Markdown文档中的图片链接
   - 确保路径正确 (`screenshots/文件名.png`)

2. **提交到Git**
   ```powershell
   git add screenshots/
   git commit -m "Add system screenshots"
   git push origin deep
   ```

3. **验证显示**
   - 在GitHub上检查图片是否正常显示
   - 确保文档格式正确

---

*📸 截图是展示系统功能的重要方式，请确保质量和完整性！*
