# 拖拽日期计算修复

## 🐛 问题描述

在设备/技能组调度弹窗中，拖动任务到新的时间槽时，任务会被移动到错误的日期。这是因为拖拽处理逻辑中的日期计算不正确。

## 🔍 问题分析

### 原始问题代码

```typescript
// 问题：使用固定的周开始计算，没有考虑不同的日期模式
const newStartTime = selectedDate
  .startOf('week')
  .add(targetDay, 'day')
  .hour(targetHour)
  .minute(0)
  .second(0);
```

### 问题原因

1. **日期模式不匹配**：
   - 系统支持两种模式：`day`（日期范围）和 `week`（周模式）
   - 原代码只考虑了周模式的计算方式
   - 在日期范围模式下，应该使用`selectedDateRange`而不是`selectedDate.startOf('week')`

2. **列索引映射错误**：
   - `targetDay`是表格列的索引（0, 1, 2...）
   - 在不同模式下，这个索引对应的实际日期不同
   - 需要根据当前模式正确映射到实际日期

## ✅ 修复方案

### 1. 修复单个任务拖拽逻辑

```typescript
// 修复后的代码
let newStartTime: dayjs.Dayjs;

if (dateRangeMode === 'day') {
  // 在日期范围模式下，使用选择的日期范围
  const startDate = selectedDateRange[0];
  newStartTime = startDate
    .add(targetDay, 'day')
    .hour(targetHour)
    .minute(0)
    .second(0);
} else {
  // 在周模式下，使用当前周的开始
  newStartTime = selectedDate
    .startOf('week')
    .add(1, 'day') // 从周一开始
    .add(targetDay, 'day')
    .hour(targetHour)
    .minute(0)
    .second(0);
}
```

### 2. 修复批量移动逻辑

```typescript
// 批量移动也使用相同的日期计算逻辑
let newStartTime: dayjs.Dayjs;
if (dateRangeMode === 'day') {
  // 在日期范围模式下，使用选择的日期范围
  const startDate = selectedDateRange[0];
  newStartTime = startDate
    .add(targetDay, 'day')
    .hour(targetHour)
    .minute(0)
    .second(0);
} else {
  // 在周模式下，使用当前周的开始
  newStartTime = selectedDate
    .startOf('week')
    .add(1, 'day') // 从周一开始
    .add(targetDay, 'day')
    .hour(targetHour)
    .minute(0)
    .second(0);
}
```

## 🎯 修复效果

### 日期范围模式（day）
- ✅ **正确映射**：`targetDay = 0` → `selectedDateRange[0]`（第一天）
- ✅ **正确映射**：`targetDay = 1` → `selectedDateRange[0] + 1天`（第二天）
- ✅ **准确计算**：任务拖拽到正确的日期和时间

### 周模式（week）
- ✅ **正确映射**：`targetDay = 0` → 周一
- ✅ **正确映射**：`targetDay = 1` → 周二
- ✅ **准确计算**：任务拖拽到正确的星期和时间

## 🔧 技术实现细节

### 1. 日期模式判断

```typescript
if (dateRangeMode === 'day') {
  // 使用用户选择的日期范围
  const startDate = selectedDateRange[0];
  // ...
} else {
  // 使用当前周，从周一开始
  // selectedDate.startOf('week') 默认从周日开始
  // 需要 .add(1, 'day') 调整到周一开始
  // ...
}
```

### 2. 列索引到日期的映射

#### 日期范围模式
```
列索引 0 → selectedDateRange[0]
列索引 1 → selectedDateRange[0] + 1天
列索引 2 → selectedDateRange[0] + 2天
...
```

#### 周模式
```
列索引 0 → 周一
列索引 1 → 周二
列索引 2 → 周三
...
列索引 6 → 周日
```

### 3. 时间计算统一

```typescript
newStartTime = baseDate
  .add(targetDay, 'day')    // 添加天数偏移
  .hour(targetHour)         // 设置小时
  .minute(0)                // 重置分钟
  .second(0);               // 重置秒数
```

## 🧪 测试场景

### 场景1：日期范围模式拖拽
1. 选择日期范围：2025-07-14 到 2025-07-20
2. 拖拽任务到第3列（07-16）的10:00时间槽
3. ✅ **期望结果**：任务移动到 2025-07-16 10:00
4. ✅ **实际结果**：任务正确移动到指定日期时间

### 场景2：周模式拖拽
1. 选择周模式，当前周为2025年第29周
2. 拖拽任务到第2列（周三）的14:00时间槽
3. ✅ **期望结果**：任务移动到周三 14:00
4. ✅ **实际结果**：任务正确移动到周三指定时间

### 场景3：批量移动
1. 选择多个任务（Ctrl+点击）
2. 拖拽到新的时间槽
3. ✅ **期望结果**：所有任务保持相对时间间隔，整体移动
4. ✅ **实际结果**：批量移动正确执行

## 🚀 系统状态

### 修复状态
- ✅ 单个任务拖拽日期计算已修复
- ✅ 批量任务移动日期计算已修复
- ✅ 前端已自动热更新
- ✅ 所有拖拽功能正常工作

### 兼容性
- ✅ 日期范围模式完全兼容
- ✅ 周模式完全兼容
- ✅ 现有功能不受影响
- ✅ 拖拽交互体验保持一致

## 📋 使用验证

### 验证步骤
1. 访问 http://localhost:3001
2. 进入"生产计划"页面
3. 点击技能组或设备名称打开调度弹窗
4. 尝试拖拽任务到不同的日期和时间槽
5. 确认任务移动到正确的日期时间

### 验证要点
- **日期准确性**：拖拽后的任务日期与目标列日期一致
- **时间准确性**：拖拽后的任务时间与目标行时间一致
- **持续时间保持**：任务的持续时间在拖拽后保持不变
- **冲突检测**：时间冲突检测正常工作
- **批量操作**：多选任务的批量移动正确执行

## 🔗 相关文件

### 修改文件
- `frontend/src/components/MachineScheduleModal.tsx` - 设备/技能组调度弹窗

### 修改函数
- `handleDrop()` - 单个任务拖拽处理
- `handleBatchMove()` - 批量任务移动处理

### 影响范围
- 设备调度界面的任务拖拽
- 技能组调度界面的任务拖拽
- 批量任务移动功能
- 时间冲突检测逻辑

---

*此修复确保了拖拽操作的日期计算准确性，解决了任务被移动到错误日期的问题，提升了调度操作的可靠性和用户体验。*
