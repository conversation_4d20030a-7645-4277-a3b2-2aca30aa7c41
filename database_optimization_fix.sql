-- MES系统数据库性能优化修正脚本
-- 修正列名错误，添加缺失的索引

-- ==================== 执行日志索引修正 ====================

-- 按执行时间查找日志（使用正确的列名 event_time）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_execution_logs_event_time_new 
ON execution_logs(event_time);

-- 复合索引：任务+时间（任务执行历史）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_execution_logs_task_time 
ON execution_logs(plan_task_id, event_time);

-- 按事件类型查找
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_execution_logs_event_type 
ON execution_logs(event_type);

-- 按用户查找执行日志
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_execution_logs_user_id 
ON execution_logs(user_id);

-- 复合索引：用户+时间（用户操作历史）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_execution_logs_user_time 
ON execution_logs(user_id, event_time);

-- ==================== 审计日志索引修正 ====================

-- 按操作类型查找（使用正确的列名 action_type）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_action_type 
ON audit_logs(action_type);

-- 复合索引：用户+时间（使用正确的列名 action_time）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_user_action_time 
ON audit_logs(user_id, action_time);

-- 按目标实体类型查找
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_target_entity_new 
ON audit_logs(target_entity);

-- 复合索引：实体类型+目标ID（实体操作历史）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_entity_target 
ON audit_logs(target_entity, target_id);

-- 复合索引：实体+时间（实体变更时间线）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_entity_time 
ON audit_logs(target_entity, action_time);

-- ==================== 验证索引创建结果 ====================

-- 查看新创建的索引
SELECT 
    schemaname,
    tablename, 
    indexname, 
    indexdef
FROM pg_indexes 
WHERE tablename IN ('execution_logs', 'audit_logs')
AND indexname LIKE '%_new' OR indexname LIKE '%task_time%' OR indexname LIKE '%user_time%' OR indexname LIKE '%entity%'
ORDER BY tablename, indexname;

-- ==================== 性能测试查询 ====================

-- 测试1：按项目查找所有相关数据的性能
\echo '测试查询1：按项目查找工单和任务'
EXPLAIN (ANALYZE, BUFFERS) 
SELECT 
    p.name as project_name,
    pb.part_id,
    wo.id as work_order_id,
    wo.status as work_order_status,
    COUNT(pt.id) as task_count
FROM projects p
JOIN project_boms pb ON p.id = pb.project_id
JOIN work_orders wo ON pb.id = wo.project_bom_id
LEFT JOIN plan_tasks pt ON wo.id = pt.work_order_id
WHERE p.id = 1
GROUP BY p.name, pb.part_id, wo.id, wo.status;

-- 测试2：甘特图时间范围查询性能
\echo '测试查询2：甘特图时间范围查询'
EXPLAIN (ANALYZE, BUFFERS)
SELECT 
    pt.id,
    pt.work_order_id,
    pt.planned_start,
    pt.planned_end,
    pt.status,
    pt.machine_id
FROM plan_tasks pt
WHERE pt.planned_start >= CURRENT_DATE - INTERVAL '30 days'
AND pt.planned_end <= CURRENT_DATE + INTERVAL '30 days'
ORDER BY pt.planned_start;

-- 测试3：设备利用率查询性能
\echo '测试查询3：设备利用率分析'
EXPLAIN (ANALYZE, BUFFERS)
SELECT 
    pt.machine_id,
    COUNT(*) as task_count,
    SUM(EXTRACT(EPOCH FROM (pt.planned_end - pt.planned_start))/3600) as total_hours
FROM plan_tasks pt
WHERE pt.machine_id IS NOT NULL
AND pt.planned_start >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY pt.machine_id
ORDER BY total_hours DESC;

-- ==================== 索引使用情况统计 ====================

\echo '索引使用情况统计：'
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as "扫描次数",
    idx_tup_read as "读取元组数",
    idx_tup_fetch as "获取元组数"
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
AND tablename IN ('projects', 'project_boms', 'work_orders', 'plan_tasks', 'execution_logs', 'audit_logs')
ORDER BY idx_scan DESC;

\echo '✅ 数据库性能优化修正完成'
\echo '📊 已执行性能测试查询'
\echo '🔍 已显示索引使用情况统计'
