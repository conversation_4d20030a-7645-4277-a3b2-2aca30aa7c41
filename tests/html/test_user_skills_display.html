<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户技能组显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #40a9ff;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .form-group {
            margin: 10px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }
        .user-card {
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            background-color: #fafafa;
        }
        .skill-tag {
            display: inline-block;
            background-color: #e6f7ff;
            color: #1890ff;
            padding: 2px 8px;
            border-radius: 4px;
            margin: 2px;
            font-size: 12px;
        }
        .warning {
            background-color: #fffbe6;
            border: 1px solid #ffe58f;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>用户技能组显示测试</h1>

    <div class="container">
        <h2>1. 登录</h2>
        <div class="form-group">
            <label>用户名:</label>
            <input type="text" id="username" value="admin" placeholder="输入用户名">
        </div>
        <div class="form-group">
            <label>密码:</label>
            <input type="password" id="password" value="admin123" placeholder="输入密码">
        </div>
        <button class="button" onclick="login()">登录</button>
        <div id="login-result"></div>
    </div>

    <div class="container">
        <h2>2. 获取用户列表</h2>
        <button class="button" onclick="getUsers()">获取用户列表</button>
        <div id="users-result"></div>
    </div>

    <div class="container">
        <h2>3. 获取技能组列表</h2>
        <button class="button" onclick="getSkillGroups()">获取技能组列表</button>
        <div id="skills-result"></div>
    </div>

    <div class="container">
        <h2>4. 用户技能组显示测试</h2>
        <button class="button" onclick="testUserSkillsDisplay()">测试用户技能组显示</button>
        <div id="display-test-result"></div>
    </div>

    <script>
        let authToken = '';
        const API_BASE = 'http://localhost:8080/api';

        // 技能组名称中文映射
        const getSkillGroupDisplayName = (groupName) => {
            const skillGroupNameMap = {
                'CNC Machining': 'CNC加工',
                'Milling': '铣削加工',
                'Turning': '车削加工',
                'Grinding': '磨削加工',
                'Assembly': '装配',
                'Quality Control': '质量控制',
                'Packaging': '包装'
            };
            return skillGroupNameMap[groupName] || groupName;
        };

        async function apiCall(endpoint, options = {}) {
            try {
                const url = `${API_BASE}${endpoint}`;
                const config = {
                    headers: {
                        'Content-Type': 'application/json',
                        ...(authToken && { 'Authorization': `Bearer ${authToken}` })
                    },
                    ...options
                };

                console.log('API调用:', url, config);
                const response = await fetch(url, config);
                const data = await response.json();

                if (response.ok) {
                    return { success: true, data };
                } else {
                    return { success: false, error: data.message || '请求失败' };
                }
            } catch (error) {
                console.error('API调用错误:', error);
                return { success: false, error: error.message };
            }
        }

        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            if (result.success) {
                element.className = 'success';
                element.innerHTML = `
                    <div><strong>成功:</strong></div>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                element.className = 'error';
                element.innerHTML = `
                    <div><strong>错误:</strong> ${result.error}</div>
                `;
            }
        }

        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            const result = await apiCall('/auth/login', {
                method: 'POST',
                body: JSON.stringify({ username, password })
            });

            if (result.success && result.data.token) {
                authToken = result.data.token;
            }

            displayResult('login-result', result);
        }

        async function getUsers() {
            const result = await apiCall('/users');
            displayResult('users-result', result);
        }

        async function getSkillGroups() {
            const result = await apiCall('/auth/skill-groups');
            displayResult('skills-result', result);
        }

        async function testUserSkillsDisplay() {
            const usersResult = await apiCall('/users');
            
            if (!usersResult.success) {
                displayResult('display-test-result', usersResult);
                return;
            }

            const users = usersResult.data.users;
            let html = '<div><strong>用户技能组显示测试结果:</strong></div>';
            
            // 检查是否有用户没有技能组
            const usersWithoutSkills = users.filter(user => !user.skills || user.skills.length === 0);
            
            if (usersWithoutSkills.length > 0) {
                html += '<div class="warning"><strong>警告：发现用户没有分配技能组</strong></div>';
                usersWithoutSkills.forEach(user => {
                    html += `<div class="user-card">
                        <strong>用户:</strong> ${user.username} (${user.full_name || '未设置姓名'})<br>
                        <strong>技能组:</strong> <span style="color: red;">无技能组</span><br>
                        <strong>原始数据:</strong> ${JSON.stringify(user.skills)}
                    </div>`;
                });
            } else {
                html += '<div class="success"><strong>所有用户都已分配技能组</strong></div>';
            }

            // 显示所有用户的技能组
            html += '<div><strong>所有用户技能组详情:</strong></div>';
            users.forEach(user => {
                html += `<div class="user-card">
                    <strong>用户:</strong> ${user.username} (${user.full_name || '未设置姓名'})<br>
                    <strong>角色:</strong> ${user.roles.join(', ')}<br>
                    <strong>技能组:</strong> `;
                
                if (user.skills && user.skills.length > 0) {
                    user.skills.forEach(skill => {
                        html += `<span class="skill-tag">${getSkillGroupDisplayName(skill)}</span>`;
                    });
                } else {
                    html += '<span style="color: red;">无技能组</span>';
                }
                
                html += `<br><strong>原始技能数据:</strong> ${JSON.stringify(user.skills)}`;
                html += '</div>';
            });

            document.getElementById('display-test-result').innerHTML = html;
            document.getElementById('display-test-result').className = 'success';
        }
    </script>
</body>
</html>
