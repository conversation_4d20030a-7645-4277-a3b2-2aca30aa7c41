<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>m001用户体验测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #40a9ff;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .user-info {
            background-color: #f0f9ff;
            border: 1px solid #bae7ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .skill-tag {
            display: inline-block;
            background-color: #e6f7ff;
            color: #1890ff;
            padding: 2px 8px;
            border-radius: 4px;
            margin: 2px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>m001用户体验测试</h1>

    <div class="container">
        <h2>1. 用户登录测试</h2>
        <button class="button" onclick="loginAsM001()">登录为m001用户</button>
        <div id="login-result"></div>
    </div>

    <div class="container">
        <h2>2. 用户信息验证</h2>
        <button class="button" onclick="verifyUserInfo()">验证用户信息</button>
        <div id="user-info-result"></div>
    </div>

    <div class="container">
        <h2>3. 权限测试</h2>
        <button class="button" onclick="testPermissions()">测试页面访问权限</button>
        <div id="permissions-result"></div>
    </div>

    <div class="container">
        <h2>4. 技能组任务测试</h2>
        <button class="button" onclick="testSkillGroupTasks()">测试技能组任务</button>
        <div id="tasks-result"></div>
    </div>

    <div class="container">
        <h2>5. 仪表板数据测试</h2>
        <button class="button" onclick="testDashboardData()">测试仪表板数据</button>
        <div id="dashboard-result"></div>
    </div>

    <script>
        let authToken = '';
        let userData = null;
        const API_BASE = 'http://localhost:8080/api';

        // 技能组名称中文映射
        const getSkillGroupDisplayName = (groupName) => {
            const skillGroupNameMap = {
                'CNC Machining': 'CNC加工',
                'Milling': '铣削加工',
                'Turning': '车削加工',
                'Grinding': '磨削加工',
                'Assembly': '装配',
                'Quality Control': '质量控制',
                'Packaging': '包装'
            };
            return skillGroupNameMap[groupName] || groupName;
        };

        async function apiCall(endpoint, options = {}) {
            try {
                const url = `${API_BASE}${endpoint}`;
                const config = {
                    headers: {
                        'Content-Type': 'application/json',
                        ...(authToken && { 'Authorization': `Bearer ${authToken}` })
                    },
                    ...options
                };

                const response = await fetch(url, config);
                const data = await response.json();

                if (response.ok) {
                    return { success: true, data, status: response.status };
                } else {
                    return { success: false, error: data.message || '请求失败', status: response.status };
                }
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function displayResult(elementId, result, title = '') {
            const element = document.getElementById(elementId);
            let className = result.success ? 'success' : 'error';
            
            let html = title ? `<h4>${title}</h4>` : '';
            
            if (result.success) {
                html += `<div><strong>✓ 成功</strong></div>`;
                if (result.data) {
                    html += `<pre>${JSON.stringify(result.data, null, 2)}</pre>`;
                }
            } else {
                html += `<div><strong>✗ 错误:</strong> ${result.error}</div>`;
                if (result.status) {
                    html += `<div><strong>状态码:</strong> ${result.status}</div>`;
                }
            }
            
            element.className = className;
            element.innerHTML = html;
        }

        async function loginAsM001() {
            const result = await apiCall('/auth/login', {
                method: 'POST',
                body: JSON.stringify({ username: 'm001', password: 'm00123' })
            });

            if (result.success && result.data.token) {
                authToken = result.data.token;
                userData = result.data.user;
            }

            displayResult('login-result', result, 'm001用户登录结果');
        }

        async function verifyUserInfo() {
            if (!userData) {
                displayResult('user-info-result', { success: false, error: '请先登录' });
                return;
            }

            let html = '<div class="user-info">';
            html += '<h4>用户信息验证</h4>';
            html += `<p><strong>用户ID:</strong> ${userData.id}</p>`;
            html += `<p><strong>用户名:</strong> ${userData.username}</p>`;
            html += `<p><strong>全名:</strong> ${userData.full_name}</p>`;
            html += `<p><strong>角色:</strong> ${userData.roles.join(', ')}</p>`;
            html += `<p><strong>技能组:</strong> `;
            
            if (userData.skills && userData.skills.length > 0) {
                userData.skills.forEach(skill => {
                    html += `<span class="skill-tag">${getSkillGroupDisplayName(skill)}</span>`;
                });
            } else {
                html += '<span style="color: red;">无技能组</span>';
            }
            
            html += `</p>`;
            html += `<p><strong>状态:</strong> ${userData.is_active ? '活跃' : '非活跃'}</p>`;
            
            // 验证预期值
            const expectedValues = {
                username: 'm001',
                roles: ['operator'],
                skills: ['Milling'],
                is_active: true
            };
            
            html += '<h5>验证结果:</h5>';
            let allValid = true;
            
            Object.entries(expectedValues).forEach(([key, expected]) => {
                const actual = userData[key];
                const isValid = JSON.stringify(actual) === JSON.stringify(expected);
                allValid = allValid && isValid;
                
                html += `<p style="color: ${isValid ? 'green' : 'red'};">
                    ${isValid ? '✓' : '✗'} ${key}: 期望 ${JSON.stringify(expected)}, 实际 ${JSON.stringify(actual)}
                </p>`;
            });
            
            html += `<p><strong>总体验证:</strong> <span style="color: ${allValid ? 'green' : 'red'};">${allValid ? '✓ 通过' : '✗ 失败'}</span></p>`;
            html += '</div>';
            
            document.getElementById('user-info-result').innerHTML = html;
            document.getElementById('user-info-result').className = allValid ? 'success' : 'error';
        }

        async function testPermissions() {
            if (!authToken) {
                displayResult('permissions-result', { success: false, error: '请先登录' });
                return;
            }

            // 操作员应该能访问的页面
            const allowedPages = [
                { path: '/dashboard/overview', name: '仪表板概览' },
                { path: '/execution/my-skill-group-tasks', name: '我的技能组任务' }
            ];

            // 操作员不应该能访问的页面
            const forbiddenPages = [
                { path: '/users', name: '用户管理' },
                { path: '/machines', name: '设备管理' }
            ];

            let html = '<div>';
            html += '<h4>权限测试结果</h4>';
            
            // 测试允许的页面
            html += '<h5>应该允许访问的页面:</h5>';
            for (const page of allowedPages) {
                const result = await apiCall(page.path);
                const status = result.success || result.status !== 403 ? '✓ 允许' : '✗ 拒绝';
                const color = result.success || result.status !== 403 ? 'green' : 'red';
                html += `<p style="color: ${color};">${status} ${page.name} (${page.path})</p>`;
            }
            
            // 测试禁止的页面
            html += '<h5>应该禁止访问的页面:</h5>';
            for (const page of forbiddenPages) {
                const result = await apiCall(page.path);
                const status = result.status === 403 ? '✓ 正确拒绝' : '✗ 意外允许';
                const color = result.status === 403 ? 'green' : 'red';
                html += `<p style="color: ${color};">${status} ${page.name} (${page.path})</p>`;
            }
            
            html += '</div>';
            
            document.getElementById('permissions-result').innerHTML = html;
            document.getElementById('permissions-result').className = 'info';
        }

        async function testSkillGroupTasks() {
            if (!authToken) {
                displayResult('tasks-result', { success: false, error: '请先登录' });
                return;
            }

            const result = await apiCall('/execution/my-skill-group-tasks');
            
            let html = '<div>';
            html += '<h4>技能组任务测试</h4>';
            html += `<p><strong>用户技能组:</strong> Milling (铣削加工)</p>`;
            
            if (result.success) {
                html += `<p style="color: green;">✓ 成功获取技能组任务</p>`;
                html += `<p><strong>任务数量:</strong> ${result.data.length || 0}</p>`;
                if (result.data.length > 0) {
                    html += '<h5>任务列表:</h5>';
                    result.data.forEach((task, index) => {
                        html += `<p>${index + 1}. ${task.work_order_number || 'N/A'} - ${task.part_name || 'N/A'}</p>`;
                    });
                } else {
                    html += '<p style="color: orange;">当前没有分配给Milling技能组的任务</p>';
                }
            } else {
                html += `<p style="color: red;">✗ 获取任务失败: ${result.error}</p>`;
            }
            
            html += '</div>';
            
            document.getElementById('tasks-result').innerHTML = html;
            document.getElementById('tasks-result').className = result.success ? 'success' : 'error';
        }

        async function testDashboardData() {
            if (!authToken) {
                displayResult('dashboard-result', { success: false, error: '请先登录' });
                return;
            }

            const result = await apiCall('/dashboard/overview');
            
            let html = '<div>';
            html += '<h4>仪表板数据测试</h4>';
            
            if (result.success) {
                html += `<p style="color: green;">✓ 成功获取仪表板数据</p>`;
                html += '<h5>数据概览:</h5>';
                html += `<p><strong>总工单数:</strong> ${result.data.production_summary?.total_work_orders || 0}</p>`;
                html += `<p><strong>进行中任务:</strong> ${result.data.production_summary?.tasks_in_progress || 0}</p>`;
                html += `<p><strong>待处理任务:</strong> ${result.data.production_summary?.tasks_pending || 0}</p>`;
                html += `<p><strong>设备利用率:</strong> ${result.data.machine_status?.utilization_rate || 0}%</p>`;
                html += `<p><strong>质量合格率:</strong> ${result.data.quality_metrics?.quality_rate || 0}%</p>`;
            } else {
                html += `<p style="color: red;">✗ 获取仪表板数据失败: ${result.error}</p>`;
            }
            
            html += '</div>';
            
            document.getElementById('dashboard-result').innerHTML = html;
            document.getElementById('dashboard-result').className = result.success ? 'success' : 'error';
        }
    </script>
</body>
</html>
