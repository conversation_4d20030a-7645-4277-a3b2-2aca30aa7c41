<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色和技能组管理安全测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.danger {
            background-color: #dc3545;
        }
        button.danger:hover {
            background-color: #c82333;
        }
        button.warning {
            background-color: #ffc107;
            color: #212529;
        }
        button.warning:hover {
            background-color: #e0a800;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 400px;
        }
        .status {
            font-weight: bold;
            margin: 10px 0;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .risk-analysis {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .safety-feature {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .dependency-info {
            background-color: #e2e3e5;
            border: 1px solid #d6d8db;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ 角色和技能组管理安全测试</h1>
        
        <div class="highlight">
            <h3>🎯 设计目标</h3>
            <ul>
                <li><strong>安全性</strong>：防止误删核心角色和技能组</li>
                <li><strong>完整性</strong>：保护数据库外键约束</li>
                <li><strong>连续性</strong>：确保业务流程不中断</li>
                <li><strong>可追溯</strong>：完整的操作审计日志</li>
            </ul>
        </div>

        <div class="risk-analysis">
            <h3>⚠️ 风险分析</h3>
            <h4>当前数据库风险：</h4>
            <ul>
                <li><strong>角色删除风险</strong>：user_roles 表有 ON DELETE CASCADE，删除角色会自动删除用户关联</li>
                <li><strong>技能组删除风险</strong>：
                    <ul>
                        <li>user_skills 表有 ON DELETE CASCADE</li>
                        <li>machines 表<strong>没有级联删除</strong>，会阻止删除</li>
                        <li>plan_tasks 表<strong>没有级联删除</strong>，会阻止删除</li>
                    </ul>
                </li>
                <li><strong>权限丢失风险</strong>：删除用户角色可能导致无法访问系统</li>
            </ul>
        </div>

        <div class="safety-feature">
            <h3>🛡️ 安全保护机制</h3>
            <ul>
                <li><strong>系统角色保护</strong>：admin、process_engineer等核心角色不可删除</li>
                <li><strong>依赖检查</strong>：删除前检查所有关联数据</li>
                <li><strong>替换机制</strong>：强制指定替换角色/技能组</li>
                <li><strong>确认流程</strong>：多步确认，防止误操作</li>
                <li><strong>软删除选项</strong>：标记为不活跃而非物理删除</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>1. 登录管理员账户</h3>
            <button onclick="testLogin()">登录</button>
            <div id="login-result"></div>
        </div>

        <div class="test-section">
            <h3>2. 获取角色和技能组数据</h3>
            <button onclick="testGetRoles()">获取角色列表</button>
            <button onclick="testGetSkillGroups()">获取技能组列表</button>
            <div id="roles-result"></div>
            <div id="skills-result"></div>
        </div>

        <div class="test-section">
            <h3>3. 角色依赖检查测试</h3>
            <p>测试删除角色前的安全检查：</p>
            <button onclick="testRoleDependencyCheck(1)" class="warning">检查admin角色依赖</button>
            <button onclick="testRoleDependencyCheck(4)" class="warning">检查operator角色依赖</button>
            <div id="role-dependency-result"></div>
        </div>

        <div class="test-section">
            <h3>4. 技能组依赖检查测试</h3>
            <p>测试删除技能组前的安全检查：</p>
            <button onclick="testSkillGroupDependencyCheck(1)" class="warning">检查CNC Machining依赖</button>
            <button onclick="testSkillGroupDependencyCheck(2)" class="warning">检查Milling依赖</button>
            <div id="skill-dependency-result"></div>
        </div>

        <div class="test-section">
            <h3>5. 安全删除模拟</h3>
            <p>模拟安全删除流程（不会真正删除）：</p>
            <button onclick="simulateRoleDeletion()" class="danger">模拟删除角色</button>
            <button onclick="simulateSkillGroupDeletion()" class="danger">模拟删除技能组</button>
            <div id="deletion-simulation-result"></div>
        </div>

        <div class="test-section">
            <h3>6. 前端界面测试</h3>
            <p>测试前端用户管理界面：</p>
            <button onclick="openUserManagement()">打开用户管理页面</button>
            <div class="info">
                <strong>测试步骤：</strong>
                <ol>
                    <li>访问用户管理页面</li>
                    <li>切换到"角色管理"标签</li>
                    <li>尝试编辑/删除系统角色（应该被禁用）</li>
                    <li>切换到"技能组管理"标签</li>
                    <li>尝试删除有依赖的技能组（应该显示警告）</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        let authToken = null;
        let roles = [];
        let skillGroups = [];
        const API_BASE = 'http://localhost:8080/api';

        async function apiCall(endpoint, options = {}) {
            const url = `${API_BASE}${endpoint}`;
            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    ...(authToken && { 'Authorization': `Bearer ${authToken}` })
                },
                ...options
            };

            try {
                const response = await fetch(url, config);
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            const isSuccess = result.success;
            
            element.className = `status ${isSuccess ? 'success' : 'error'}`;
            element.innerHTML = `
                <div><strong>状态:</strong> ${isSuccess ? '✅ 成功' : '❌ 失败'}</div>
                <pre>${JSON.stringify(result, null, 2)}</pre>
            `;
        }

        async function testLogin() {
            const result = await apiCall('/auth/login', {
                method: 'POST',
                body: JSON.stringify({
                    username: 'admin',
                    password: 'admin123'
                })
            });

            if (result.success && result.data.token) {
                authToken = result.data.token;
            }

            displayResult('login-result', result);
        }

        async function testGetRoles() {
            if (!authToken) {
                displayResult('roles-result', { success: false, error: '请先登录' });
                return;
            }

            const result = await apiCall('/auth/roles');
            
            if (result.success && result.data.roles) {
                roles = result.data.roles;
            }
            
            displayResult('roles-result', result);
        }

        async function testGetSkillGroups() {
            if (!authToken) {
                displayResult('skills-result', { success: false, error: '请先登录' });
                return;
            }

            const result = await apiCall('/auth/skill-groups');
            
            if (result.success && result.data.skill_groups) {
                skillGroups = result.data.skill_groups;
            }
            
            displayResult('skills-result', result);
        }

        async function testRoleDependencyCheck(roleId) {
            if (!authToken) {
                displayResult('role-dependency-result', { success: false, error: '请先登录' });
                return;
            }

            // 模拟依赖检查结果
            const mockDependencyInfo = {
                role_id: roleId,
                role_name: roleId === 1 ? 'admin' : 'operator',
                can_delete: roleId !== 1, // admin角色不能删除
                blocking_reason: roleId === 1 ? '系统核心角色，不可删除' : null,
                affected_users: [
                    { id: 1, username: 'admin', full_name: 'System Administrator' },
                    { id: 2, username: 'user1', full_name: 'Test User 1' }
                ],
                user_count: 2
            };

            const result = {
                success: true,
                data: mockDependencyInfo,
                message: '依赖检查完成'
            };

            const element = document.getElementById('role-dependency-result');
            element.className = 'status info';
            element.innerHTML = `
                <div><strong>角色依赖检查结果:</strong></div>
                <div class="dependency-info">
                    <strong>角色:</strong> ${mockDependencyInfo.role_name}<br>
                    <strong>可删除:</strong> ${mockDependencyInfo.can_delete ? '✅ 是' : '❌ 否'}<br>
                    ${mockDependencyInfo.blocking_reason ? `<strong>阻止原因:</strong> ${mockDependencyInfo.blocking_reason}<br>` : ''}
                    <strong>影响用户数:</strong> ${mockDependencyInfo.user_count}<br>
                    <strong>受影响用户:</strong>
                    <ul>
                        ${mockDependencyInfo.affected_users.map(user => 
                            `<li>${user.username} (${user.full_name || '无姓名'})</li>`
                        ).join('')}
                    </ul>
                </div>
            `;
        }

        async function testSkillGroupDependencyCheck(skillGroupId) {
            if (!authToken) {
                displayResult('skill-dependency-result', { success: false, error: '请先登录' });
                return;
            }

            // 模拟技能组依赖检查结果
            const mockDependencyInfo = {
                skill_group_id: skillGroupId,
                group_name: skillGroupId === 1 ? 'CNC Machining' : 'Milling',
                can_delete: false, // 有依赖，不能直接删除
                blocking_reason: '存在关联的设备和生产计划',
                affected_users: [
                    { id: 1, username: 'admin', full_name: 'System Administrator' }
                ],
                affected_machines: [
                    { id: 1, machine_name: 'CNC-001' },
                    { id: 2, machine_name: 'CNC-002' }
                ],
                affected_plan_tasks: [
                    { id: 1, work_order_id: 1 },
                    { id: 2, work_order_id: 2 }
                ],
                user_count: 1,
                machine_count: 2,
                plan_task_count: 2
            };

            const element = document.getElementById('skill-dependency-result');
            element.className = 'status warning';
            element.innerHTML = `
                <div><strong>技能组依赖检查结果:</strong></div>
                <div class="dependency-info">
                    <strong>技能组:</strong> ${mockDependencyInfo.group_name}<br>
                    <strong>可删除:</strong> ${mockDependencyInfo.can_delete ? '✅ 是' : '❌ 否'}<br>
                    <strong>阻止原因:</strong> ${mockDependencyInfo.blocking_reason}<br>
                    <strong>影响统计:</strong>
                    <ul>
                        <li>用户: ${mockDependencyInfo.user_count} 个</li>
                        <li>设备: ${mockDependencyInfo.machine_count} 台</li>
                        <li>生产计划: ${mockDependencyInfo.plan_task_count} 个</li>
                    </ul>
                    <strong>⚠️ 删除此技能组需要:</strong>
                    <ol>
                        <li>选择替换技能组</li>
                        <li>迁移所有用户技能</li>
                        <li>重新分配设备</li>
                        <li>更新生产计划</li>
                    </ol>
                </div>
            `;
        }

        function simulateRoleDeletion() {
            const element = document.getElementById('deletion-simulation-result');
            element.className = 'status info';
            element.innerHTML = `
                <div><strong>🔄 角色删除流程模拟:</strong></div>
                <div class="dependency-info">
                    <h4>步骤1: 依赖检查</h4>
                    <p>✅ 检查角色关联的用户数量</p>
                    <p>✅ 验证是否为系统核心角色</p>
                    
                    <h4>步骤2: 安全确认</h4>
                    <p>⚠️ 显示受影响用户列表</p>
                    <p>⚠️ 要求选择替换角色</p>
                    <p>⚠️ 用户确认操作风险</p>
                    
                    <h4>步骤3: 数据迁移</h4>
                    <p>🔄 将用户角色迁移到替换角色</p>
                    <p>🔄 更新权限缓存</p>
                    
                    <h4>步骤4: 审计记录</h4>
                    <p>📝 记录删除操作到审计日志</p>
                    <p>📝 记录受影响的用户</p>
                    
                    <p><strong>✅ 模拟完成 - 实际环境中会执行真实的数据库操作</strong></p>
                </div>
            `;
        }

        function simulateSkillGroupDeletion() {
            const element = document.getElementById('deletion-simulation-result');
            element.className = 'status warning';
            element.innerHTML = `
                <div><strong>🔄 技能组删除流程模拟:</strong></div>
                <div class="dependency-info">
                    <h4>步骤1: 全面依赖检查</h4>
                    <p>✅ 检查用户技能关联</p>
                    <p>✅ 检查设备分配</p>
                    <p>✅ 检查生产计划任务</p>
                    
                    <h4>步骤2: 风险评估</h4>
                    <p>⚠️ 评估对生产的影响</p>
                    <p>⚠️ 检查是否有进行中的任务</p>
                    <p>⚠️ 验证替换技能组的兼容性</p>
                    
                    <h4>步骤3: 数据迁移</h4>
                    <p>🔄 迁移用户技能到替换技能组</p>
                    <p>🔄 重新分配设备到替换技能组</p>
                    <p>🔄 更新生产计划任务</p>
                    
                    <h4>步骤4: 验证和回滚</h4>
                    <p>✅ 验证所有迁移操作成功</p>
                    <p>🔄 如有失败，自动回滚所有更改</p>
                    
                    <p><strong>⚠️ 高风险操作 - 建议在维护窗口期执行</strong></p>
                </div>
            `;
        }

        function openUserManagement() {
            window.open('http://localhost:3008/users', '_blank');
        }

        // 页面加载时显示说明
        window.onload = function() {
            console.log('角色和技能组管理安全测试页面已加载');
            console.log('这个页面演示了安全删除机制的设计和实现');
        };
    </script>
</body>
</html>
