<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技能组获取调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #f6ffed; border-color: #b7eb8f; }
        .error { background-color: #fff2f0; border-color: #ffccc7; }
        .warning { background-color: #fffbe6; border-color: #ffe58f; }
        .info { background-color: #f0f9ff; border-color: #91d5ff; }
        
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #40a9ff; }
        button:disabled { background-color: #d9d9d9; cursor: not-allowed; }
        
        .user-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #1890ff;
        }
        
        .skill-tag {
            display: inline-block;
            background: #e6f7ff;
            color: #1890ff;
            padding: 2px 8px;
            border-radius: 3px;
            margin: 2px;
            border: 1px solid #91d5ff;
        }
        
        .json-display {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: #fafafa;
            border-left: 3px solid #1890ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 MES系统技能组获取调试测试</h1>
        <p>此页面用于调试用户登录后技能组信息获取和显示的问题</p>
    </div>

    <div class="container">
        <h2>📋 测试步骤</h2>
        <div class="step">
            <strong>步骤1:</strong> 点击"测试登录"按钮，使用测试用户 m001 登录
        </div>
        <div class="step">
            <strong>步骤2:</strong> 检查登录响应中的用户信息和技能组数据
        </div>
        <div class="step">
            <strong>步骤3:</strong> 验证技能组信息是否正确显示
        </div>
        <div class="step">
            <strong>步骤4:</strong> 测试获取当前用户信息的API
        </div>
    </div>

    <div class="container">
        <h2>🧪 测试控制</h2>
        <button onclick="testLogin()">测试登录 (m001)</button>
        <button onclick="testCurrentUser()" id="currentUserBtn" disabled>获取当前用户信息</button>
        <button onclick="testSkillGroups()">获取所有技能组</button>
        <button onclick="clearResults()">清除结果</button>
    </div>

    <div id="results"></div>

    <script>
        let authToken = null;

        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `container test-section ${type}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <div>${content}</div>
                <small>时间: ${new Date().toLocaleString()}</small>
            `;
            resultsDiv.appendChild(resultDiv);
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function getSkillGroupDisplayName(groupName) {
            const skillGroupNameMap = {
                'CNC Machining': 'CNC加工',
                'Milling': '铣削加工',
                'Turning': '车削加工',
                'Grinding': '磨削加工',
                'Assembly': '装配',
                'Quality Control': '质量控制',
                'Packaging': '包装'
            };
            return skillGroupNameMap[groupName] || groupName;
        }

        async function testLogin() {
            try {
                addResult('🔐 开始登录测试', '正在使用用户 m001 进行登录...', 'info');
                
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'm001',
                        password: 'm00123'
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                authToken = data.token;
                
                // 启用获取当前用户信息按钮
                document.getElementById('currentUserBtn').disabled = false;

                let resultHtml = `
                    <div class="user-info">
                        <strong>✅ 登录成功!</strong><br>
                        <strong>用户ID:</strong> ${data.user.id}<br>
                        <strong>用户名:</strong> ${data.user.username}<br>
                        <strong>姓名:</strong> ${data.user.full_name || '未设置'}<br>
                        <strong>角色:</strong> ${data.user.roles.join(', ')}<br>
                        <strong>技能组:</strong> `;
                
                if (data.user.skills && data.user.skills.length > 0) {
                    data.user.skills.forEach(skill => {
                        resultHtml += `<span class="skill-tag">${getSkillGroupDisplayName(skill)}</span>`;
                    });
                } else {
                    resultHtml += '<span style="color: red;">❌ 无技能组数据</span>';
                }
                
                resultHtml += `<br><strong>状态:</strong> ${data.user.is_active ? '激活' : '未激活'}`;
                resultHtml += '</div>';
                
                resultHtml += '<div class="json-display"><strong>完整响应数据:</strong>\n' + JSON.stringify(data, null, 2) + '</div>';
                
                // 分析技能组数据
                resultHtml += '<div><strong>🔍 技能组数据分析:</strong><br>';
                resultHtml += `技能组数据类型: ${typeof data.user.skills}<br>`;
                resultHtml += `技能组数组长度: ${data.user.skills ? data.user.skills.length : 'undefined'}<br>`;
                resultHtml += `技能组是否为数组: ${Array.isArray(data.user.skills)}<br>`;
                if (data.user.skills && data.user.skills.length > 0) {
                    resultHtml += '技能组详情:<br>';
                    data.user.skills.forEach((skill, index) => {
                        resultHtml += `  [${index}] "${skill}" (${typeof skill})<br>`;
                    });
                }
                resultHtml += '</div>';

                addResult('🎉 登录测试结果', resultHtml, 'success');
                
            } catch (error) {
                addResult('❌ 登录测试失败', `错误信息: ${error.message}`, 'error');
            }
        }

        async function testCurrentUser() {
            if (!authToken) {
                addResult('⚠️ 获取当前用户信息失败', '请先登录获取认证令牌', 'warning');
                return;
            }

            try {
                addResult('👤 开始获取当前用户信息', '正在调用 /api/auth/me 接口...', 'info');
                
                const response = await fetch('/api/auth/me', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                let resultHtml = `
                    <div class="user-info">
                        <strong>✅ 获取用户信息成功!</strong><br>
                        <strong>用户ID:</strong> ${data.id}<br>
                        <strong>用户名:</strong> ${data.username}<br>
                        <strong>姓名:</strong> ${data.full_name || '未设置'}<br>
                        <strong>角色:</strong> ${data.roles.join(', ')}<br>
                        <strong>技能组:</strong> `;
                
                if (data.skills && data.skills.length > 0) {
                    data.skills.forEach(skill => {
                        resultHtml += `<span class="skill-tag">${getSkillGroupDisplayName(skill)}</span>`;
                    });
                } else {
                    resultHtml += '<span style="color: red;">❌ 无技能组数据</span>';
                }
                
                resultHtml += `<br><strong>状态:</strong> ${data.is_active ? '激活' : '未激活'}`;
                resultHtml += '</div>';
                
                resultHtml += '<div class="json-display"><strong>完整响应数据:</strong>\n' + JSON.stringify(data, null, 2) + '</div>';

                addResult('👤 当前用户信息', resultHtml, 'success');
                
            } catch (error) {
                addResult('❌ 获取当前用户信息失败', `错误信息: ${error.message}`, 'error');
            }
        }

        async function testSkillGroups() {
            try {
                addResult('🛠️ 开始获取技能组列表', '正在调用 /api/auth/skill-groups 接口...', 'info');
                
                const response = await fetch('/api/auth/skill-groups', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                let resultHtml = `<div class="user-info"><strong>✅ 获取技能组列表成功!</strong><br>`;
                resultHtml += `<strong>技能组总数:</strong> ${data.skill_groups.length}<br><br>`;
                
                resultHtml += '<strong>技能组列表:</strong><br>';
                data.skill_groups.forEach(skillGroup => {
                    resultHtml += `<span class="skill-tag">[${skillGroup.id}] ${getSkillGroupDisplayName(skillGroup.group_name)}</span>`;
                });
                resultHtml += '</div>';
                
                resultHtml += '<div class="json-display"><strong>完整响应数据:</strong>\n' + JSON.stringify(data, null, 2) + '</div>';

                addResult('🛠️ 技能组列表', resultHtml, 'success');
                
            } catch (error) {
                addResult('❌ 获取技能组列表失败', `错误信息: ${error.message}`, 'error');
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            authToken = null;
            document.getElementById('currentUserBtn').disabled = true;
        }

        // 页面加载时的初始化
        window.onload = function() {
            addResult('🚀 调试页面已加载', '请按照测试步骤进行操作', 'info');
        };
    </script>
</body>
</html>
