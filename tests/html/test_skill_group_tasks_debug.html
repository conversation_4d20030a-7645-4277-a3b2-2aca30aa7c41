<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技能组任务调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .user-section {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .user-card {
            flex: 1;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .login-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .login-btn:hover {
            background: #40a9ff;
        }
        .test-btn {
            background: #52c41a;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-btn:hover {
            background: #73d13d;
        }
        .result {
            background: #f6f6f6;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #a8071a;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
        }
        .info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #0050b3;
        }
        .current-user {
            background: #fff7e6;
            border: 1px solid #ffd591;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 技能组任务调试测试</h1>
        <p>测试不同用户的技能组绑定和任务获取情况</p>
        
        <div id="currentUser" class="current-user" style="display: none;">
            <strong>当前登录用户：</strong><span id="currentUserInfo"></span>
        </div>

        <div class="user-section">
            <div class="user-card">
                <h3>🔩 磨床用户 (g001)</h3>
                <p><strong>用户名:</strong> g001</p>
                <p><strong>密码:</strong> g00123</p>
                <p><strong>预期技能组:</strong> Grinding</p>
                <button class="login-btn" onclick="loginUser('g001', 'g00123')">登录</button>
            </div>
            
            <div class="user-card">
                <h3>🏭 CNC用户 (cn01)</h3>
                <p><strong>用户名:</strong> cn01</p>
                <p><strong>密码:</strong> cn0123</p>
                <p><strong>预期技能组:</strong> CNC Machining</p>
                <button class="login-btn" onclick="loginUser('cn01', 'cn0123')">登录</button>
            </div>
            
            <div class="user-card">
                <h3>⚙️ 测试用户 (m001)</h3>
                <p><strong>用户名:</strong> m001</p>
                <p><strong>密码:</strong> m00123</p>
                <p><strong>预期技能组:</strong> Milling</p>
                <button class="login-btn" onclick="loginUser('m001', 'm00123')">登录</button>
            </div>
        </div>

        <div class="container">
            <h2>🧪 API测试</h2>
            <button class="test-btn" onclick="testCurrentUser()">获取当前用户信息</button>
            <button class="test-btn" onclick="testMySkillGroupTasks()">获取我的技能组任务</button>
            <button class="test-btn" onclick="testUserSkills()">获取用户技能组绑定</button>
            <button class="test-btn" onclick="testPlanAssignmentMode()">获取计划分配模式</button>
            <button class="test-btn" onclick="testAllSkillGroups()">获取所有技能组</button>
            
            <div id="testResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        let currentToken = null;

        function log(message, type = 'info') {
            const resultDiv = document.getElementById('testResult');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            resultDiv.innerHTML = `<div class="${className}">[${timestamp}] ${message}</div>` + resultDiv.innerHTML;
        }

        async function loginUser(username, password) {
            try {
                log(`正在登录用户: ${username}...`);
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                if (!response.ok) {
                    throw new Error(`登录失败: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                currentToken = data.token;
                
                // 显示当前用户信息
                const userDiv = document.getElementById('currentUser');
                const userInfo = document.getElementById('currentUserInfo');
                userInfo.textContent = `${username} (Token: ${currentToken.substring(0, 20)}...)`;
                userDiv.style.display = 'block';
                
                log(`✅ 登录成功: ${username}`, 'success');
                log(`Token: ${currentToken.substring(0, 50)}...`, 'info');
                
                // 自动获取用户信息
                await testCurrentUser();
                
            } catch (error) {
                log(`❌ 登录失败: ${error.message}`, 'error');
            }
        }

        async function testCurrentUser() {
            if (!currentToken) {
                log('❌ 请先登录', 'error');
                return;
            }

            try {
                log('正在获取当前用户信息...');
                
                const response = await fetch(`${API_BASE}/auth/me`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`请求失败: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                log(`✅ 用户信息获取成功:\n${JSON.stringify(data, null, 2)}`, 'success');
                
            } catch (error) {
                log(`❌ 获取用户信息失败: ${error.message}`, 'error');
            }
        }

        async function testMySkillGroupTasks() {
            if (!currentToken) {
                log('❌ 请先登录', 'error');
                return;
            }

            try {
                log('正在获取我的技能组任务...');
                
                const response = await fetch(`${API_BASE}/execution/my-tasks`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`请求失败: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                log(`✅ 技能组任务获取成功:\n${JSON.stringify(data, null, 2)}`, 'success');
                
                // 分析任务数据
                if (data.tasks && Array.isArray(data.tasks)) {
                    log(`📊 任务分析: 共 ${data.tasks.length} 个任务`, 'info');
                    if (data.tasks.length > 0) {
                        const firstTask = data.tasks[0];
                        log(`📋 第一个任务的技能组: ${firstTask.skill_group_name || '未设置'}`, 'info');
                    }
                } else {
                    log(`⚠️ 数据格式异常: tasks字段不是数组`, 'error');
                }
                
            } catch (error) {
                log(`❌ 获取技能组任务失败: ${error.message}`, 'error');
            }
        }

        async function testUserSkills() {
            if (!currentToken) {
                log('❌ 请先登录', 'error');
                return;
            }

            try {
                log('正在获取用户技能组绑定...');
                
                const response = await fetch(`${API_BASE}/users/debug/skills`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`请求失败: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                log(`✅ 用户技能组绑定获取成功:\n${JSON.stringify(data, null, 2)}`, 'success');
                
            } catch (error) {
                log(`❌ 获取用户技能组绑定失败: ${error.message}`, 'error');
            }
        }

        async function testPlanAssignmentMode() {
            if (!currentToken) {
                log('❌ 请先登录', 'error');
                return;
            }

            try {
                log('正在获取计划分配模式...');
                
                const response = await fetch(`${API_BASE}/system/plan-assignment-mode`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`请求失败: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                log(`✅ 计划分配模式获取成功:\n${JSON.stringify(data, null, 2)}`, 'success');
                
            } catch (error) {
                log(`❌ 获取计划分配模式失败: ${error.message}`, 'error');
            }
        }

        async function testAllSkillGroups() {
            try {
                log('正在获取所有技能组...');
                
                const response = await fetch(`${API_BASE}/auth/skill-groups`);

                if (!response.ok) {
                    throw new Error(`请求失败: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                log(`✅ 所有技能组获取成功:\n${JSON.stringify(data, null, 2)}`, 'success');
                
            } catch (error) {
                log(`❌ 获取所有技能组失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时的初始化
        window.onload = function() {
            log('🚀 技能组任务调试测试页面已加载');
            log('请先选择一个用户登录，然后进行API测试');
        };
    </script>
</body>
</html>
