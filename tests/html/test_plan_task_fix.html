<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生产计划工艺步骤过滤测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            margin-top: 15px;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .step-item {
            padding: 8px;
            margin: 5px 0;
            background: #e9ecef;
            border-radius: 4px;
            border-left: 3px solid #007bff;
        }
        .step-number {
            font-weight: bold;
            color: #007bff;
        }
        .step-name {
            font-weight: bold;
        }
        .step-instructions {
            color: #666;
            font-size: 12px;
            margin-top: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 生产计划工艺步骤过滤测试</h1>
        <p>此页面用于测试选择工单后，工艺步骤是否正确过滤为该工单对应零件的工艺步骤。</p>
        
        <div class="form-group">
            <label for="workOrderSelect">选择工单：</label>
            <select id="workOrderSelect">
                <option value="">请选择工单</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="routingSelect">工艺步骤：</label>
            <select id="routingSelect" disabled>
                <option value="">请先选择工单</option>
            </select>
        </div>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>📊 数据概览</h2>
        <div id="dataOverview"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        let authToken = '';
        let workOrders = [];
        let routings = [];

        // 登录获取token
        async function login() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                authToken = data.token;
                return true;
            } catch (error) {
                console.error('登录失败:', error);
                return false;
            }
        }

        // 获取工单数据
        async function fetchWorkOrders() {
            try {
                const response = await fetch(`${API_BASE}/work-orders`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                const data = await response.json();
                workOrders = data.data.work_orders;
                return workOrders;
            } catch (error) {
                console.error('获取工单失败:', error);
                return [];
            }
        }

        // 获取工艺路由数据
        async function fetchRoutings() {
            try {
                const response = await fetch(`${API_BASE}/routings`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                const data = await response.json();
                routings = data.routings;
                return routings;
            } catch (error) {
                console.error('获取工艺路由失败:', error);
                return [];
            }
        }

        // 填充工单选择器
        function populateWorkOrderSelect() {
            const select = document.getElementById('workOrderSelect');
            select.innerHTML = '<option value="">请选择工单</option>';
            
            workOrders.forEach(wo => {
                const option = document.createElement('option');
                option.value = wo.id;
                option.textContent = `工单#${wo.id} - ${wo.project_name} - ${wo.part_number} (数量: ${wo.quantity})`;
                option.dataset.partId = wo.part_id;
                select.appendChild(option);
            });
        }

        // 过滤并填充工艺步骤选择器
        function populateRoutingSelect(partId) {
            const select = document.getElementById('routingSelect');
            const resultDiv = document.getElementById('result');
            
            if (!partId) {
                select.innerHTML = '<option value="">请先选择工单</option>';
                select.disabled = true;
                resultDiv.style.display = 'none';
                return;
            }

            // 过滤属于该零件的工艺步骤
            const filteredRoutings = routings.filter(routing => routing.part_id == partId);
            
            select.innerHTML = '<option value="">请选择工艺步骤</option>';
            select.disabled = false;
            
            if (filteredRoutings.length === 0) {
                select.innerHTML = '<option value="">该零件暂无工艺步骤</option>';
                select.disabled = true;
                showResult('该零件暂无工艺步骤，请先在工艺管理页面为该零件创建工艺路由。', 'error');
                return;
            }

            // 按步骤号排序
            filteredRoutings.sort((a, b) => a.step_number - b.step_number);
            
            filteredRoutings.forEach(routing => {
                const option = document.createElement('option');
                option.value = routing.id;
                option.textContent = `步骤${routing.step_number}: ${routing.process_name}`;
                if (routing.work_instructions) {
                    option.textContent += ` - ${routing.work_instructions.substring(0, 30)}${routing.work_instructions.length > 30 ? '...' : ''}`;
                }
                select.appendChild(option);
            });

            showResult(`✅ 成功过滤出 ${filteredRoutings.length} 个工艺步骤，只显示零件 ${filteredRoutings[0].part_number} 的工艺步骤。`, 'success');
        }

        // 显示结果
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            resultDiv.style.display = 'block';
        }

        // 显示数据概览
        function showDataOverview() {
            const overviewDiv = document.getElementById('dataOverview');
            
            // 按零件分组统计工艺步骤
            const partRoutingStats = {};
            routings.forEach(routing => {
                const partKey = `${routing.part_number} (ID: ${routing.part_id})`;
                if (!partRoutingStats[partKey]) {
                    partRoutingStats[partKey] = [];
                }
                partRoutingStats[partKey].push(routing);
            });

            let html = `
                <h3>📋 工单列表 (${workOrders.length} 个)</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 10px; margin-bottom: 20px;">
            `;
            
            workOrders.forEach(wo => {
                html += `
                    <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>工单#${wo.id}</strong><br>
                        项目: ${wo.project_name}<br>
                        零件: ${wo.part_number} (ID: ${wo.part_id})<br>
                        数量: ${wo.quantity}
                    </div>
                `;
            });
            
            html += `</div><h3>🔧 零件工艺步骤统计</h3>`;
            
            Object.entries(partRoutingStats).forEach(([partKey, steps]) => {
                html += `
                    <div style="margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                        <strong>${partKey}</strong> - ${steps.length} 个工艺步骤
                        <div style="margin-top: 8px;">
                `;
                
                steps.sort((a, b) => a.step_number - b.step_number).forEach(step => {
                    html += `
                        <div class="step-item">
                            <span class="step-number">步骤${step.step_number}:</span>
                            <span class="step-name">${step.process_name}</span>
                            ${step.work_instructions ? `<div class="step-instructions">${step.work_instructions}</div>` : ''}
                        </div>
                    `;
                });
                
                html += `</div></div>`;
            });
            
            overviewDiv.innerHTML = html;
        }

        // 事件监听器
        document.getElementById('workOrderSelect').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const partId = selectedOption.dataset.partId;
            populateRoutingSelect(partId);
        });

        // 初始化
        async function init() {
            const loginSuccess = await login();
            if (!loginSuccess) {
                showResult('登录失败，请检查后端服务是否正常运行。', 'error');
                return;
            }

            await Promise.all([fetchWorkOrders(), fetchRoutings()]);
            populateWorkOrderSelect();
            showDataOverview();
            
            showResult('数据加载完成，请选择工单测试工艺步骤过滤功能。', 'success');
        }

        init();
    </script>
</body>
</html>
