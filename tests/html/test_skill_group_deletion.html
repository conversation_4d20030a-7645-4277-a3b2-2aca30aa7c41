<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技能组删除测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .success { background-color: #4CAF50; color: white; }
        .danger { background-color: #f44336; color: white; }
        .info { background-color: #2196F3; color: white; }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
            background-color: #f5f5f5;
        }
    </style>
</head>
<body>
    <h1>技能组删除功能测试</h1>
    
    <div class="test-section">
        <h3>1. 创建测试技能组</h3>
        <button class="success" onclick="createTestSkillGroup()">创建测试技能组</button>
        <div id="create-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 获取技能组列表</h3>
        <button class="info" onclick="getSkillGroups()">获取技能组列表</button>
        <div id="list-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 测试删除系统技能组（应该失败）</h3>
        <button class="danger" onclick="deleteSystemSkillGroup()">删除系统技能组 (ID: 1)</button>
        <div id="delete-system-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>4. 测试删除自定义技能组（应该成功）</h3>
        <input type="number" id="custom-skill-group-id" placeholder="输入技能组ID" />
        <button class="danger" onclick="deleteCustomSkillGroup()">删除自定义技能组</button>
        <div id="delete-custom-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api';
        
        // 模拟的JWT token（实际应用中应该从localStorage或其他地方获取）
        const token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwidXNlcm5hbWUiOiJhZG1pbiIsInJvbGVzIjpbImFkbWluIl0sInNraWxscyI6WyJDTkMgTWFjaGluaW5nIiwiTWlsbGluZyIsIlR1cm5pbmciXSwiZXhwIjoxNzUyMDY5NjE2LCJpYXQiOjE3NTE5ODMyMTZ9.LkK1p4lNeZreKZpLVxmfk2--X39S9Tloc4jtVHRxAXE';

        async function apiCall(method, url, data = null) {
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            };
            
            if (data) {
                options.body = JSON.stringify(data);
            } else if (method === 'DELETE') {
                options.body = JSON.stringify({});
            }
            
            try {
                const response = await fetch(url, options);
                const result = await response.json();
                return { success: response.ok, data: result, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function createTestSkillGroup() {
            const result = await apiCall('POST', `${API_BASE}/skill-groups`, {
                group_name: `测试技能组_${Date.now()}`
            });
            
            const resultDiv = document.getElementById('create-result');
            if (result.success) {
                resultDiv.innerHTML = `<span style="color: green;">✓ 创建成功: ${JSON.stringify(result.data)}</span>`;
                document.getElementById('custom-skill-group-id').value = result.data.skill_group.id;
            } else {
                resultDiv.innerHTML = `<span style="color: red;">✗ 创建失败: ${JSON.stringify(result)}</span>`;
            }
        }

        async function getSkillGroups() {
            const result = await apiCall('GET', `${API_BASE}/skill-groups`);
            
            const resultDiv = document.getElementById('list-result');
            if (result.success) {
                const skillGroups = result.data.map(sg => `${sg.id}: ${sg.group_name}`).join('<br>');
                resultDiv.innerHTML = `<span style="color: green;">✓ 获取成功:<br>${skillGroups}</span>`;
            } else {
                resultDiv.innerHTML = `<span style="color: red;">✗ 获取失败: ${JSON.stringify(result)}</span>`;
            }
        }

        async function deleteSystemSkillGroup() {
            const result = await apiCall('DELETE', `${API_BASE}/skill-groups/1`);
            
            const resultDiv = document.getElementById('delete-system-result');
            if (result.success) {
                resultDiv.innerHTML = `<span style="color: orange;">⚠ 意外成功: ${JSON.stringify(result.data)}</span>`;
            } else {
                resultDiv.innerHTML = `<span style="color: green;">✓ 正确失败: ${JSON.stringify(result.data)}</span>`;
            }
        }

        async function deleteCustomSkillGroup() {
            const id = document.getElementById('custom-skill-group-id').value;
            if (!id) {
                alert('请输入技能组ID');
                return;
            }
            
            const result = await apiCall('DELETE', `${API_BASE}/skill-groups/${id}`);
            
            const resultDiv = document.getElementById('delete-custom-result');
            if (result.success) {
                resultDiv.innerHTML = `<span style="color: green;">✓ 删除成功: ${JSON.stringify(result.data)}</span>`;
            } else {
                resultDiv.innerHTML = `<span style="color: red;">✗ 删除失败: ${JSON.stringify(result)}</span>`;
            }
        }
    </script>
</body>
</html>
