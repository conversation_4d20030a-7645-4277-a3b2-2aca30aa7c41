<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户技能调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #40a9ff;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        input, select {
            padding: 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            margin: 5px;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>用户技能调试工具</h1>
    
    <div class="container">
        <h2>1. 登录</h2>
        <div class="form-group">
            <label>用户名:</label>
            <input type="text" id="username" value="admin" placeholder="输入用户名">
        </div>
        <div class="form-group">
            <label>密码:</label>
            <input type="password" id="password" value="admin123" placeholder="输入密码">
        </div>
        <button class="button" onclick="login()">登录</button>
        <div id="login-result"></div>
    </div>

    <div class="container">
        <h2>2. 获取所有用户</h2>
        <button class="button" onclick="getUsers()">获取用户列表</button>
        <div id="users-result"></div>
    </div>

    <div class="container">
        <h2>3. 获取技能组</h2>
        <button class="button" onclick="getSkillGroups()">获取技能组列表</button>
        <div id="skills-result"></div>
    </div>

    <div class="container">
        <h2>4. 更新用户技能</h2>
        <div class="form-group">
            <label>用户ID:</label>
            <input type="number" id="user-id" placeholder="输入用户ID">
        </div>
        <div class="form-group">
            <label>技能组ID:</label>
            <input type="text" id="skill-ids" placeholder="输入技能组ID，用逗号分隔，如: 1,2,3">
        </div>
        <button class="button" onclick="updateUserSkills()">更新用户技能</button>
        <div id="update-result"></div>
    </div>

    <div class="container">
        <h2>5. 检查特定用户</h2>
        <div class="form-group">
            <label>用户ID:</label>
            <input type="number" id="check-user-id" placeholder="输入用户ID">
        </div>
        <button class="button" onclick="checkUser()">检查用户详情</button>
        <div id="check-result"></div>
    </div>

    <div class="container">
        <h2>6. 数据库调试信息</h2>
        <button class="button" onclick="debugUserSkills()">获取数据库调试信息</button>
        <div id="debug-result"></div>
    </div>

    <script>
        let authToken = '';
        const API_BASE = 'http://localhost:8080/api';

        async function apiCall(endpoint, options = {}) {
            try {
                const url = `${API_BASE}${endpoint}`;
                const config = {
                    headers: {
                        'Content-Type': 'application/json',
                        ...(authToken && { 'Authorization': `Bearer ${authToken}` })
                    },
                    ...options
                };

                console.log('API调用:', url, config);
                const response = await fetch(url, config);
                const data = await response.json();

                if (response.ok) {
                    return { success: true, data };
                } else {
                    return { success: false, error: data.message || '请求失败' };
                }
            } catch (error) {
                console.error('API调用错误:', error);
                return { success: false, error: error.message };
            }
        }

        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            if (result.success) {
                element.className = 'success';
                element.innerHTML = `
                    <div><strong>成功:</strong></div>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                element.className = 'error';
                element.innerHTML = `
                    <div><strong>错误:</strong> ${result.error}</div>
                `;
            }
        }

        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            const result = await apiCall('/auth/login', {
                method: 'POST',
                body: JSON.stringify({ username, password })
            });

            if (result.success && result.data.token) {
                authToken = result.data.token;
            }

            displayResult('login-result', result);
        }

        async function getUsers() {
            const result = await apiCall('/users');
            displayResult('users-result', result);
        }

        async function getSkillGroups() {
            const result = await apiCall('/auth/skill-groups');
            displayResult('skills-result', result);
        }

        async function updateUserSkills() {
            const userId = document.getElementById('user-id').value;
            const skillIds = document.getElementById('skill-ids').value
                .split(',')
                .map(id => parseInt(id.trim()))
                .filter(id => !isNaN(id));

            if (!userId) {
                displayResult('update-result', { success: false, error: '请输入用户ID' });
                return;
            }

            const result = await apiCall(`/users/${userId}/skills`, {
                method: 'POST',
                body: JSON.stringify({ skill_group_ids: skillIds })
            });

            displayResult('update-result', result);
        }

        async function checkUser() {
            const userId = document.getElementById('check-user-id').value;

            if (!userId) {
                displayResult('check-result', { success: false, error: '请输入用户ID' });
                return;
            }

            const result = await apiCall(`/users/${userId}`);
            displayResult('check-result', result);
        }

        async function debugUserSkills() {
            const result = await apiCall('/users/debug/skills');
            displayResult('debug-result', result);
        }
    </script>
</body>
</html>
