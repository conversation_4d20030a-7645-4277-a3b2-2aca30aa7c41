<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; }
        input { width: 100%; padding: 8px; margin-bottom: 10px; }
        button { padding: 10px 20px; margin: 5px; }
        .result { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>MES系统登录测试</h1>
        
        <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" value="admin">
        </div>
        
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="admin123">
        </div>
        
        <div class="form-group">
            <button onclick="testLogin()">测试登录</button>
            <button onclick="testDirect()">直接测试后端</button>
            <button onclick="clearResults()">清除结果</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            addResult('开始测试前端代理登录...', 'info');
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    addResult('前端代理登录成功!', 'success');
                    addResult(`Token: ${data.token.substring(0, 50)}...`, 'success');
                    addResult(`User: ${JSON.stringify(data.user, null, 2)}`, 'success');
                } else {
                    addResult(`前端代理登录失败: ${response.status}`, 'error');
                    addResult(`错误信息: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                addResult(`前端代理登录异常: ${error.message}`, 'error');
                console.error('Login error:', error);
            }
        }
        
        async function testDirect() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            addResult('开始测试直接后端登录...', 'info');
            
            try {
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    addResult('直接后端登录成功!', 'success');
                    addResult(`Token: ${data.token.substring(0, 50)}...`, 'success');
                    addResult(`User: ${JSON.stringify(data.user, null, 2)}`, 'success');
                } else {
                    addResult(`直接后端登录失败: ${response.status}`, 'error');
                    addResult(`错误信息: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                addResult(`直接后端登录异常: ${error.message}`, 'error');
                console.error('Direct login error:', error);
            }
        }
        
        function addResult(message, type) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // 页面加载时显示当前信息
        window.onload = function() {
            addResult(`当前页面: ${window.location.href}`, 'info');
            addResult('准备测试登录功能...', 'info');
        };
    </script>
</body>
</html>
