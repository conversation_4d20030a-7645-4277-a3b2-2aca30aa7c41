-- 检查用户技能绑定的SQL查询

-- 1. 查看所有用户及其技能组
SELECT 
    u.id as user_id,
    u.username,
    u.full_name,
    sg.id as skill_group_id,
    sg.group_name as skill_group_name
FROM users u
LEFT JOIN user_skills us ON u.id = us.user_id
LEFT JOIN skill_groups sg ON us.skill_group_id = sg.id
ORDER BY u.id, sg.id;

-- 2. 查看没有技能组的用户
SELECT 
    u.id,
    u.username,
    u.full_name,
    u.is_active
FROM users u
LEFT JOIN user_skills us ON u.id = us.user_id
WHERE us.user_id IS NULL
ORDER BY u.id;

-- 3. 查看所有技能组
SELECT 
    id,
    group_name
FROM skill_groups
ORDER BY id;

-- 4. 查看user_skills表的所有记录
SELECT 
    us.user_id,
    us.skill_group_id,
    u.username,
    sg.group_name
FROM user_skills us
JOIN users u ON us.user_id = u.id
JOIN skill_groups sg ON us.skill_group_id = sg.id
ORDER BY us.user_id, us.skill_group_id;

-- 5. 统计信息
SELECT 
    'Total Users' as metric,
    COUNT(*) as count
FROM users
UNION ALL
SELECT 
    'Users with Skills' as metric,
    COUNT(DISTINCT us.user_id) as count
FROM user_skills us
UNION ALL
SELECT 
    'Users without Skills' as metric,
    COUNT(*) as count
FROM users u
LEFT JOIN user_skills us ON u.id = us.user_id
WHERE us.user_id IS NULL
UNION ALL
SELECT 
    'Total Skill Groups' as metric,
    COUNT(*) as count
FROM skill_groups;
