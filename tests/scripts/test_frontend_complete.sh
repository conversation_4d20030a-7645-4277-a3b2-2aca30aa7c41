#!/bin/bash

# 完整的前端功能测试脚本
# 模拟浏览器操作，测试所有前端功能

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 配置
FRONTEND_URL="http://localhost:3000"
BACKEND_URL="http://localhost:8080"
API_URL="${FRONTEND_URL}/api"

echo -e "${CYAN}🧪 MES系统前端完整功能测试${NC}"
echo "================================"

# 获取认证token
get_token() {
    echo -e "${BLUE}🔐 获取认证token...${NC}"
    
    login_response=$(curl -s -X POST -H "Content-Type: application/json" \
        -d '{"username": "admin", "password": "admin123"}' \
        "$API_URL/auth/login")
    
    if echo "$login_response" | grep -q "token"; then
        TOKEN=$(echo "$login_response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
        echo -e "${GREEN}✅ 认证成功${NC}"
        echo "Token: ${TOKEN:0:50}..."
        return 0
    else
        echo -e "${RED}❌ 认证失败${NC}"
        echo "响应: $login_response"
        return 1
    fi
}

# 测试仪表板数据
test_dashboard() {
    echo -e "\n${PURPLE}📊 测试仪表板功能${NC}"
    echo "--------------------------------"
    
    echo "获取仪表板概览..."
    dashboard_response=$(curl -s -H "Authorization: Bearer $TOKEN" "$API_URL/dashboard/overview")
    
    if echo "$dashboard_response" | grep -q "machine_status"; then
        echo -e "${GREEN}✅ 仪表板数据获取成功${NC}"
        
        # 解析关键数据
        echo "解析仪表板数据:"
        echo "$dashboard_response" | python3 -c "
import json, sys
data = json.load(sys.stdin)
print(f'  设备利用率: {data.get(\"machine_status\", {}).get(\"utilization_rate\", 0)}%')
print(f'  质量合格率: {data.get(\"quality_metrics\", {}).get(\"quality_rate\", 0)}%')
print(f'  总工单数: {data.get(\"production_summary\", {}).get(\"total_work_orders\", 0)}')
print(f'  进行中工单: {data.get(\"work_order_status\", {}).get(\"in_progress_orders\", 0)}')
"
    else
        echo -e "${RED}❌ 仪表板数据获取失败${NC}"
        echo "响应: $dashboard_response"
    fi
}

# 测试项目管理
test_projects() {
    echo -e "\n${PURPLE}📋 测试项目管理功能${NC}"
    echo "--------------------------------"
    
    # 获取项目列表
    echo "获取项目列表..."
    projects_response=$(curl -s -H "Authorization: Bearer $TOKEN" "$API_URL/projects")
    
    if echo "$projects_response" | grep -q "projects"; then
        project_count=$(echo "$projects_response" | python3 -c "
import json, sys
data = json.load(sys.stdin)
print(len(data.get('projects', [])))
")
        echo -e "${GREEN}✅ 项目列表获取成功，共 $project_count 个项目${NC}"
        
        # 显示项目详情
        echo "项目详情:"
        echo "$projects_response" | python3 -c "
import json, sys
data = json.load(sys.stdin)
for i, project in enumerate(data.get('projects', [])[:3]):
    print(f'  {i+1}. {project.get(\"project_name\", \"未知\")} (客户: {project.get(\"customer_name\", \"未指定\")})')
"
    else
        echo -e "${RED}❌ 项目列表获取失败${NC}"
    fi
    
    # 创建测试项目
    echo "创建测试项目..."
    create_response=$(curl -s -X POST -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d '{"project_name": "前端测试项目", "customer_name": "测试客户"}' \
        "$API_URL/projects")
    
    if echo "$create_response" | grep -q "id"; then
        echo -e "${GREEN}✅ 项目创建成功${NC}"
    else
        echo -e "${YELLOW}⚠️ 项目创建失败或已存在${NC}"
    fi
}

# 测试零件管理
test_parts() {
    echo -e "\n${PURPLE}🔧 测试零件管理功能${NC}"
    echo "--------------------------------"
    
    # 获取零件列表
    echo "获取零件列表..."
    parts_response=$(curl -s -H "Authorization: Bearer $TOKEN" "$API_URL/parts")
    
    if echo "$parts_response" | grep -q "parts"; then
        part_count=$(echo "$parts_response" | python3 -c "
import json, sys
data = json.load(sys.stdin)
print(len(data.get('parts', [])))
")
        echo -e "${GREEN}✅ 零件列表获取成功，共 $part_count 个零件${NC}"
        
        # 显示零件详情
        echo "零件详情:"
        echo "$parts_response" | python3 -c "
import json, sys
data = json.load(sys.stdin)
for i, part in enumerate(data.get('parts', [])[:3]):
    print(f'  {i+1}. {part.get(\"part_number\", \"未知\")} - {part.get(\"part_name\", \"未知\")} ({part.get(\"version\", \"未知\")})')
"
    else
        echo -e "${RED}❌ 零件列表获取失败${NC}"
    fi
}

# 测试设备管理
test_machines() {
    echo -e "\n${PURPLE}⚙️ 测试设备管理功能${NC}"
    echo "--------------------------------"
    
    # 获取设备列表
    echo "获取设备列表..."
    machines_response=$(curl -s -H "Authorization: Bearer $TOKEN" "$API_URL/machines")
    
    if echo "$machines_response" | grep -q "machines"; then
        machine_count=$(echo "$machines_response" | python3 -c "
import json, sys
data = json.load(sys.stdin)
print(len(data.get('machines', [])))
")
        echo -e "${GREEN}✅ 设备列表获取成功，共 $machine_count 台设备${NC}"
        
        # 显示设备详情
        echo "设备详情:"
        echo "$machines_response" | python3 -c "
import json, sys
data = json.load(sys.stdin)
for i, machine in enumerate(data.get('machines', [])[:3]):
    print(f'  {i+1}. {machine.get(\"machine_name\", \"未知\")} - {machine.get(\"status\", \"未知\")} ({machine.get(\"skill_group_name\", \"未知\")})')
"
    else
        echo -e "${RED}❌ 设备列表获取失败${NC}"
    fi
}

# 测试工单管理
test_work_orders() {
    echo -e "\n${PURPLE}📄 测试工单管理功能${NC}"
    echo "--------------------------------"
    
    # 获取工单列表
    echo "获取工单列表..."
    orders_response=$(curl -s -H "Authorization: Bearer $TOKEN" "$API_URL/work-orders")
    
    if echo "$orders_response" | grep -q "work_orders"; then
        order_count=$(echo "$orders_response" | python3 -c "
import json, sys
data = json.load(sys.stdin)
print(len(data.get('work_orders', [])))
")
        echo -e "${GREEN}✅ 工单列表获取成功，共 $order_count 个工单${NC}"
        
        if [ "$order_count" -gt 0 ]; then
            echo "工单详情:"
            echo "$orders_response" | python3 -c "
import json, sys
data = json.load(sys.stdin)
for i, order in enumerate(data.get('work_orders', [])[:3]):
    print(f'  {i+1}. 工单ID: {order.get(\"id\", \"未知\")} - 状态: {order.get(\"status\", \"未知\")} - 数量: {order.get(\"quantity\", 0)}')
"
        fi
    else
        echo -e "${RED}❌ 工单列表获取失败${NC}"
    fi
}

# 测试用户管理
test_users() {
    echo -e "\n${PURPLE}👥 测试用户管理功能${NC}"
    echo "--------------------------------"
    
    # 获取当前用户信息
    echo "获取当前用户信息..."
    user_response=$(curl -s -H "Authorization: Bearer $TOKEN" "$API_URL/auth/me")
    
    if echo "$user_response" | grep -q "username"; then
        echo -e "${GREEN}✅ 用户信息获取成功${NC}"
        echo "当前用户:"
        echo "$user_response" | python3 -c "
import json, sys
data = json.load(sys.stdin)
print(f'  用户名: {data.get(\"username\", \"未知\")}')
print(f'  全名: {data.get(\"full_name\", \"未指定\")}')
print(f'  角色: {data.get(\"roles\", [])}')
print(f'  技能: {data.get(\"skills\", [])}')
"
    else
        echo -e "${RED}❌ 用户信息获取失败${NC}"
    fi
}

# 生成前端访问报告
generate_frontend_report() {
    echo -e "\n${CYAN}📋 前端访问报告${NC}"
    echo "================================"
    
    echo -e "${BLUE}🌐 访问地址:${NC}"
    echo "  前端应用: $FRONTEND_URL"
    echo "  后端API: $BACKEND_URL"
    echo ""
    
    echo -e "${BLUE}🔑 登录信息:${NC}"
    echo "  用户名: admin"
    echo "  密码: admin123"
    echo ""
    
    echo -e "${BLUE}📱 可用页面:${NC}"
    echo "  仪表板: $FRONTEND_URL/dashboard"
    echo "  项目管理: $FRONTEND_URL/projects"
    echo "  零件管理: $FRONTEND_URL/parts"
    echo "  设备管理: $FRONTEND_URL/machines"
    echo "  工单管理: $FRONTEND_URL/work-orders"
    echo "  生产计划: $FRONTEND_URL/plan-tasks"
    echo "  执行跟踪: $FRONTEND_URL/execution"
    echo "  质量管理: $FRONTEND_URL/quality"
    echo "  BOM管理: $FRONTEND_URL/bom"
    echo "  用户管理: $FRONTEND_URL/users"
    echo "  API测试: $FRONTEND_URL/api-test"
    echo "  数据库查看: $FRONTEND_URL/database"
    echo "  API调试: $FRONTEND_URL/api-debug"
    echo ""
    
    echo -e "${BLUE}🔧 VSCode端口转发设置:${NC}"
    echo "  1. 在VSCode底部找到'PORTS'标签"
    echo "  2. 点击'Forward a Port'"
    echo "  3. 添加端口: 3000 (前端)"
    echo "  4. 添加端口: 8080 (后端)"
    echo "  5. 在本地浏览器访问 http://localhost:3000"
}

# 主函数
main() {
    # 检查服务状态
    if ! curl -s "$FRONTEND_URL" > /dev/null; then
        echo -e "${RED}❌ 前端服务未运行${NC}"
        exit 1
    fi
    
    if ! curl -s "$BACKEND_URL/health" > /dev/null; then
        echo -e "${RED}❌ 后端服务未运行${NC}"
        exit 1
    fi
    
    # 获取认证token
    if ! get_token; then
        exit 1
    fi
    
    # 运行所有测试
    test_dashboard
    test_projects
    test_parts
    test_machines
    test_work_orders
    test_users
    
    # 生成报告
    generate_frontend_report
    
    echo -e "\n${GREEN}🎉 前端功能测试完成！${NC}"
    echo -e "${YELLOW}💡 建议使用VSCode端口转发功能在本地浏览器中访问前端应用${NC}"
}

# 运行测试
main
