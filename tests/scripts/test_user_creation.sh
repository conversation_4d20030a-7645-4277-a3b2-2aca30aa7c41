#!/bin/bash

# 测试用户创建和技能组绑定

# 首先登录获取token
echo "=== 登录获取token ==="
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }')

echo "登录响应: $LOGIN_RESPONSE"

# 提取token (使用sed而不是jq)
TOKEN=$(echo $LOGIN_RESPONSE | sed -n 's/.*"token":"\([^"]*\)".*/\1/p')
echo "Token: $TOKEN"

if [ -z "$TOKEN" ]; then
  echo "登录失败，无法获取token"
  exit 1
fi

# 获取技能组列表
echo -e "\n=== 获取技能组列表 ==="
SKILL_GROUPS=$(curl -s -X GET http://localhost:8080/api/skill-groups \
  -H "Authorization: Bearer $TOKEN")
echo "技能组列表: $SKILL_GROUPS"

# 获取角色列表
echo -e "\n=== 获取角色列表 ==="
ROLES=$(curl -s -X GET http://localhost:8080/api/roles \
  -H "Authorization: Bearer $TOKEN")
echo "角色列表: $ROLES"

# 创建测试用户
echo -e "\n=== 创建测试用户 ==="
CREATE_RESPONSE=$(curl -s -X POST http://localhost:8080/api/auth/users \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "username": "test_operator",
    "password": "test123",
    "full_name": "测试操作员",
    "role_ids": [2],
    "skill_group_ids": [1, 2]
  }')

echo "创建用户响应: $CREATE_RESPONSE"

# 检查数据库中的用户技能绑定
echo -e "\n=== 检查数据库中的用户技能绑定 ==="
PGPASSWORD=password psql -h localhost -U postgres -d mes_db -c "SELECT u.id, u.username, u.full_name, sg.id as skill_group_id, sg.group_name FROM users u LEFT JOIN user_skills us ON u.id = us.user_id LEFT JOIN skill_groups sg ON us.skill_group_id = sg.id WHERE u.username = 'test_operator' ORDER BY u.id;"

# 测试新用户登录
echo -e "\n=== 测试新用户登录 ==="
TEST_LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "test_operator",
    "password": "test123"
  }')

echo "测试用户登录响应: $TEST_LOGIN_RESPONSE"

# 提取新用户的token和用户信息
TEST_TOKEN=$(echo $TEST_LOGIN_RESPONSE | sed -n 's/.*"token":"\([^"]*\)".*/\1/p')

echo "测试用户Token: $TEST_TOKEN"
echo "测试用户登录完整响应: $TEST_LOGIN_RESPONSE"

# 检查用户的技能组信息
echo -e "\n=== 检查用户技能组信息 ==="
echo "从登录响应中提取技能组信息..."
