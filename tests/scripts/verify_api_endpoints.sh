#!/bin/bash

# API Endpoint Verification Script for MES System
# This script checks the availability of key API endpoints

BASE_URL="http://localhost:8080"
API_BASE="$BASE_URL/api"

echo "🔍 MES System API Endpoint Verification"
echo "========================================"
echo "Base URL: $BASE_URL"
echo "API Base: $API_BASE"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to test endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local description=$3
    local auth_header=$4
    
    echo -n "Testing $method $endpoint ... "
    
    if [ -n "$auth_header" ]; then
        response=$(curl -s -w "%{http_code}" -X "$method" "$endpoint" -H "$auth_header" -o /dev/null)
    else
        response=$(curl -s -w "%{http_code}" -X "$method" "$endpoint" -o /dev/null)
    fi
    
    case $response in
        200|201)
            echo -e "${GREEN}✓ OK ($response)${NC}"
            ;;
        401)
            echo -e "${YELLOW}⚠ Auth Required ($response)${NC}"
            ;;
        404)
            echo -e "${RED}✗ Not Found ($response)${NC}"
            ;;
        500)
            echo -e "${RED}✗ Server Error ($response)${NC}"
            ;;
        000)
            echo -e "${RED}✗ Connection Failed${NC}"
            ;;
        *)
            echo -e "${YELLOW}? Unexpected ($response)${NC}"
            ;;
    esac
}

# Check if server is running
echo "🔌 Checking server connectivity..."
test_endpoint "GET" "$BASE_URL" "Root endpoint"
test_endpoint "GET" "$BASE_URL/health" "Health check"
echo ""

# Test public endpoints
echo "🌐 Testing public endpoints..."
test_endpoint "GET" "$API_BASE/auth/roles" "Get roles"
test_endpoint "GET" "$API_BASE/auth/skill-groups" "Get skill groups"
echo ""

# Test login endpoint
echo "🔐 Testing authentication..."
echo -n "Testing POST $API_BASE/auth/login ... "
login_response=$(curl -s -w "%{http_code}" -X POST "$API_BASE/auth/login" \
    -H "Content-Type: application/json" \
    -d '{"username":"admin","password":"admin123"}' \
    -o /tmp/login_response.json)

case $login_response in
    200)
        echo -e "${GREEN}✓ OK ($login_response)${NC}"
        # Extract token if login successful
        if command -v jq &> /dev/null; then
            TOKEN=$(jq -r '.token // .data.token // empty' /tmp/login_response.json 2>/dev/null)
            if [ -n "$TOKEN" ] && [ "$TOKEN" != "null" ]; then
                echo "  Token extracted successfully"
                AUTH_HEADER="Authorization: Bearer $TOKEN"
            else
                echo "  Could not extract token from response"
                AUTH_HEADER=""
            fi
        else
            echo "  jq not available, cannot extract token"
            AUTH_HEADER=""
        fi
        ;;
    401)
        echo -e "${YELLOW}⚠ Invalid credentials ($login_response)${NC}"
        AUTH_HEADER=""
        ;;
    *)
        echo -e "${RED}✗ Failed ($login_response)${NC}"
        AUTH_HEADER=""
        ;;
esac
echo ""

# Test protected endpoints (will show 401 if no token)
echo "🔒 Testing protected endpoints..."
test_endpoint "GET" "$API_BASE/auth/me" "Get current user" "$AUTH_HEADER"
test_endpoint "GET" "$API_BASE/users" "Get users" "$AUTH_HEADER"
test_endpoint "GET" "$API_BASE/projects" "Get projects" "$AUTH_HEADER"
test_endpoint "GET" "$API_BASE/parts" "Get parts" "$AUTH_HEADER"
test_endpoint "GET" "$API_BASE/machines" "Get machines" "$AUTH_HEADER"
test_endpoint "GET" "$API_BASE/work-orders" "Get work orders" "$AUTH_HEADER"
test_endpoint "GET" "$API_BASE/plan-tasks" "Get plan tasks" "$AUTH_HEADER"
test_endpoint "GET" "$API_BASE/dashboard/overview" "Get dashboard" "$AUTH_HEADER"
test_endpoint "GET" "$API_BASE/quality/inspections" "Get quality inspections" "$AUTH_HEADER"
test_endpoint "GET" "$API_BASE/execution/dashboard" "Get execution dashboard" "$AUTH_HEADER"
echo ""

# Test some POST endpoints
echo "📝 Testing POST endpoints..."
test_endpoint "POST" "$API_BASE/projects" "Create project" "$AUTH_HEADER"
test_endpoint "POST" "$API_BASE/parts" "Create part" "$AUTH_HEADER"
test_endpoint "POST" "$API_BASE/work-orders" "Create work order" "$AUTH_HEADER"
echo ""

# Summary
echo "📊 Verification Summary"
echo "======================="
echo "• Public endpoints should return 200 OK"
echo "• Protected endpoints should return 200 OK with valid token, 401 without"
echo "• POST endpoints may return 400 (validation error) with empty body"
echo "• 404 errors indicate missing endpoints"
echo "• Connection failures indicate server is not running"
echo ""
echo "💡 To start the server: cargo run"
echo "📚 See API_DOCUMENTATION.md for complete endpoint details"

# Cleanup
rm -f /tmp/login_response.json
