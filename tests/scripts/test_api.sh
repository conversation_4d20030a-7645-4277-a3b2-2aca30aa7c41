#!/bin/bash

# MES System API Test Script
# This script tests the main API endpoints to ensure they are working correctly

BASE_URL="http://localhost:8080/api"
TOKEN=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✓ $2${NC}"
    else
        echo -e "${RED}✗ $2${NC}"
    fi
}

print_info() {
    echo -e "${YELLOW}ℹ $1${NC}"
}

# Function to test API endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=$4
    local description=$5
    
    print_info "Testing: $description"
    
    if [ -n "$data" ]; then
        if [ -n "$TOKEN" ]; then
            response=$(curl -s -w "%{http_code}" -X $method "$BASE_URL$endpoint" \
                -H "Authorization: Bearer $TOKEN" \
                -H "Content-Type: application/json" \
                -d "$data")
        else
            response=$(curl -s -w "%{http_code}" -X $method "$BASE_URL$endpoint" \
                -H "Content-Type: application/json" \
                -d "$data")
        fi
    else
        if [ -n "$TOKEN" ]; then
            response=$(curl -s -w "%{http_code}" -X $method "$BASE_URL$endpoint" \
                -H "Authorization: Bearer $TOKEN")
        else
            response=$(curl -s -w "%{http_code}" -X $method "$BASE_URL$endpoint")
        fi
    fi
    
    status_code="${response: -3}"
    body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        print_status 0 "$description - Status: $status_code"
        return 0
    else
        print_status 1 "$description - Expected: $expected_status, Got: $status_code"
        echo "Response: $body"
        return 1
    fi
}

echo "=== MES System API Test Suite ==="
echo

# Test 1: Health check (if available)
print_info "Starting API tests..."

# Test 2: Login
print_info "Testing Authentication..."
login_data='{"username": "admin", "password": "admin123"}'
login_response=$(curl -s -w "%{http_code}" -X POST "$BASE_URL/auth/login" \
    -H "Content-Type: application/json" \
    -d "$login_data")

login_status="${login_response: -3}"
login_body="${login_response%???}"

if [ "$login_status" = "200" ]; then
    print_status 0 "Login successful"
    # Extract token from response (assuming it's in JSON format)
    TOKEN=$(echo "$login_body" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    if [ -n "$TOKEN" ]; then
        print_info "Token extracted successfully"
    else
        print_info "Warning: Could not extract token from response"
    fi
else
    print_status 1 "Login failed - Status: $login_status"
    echo "Response: $login_body"
    print_info "Continuing tests without authentication..."
fi

echo

# Test 3: User Management
print_info "Testing User Management..."
test_endpoint "GET" "/users" "" "200" "Get all users"

echo

# Test 4: Project Management
print_info "Testing Project Management..."
test_endpoint "GET" "/projects" "" "200" "Get all projects"

# Create a test project
project_data='{"project_name": "Test Project", "description": "API Test Project", "customer_name": "Test Customer"}'
test_endpoint "POST" "/projects" "$project_data" "200" "Create test project"

echo

# Test 5: Parts Management
print_info "Testing Parts Management..."
test_endpoint "GET" "/parts" "" "200" "Get all parts"

# Create a test part
part_data='{"part_number": "TEST-001", "part_name": "Test Part", "description": "API Test Part", "version": "1.0"}'
test_endpoint "POST" "/parts" "$part_data" "200" "Create test part"

echo

# Test 6: Machines Management
print_info "Testing Machines Management..."
test_endpoint "GET" "/machines" "" "200" "Get all machines"

echo

# Test 7: Skill Groups Management
print_info "Testing Skill Groups Management..."
test_endpoint "GET" "/skill-groups" "" "200" "Get all skill groups"

echo

# Test 8: Work Orders Management
print_info "Testing Work Orders Management..."
test_endpoint "GET" "/work-orders" "" "200" "Get all work orders"

echo

# Test 9: Production Planning
print_info "Testing Production Planning..."
test_endpoint "GET" "/planning/plan-tasks" "" "200" "Get all plan tasks"
test_endpoint "GET" "/planning/gantt?start_date=2025-07-01T00:00:00Z&end_date=2025-07-31T23:59:59Z" "" "200" "Get Gantt chart data"

echo

# Test 10: Execution Tracking
print_info "Testing Execution Tracking..."
test_endpoint "GET" "/execution/logs" "" "200" "Get execution logs"
test_endpoint "GET" "/execution/tasks/active" "" "200" "Get active tasks"
test_endpoint "GET" "/execution/dashboard" "" "200" "Get shop floor dashboard"

echo

# Test 11: Dashboard and Reporting
print_info "Testing Dashboard and Reporting..."
test_endpoint "GET" "/dashboard/overview" "" "200" "Get dashboard overview"
test_endpoint "GET" "/dashboard/production-summary" "" "200" "Get production summary"
test_endpoint "GET" "/dashboard/kpi-metrics" "" "200" "Get KPI metrics"

echo

# Test 12: Quality Management
print_info "Testing Quality Management..."
test_endpoint "GET" "/quality/inspections" "" "200" "Get quality inspections"
test_endpoint "GET" "/quality/metrics" "" "200" "Get quality metrics"
test_endpoint "GET" "/quality/pending" "" "200" "Get pending inspections"

echo

# Test 13: Audit Logging (Admin only)
if [ -n "$TOKEN" ]; then
    print_info "Testing Audit Logging..."
    test_endpoint "GET" "/audit/logs" "" "200" "Get audit logs"
    test_endpoint "GET" "/audit/statistics" "" "200" "Get audit statistics"
else
    print_info "Skipping audit tests (no authentication token)"
fi

echo

# Test 14: Error handling
print_info "Testing Error Handling..."
test_endpoint "GET" "/nonexistent-endpoint" "" "404" "Non-existent endpoint"
test_endpoint "GET" "/users/99999" "" "404" "Non-existent user"

echo

print_info "=== Test Summary ==="
print_info "API test suite completed!"
print_info "Check the results above for any failed tests."
print_info ""
print_info "To run individual tests:"
print_info "1. Start the MES server: cargo run"
print_info "2. Run this script: ./test_api.sh"
print_info "3. Check the server logs for any errors"

echo
