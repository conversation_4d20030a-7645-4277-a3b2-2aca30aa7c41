<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MES系统前端功能测试指南</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .test-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #17a2b8;
        }
        .checklist {
            list-style-type: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .checklist li:before {
            content: "☐ ";
            font-size: 18px;
            color: #3498db;
        }
        .url-box {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 16px;
            margin: 10px 0;
            border: 1px solid #ced4da;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .feature-card h4 {
            color: #495057;
            margin-top: 0;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            font-weight: bold;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn.success { background-color: #28a745; }
        .btn.warning { background-color: #ffc107; color: #212529; }
        .btn.danger { background-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏭 MES系统前端功能测试指南</h1>
        
        <div class="status success">
            ✅ 前端服务器运行正常 - http://localhost:3000
        </div>
        <div class="status success">
            ✅ 后端API服务器运行正常 - http://localhost:8080
        </div>
        <div class="status warning">
            ⚠️ 需要手动测试 - Browser MCP扩展需要安装
        </div>

        <h2>🚀 快速开始</h2>
        <div class="url-box">
            <strong>主应用地址:</strong> <a href="http://localhost:3000" target="_blank">http://localhost:3000</a><br>
            <strong>网络访问:</strong> <a href="http://************:3000" target="_blank">http://************:3000</a>
        </div>

        <h2>📋 系统功能测试清单</h2>

        <div class="feature-grid">
            <div class="feature-card">
                <h4>🔐 用户认证</h4>
                <ul class="checklist">
                    <li>访问登录页面</li>
                    <li>测试有效用户登录</li>
                    <li>测试无效凭据</li>
                    <li>测试登出功能</li>
                    <li>验证会话管理</li>
                </ul>
                <a href="http://localhost:3000/login" class="btn" target="_blank">测试登录</a>
            </div>

            <div class="feature-card">
                <h4>📊 仪表板</h4>
                <ul class="checklist">
                    <li>查看主仪表板</li>
                    <li>验证数据图表</li>
                    <li>检查实时更新</li>
                    <li>测试响应式布局</li>
                </ul>
                <a href="http://localhost:3000/dashboard" class="btn" target="_blank">查看仪表板</a>
            </div>

            <div class="feature-card">
                <h4>🏭 生产管理</h4>
                <ul class="checklist">
                    <li>查看生产订单列表</li>
                    <li>创建新生产订单</li>
                    <li>编辑订单信息</li>
                    <li>更新订单状态</li>
                    <li>删除订单</li>
                </ul>
                <a href="http://localhost:3000/work-orders" class="btn" target="_blank">生产订单</a>
            </div>

            <div class="feature-card">
                <h4>🔧 设备管理</h4>
                <ul class="checklist">
                    <li>查看设备列表</li>
                    <li>监控设备状态</li>
                    <li>记录维护信息</li>
                    <li>设备配置管理</li>
                </ul>
                <a href="http://localhost:3000/machines" class="btn" target="_blank">设备管理</a>
            </div>

            <div class="feature-card">
                <h4>✅ 质量管理</h4>
                <ul class="checklist">
                    <li>质量检查记录</li>
                    <li>缺陷管理</li>
                    <li>质量报告</li>
                    <li>统计分析</li>
                </ul>
                <a href="http://localhost:3000/quality" class="btn" target="_blank">质量管理</a>
            </div>

            <div class="feature-card">
                <h4>📦 库存管理</h4>
                <ul class="checklist">
                    <li>物料列表查看</li>
                    <li>库存查询</li>
                    <li>入库操作</li>
                    <li>出库操作</li>
                </ul>
                <a href="http://localhost:3000/parts" class="btn" target="_blank">库存管理</a>
            </div>

            <div class="feature-card">
                <h4>👥 用户管理</h4>
                <ul class="checklist">
                    <li>用户列表</li>
                    <li>创建用户</li>
                    <li>权限管理</li>
                    <li>角色分配</li>
                </ul>
                <a href="http://localhost:3000/users" class="btn" target="_blank">用户管理</a>
            </div>

            <div class="feature-card">
                <h4>📋 项目管理</h4>
                <ul class="checklist">
                    <li>项目列表</li>
                    <li>项目创建</li>
                    <li>进度跟踪</li>
                    <li>甘特图显示</li>
                </ul>
                <a href="http://localhost:3000/projects" class="btn" target="_blank">项目管理</a>
            </div>
        </div>

        <h2>🧪 详细测试步骤</h2>

        <div class="test-section">
            <h3>1. 登录功能测试</h3>
            <ol>
                <li>访问 <code>http://localhost:3000</code></li>
                <li>如果未登录，应自动跳转到登录页面</li>
                <li>尝试使用测试账户登录：
                    <ul>
                        <li>用户名: admin</li>
                        <li>密码: admin123</li>
                    </ul>
                </li>
                <li>验证登录成功后跳转到仪表板</li>
                <li>测试登出功能</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>2. 数据操作测试</h3>
            <ol>
                <li>在每个模块中测试CRUD操作：
                    <ul>
                        <li><strong>Create</strong>: 创建新记录</li>
                        <li><strong>Read</strong>: 查看记录列表和详情</li>
                        <li><strong>Update</strong>: 编辑现有记录</li>
                        <li><strong>Delete</strong>: 删除记录</li>
                    </ul>
                </li>
                <li>验证表单验证功能</li>
                <li>检查错误处理</li>
                <li>确认数据持久化</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>3. 响应式设计测试</h3>
            <ol>
                <li>在不同屏幕尺寸下测试：
                    <ul>
                        <li>桌面 (1920x1080)</li>
                        <li>平板 (768x1024)</li>
                        <li>手机 (375x667)</li>
                    </ul>
                </li>
                <li>验证导航菜单适配</li>
                <li>检查表格和图表的响应式行为</li>
            </ol>
        </div>

        <h2>🔍 调试工具</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <h4>🛠️ 开发者工具</h4>
                <ul>
                    <li>按F12打开开发者工具</li>
                    <li>查看Console错误</li>
                    <li>检查Network请求</li>
                    <li>使用Elements检查DOM</li>
                </ul>
            </div>

            <div class="feature-card">
                <h4>📡 API测试</h4>
                <ul>
                    <li>API基础地址: http://localhost:8080</li>
                    <li>查看Network标签页的API调用</li>
                    <li>验证请求和响应数据</li>
                    <li>检查错误状态码</li>
                </ul>
            </div>
        </div>

        <h2>📝 测试报告模板</h2>
        <div class="test-section">
            <h3>功能测试结果记录</h3>
            <table style="width: 100%; border-collapse: collapse; margin-top: 15px;">
                <thead>
                    <tr style="background-color: #f8f9fa;">
                        <th style="border: 1px solid #dee2e6; padding: 12px;">功能模块</th>
                        <th style="border: 1px solid #dee2e6; padding: 12px;">测试状态</th>
                        <th style="border: 1px solid #dee2e6; padding: 12px;">发现问题</th>
                        <th style="border: 1px solid #dee2e6; padding: 12px;">备注</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="border: 1px solid #dee2e6; padding: 12px;">用户登录</td>
                        <td style="border: 1px solid #dee2e6; padding: 12px;">☐ 通过 ☐ 失败</td>
                        <td style="border: 1px solid #dee2e6; padding: 12px;"></td>
                        <td style="border: 1px solid #dee2e6; padding: 12px;"></td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #dee2e6; padding: 12px;">仪表板</td>
                        <td style="border: 1px solid #dee2e6; padding: 12px;">☐ 通过 ☐ 失败</td>
                        <td style="border: 1px solid #dee2e6; padding: 12px;"></td>
                        <td style="border: 1px solid #dee2e6; padding: 12px;"></td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #dee2e6; padding: 12px;">生产管理</td>
                        <td style="border: 1px solid #dee2e6; padding: 12px;">☐ 通过 ☐ 失败</td>
                        <td style="border: 1px solid #dee2e6; padding: 12px;"></td>
                        <td style="border: 1px solid #dee2e6; padding: 12px;"></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 20px; background-color: #e9ecef; border-radius: 8px;">
            <h3>🎯 开始测试</h3>
            <p>点击上方的功能按钮开始测试各个模块，或直接访问主应用开始全面测试。</p>
            <a href="http://localhost:3000" class="btn success" target="_blank" style="font-size: 18px; padding: 15px 30px;">
                🚀 打开MES系统
            </a>
        </div>
    </div>

    <script>
        // 自动检查服务器状态
        function checkServerStatus() {
            fetch('http://localhost:3000')
                .then(response => {
                    if (response.ok) {
                        console.log('前端服务器正常');
                    }
                })
                .catch(error => {
                    console.warn('前端服务器连接失败:', error);
                });

            fetch('http://localhost:8080/api/health')
                .then(response => {
                    if (response.ok) {
                        console.log('后端API服务器正常');
                    }
                })
                .catch(error => {
                    console.warn('后端API服务器连接失败:', error);
                });
        }

        // 页面加载时检查服务器状态
        window.addEventListener('load', checkServerStatus);
    </script>
</body>
</html>
