<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MES系统前端状态</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1890ff;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 10px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .status-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #52c41a;
        }
        .status-card.warning {
            border-left-color: #faad14;
        }
        .status-card.error {
            border-left-color: #f5222d;
        }
        .status-card h3 {
            margin-top: 0;
            color: #333;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .data-table th {
            background-color: #1890ff;
            color: white;
        }
        .data-table tr:hover {
            background-color: #f5f5f5;
        }
        .link-list {
            list-style: none;
            padding: 0;
        }
        .link-list li {
            margin: 8px 0;
        }
        .link-list a {
            color: #1890ff;
            text-decoration: none;
            padding: 8px 12px;
            background: #e6f7ff;
            border-radius: 4px;
            display: inline-block;
            transition: background-color 0.3s;
        }
        .link-list a:hover {
            background: #bae7ff;
        }
        .success { color: #52c41a; font-weight: bold; }
        .warning { color: #faad14; font-weight: bold; }
        .error { color: #f5222d; font-weight: bold; }
        .code {
            background: #f6f8fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        .instructions {
            background: #e6f7ff;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
            border: 1px solid #91d5ff;
        }
        .instructions h3 {
            margin-top: 0;
            color: #1890ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏭 MES系统前端状态报告</h1>
        
        <div class="instructions">
            <h3>🔧 VSCode端口转发设置</h3>
            <ol>
                <li>在VSCode底部找到 <span class="code">PORTS</span> 标签</li>
                <li>点击 <span class="code">Forward a Port</span></li>
                <li>添加端口 <span class="code">3000</span> (前端)</li>
                <li>添加端口 <span class="code">8080</span> (后端)</li>
                <li>在本地浏览器访问 <span class="code">http://localhost:3000</span></li>
            </ol>
        </div>

        <div class="status-grid">
            <div class="status-card">
                <h3>✅ 系统状态</h3>
                <p><strong>前端服务:</strong> <span class="success">运行正常</span></p>
                <p><strong>后端服务:</strong> <span class="success">运行正常</span></p>
                <p><strong>数据库:</strong> <span class="success">连接正常</span></p>
                <p><strong>认证系统:</strong> <span class="success">工作正常</span></p>
            </div>

            <div class="status-card">
                <h3>📊 数据统计</h3>
                <p><strong>项目数量:</strong> 4 个</p>
                <p><strong>零件数量:</strong> 3 个</p>
                <p><strong>设备数量:</strong> 2 台</p>
                <p><strong>工单数量:</strong> 1 个</p>
            </div>

            <div class="status-card">
                <h3>🎯 仪表板数据</h3>
                <p><strong>设备利用率:</strong> 50.0%</p>
                <p><strong>质量合格率:</strong> 95.0%</p>
                <p><strong>总工单数:</strong> 1</p>
                <p><strong>进行中工单:</strong> 0</p>
            </div>

            <div class="status-card">
                <h3>🔑 登录信息</h3>
                <p><strong>用户名:</strong> <span class="code">admin</span></p>
                <p><strong>密码:</strong> <span class="code">admin123</span></p>
                <p><strong>角色:</strong> 系统管理员</p>
                <p><strong>权限:</strong> 全部功能</p>
            </div>
        </div>

        <h2>🌐 可用页面</h2>
        <div class="status-grid">
            <div class="status-card">
                <h3>核心功能</h3>
                <ul class="link-list">
                    <li><a href="http://localhost:3008/dashboard">📊 仪表板</a></li>
                    <li><a href="http://localhost:3008/projects">📋 项目管理</a></li>
                    <li><a href="http://localhost:3008/parts">🔧 零件管理</a></li>
                    <li><a href="http://localhost:3008/machines">⚙️ 设备管理</a></li>
                    <li><a href="http://localhost:3008/work-orders">📄 工单管理</a></li>
                </ul>
            </div>

            <div class="status-card">
                <h3>生产管理</h3>
                <ul class="link-list">
                    <li><a href="http://localhost:3008/plan-tasks">📅 生产计划</a></li>
                    <li><a href="http://localhost:3008/execution">▶️ 执行跟踪</a></li>
                    <li><a href="http://localhost:3008/quality">🔍 质量管理</a></li>
                    <li><a href="http://localhost:3008/bom">📋 BOM管理</a></li>
                    <li><a href="http://localhost:3008/routings">⚙️ 工艺管理</a></li>
                </ul>
            </div>

            <div class="status-card">
                <h3>系统管理</h3>
                <ul class="link-list">
                    <li><a href="http://localhost:3008/users">👥 用户管理</a></li>
                    <li><a href="http://localhost:3000/api-test">🧪 API测试</a></li>
                    <li><a href="http://localhost:3000/database">🗄️ 数据库查看</a></li>
                    <li><a href="http://localhost:3000/api-debug">🔧 API调试</a></li>
                </ul>
            </div>
        </div>

        <h2>📋 数据详情</h2>
        
        <h3>项目列表</h3>
        <table class="data-table">
            <thead>
                <tr>
                    <th>项目名称</th>
                    <th>客户名称</th>
                    <th>状态</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>士大夫</td>
                    <td>未指定</td>
                    <td><span class="success">活跃</span></td>
                </tr>
                <tr>
                    <td>测试项目001</td>
                    <td>测试客户</td>
                    <td><span class="success">活跃</span></td>
                </tr>
                <tr>
                    <td>CN0125111</td>
                    <td>dsa</td>
                    <td><span class="success">活跃</span></td>
                </tr>
                <tr>
                    <td>前端测试项目</td>
                    <td>测试客户</td>
                    <td><span class="success">活跃</span></td>
                </tr>
            </tbody>
        </table>

        <h3>零件列表</h3>
        <table class="data-table">
            <thead>
                <tr>
                    <th>零件编号</th>
                    <th>零件名称</th>
                    <th>版本</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>PART-001</td>
                    <td>Base Plate</td>
                    <td>v1.0</td>
                </tr>
                <tr>
                    <td>PART-002</td>
                    <td>Cover</td>
                    <td>v1.0</td>
                </tr>
                <tr>
                    <td>PART-003</td>
                    <td>测试零件</td>
                    <td>v1.0</td>
                </tr>
            </tbody>
        </table>

        <h3>设备列表</h3>
        <table class="data-table">
            <thead>
                <tr>
                    <th>设备名称</th>
                    <th>状态</th>
                    <th>技能组</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>CNC-001</td>
                    <td><span class="warning">使用中</span></td>
                    <td>CNC Machining</td>
                </tr>
                <tr>
                    <td>Mill-002</td>
                    <td><span class="success">可用</span></td>
                    <td>Milling</td>
                </tr>
            </tbody>
        </table>

        <h2>🚀 下一步操作</h2>
        <div class="status-card">
            <h3>建议的测试流程</h3>
            <ol>
                <li><strong>设置端口转发:</strong> 在VSCode中转发端口3000和8080</li>
                <li><strong>访问前端:</strong> 在浏览器中打开 http://localhost:3000</li>
                <li><strong>登录系统:</strong> 使用 admin/admin123 登录</li>
                <li><strong>测试仪表板:</strong> 查看数据是否正确显示</li>
                <li><strong>测试各功能:</strong> 依次访问各个管理页面</li>
                <li><strong>添加数据:</strong> 使用API测试页面添加更多测试数据</li>
                <li><strong>验证功能:</strong> 测试CRUD操作和数据同步</li>
            </ol>
        </div>

        <div class="status-card warning">
            <h3>⚠️ 注意事项</h3>
            <ul>
                <li>确保VSCode的Remote SSH扩展已安装</li>
                <li>端口转发可能需要几秒钟生效</li>
                <li>如果无法访问，检查防火墙设置</li>
                <li>建议使用Chrome或Firefox浏览器</li>
            </ul>
        </div>

        <p style="text-align: center; margin-top: 40px; color: #666;">
            <strong>MES系统前端已就绪，所有功能正常运行！</strong><br>
            生成时间: <span id="timestamp"></span>
        </p>
    </div>

    <script>
        document.getElementById('timestamp').textContent = new Date().toLocaleString('zh-CN');
    </script>
</body>
</html>
