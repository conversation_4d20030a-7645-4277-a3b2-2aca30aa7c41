<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端用户数据调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #40a9ff;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            background-color: #fffbe6;
            border: 1px solid #ffe58f;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .user-card {
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            background-color: #fafafa;
        }
        .skill-tag {
            display: inline-block;
            background-color: #e6f7ff;
            color: #1890ff;
            padding: 2px 8px;
            border-radius: 4px;
            margin: 2px;
            font-size: 12px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>前端用户数据调试</h1>

    <div class="container">
        <h2>1. 登录</h2>
        <button class="button" onclick="login()">登录为admin</button>
        <div id="login-result"></div>
    </div>

    <div class="container">
        <h2>2. 获取用户数据</h2>
        <button class="button" onclick="fetchUsers()">获取用户数据</button>
        <div id="users-result"></div>
    </div>

    <div class="container">
        <h2>3. 分析用户技能组</h2>
        <button class="button" onclick="analyzeUserSkills()">分析用户技能组</button>
        <div id="analysis-result"></div>
    </div>

    <script>
        let authToken = '';
        const API_BASE = 'http://localhost:8080/api';
        let usersData = null;

        // 技能组名称中文映射
        const getSkillGroupDisplayName = (groupName) => {
            const skillGroupNameMap = {
                'CNC Machining': 'CNC加工',
                'Milling': '铣削加工',
                'Turning': '车削加工',
                'Grinding': '磨削加工',
                'Assembly': '装配',
                'Quality Control': '质量控制',
                'Packaging': '包装'
            };
            return skillGroupNameMap[groupName] || groupName;
        };

        async function apiCall(endpoint, options = {}) {
            try {
                const url = `${API_BASE}${endpoint}`;
                const config = {
                    headers: {
                        'Content-Type': 'application/json',
                        ...(authToken && { 'Authorization': `Bearer ${authToken}` })
                    },
                    ...options
                };

                console.log('API调用:', url, config);
                const response = await fetch(url, config);
                const data = await response.json();

                if (response.ok) {
                    return { success: true, data };
                } else {
                    return { success: false, error: data.message || '请求失败' };
                }
            } catch (error) {
                console.error('API调用错误:', error);
                return { success: false, error: error.message };
            }
        }

        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            if (result.success) {
                element.className = 'success';
                element.innerHTML = `
                    <div><strong>成功:</strong></div>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                element.className = 'error';
                element.innerHTML = `
                    <div><strong>错误:</strong> ${result.error}</div>
                `;
            }
        }

        async function login() {
            const result = await apiCall('/auth/login', {
                method: 'POST',
                body: JSON.stringify({ username: 'admin', password: 'admin123' })
            });

            if (result.success && result.data.token) {
                authToken = result.data.token;
            }

            displayResult('login-result', result);
        }

        async function fetchUsers() {
            const result = await apiCall('/users');
            if (result.success) {
                usersData = result.data.users;
            }
            displayResult('users-result', result);
        }

        async function analyzeUserSkills() {
            if (!usersData) {
                document.getElementById('analysis-result').innerHTML = 
                    '<div class="error">请先获取用户数据</div>';
                return;
            }

            let html = '<div><strong>用户技能组分析结果:</strong></div>';
            
            // 统计信息
            const totalUsers = usersData.length;
            const usersWithSkills = usersData.filter(user => user.skills && user.skills.length > 0);
            const usersWithoutSkills = usersData.filter(user => !user.skills || user.skills.length === 0);
            
            html += `<div class="warning">
                <strong>统计信息:</strong><br>
                总用户数: ${totalUsers}<br>
                有技能组用户: ${usersWithSkills.length}<br>
                无技能组用户: ${usersWithoutSkills.length}
            </div>`;

            // 检查前端逻辑
            html += '<div><strong>前端逻辑测试:</strong></div>';
            usersData.forEach(user => {
                const hasSkills = user.skills && user.skills.length > 0;
                const filterResult1 = !user.skills || user.skills.length === 0;
                const filterResult2 = user.skills && user.skills.length > 0;
                
                html += `<div class="user-card">
                    <strong>用户:</strong> ${user.username} (${user.full_name || '未设置姓名'})<br>
                    <strong>原始技能数据:</strong> ${JSON.stringify(user.skills)}<br>
                    <strong>技能数据类型:</strong> ${typeof user.skills}<br>
                    <strong>技能数组长度:</strong> ${user.skills ? user.skills.length : 'undefined'}<br>
                    <strong>hasSkills (skills && skills.length > 0):</strong> ${hasSkills}<br>
                    <strong>无技能过滤器 (!skills || skills.length === 0):</strong> ${filterResult1}<br>
                    <strong>有技能过滤器 (skills && skills.length > 0):</strong> ${filterResult2}<br>
                    <strong>显示的技能组:</strong> `;
                
                if (hasSkills) {
                    user.skills.forEach(skill => {
                        html += `<span class="skill-tag">${getSkillGroupDisplayName(skill)}</span>`;
                    });
                } else {
                    html += '<span style="color: red;">无技能组</span>';
                }
                
                html += '</div>';
            });

            // 模拟前端警告条件
            const shouldShowWarning = usersData.filter(user => !user.skills || user.skills.length === 0).length > 0;
            html += `<div class="warning">
                <strong>前端警告条件测试:</strong><br>
                应该显示警告: ${shouldShowWarning}<br>
                警告条件: users.filter(user => !user.skills || user.skills.length === 0).length > 0
            </div>`;

            document.getElementById('analysis-result').innerHTML = html;
            document.getElementById('analysis-result').className = 'success';
        }
    </script>
</body>
</html>
