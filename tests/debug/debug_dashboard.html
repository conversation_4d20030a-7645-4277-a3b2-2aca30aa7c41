<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表板数据调试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #1890ff;
        }
        .result {
            background: #f6f8fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
        .success { border-left: 4px solid #52c41a; }
        .error { border-left: 4px solid #f5222d; }
        .warning { border-left: 4px solid #faad14; }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success { background: #f6ffed; color: #52c41a; }
        .status.error { background: #fff2f0; color: #f5222d; }
        .status.loading { background: #e6f7ff; color: #1890ff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 仪表板数据调试工具</h1>
        
        <div class="test-section">
            <h3>🔗 连接测试</h3>
            <button onclick="testConnection()">测试前端连接</button>
            <button onclick="testBackend()">测试后端连接</button>
            <button onclick="testAuth()">测试认证</button>
            <div id="connection-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📊 仪表板API测试</h3>
            <button onclick="testDashboardAPI()">测试仪表板API</button>
            <button onclick="testProductionAPI()">测试生产摘要API</button>
            <div id="dashboard-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🧪 前端API客户端测试</h3>
            <button onclick="testFrontendAPI()">测试前端API调用</button>
            <div id="frontend-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📋 当前状态</h3>
            <p><strong>前端地址:</strong> <span id="frontend-url"></span></p>
            <p><strong>后端地址:</strong> <span id="backend-url"></span></p>
            <p><strong>认证状态:</strong> <span id="auth-status" class="status">未知</span></p>
            <p><strong>Token:</strong> <span id="token-info">未获取</span></p>
        </div>

        <div class="test-section">
            <h3>💡 调试建议</h3>
            <ol>
                <li>确保VSCode端口转发已设置 (3000, 8080)</li>
                <li>检查浏览器控制台是否有错误信息</li>
                <li>验证网络请求是否成功</li>
                <li>确认认证token是否有效</li>
            </ol>
        </div>
    </div>

    <script>
        // 更新当前状态
        document.getElementById('frontend-url').textContent = window.location.origin;
        document.getElementById('backend-url').textContent = window.location.origin.replace('3000', '8080');

        let authToken = null;

        // 测试前端连接
        async function testConnection() {
            const result = document.getElementById('connection-result');
            result.textContent = '正在测试连接...';
            
            try {
                const response = await fetch('/');
                if (response.ok) {
                    result.textContent = '✅ 前端连接正常';
                    result.className = 'result success';
                } else {
                    result.textContent = `❌ 前端连接失败: ${response.status}`;
                    result.className = 'result error';
                }
            } catch (error) {
                result.textContent = `❌ 前端连接错误: ${error.message}`;
                result.className = 'result error';
            }
        }

        // 测试后端连接
        async function testBackend() {
            const result = document.getElementById('connection-result');
            result.textContent = '正在测试后端连接...';
            
            try {
                const response = await fetch('/api/health');
                if (response.ok) {
                    result.textContent = '✅ 后端连接正常';
                    result.className = 'result success';
                } else {
                    result.textContent = `❌ 后端连接失败: ${response.status}`;
                    result.className = 'result error';
                }
            } catch (error) {
                result.textContent = `❌ 后端连接错误: ${error.message}`;
                result.className = 'result error';
            }
        }

        // 测试认证
        async function testAuth() {
            const result = document.getElementById('connection-result');
            result.textContent = '正在测试认证...';
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.token;
                    document.getElementById('auth-status').textContent = '已认证';
                    document.getElementById('auth-status').className = 'status success';
                    document.getElementById('token-info').textContent = authToken.substring(0, 50) + '...';
                    result.textContent = '✅ 认证成功\n' + JSON.stringify(data, null, 2);
                    result.className = 'result success';
                } else {
                    const errorText = await response.text();
                    result.textContent = `❌ 认证失败: ${response.status}\n${errorText}`;
                    result.className = 'result error';
                }
            } catch (error) {
                result.textContent = `❌ 认证错误: ${error.message}`;
                result.className = 'result error';
            }
        }

        // 测试仪表板API
        async function testDashboardAPI() {
            const result = document.getElementById('dashboard-result');
            
            if (!authToken) {
                result.textContent = '❌ 请先进行认证测试';
                result.className = 'result error';
                return;
            }

            result.textContent = '正在测试仪表板API...';
            
            try {
                const response = await fetch('/api/dashboard/overview', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    result.textContent = '✅ 仪表板API测试成功\n' + JSON.stringify(data, null, 2);
                    result.className = 'result success';
                } else {
                    const errorText = await response.text();
                    result.textContent = `❌ 仪表板API失败: ${response.status}\n${errorText}`;
                    result.className = 'result error';
                }
            } catch (error) {
                result.textContent = `❌ 仪表板API错误: ${error.message}`;
                result.className = 'result error';
            }
        }

        // 测试生产摘要API
        async function testProductionAPI() {
            const result = document.getElementById('dashboard-result');
            
            if (!authToken) {
                result.textContent = '❌ 请先进行认证测试';
                result.className = 'result error';
                return;
            }

            result.textContent = '正在测试生产摘要API...';
            
            try {
                const response = await fetch('/api/dashboard/production-summary', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    result.textContent = '✅ 生产摘要API测试成功\n' + JSON.stringify(data, null, 2);
                    result.className = 'result success';
                } else {
                    const errorText = await response.text();
                    result.textContent = `❌ 生产摘要API失败: ${response.status}\n${errorText}`;
                    result.className = 'result error';
                }
            } catch (error) {
                result.textContent = `❌ 生产摘要API错误: ${error.message}`;
                result.className = 'result error';
            }
        }

        // 测试前端API客户端
        async function testFrontendAPI() {
            const result = document.getElementById('frontend-result');
            result.textContent = '正在测试前端API客户端...';
            
            // 检查是否有React应用运行
            if (typeof window.React !== 'undefined') {
                result.textContent = '✅ React应用已加载';
                result.className = 'result success';
            } else {
                result.textContent = '⚠️ 这是静态HTML页面，无法测试React组件\n建议在浏览器中访问 http://localhost:3000/api-debug';
                result.className = 'result warning';
            }
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            testConnection();
        };
    </script>
</body>
</html>
