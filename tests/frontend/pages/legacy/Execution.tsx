import React, { useState } from 'react';
import { Row, Col, Card, Typo<PERSON>, Button, Space, Table, Tag, Tabs, Modal, message, Divider } from 'antd';
import { PlayCircleOutlined, PauseCircleOutlined, CheckCircleOutlined, ClockCircleOutlined, ToolOutlined } from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import type { ExecutionLog, PlanTaskWithDetails } from '@/types/api';
import { useAuthStore } from '@/store/auth';
// import UserDebugInfo from '@/components/UserDebugInfo';
import dayjs from '@/utils/dayjs';

const { Title, Text } = Typography;

const Execution: React.FC = () => {
  const [activeTab, setActiveTab] = useState('my-tasks');
  const [selectedTask, setSelectedTask] = useState<PlanTaskWithDetails | null>(null);
  const [isTaskModalVisible, setIsTaskModalVisible] = useState(false);
  const queryClient = useQueryClient();
  const { user } = useAuthStore();

  const { data: executionLogs, isLoading: logsLoading } = useQuery(
    'execution-logs',
    () => apiClient.getExecutionLogs()
  );

  const { data: myTasks, isLoading: tasksLoading } = useQuery(
    'my-skill-group-tasks',
    () => apiClient.getMySkillGroupTasks(),
    {
      enabled: !!user?.id, // 确保用户已登录且有ID
      refetchInterval: 30000, // 30秒刷新一次
    }
  );

  // 任务执行操作
  const startTaskMutation = useMutation(
    (taskId: number) => apiClient.startTask({ plan_task_id: taskId }),
    {
      onSuccess: () => {
        message.success('任务已开始');
        queryClient.invalidateQueries('my-skill-group-tasks');
        queryClient.invalidateQueries('execution-logs');
      },
      onError: () => {
        message.error('开始任务失败');
      },
    }
  );

  const completeTaskMutation = useMutation(
    (taskId: number) => apiClient.completeTask({ plan_task_id: taskId }),
    {
      onSuccess: () => {
        message.success('任务已完成');
        queryClient.invalidateQueries('my-skill-group-tasks');
        queryClient.invalidateQueries('execution-logs');
      },
      onError: () => {
        message.error('完成任务失败');
      },
    }
  );

  const pauseTaskMutation = useMutation(
    (taskId: number) => apiClient.pauseTask({ plan_task_id: taskId }),
    {
      onSuccess: () => {
        message.success('任务已暂停');
        queryClient.invalidateQueries('my-skill-group-tasks');
        queryClient.invalidateQueries('execution-logs');
      },
      onError: () => {
        message.error('暂停任务失败');
      },
    }
  );

  const getEventTypeTag = (eventType: string) => {
    const typeMap: Record<string, { color: string; text: string }> = {
      start: { color: 'blue', text: '开始' },
      pause: { color: 'orange', text: '暂停' },
      resume: { color: 'cyan', text: '恢复' },
      complete: { color: 'green', text: '完成' },
      cancel: { color: 'red', text: '取消' },
    };
    const config = typeMap[eventType] || { color: 'default', text: eventType };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getStatusTag = (status: string) => {
    const statusMap: Record<string, { color: string; text: string }> = {
      planned: { color: 'default', text: '计划中' },
      scheduled: { color: 'blue', text: '已排程' },
      in_progress: { color: 'processing', text: '进行中' },
      completed: { color: 'success', text: '已完成' },
      cancelled: { color: 'error', text: '已取消' },
    };
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const handleTaskAction = (task: PlanTaskWithDetails, action: string) => {
    Modal.confirm({
      title: `确认${action}任务`,
      content: `确定要${action}任务 "${task.process_name}" 吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        switch (action) {
          case '开始':
            startTaskMutation.mutate(task.id);
            break;
          case '完成':
            completeTaskMutation.mutate(task.id);
            break;
          case '暂停':
            pauseTaskMutation.mutate(task.id);
            break;
        }
      },
    });
  };

  const handleViewTask = (task: PlanTaskWithDetails) => {
    setSelectedTask(task);
    setIsTaskModalVisible(true);
  };

  // 我的任务表格列定义
  const myTasksColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '项目',
      dataIndex: 'project_name',
      key: 'project_name',
      width: 120,
    },
    {
      title: '零件',
      dataIndex: 'part_number',
      key: 'part_number',
      width: 120,
    },
    {
      title: '工艺',
      dataIndex: 'process_name',
      key: 'process_name',
      width: 150,
    },
    {
      title: '技能组',
      dataIndex: 'skill_group_name',
      key: 'skill_group_name',
      width: 100,
    },
    {
      title: '计划开始',
      dataIndex: 'planned_start',
      key: 'planned_start',
      width: 120,
      render: (text: string) => dayjs(text).format('MM-DD HH:mm'),
    },
    {
      title: '计划结束',
      dataIndex: 'planned_end',
      key: 'planned_end',
      width: 120,
      render: (text: string) => dayjs(text).format('MM-DD HH:mm'),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record: PlanTaskWithDetails) => (
        <Space>
          <Button
            size="small"
            onClick={() => handleViewTask(record)}
          >
            查看
          </Button>
          {record.status === 'planned' || record.status === 'scheduled' ? (
            <Button
              type="primary"
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={() => handleTaskAction(record, '开始')}
              loading={startTaskMutation.isLoading}
            >
              开始
            </Button>
          ) : null}
          {record.status === 'in_progress' ? (
            <>
              <Button
                size="small"
                icon={<PauseCircleOutlined />}
                onClick={() => handleTaskAction(record, '暂停')}
                loading={pauseTaskMutation.isLoading}
              >
                暂停
              </Button>
              <Button
                type="primary"
                size="small"
                icon={<CheckCircleOutlined />}
                onClick={() => handleTaskAction(record, '完成')}
                loading={completeTaskMutation.isLoading}
                style={{ backgroundColor: '#52c41a' }}
              >
                完成
              </Button>
            </>
          ) : null}
        </Space>
      ),
    },
  ];

  // 执行日志表格列定义
  const logColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '任务ID',
      dataIndex: 'plan_task_id',
      key: 'plan_task_id',
    },
    {
      title: '设备ID',
      dataIndex: 'machine_id',
      key: 'machine_id',
      render: (text: number) => text || '未指定',
    },
    {
      title: '操作员',
      dataIndex: 'user_id',
      key: 'user_id',
    },
    {
      title: '事件类型',
      dataIndex: 'event_type',
      key: 'event_type',
      render: (eventType: string) => getEventTypeTag(eventType),
    },
    {
      title: '事件时间',
      dataIndex: 'event_time',
      key: 'event_time',
      render: (text: string) => dayjs(text).format('MM-DD HH:mm:ss'),
    },
    {
      title: '备注',
      dataIndex: 'notes',
      key: 'notes',
      render: (text: string) => (
        <Text ellipsis={{ tooltip: text }} style={{ maxWidth: 150 }}>
          {text || '无'}
        </Text>
      ),
    },
  ];

  // Debug info removed for production
  // const showDebugInfo = true;

  return (
    <div>
      {/* Debug info removed for production */}
      {/* {showDebugInfo && <UserDebugInfo />} */}

      <div className="page-header">
        <Title level={2} style={{ margin: 0 }}>
          生产执行
        </Title>
        <Typography.Paragraph style={{ margin: 0, color: '#666' }}>
          查看我的技能组任务（前2天至后3天），快速执行生产操作
        </Typography.Paragraph>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <ClockCircleOutlined style={{ fontSize: 32, color: '#1890ff', marginBottom: 8 }} />
              <div>
                <Text strong>待执行任务</Text>
              </div>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>
                {myTasks?.filter(t => t.status === 'planned' || t.status === 'scheduled').length || 0}
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <PlayCircleOutlined style={{ fontSize: 32, color: '#faad14', marginBottom: 8 }} />
              <div>
                <Text strong>进行中任务</Text>
              </div>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#faad14' }}>
                {myTasks?.filter(t => t.status === 'in_progress').length || 0}
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <CheckCircleOutlined style={{ fontSize: 32, color: '#52c41a', marginBottom: 8 }} />
              <div>
                <Text strong>今日完成</Text>
              </div>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#52c41a' }}>
                {myTasks?.filter(t => t.status === 'completed').length || 0}
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <ToolOutlined style={{ fontSize: 32, color: '#722ed1', marginBottom: 8 }} />
              <div>
                <Text strong>我的技能组</Text>
              </div>
              <div style={{ fontSize: 16, fontWeight: 'bold', color: '#722ed1' }}>
                {myTasks?.[0]?.skill_group_name || '未分配'}
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'my-tasks',
              label: '我的任务',
              children: (
                <Table
                  columns={myTasksColumns}
                  dataSource={myTasks}
                  rowKey="id"
                  loading={tasksLoading}
                  pagination={{
                    total: myTasks?.length || 0,
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条任务`,
                  }}
                  scroll={{ x: 1200 }}
                />
              ),
            },
            {
              key: 'execution-logs',
              label: '执行日志',
              children: (
                <Table
                  columns={logColumns}
                  dataSource={executionLogs}
                  rowKey="id"
                  loading={logsLoading}
                  pagination={{
                    total: executionLogs?.length || 0,
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条记录`,
                  }}
                />
              ),
            },
          ]}
        />
      </Card>

      {/* 任务详情模态框 */}
      <Modal
        title="任务详情"
        open={isTaskModalVisible}
        onCancel={() => {
          setIsTaskModalVisible(false);
          setSelectedTask(null);
        }}
        footer={[
          <Button key="close" onClick={() => setIsTaskModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedTask && (
          <div>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Card size="small" title="基本信息">
                  <p><strong>任务ID：</strong>{selectedTask.id}</p>
                  <p><strong>项目：</strong>{selectedTask.project_name}</p>
                  <p><strong>零件：</strong>{selectedTask.part_number}</p>
                  <p><strong>工艺：</strong>{selectedTask.process_name}</p>
                  <p><strong>技能组：</strong>{selectedTask.skill_group_name}</p>
                  <p><strong>状态：</strong>{getStatusTag(selectedTask.status)}</p>
                </Card>
              </Col>
              <Col span={12}>
                <Card size="small" title="时间信息">
                  <p><strong>计划开始：</strong>{dayjs(selectedTask.planned_start).format('YYYY-MM-DD HH:mm')}</p>
                  <p><strong>计划结束：</strong>{dayjs(selectedTask.planned_end).format('YYYY-MM-DD HH:mm')}</p>
                  <p><strong>标准工时：</strong>{selectedTask.standard_hours || '未设置'} 小时</p>
                  <p><strong>工单数量：</strong>{selectedTask.work_order_quantity}</p>
                </Card>
              </Col>
            </Row>

            {selectedTask.work_instructions && (
              <Card size="small" title="作业指导书" style={{ marginTop: 16 }}>
                <div style={{
                  padding: 12,
                  backgroundColor: '#f5f5f5',
                  borderRadius: 6,
                  whiteSpace: 'pre-wrap'
                }}>
                  {selectedTask.work_instructions}
                </div>
              </Card>
            )}

            <Divider />

            <div style={{ textAlign: 'center' }}>
              {selectedTask.status === 'planned' || selectedTask.status === 'scheduled' ? (
                <Button
                  type="primary"
                  size="large"
                  icon={<PlayCircleOutlined />}
                  onClick={() => {
                    handleTaskAction(selectedTask, '开始');
                    setIsTaskModalVisible(false);
                  }}
                  loading={startTaskMutation.isLoading}
                >
                  开始任务
                </Button>
              ) : null}

              {selectedTask.status === 'in_progress' ? (
                <Space>
                  <Button
                    size="large"
                    icon={<PauseCircleOutlined />}
                    onClick={() => {
                      handleTaskAction(selectedTask, '暂停');
                      setIsTaskModalVisible(false);
                    }}
                    loading={pauseTaskMutation.isLoading}
                  >
                    暂停任务
                  </Button>
                  <Button
                    type="primary"
                    size="large"
                    icon={<CheckCircleOutlined />}
                    onClick={() => {
                      handleTaskAction(selectedTask, '完成');
                      setIsTaskModalVisible(false);
                    }}
                    loading={completeTaskMutation.isLoading}
                    style={{ backgroundColor: '#52c41a' }}
                  >
                    完成任务
                  </Button>
                </Space>
              ) : null}
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Execution;
