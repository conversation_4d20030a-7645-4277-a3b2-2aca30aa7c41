import React, { useState } from 'react';
import { Card, Tabs, Button, Space, message, Row, Col } from 'antd';
import {
  PlayCircleOutlined,
  SettingOutlined,
  SafetyCertificateOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import RoleBasedAccess from '@/components/RoleBasedAccess';
import SkillBasedAccess from '@/components/SkillBasedAccess';
import { ROLES } from '@/utils/permissions';
import TaskConfirmation from '@/components/operator/TaskConfirmation';
import MachineStatusUpdate from '@/components/operator/MachineStatusUpdate';
import QualityDataEntry from '@/components/operator/QualityDataEntry';
import TaskCompletion from '@/components/operator/TaskCompletion';

const { TabPane } = Tabs;

/**
 * 操作员执行界面
 * 支持扫码和手动输入的生产执行操作
 */
const OperatorExecution: React.FC = () => {
  const [activeTab, setActiveTab] = useState('task-confirm');

  return (
    <RoleBasedAccess allowedRoles={[ROLES.OPERATOR, ROLES.ADMIN]}>
      <div>
        <div className="page-header" style={{ marginBottom: 24 }}>
          <h2 style={{ margin: 0 }}>生产执行</h2>
          <p style={{ margin: 0, color: '#666' }}>
            任务确认、设备状态更新、质量数据录入
          </p>
        </div>

        <Card>
          <Tabs 
            activeKey={activeTab} 
            onChange={setActiveTab}
            size="large"
            tabBarStyle={{ marginBottom: 24 }}
          >
            <TabPane
              tab={
                <Space>
                  <PlayCircleOutlined />
                  任务确认
                </Space>
              }
              key="task-confirm"
            >
              <TaskConfirmation />
            </TabPane>

            <TabPane
              tab={
                <Space>
                  <SettingOutlined />
                  设备状态
                </Space>
              }
              key="machine-status"
            >
              <MachineStatusUpdate />
            </TabPane>

            <TabPane
              tab={
                <Space>
                  <SafetyCertificateOutlined />
                  质量检查
                </Space>
              }
              key="quality-check"
            >
              <QualityDataEntry />
            </TabPane>

            <TabPane
              tab={
                <Space>
                  <CheckCircleOutlined />
                  完工确认
                </Space>
              }
              key="task-completion"
            >
              <TaskCompletion />
            </TabPane>
          </Tabs>
        </Card>
      </div>
    </RoleBasedAccess>
  );
};

export default OperatorExecution;
