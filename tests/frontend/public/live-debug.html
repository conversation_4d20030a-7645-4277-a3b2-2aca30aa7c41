<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MES系统实时调试工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .panel {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #1890ff;
        }
        .panel h3 {
            margin-top: 0;
            color: #333;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-indicator.success { background-color: #52c41a; }
        .status-indicator.error { background-color: #f5222d; }
        .status-indicator.warning { background-color: #faad14; }
        .status-indicator.loading { background-color: #1890ff; animation: pulse 1s infinite; }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .metric-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            border: 1px solid #d9d9d9;
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
            margin: 5px 0;
        }
        .metric-label {
            font-size: 14px;
            color: #666;
        }
        .log-area {
            background: #f6f8fa;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #40a9ff;
        }
        button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .auto-refresh {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 10px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #d9d9d9;
        }
        .iframe-container {
            margin: 20px 0;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            overflow: hidden;
        }
        .iframe-container iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="auto-refresh">
        <label>
            <input type="checkbox" id="auto-refresh" checked> 自动刷新 (5秒)
        </label>
    </div>

    <div class="container">
        <h1>🔍 MES系统实时调试工具</h1>
        <p>实时监控前端应用状态和数据</p>

        <div class="debug-panel">
            <div class="panel">
                <h3>🔗 连接状态</h3>
                <div id="connection-status">
                    <div><span class="status-indicator loading"></span>检查中...</div>
                </div>
                <button onclick="checkConnections()">重新检查</button>
                <button onclick="performLogin()">执行登录</button>
            </div>

            <div class="panel">
                <h3>📊 仪表板数据</h3>
                <div id="dashboard-metrics">
                    <div class="metric-card">
                        <div class="metric-value" id="utilization-rate">-</div>
                        <div class="metric-label">设备利用率</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="quality-rate">-</div>
                        <div class="metric-label">质量合格率</div>
                    </div>
                </div>
                <button onclick="fetchDashboardData()">刷新数据</button>
            </div>
        </div>

        <div class="debug-panel">
            <div class="panel">
                <h3>📋 数据统计</h3>
                <div id="data-stats">
                    <div>项目数量: <span id="projects-count">-</span></div>
                    <div>零件数量: <span id="parts-count">-</span></div>
                    <div>设备数量: <span id="machines-count">-</span></div>
                    <div>工单数量: <span id="orders-count">-</span></div>
                </div>
                <button onclick="fetchAllData()">获取统计</button>
            </div>

            <div class="panel">
                <h3>🔧 浏览器信息</h3>
                <div id="browser-info">
                    <div>User Agent: <span id="user-agent"></span></div>
                    <div>当前URL: <span id="current-url"></span></div>
                    <div>LocalStorage: <span id="localstorage-info"></span></div>
                    <div>Token状态: <span id="token-status"></span></div>
                </div>
                <button onclick="updateBrowserInfo()">更新信息</button>
                <button onclick="clearStorage()">清除存储</button>
            </div>
        </div>

        <div class="panel">
            <h3>📝 实时日志</h3>
            <div id="debug-log" class="log-area">等待日志...</div>
            <button onclick="clearLog()">清除日志</button>
            <button onclick="exportLog()">导出日志</button>
        </div>

        <div class="panel">
            <h3>🖼️ 前端页面预览</h3>
            <p>在下方iframe中查看实际的前端页面：</p>
            <div class="iframe-container">
                <iframe src="/dashboard" id="preview-iframe"></iframe>
            </div>
            <button onclick="refreshPreview()">刷新预览</button>
            <button onclick="changePreviewPage('/projects')">项目页面</button>
            <button onclick="changePreviewPage('/parts')">零件页面</button>
            <button onclick="changePreviewPage('/machines')">设备页面</button>
        </div>
    </div>

    <script>
        let authToken = null;
        let autoRefreshInterval = null;

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logArea = document.getElementById('debug-log');
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logArea.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        // 检查连接状态
        async function checkConnections() {
            log('开始检查连接状态...');
            const statusDiv = document.getElementById('connection-status');
            statusDiv.innerHTML = '<div><span class="status-indicator loading"></span>检查中...</div>';

            let html = '';

            // 检查前端
            try {
                const response = await fetch('/');
                if (response.ok) {
                    html += '<div><span class="status-indicator success"></span>前端服务: 正常</div>';
                    log('前端服务连接正常', 'success');
                } else {
                    html += '<div><span class="status-indicator error"></span>前端服务: 异常</div>';
                    log('前端服务连接异常', 'error');
                }
            } catch (error) {
                html += '<div><span class="status-indicator error"></span>前端服务: 错误</div>';
                log(`前端服务错误: ${error.message}`, 'error');
            }

            // 检查后端API
            try {
                const response = await fetch('/api/health');
                if (response.ok) {
                    html += '<div><span class="status-indicator success"></span>后端API: 正常</div>';
                    log('后端API连接正常', 'success');
                } else {
                    html += '<div><span class="status-indicator error"></span>后端API: 异常</div>';
                    log('后端API连接异常', 'error');
                }
            } catch (error) {
                html += '<div><span class="status-indicator error"></span>后端API: 错误</div>';
                log(`后端API错误: ${error.message}`, 'error');
            }

            // 检查认证状态
            const token = localStorage.getItem('token');
            if (token) {
                html += '<div><span class="status-indicator success"></span>认证状态: 已登录</div>';
                authToken = token;
                log('用户已登录', 'success');
            } else {
                html += '<div><span class="status-indicator warning"></span>认证状态: 未登录</div>';
                log('用户未登录', 'warning');
            }

            statusDiv.innerHTML = html;
        }

        // 执行登录
        async function performLogin() {
            log('开始执行登录...');
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.token;
                    localStorage.setItem('token', authToken);
                    log('登录成功', 'success');
                    checkConnections();
                    updateBrowserInfo();
                } else {
                    log('登录失败', 'error');
                }
            } catch (error) {
                log(`登录错误: ${error.message}`, 'error');
            }
        }

        // 获取仪表板数据
        async function fetchDashboardData() {
            if (!authToken) {
                log('未登录，无法获取仪表板数据', 'warning');
                return;
            }

            log('获取仪表板数据...');
            try {
                const response = await fetch('/api/dashboard/overview', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('utilization-rate').textContent = 
                        (data.machine_status?.utilization_rate || 0) + '%';
                    document.getElementById('quality-rate').textContent = 
                        (data.quality_metrics?.quality_rate || 0) + '%';
                    log('仪表板数据更新成功', 'success');
                } else {
                    log('仪表板数据获取失败', 'error');
                }
            } catch (error) {
                log(`仪表板数据错误: ${error.message}`, 'error');
            }
        }

        // 获取所有数据统计
        async function fetchAllData() {
            if (!authToken) {
                log('未登录，无法获取数据统计', 'warning');
                return;
            }

            log('获取数据统计...');
            const endpoints = [
                { name: 'projects', id: 'projects-count' },
                { name: 'parts', id: 'parts-count' },
                { name: 'machines', id: 'machines-count' },
                { name: 'work-orders', id: 'orders-count' }
            ];

            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(`/api/${endpoint.name}`, {
                        headers: {
                            'Authorization': `Bearer ${authToken}`
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        const key = Object.keys(data)[0];
                        const count = Array.isArray(data[key]) ? data[key].length : 0;
                        document.getElementById(endpoint.id).textContent = count;
                    } else {
                        document.getElementById(endpoint.id).textContent = '错误';
                    }
                } catch (error) {
                    document.getElementById(endpoint.id).textContent = '错误';
                    log(`${endpoint.name}数据获取错误: ${error.message}`, 'error');
                }
            }
            log('数据统计更新完成', 'success');
        }

        // 更新浏览器信息
        function updateBrowserInfo() {
            document.getElementById('user-agent').textContent = navigator.userAgent.substring(0, 50) + '...';
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('localstorage-info').textContent = `${localStorage.length} 项`;
            
            const token = localStorage.getItem('token');
            document.getElementById('token-status').textContent = token ? '有效' : '无';
        }

        // 清除存储
        function clearStorage() {
            localStorage.clear();
            sessionStorage.clear();
            authToken = null;
            log('存储已清除', 'success');
            updateBrowserInfo();
            checkConnections();
        }

        // 清除日志
        function clearLog() {
            document.getElementById('debug-log').textContent = '';
        }

        // 导出日志
        function exportLog() {
            const logContent = document.getElementById('debug-log').textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `mes-debug-log-${new Date().toISOString().slice(0, 19)}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 刷新预览
        function refreshPreview() {
            const iframe = document.getElementById('preview-iframe');
            iframe.src = iframe.src;
            log('预览页面已刷新');
        }

        // 切换预览页面
        function changePreviewPage(page) {
            const iframe = document.getElementById('preview-iframe');
            iframe.src = page;
            log(`切换到页面: ${page}`);
        }

        // 自动刷新功能
        function toggleAutoRefresh() {
            const checkbox = document.getElementById('auto-refresh');
            if (checkbox.checked) {
                autoRefreshInterval = setInterval(() => {
                    fetchDashboardData();
                    fetchAllData();
                }, 5000);
                log('自动刷新已启用');
            } else {
                if (autoRefreshInterval) {
                    clearInterval(autoRefreshInterval);
                    autoRefreshInterval = null;
                }
                log('自动刷新已禁用');
            }
        }

        // 页面加载时初始化
        window.onload = function() {
            log('调试工具已加载');
            updateBrowserInfo();
            checkConnections();
            
            // 如果已登录，获取数据
            if (localStorage.getItem('token')) {
                authToken = localStorage.getItem('token');
                fetchDashboardData();
                fetchAllData();
            }

            // 设置自动刷新
            document.getElementById('auto-refresh').addEventListener('change', toggleAutoRefresh);
            toggleAutoRefresh();
        };
    </script>
</body>
</html>
