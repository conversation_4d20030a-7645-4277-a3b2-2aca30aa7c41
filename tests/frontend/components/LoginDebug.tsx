import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, Typography, Space, Input } from 'antd';
import { apiClient } from '@/lib/api';
import { useMessage } from '@/hooks/useMessage';

const { Title, Text, Paragraph } = Typography;

const LoginDebug: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<any>(null);
  const { success, error: showError } = useMessage();

  const testDirectLogin = async () => {
    setLoading(true);
    setResult(null);
    setError(null);
    
    try {
      console.log('Testing direct API call...');
      const response = await apiClient.login({
        username: 'admin',
        password: 'admin123'
      });
      
      console.log('Login response:', response);
      setResult(response);
      success('直接API调用成功');
    } catch (err: any) {
      console.error('Login error:', err);
      setError(err);
      showError('直接API调用失败');
    } finally {
      setLoading(false);
    }
  };

  const testFetch = async () => {
    setLoading(true);
    setResult(null);
    setError(null);
    
    try {
      console.log('Testing fetch API...');
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: 'admin',
          password: 'admin123'
        })
      });
      
      const data = await response.json();
      console.log('Fetch response:', data);
      setResult(data);
      success('Fetch API调用成功');
    } catch (err: any) {
      console.error('Fetch error:', err);
      setError(err);
      showError('Fetch API调用失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card title="登录调试工具" style={{ margin: '20px' }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Title level={4}>测试登录API</Title>
        
        <Space>
          <Button 
            type="primary" 
            onClick={testDirectLogin} 
            loading={loading}
          >
            测试直接API调用
          </Button>
          
          <Button 
            onClick={testFetch} 
            loading={loading}
          >
            测试Fetch API
          </Button>
        </Space>

        {result && (
          <Card title="成功响应" type="inner">
            <Paragraph>
              <Text strong>Token:</Text> {result.token?.substring(0, 50)}...
            </Paragraph>
            <Paragraph>
              <Text strong>User:</Text> {JSON.stringify(result.user, null, 2)}
            </Paragraph>
            <Paragraph>
              <Text strong>完整响应:</Text>
              <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
                {JSON.stringify(result, null, 2)}
              </pre>
            </Paragraph>
          </Card>
        )}

        {error && (
          <Card title="错误信息" type="inner">
            <Paragraph>
              <Text strong>错误类型:</Text> {error.constructor.name}
            </Paragraph>
            <Paragraph>
              <Text strong>错误消息:</Text> {error.message}
            </Paragraph>
            {error.response && (
              <Paragraph>
                <Text strong>响应状态:</Text> {error.response.status}
              </Paragraph>
            )}
            {error.response?.data && (
              <Paragraph>
                <Text strong>响应数据:</Text>
                <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
                  {JSON.stringify(error.response.data, null, 2)}
                </pre>
              </Paragraph>
            )}
            <Paragraph>
              <Text strong>完整错误:</Text>
              <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
                {JSON.stringify(error, null, 2)}
              </pre>
            </Paragraph>
          </Card>
        )}

        <Card title="网络信息" type="inner">
          <Paragraph>
            <Text strong>当前URL:</Text> {window.location.href}
          </Paragraph>
          <Paragraph>
            <Text strong>API Base URL:</Text> /api
          </Paragraph>
          <Paragraph>
            <Text strong>User Agent:</Text> {navigator.userAgent}
          </Paragraph>
        </Card>
      </Space>
    </Card>
  );
};

export default LoginDebug;
