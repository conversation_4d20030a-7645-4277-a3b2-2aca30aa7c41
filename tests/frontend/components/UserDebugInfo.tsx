import React, { useState } from 'react';
import { Card, Button, Typography, Space, Tag, Alert, Divider } from 'antd';
import { useAuthStore } from '@/store/auth';
import { apiClient } from '@/lib/api';

const { Title, Text, Paragraph } = Typography;

/**
 * 用户调试信息组件
 * 用于调试用户登录状态和技能组信息
 */
const UserDebugInfo: React.FC = () => {
  const { user, token, isAuthenticated } = useAuthStore();
  const [currentUserInfo, setCurrentUserInfo] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getSkillGroupDisplayName = (groupName: string) => {
    const skillGroupNameMap: Record<string, string> = {
      'CNC Machining': 'CNC加工',
      'Milling': '铣削加工',
      'Turning': '车削加工',
      'Grinding': '磨削加工',
      'Assembly': '装配',
      'Quality Control': '质量控制',
      'Packaging': '包装'
    };
    return skillGroupNameMap[groupName] || groupName;
  };

  const fetchCurrentUser = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('🔍 Fetching current user info...');
      const userInfo = await apiClient.getCurrentUser();
      console.log('✅ Current user info received:', userInfo);
      setCurrentUserInfo(userInfo);
    } catch (err: any) {
      console.error('❌ Failed to fetch current user:', err);
      setError(err.message || '获取用户信息失败');
    } finally {
      setLoading(false);
    }
  };

  const renderUserInfo = (userData: any, title: string) => {
    if (!userData) return null;

    return (
      <Card size="small" style={{ marginBottom: 16 }}>
        <Title level={5}>{title}</Title>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>用户ID: </Text>
            <Text>{userData.id}</Text>
          </div>
          <div>
            <Text strong>用户名: </Text>
            <Text>{userData.username}</Text>
          </div>
          <div>
            <Text strong>姓名: </Text>
            <Text>{userData.full_name || '未设置'}</Text>
          </div>
          <div>
            <Text strong>状态: </Text>
            <Tag color={userData.is_active ? 'green' : 'red'}>
              {userData.is_active ? '激活' : '未激活'}
            </Tag>
          </div>
          <div>
            <Text strong>角色: </Text>
            {userData.roles && userData.roles.length > 0 ? (
              userData.roles.map((role: string) => (
                <Tag key={role} color="blue">{role}</Tag>
              ))
            ) : (
              <Text type="secondary">无角色</Text>
            )}
          </div>
          <div>
            <Text strong>技能组: </Text>
            {userData.skills && userData.skills.length > 0 ? (
              userData.skills.map((skill: string) => (
                <Tag key={skill} color="orange">
                  {getSkillGroupDisplayName(skill)}
                </Tag>
              ))
            ) : (
              <Text type="danger">❌ 无技能组</Text>
            )}
          </div>
          <div>
            <Text strong>原始技能数据: </Text>
            <Text code>{JSON.stringify(userData.skills)}</Text>
          </div>
        </Space>
      </Card>
    );
  };

  return (
    <Card title="🔍 用户状态调试信息" style={{ margin: '16px 0' }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Alert
          message="调试信息"
          description="此组件用于调试用户登录状态和技能组信息获取问题"
          type="info"
          showIcon
        />

        <div>
          <Text strong>认证状态: </Text>
          <Tag color={isAuthenticated ? 'green' : 'red'}>
            {isAuthenticated ? '已认证' : '未认证'}
          </Tag>
        </div>

        <div>
          <Text strong>Token存在: </Text>
          <Tag color={token ? 'green' : 'red'}>
            {token ? '是' : '否'}
          </Tag>
        </div>

        <div>
          <Text strong>LocalStorage Token: </Text>
          <Tag color={localStorage.getItem('token') ? 'green' : 'red'}>
            {localStorage.getItem('token') ? '存在' : '不存在'}
          </Tag>
        </div>

        <Divider />

        {renderUserInfo(user, '📱 Zustand Store 中的用户信息')}

        <Button 
          type="primary" 
          onClick={fetchCurrentUser}
          loading={loading}
        >
          🔄 重新获取当前用户信息
        </Button>

        {error && (
          <Alert
            message="获取用户信息失败"
            description={error}
            type="error"
            showIcon
          />
        )}

        {renderUserInfo(currentUserInfo, '🌐 API 返回的当前用户信息')}

        <Divider />

        <Card size="small" title="🔧 调试建议">
          <Paragraph>
            <Text strong>如果技能组显示为空：</Text>
            <ul>
              <li>检查数据库中用户是否正确绑定了技能组</li>
              <li>检查后端API是否正确返回技能组信息</li>
              <li>检查前端状态管理是否正确更新</li>
              <li>检查JWT token中是否包含技能组信息</li>
            </ul>
          </Paragraph>
        </Card>
      </Space>
    </Card>
  );
};

export default UserDebugInfo;
