# 基础服务Docker Compose配置 - 仅启动数据库和缓存
version: '3.8'

networks:
  mes-network:
    driver: bridge
    enable_ipv6: true
    ipam:
      config:
        - subnet: **********/16
        - subnet: 2001:db8::/32

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: mes-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-mes_db}
      POSTGRES_USER: ${POSTGRES_USER:-mes_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-mes_password}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
      - "[::]:5432:5432"
    networks:
      - mes-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-mes_user} -d ${POSTGRES_DB:-mes_db}"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: mes-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
      - "[::]:6379:6379"
    networks:
      - mes-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Nginx代理（用于测试）
  nginx:
    image: nginx:alpine
    container_name: mes-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "[::]:80:80"
      - "443:443"
      - "[::]:443:443"
    networks:
      - mes-network
    volumes:
      - ./nginx-basic.conf:/etc/nginx/nginx.conf:ro
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M
