# MES系统快速参考

## 🚀 快速启动命令

### 最常用的启动方式

```bash
# 1. 开发模式（推荐开发时使用）
./mes-quick-manager.sh
# 选择选项 3 (前台) 或 4 (后台)

# 2. 生产模式（部署时使用）
./mes-quick-manager.sh
# 选择选项 1 (前台) 或 2 (后台)

# 3. 混合模式（测试时使用）
./mes-quick-manager.sh
# 选择选项 5-8
```

### 一键命令

```bash
# 查看状态
./mes-quick-manager.sh --status

# 停止所有服务
./mes-quick-manager.sh --stop

# 查看帮助
./mes-quick-manager.sh --help
```

## 📋 启动模式对照表

| 选项 | 后端模式 | 前端模式 | 运行方式 | 适用场景 |
|------|----------|----------|----------|----------|
| 1 | 生产 | 生产 | 前台 | 生产部署测试 |
| 2 | 生产 | 生产 | 后台 | 生产环境运行 |
| 3 | 开发 | 开发 | 前台 | 日常开发 |
| 4 | 开发 | 开发 | 后台 | 开发环境后台 |
| 5 | 生产 | 开发 | 前台 | 测试生产后端 |
| 6 | 生产 | 开发 | 后台 | 后端稳定开发前端 |
| 7 | 开发 | 生产 | 前台 | 测试前端构建 |
| 8 | 开发 | 生产 | 后台 | 前端稳定开发后端 |

## 🔧 开发场景推荐

### 日常开发
```bash
./mes-quick-manager.sh
# 选择选项 3 或 4
# 后端：cargo run (热重载)
# 前端：vite dev (热重载)
```

### 测试后端生产版本
```bash
./mes-quick-manager.sh
# 选择选项 5 或 6
# 后端：预编译二进制 (稳定)
# 前端：vite dev (可调试)
```

### 测试前端构建结果
```bash
./mes-quick-manager.sh
# 选择选项 7 或 8
# 后端：cargo run (可调试)
# 前端：静态文件 (测试构建)
```

## 🌐 访问地址

- **后端API**: http://localhost:9001
- **前端生产**: http://localhost:3080
- **前端开发**: http://localhost:3000

## 📁 重要文件位置

### 日志文件
```
logs/backend.log      # 后端生产日志
logs/frontend.log     # 前端生产日志
logs/backend-dev.log  # 后端开发日志
logs/frontend-dev.log # 前端开发日志
```

### PID文件
```
logs/backend.pid      # 后端生产进程ID
logs/frontend.pid     # 前端生产进程ID
logs/backend-dev.pid  # 后端开发进程ID
logs/frontend-dev.pid # 前端开发进程ID
```

## 🛠️ 故障排除

### 端口被占用
```bash
# 停止所有服务
./mes-quick-manager.sh --stop

# 或手动清理
pkill -f mes-system
pkill -f vite
pkill -f "npm.*dev"
```

### 构建文件缺失
```bash
./mes-quick-manager.sh
# 选择选项 12 进行构建
```

### 查看实时日志
```bash
# 后端日志
tail -f logs/backend.log

# 前端开发日志
tail -f logs/frontend-dev.log
```

## ⚡ 快速操作

### 重启服务
```bash
./mes-quick-manager.sh --stop
./mes-quick-manager.sh
# 选择对应的启动选项
```

### 切换模式
```bash
# 无需停止，直接启动新模式
# 脚本会自动处理冲突
./mes-quick-manager.sh
```

### 查看运行状态
```bash
./mes-quick-manager.sh --status
# 或
./mes-quick-manager.sh
# 选择选项 9
```

## 🔄 模式切换示例

### 从开发切换到生产测试
```bash
# 当前：开发模式运行
./mes-quick-manager.sh --stop

# 启动生产模式
./mes-quick-manager.sh
# 选择选项 1 或 2
```

### 从前台切换到后台
```bash
# 当前：前台运行 (Ctrl+C 停止)
# 重新启动为后台模式
./mes-quick-manager.sh
# 选择对应的后台选项 (偶数选项)
```

## 📞 获取帮助

```bash
# 查看快速管理工具帮助
./mes-quick-manager.sh --help

# 查看完整管理工具帮助
./mes-launcher.sh --help

# 查看详细文档
cat MES-LAUNCHER-GUIDE.md
```

---

**提示**: 建议将此文件保存为书签，方便快速查阅常用命令！
