# 多阶段构建 - 构建阶段
FROM rust:1.86-slim as builder

# 安装构建依赖
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 复制所有源代码
COPY . .

# 构建应用（跳过SQLx编译时检查）
RUN cargo build --release --features sqlx/runtime-tokio-rustls

# 运行阶段 - 使用更小的基础镜像
FROM debian:bookworm-slim

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    libpq5 \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/target/release/mes-system /app/mes-system
COPY --from=builder /app/migrations /app/migrations

# 创建非root用户
RUN groupadd -r mes && useradd -r -g mes mes
RUN chown -R mes:mes /app
USER mes

# 暴露端口（支持IPv4和IPv6）
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/api/health || exit 1

# 启动命令
CMD ["./mes-system"]
