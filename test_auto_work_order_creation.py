#!/usr/bin/env python3
"""
测试自动工单创建功能
"""

import requests
import json
import time
import sys

BASE_URL = "http://localhost:9001/api"

def get_admin_token():
    """获取管理员token"""
    response = requests.post(f"{BASE_URL}/auth/login", json={
        "username": "admin",
        "password": "admin123"
    })
    
    if response.status_code == 200:
        return response.json()["token"]
    else:
        print(f"❌ 管理员登录失败: {response.status_code}")
        return None

def create_test_project(token):
    """创建测试项目"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    project_data = {
        "project_name": f"自动工单测试项目-{int(time.time())}",
        "customer_name": "测试客户",
        "description": "用于测试自动工单创建功能的项目",
        "start_date": "2025-01-01",
        "due_date": "2025-03-01"
    }
    
    response = requests.post(f"{BASE_URL}/projects", headers=headers, json=project_data)
    
    if response.status_code == 200:
        project = response.json()["project"]
        print(f"✅ 创建测试项目成功: {project['project_name']} (ID: {project['id']})")
        return project
    else:
        print(f"❌ 创建测试项目失败: {response.status_code}")
        print(f"错误: {response.text}")
        return None

def create_test_part(token):
    """创建测试零件"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    part_data = {
        "part_number": f"AUTO-TEST-{int(time.time())}",
        "part_name": "自动工单测试零件",
        "version": "1.0",
        "specifications": "用于测试自动工单创建功能的零件"
    }
    
    print(f"创建测试零件: {part_data['part_number']}")
    
    response = requests.post(f"{BASE_URL}/parts", headers=headers, json=part_data)
    
    if response.status_code == 200:
        result = response.json()
        part = result.get("part")
        auto_work_orders = result.get("auto_work_orders", [])
        
        print(f"✅ 零件创建成功: {part['part_name']} (ID: {part['id']})")
        
        if auto_work_orders:
            print(f"🎉 自动创建了 {len(auto_work_orders)} 个工单!")
            for wo in auto_work_orders:
                print(f"  - 工单ID: {wo['work_order_id']}")
        else:
            print("⚠️  没有自动创建工单 (可能是因为零件还没有在任何BOM中)")
        
        return part
    else:
        print(f"❌ 创建测试零件失败: {response.status_code}")
        print(f"错误: {response.text}")
        return None

def add_part_to_project_bom(token, project_id, part_id):
    """将零件添加到项目BOM"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    bom_data = {
        "part_id": part_id,
        "quantity": 5
    }
    
    print(f"将零件添加到项目BOM: 项目ID {project_id}, 零件ID {part_id}, 数量 {bom_data['quantity']}")
    
    response = requests.post(f"{BASE_URL}/projects/{project_id}/bom", headers=headers, json=bom_data)
    
    if response.status_code == 200:
        result = response.json()
        bom = result.get("bom")
        auto_work_orders = result.get("auto_work_orders", [])
        
        print(f"✅ BOM添加成功: BOM ID {bom['id']}")
        
        if auto_work_orders:
            print(f"🎉 自动创建了 {len(auto_work_orders)} 个工单!")
            for wo in auto_work_orders:
                print(f"  - 工单ID: {wo['work_order_id']}")
                if wo.get('plan_task_ids'):
                    print(f"  - 计划任务ID: {wo['plan_task_ids']}")
        else:
            print("⚠️  没有自动创建工单")
        
        return bom
    else:
        print(f"❌ 添加BOM失败: {response.status_code}")
        print(f"错误: {response.text}")
        return None

def check_work_orders(token):
    """检查工单列表"""
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(f"{BASE_URL}/work-orders", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        work_orders = data.get("work_orders", [])
        print(f"📊 当前工单总数: {len(work_orders)}")
        
        for wo in work_orders:
            print(f"  - 工单ID: {wo['id']}, 数量: {wo['quantity']}, 状态: {wo.get('status', 'N/A')}")
        
        return work_orders
    else:
        print(f"❌ 获取工单列表失败: {response.status_code}")
        return []

def check_plan_tasks(token):
    """检查计划任务列表"""
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(f"{BASE_URL}/plan-tasks", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        plan_tasks = data.get("plan_tasks", [])
        print(f"📊 当前计划任务总数: {data.get('total_count', 0)}")
        
        for task in plan_tasks:
            print(f"  - 任务ID: {task['id']}, 工单ID: {task.get('work_order_id', 'N/A')}, 状态: {task.get('status', 'N/A')}")
        
        return plan_tasks
    else:
        print(f"❌ 获取计划任务列表失败: {response.status_code}")
        return []

def main():
    print("🧪 测试自动工单创建功能...")
    
    # 获取管理员token
    token = get_admin_token()
    if not token:
        return 1
    
    print("\n📊 测试前状态检查:")
    work_orders_before = check_work_orders(token)
    plan_tasks_before = check_plan_tasks(token)
    
    print("\n🏗️  开始测试流程:")
    
    # 1. 创建测试项目
    print("\n1️⃣ 创建测试项目...")
    project = create_test_project(token)
    if not project:
        return 1
    
    # 2. 创建测试零件
    print("\n2️⃣ 创建测试零件...")
    part = create_test_part(token)
    if not part:
        return 1
    
    # 3. 将零件添加到项目BOM (这应该触发自动工单创建)
    print("\n3️⃣ 添加零件到项目BOM...")
    bom = add_part_to_project_bom(token, project["id"], part["id"])
    if not bom:
        return 1
    
    # 4. 检查结果
    print("\n📊 测试后状态检查:")
    work_orders_after = check_work_orders(token)
    plan_tasks_after = check_plan_tasks(token)
    
    # 5. 分析结果
    print("\n📈 测试结果分析:")
    work_orders_created = len(work_orders_after) - len(work_orders_before)
    plan_tasks_created = len(plan_tasks_after) - len(plan_tasks_before)
    
    print(f"新创建的工单数量: {work_orders_created}")
    print(f"新创建的计划任务数量: {plan_tasks_created}")
    
    if work_orders_created > 0:
        print("🎉 自动工单创建功能正常工作!")
        if plan_tasks_created > 0:
            print("🎉 自动计划任务创建功能也正常工作!")
        else:
            print("⚠️  工单创建成功，但没有自动创建计划任务")
    else:
        print("❌ 自动工单创建功能没有工作")
        print("可能的原因:")
        print("1. 自动工单配置被禁用")
        print("2. 触发事件没有正确发送")
        print("3. BOM信息查找失败")
        print("4. 工单创建过程中出错")
    
    return 0 if work_orders_created > 0 else 1

if __name__ == "__main__":
    sys.exit(main())
