# MES系统技术实施指南

## 🎯 立即可执行的优化任务

### 1. 班次管理系统修复（高优先级）

#### 步骤1：依赖版本统一
```toml
# Cargo.toml 修改
[dependencies]
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid", "bigdecimal"] }
bigdecimal = "0.3.1"  # 与sqlx兼容的版本
```

#### 步骤2：类型定义修复
```rust
// src/models/shift.rs
use sqlx::types::BigDecimal;
use chrono::NaiveDateTime;

#[derive(FromRow, Serialize, Deserialize)]
pub struct ShiftTemplate {
    pub id: i32,
    pub name: String,
    pub start_hour: i32,
    pub start_minute: i32,        // 非可选，数据库有默认值0
    pub end_hour: i32,
    pub end_minute: i32,          // 非可选，数据库有默认值0
    pub duration_hours: BigDecimal, // 使用sqlx兼容的BigDecimal
    pub created_at: NaiveDateTime,  // 使用NaiveDateTime
    pub updated_at: NaiveDateTime,
}
```

#### 步骤3：服务层修复
```rust
// src/services/shift_service.rs
impl ShiftService {
    fn calculate_shift_duration(&self, start_hour: i32, start_minute: i32, end_hour: i32, end_minute: i32) -> BigDecimal {
        let start_minutes = start_hour as f64 * 60.0 + start_minute as f64;
        let end_minutes = end_hour as f64 * 60.0 + end_minute as f64;
        
        let duration_hours = if end_minutes > start_minutes {
            (end_minutes - start_minutes) / 60.0
        } else {
            (24.0 * 60.0 - start_minutes + end_minutes) / 60.0
        };
        
        BigDecimal::from_f64(duration_hours).unwrap_or_default()
    }
}
```

### 2. 数据库索引优化（中优先级）

#### 创建性能优化索引
```sql
-- migrations/0013_performance_indexes.sql
-- 项目相关索引
CREATE INDEX CONCURRENTLY idx_plan_tasks_project_id ON plan_tasks(project_id);
CREATE INDEX CONCURRENTLY idx_work_orders_project_id ON work_orders(project_id);

-- 状态查询索引
CREATE INDEX CONCURRENTLY idx_plan_tasks_status ON plan_tasks(status);
CREATE INDEX CONCURRENTLY idx_work_orders_status ON work_orders(status);

-- 时间范围查询索引
CREATE INDEX CONCURRENTLY idx_plan_tasks_dates ON plan_tasks(planned_start_date, planned_end_date);
CREATE INDEX CONCURRENTLY idx_work_orders_dates ON work_orders(created_at, updated_at);

-- 用户相关索引
CREATE INDEX CONCURRENTLY idx_plan_tasks_assigned_to ON plan_tasks(assigned_to);
CREATE INDEX CONCURRENTLY idx_work_orders_assigned_to ON work_orders(assigned_to);

-- 复合索引
CREATE INDEX CONCURRENTLY idx_plan_tasks_project_status ON plan_tasks(project_id, status);
CREATE INDEX CONCURRENTLY idx_work_orders_project_status ON work_orders(project_id, status);
```

### 3. 前端性能优化（中优先级）

#### 代码分割实施
```typescript
// src/App.tsx
import { lazy, Suspense } from 'react';
import { LoadingSpinner } from './components/LoadingSpinner';

// 路由级别代码分割
const Dashboard = lazy(() => import('./pages/Dashboard'));
const Projects = lazy(() => import('./pages/Projects'));
const WorkOrders = lazy(() => import('./pages/WorkOrders'));
const PlanTasks = lazy(() => import('./pages/PlanTasks'));
const GanttChart = lazy(() => import('./components/GanttChart'));

// 路由配置
const AppRoutes = () => (
  <Suspense fallback={<LoadingSpinner />}>
    <Routes>
      <Route path="/dashboard" element={<Dashboard />} />
      <Route path="/projects" element={<Projects />} />
      <Route path="/work-orders" element={<WorkOrders />} />
      <Route path="/plan-tasks" element={<PlanTasks />} />
    </Routes>
  </Suspense>
);
```

#### React Query优化配置
```typescript
// src/lib/queryClient.ts
import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,      // 5分钟内数据视为新鲜
      cacheTime: 10 * 60 * 1000,     // 10分钟缓存时间
      refetchOnWindowFocus: false,    // 窗口聚焦时不自动刷新
      refetchOnReconnect: true,       // 网络重连时刷新
      retry: 3,                       // 失败重试3次
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      retry: 1,
    },
  },
});
```

### 4. 错误处理标准化（中优先级）

#### 统一错误响应格式
```rust
// src/utils/error.rs
use axum::{response::IntoResponse, Json};
use serde_json::json;

#[derive(Debug)]
pub struct AppError {
    pub code: String,
    pub message: String,
    pub details: Option<String>,
}

impl IntoResponse for AppError {
    fn into_response(self) -> axum::response::Response {
        let status = match self.code.as_str() {
            "VALIDATION_ERROR" => StatusCode::BAD_REQUEST,
            "NOT_FOUND" => StatusCode::NOT_FOUND,
            "UNAUTHORIZED" => StatusCode::UNAUTHORIZED,
            "FORBIDDEN" => StatusCode::FORBIDDEN,
            _ => StatusCode::INTERNAL_SERVER_ERROR,
        };

        let body = Json(json!({
            "error": {
                "code": self.code,
                "message": self.message,
                "details": self.details
            }
        }));

        (status, body).into_response()
    }
}
```

#### 前端错误处理
```typescript
// src/utils/errorHandler.ts
import { message } from 'antd';

export interface ApiError {
  code: string;
  message: string;
  details?: string;
}

export const handleApiError = (error: any) => {
  if (error.response?.data?.error) {
    const apiError: ApiError = error.response.data.error;
    
    switch (apiError.code) {
      case 'VALIDATION_ERROR':
        message.error(`输入验证失败: ${apiError.message}`);
        break;
      case 'NOT_FOUND':
        message.error('请求的资源不存在');
        break;
      case 'UNAUTHORIZED':
        message.error('请先登录');
        // 重定向到登录页
        window.location.href = '/login';
        break;
      case 'FORBIDDEN':
        message.error('权限不足');
        break;
      default:
        message.error(`系统错误: ${apiError.message}`);
    }
  } else {
    message.error('网络连接失败，请检查网络设置');
  }
};
```

## 🔧 快速实施脚本

### 班次管理修复脚本
```bash
#!/bin/bash
# fix-shift-management.sh

echo "🔧 修复班次管理系统..."

# 1. 更新依赖
echo "📦 更新Cargo依赖..."
sed -i 's/bigdecimal = "0.4.8"/bigdecimal = "0.3.1"/' Cargo.toml

# 2. 重新启用模块
echo "🔄 重新启用班次管理模块..."
sed -i 's|// pub mod shift;|pub mod shift;|' src/models/mod.rs
sed -i 's|// pub mod shift_service;|pub mod shift_service;|' src/services/mod.rs
sed -i 's|// pub mod shift;|pub mod shift;|' src/handlers/mod.rs

# 3. 重新构建
echo "🏗️ 重新构建项目..."
cargo build

if [ $? -eq 0 ]; then
    echo "✅ 班次管理系统修复成功！"
else
    echo "❌ 修复失败，请检查错误信息"
    exit 1
fi
```

### 数据库优化脚本
```bash
#!/bin/bash
# optimize-database.sh

echo "🗄️ 优化数据库性能..."

# 创建索引
psql $DATABASE_URL << EOF
-- 性能优化索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_plan_tasks_project_id ON plan_tasks(project_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_plan_tasks_status ON plan_tasks(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_work_orders_project_id ON work_orders(project_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_work_orders_status ON work_orders(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_plan_tasks_dates ON plan_tasks(planned_start_date, planned_end_date);

-- 分析表统计信息
ANALYZE plan_tasks;
ANALYZE work_orders;
ANALYZE projects;
ANALYZE users;

\echo '✅ 数据库优化完成'
EOF

echo "✅ 数据库索引创建完成！"
```

### 前端优化脚本
```bash
#!/bin/bash
# optimize-frontend.sh

echo "⚡ 优化前端性能..."

cd frontend

# 1. 安装优化依赖
echo "📦 安装性能优化依赖..."
npm install --save-dev webpack-bundle-analyzer

# 2. 分析构建产物
echo "📊 分析构建产物大小..."
npm run build:prod
npx webpack-bundle-analyzer dist/assets/*.js

# 3. 清理缓存
echo "🧹 清理缓存..."
npm run clean

echo "✅ 前端优化完成！"
```

## 📋 验证清单

### 班次管理修复验证
- [ ] 编译无错误
- [ ] 班次模板CRUD功能正常
- [ ] 班次实例创建正常
- [ ] 时间计算准确
- [ ] 数据库操作正常

### 性能优化验证
- [ ] 页面加载时间 < 3秒
- [ ] API响应时间 < 500ms
- [ ] 数据库查询优化生效
- [ ] 前端包大小减少
- [ ] 内存使用稳定

### 错误处理验证
- [ ] 统一错误格式
- [ ] 用户友好错误消息
- [ ] 错误日志记录完整
- [ ] 异常情况处理正确

## 🚀 下一步计划

1. **立即执行**：班次管理修复
2. **本周内**：数据库索引优化
3. **下周**：前端性能优化
4. **两周内**：错误处理标准化
5. **一个月内**：监控和安全增强
