#!/usr/bin/env node

/**
 * 简单的代理服务器 - 不依赖复杂的中间件
 * 用于生产环境下代理API请求到后端服务
 */

const express = require('express');
const path = require('path');
const { createProxyServer } = require('http-proxy');

// 配置
const FRONTEND_PORT = process.env.FRONTEND_PORT || 3080;
const BACKEND_PORT = process.env.MES_PORT || process.env.BACKEND_PORT || 9001;
const BACKEND_URL = `http://localhost:${BACKEND_PORT}`;

console.log(`🚀 启动前端代理服务器...`);
console.log(`📁 静态文件目录: ${path.join(__dirname, 'dist')}`);
console.log(`🔗 API代理目标: ${BACKEND_URL}`);
console.log(`🌐 前端端口: ${FRONTEND_PORT}`);

const app = express();
const proxy = createProxyServer({});

// API代理处理
app.use('/api', (req, res) => {
  console.log(`🔄 代理请求: ${req.method} ${req.url} -> ${BACKEND_URL}${req.url}`);
  
  proxy.web(req, res, {
    target: BACKEND_URL,
    changeOrigin: true,
    timeout: 30000
  }, (err) => {
    console.error('❌ 代理错误:', err.message);
    if (!res.headersSent) {
      res.status(500).json({
        error: 'Proxy Error',
        message: '无法连接到后端服务',
        backend: BACKEND_URL
      });
    }
  });
});

// 静态文件服务
app.use(express.static(path.join(__dirname, 'dist'), {
  maxAge: '1d',
  etag: true,
  lastModified: true
}));

// SPA路由支持 - 所有非API请求都返回index.html
app.get('*', (req, res) => {
  // 排除API请求
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({ error: 'API endpoint not found' });
  }
  
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('❌ 服务器错误:', err);
  res.status(500).json({
    error: 'Internal Server Error',
    message: err.message
  });
});

// 启动服务器
const server = app.listen(FRONTEND_PORT, () => {
  console.log('');
  console.log('🎉 前端代理服务器启动成功！');
  console.log('');
  console.log(`📱 前端地址: http://localhost:${FRONTEND_PORT}`);
  console.log(`🔗 API代理: http://localhost:${FRONTEND_PORT}/api -> ${BACKEND_URL}/api`);
  console.log('');
  console.log('按 Ctrl+C 停止服务');
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('🛑 收到SIGTERM信号，正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 收到SIGINT信号，正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});

// 未捕获的异常处理
process.on('uncaughtException', (err) => {
  console.error('❌ 未捕获的异常:', err);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});
