# MES Frontend

基于 React + TypeScript + Vite 的制造执行系统前端应用。

## 技术栈

- **React 18** - 用户界面库
- **TypeScript** - 类型安全的 JavaScript
- **Vite** - 快速构建工具
- **Ant Design** - UI 组件库
- **React Router** - 路由管理
- **Zustand** - 状态管理
- **React Query** - 数据获取和缓存
- **Axios** - HTTP 客户端
- **Recharts** - 图表库
- **Tailwind CSS** - 样式框架

## 功能特性

### 🔐 认证系统
- JWT 令牌认证
- 基于角色的访问控制
- 自动令牌刷新

### 📊 仪表板
- 生产概览统计
- 实时数据图表
- KPI 指标监控

### 🏭 核心功能
- **项目管理** - 项目创建、编辑、删除
- **零件管理** - 零件主数据维护
- **设备管理** - 设备状态监控
- **工单管理** - 生产工单跟踪
- **生产计划** - 任务调度和计划
- **执行跟踪** - 实时生产执行
- **质量管理** - 质量检验流程
- **用户管理** - 用户权限管理

## 快速开始

### 环境要求
- Node.js >= 18.0.0
- npm 或 yarn

### 安装依赖
```bash
npm install
# 或
yarn install
```

### 开发模式
```bash
npm run dev
# 或
yarn dev
```

应用将在 http://localhost:3000 启动

### 生产模式

#### 方法一：使用启动脚本（推荐）
```bash
# Linux/macOS
./start-production.sh

# Windows
start-production.bat
```

#### 方法二：手动构建和启动
```bash
# 1. 构建生产版本
npm run build:prod

# 2. 启动代理服务器
npm run serve:proxy
```

访问 http://localhost:3080

### 预览模式（仅静态文件，无API代理）
```bash
npm run preview
```

访问 http://localhost:3000

## 开发模式 vs 生产模式

| 特性 | 开发模式 | 生产模式 |
|------|----------|----------|
| 端口 | 3000 | 3080 |
| 构建 | 实时编译 | 预构建优化 |
| 热重载 | ✅ | ❌ |
| 源码映射 | 详细 | 压缩 |
| 文件大小 | 大 | 小（压缩） |
| 启动速度 | 快 | 中等 |
| 性能 | 一般 | 优化 |
| 调试 | 容易 | 困难 |

**使用建议**：
- 开发时使用开发模式（`npm run dev`）
- 测试和部署时使用生产模式（`./start-production.sh`）

## 项目结构

```
src/
├── components/          # 通用组件
│   ├── Layout.tsx      # 主布局组件
│   └── LoadingSpinner.tsx
├── lib/                # 工具库
│   └── api.ts          # API 客户端
├── pages/              # 页面组件
│   ├── Dashboard.tsx   # 仪表板
│   ├── Login.tsx       # 登录页
│   ├── Projects.tsx    # 项目管理
│   ├── Parts.tsx       # 零件管理
│   ├── Machines.tsx    # 设备管理
│   ├── WorkOrders.tsx  # 工单管理
│   ├── PlanTasks.tsx   # 生产计划
│   ├── Execution.tsx   # 执行跟踪
│   ├── Quality.tsx     # 质量管理
│   └── Users.tsx       # 用户管理
├── store/              # 状态管理
│   └── auth.ts         # 认证状态
├── types/              # TypeScript 类型
│   └── api.ts          # API 类型定义
├── App.tsx             # 主应用组件
├── App.css             # 全局样式
└── main.tsx            # 应用入口
```

## API 集成

前端通过 Axios 与后端 API 通信，支持：

- 自动 JWT 令牌注入
- 请求/响应拦截器
- 错误处理和重试
- 类型安全的 API 调用

### API 客户端使用示例

```typescript
import { apiClient } from '@/lib/api';

// 获取项目列表
const projects = await apiClient.getProjects();

// 创建新项目
const newProject = await apiClient.createProject({
  project_name: '新项目',
  customer_name: '客户名称'
});
```

## 状态管理

使用 Zustand 进行轻量级状态管理：

```typescript
import { useAuthStore } from '@/store/auth';

const { user, login, logout } = useAuthStore();
```

## 数据获取

使用 React Query 进行数据获取和缓存：

```typescript
import { useQuery } from 'react-query';
import { apiClient } from '@/lib/api';

const { data, isLoading, error } = useQuery(
  'projects',
  () => apiClient.getProjects()
);
```

## 样式系统

结合 Ant Design 和 Tailwind CSS：

- Ant Design 提供组件样式
- Tailwind CSS 提供工具类
- 自定义 CSS 用于特殊样式

## 开发指南

### 添加新页面

1. 在 `src/pages/` 创建新组件
2. 在 `src/App.tsx` 添加路由
3. 在 `src/components/Layout.tsx` 添加菜单项

### 添加新 API

1. 在 `src/types/api.ts` 定义类型
2. 在 `src/lib/api.ts` 添加 API 方法
3. 在组件中使用 React Query 调用

### 代码规范

- 使用 TypeScript 严格模式
- 遵循 ESLint 规则
- 组件使用函数式组件 + Hooks
- 使用 React Query 进行数据管理

## 部署

### 环境变量

创建 `.env` 文件：

```env
VITE_API_BASE_URL=http://localhost:8080/api
```

### Docker 部署

```dockerfile
FROM node:18-alpine as builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 环境变量配置

### 开发模式配置
创建 `.env.development` 文件：
```bash
# 开发模式使用Vite代理
VITE_API_URL=/api
```

### 生产模式配置
`.env.production` 文件：
```bash
# 生产模式使用相对路径，通过代理服务器转发
VITE_API_URL=/api
```

### 通用配置
`.env` 文件：
```bash
# 应用配置
VITE_APP_TITLE=MES制造执行系统
VITE_APP_VERSION=1.0.0

# 开发服务器配置
VITE_PORT=3000
VITE_HOST=localhost
```

### ⚠️ 重要提示
- **不要**在 `.env.production` 中设置直连后端的URL（如 `http://localhost:9001/api`）
- **必须**使用相对路径 `/api` 以确保请求通过代理服务器转发
- 这样可以避免CORS问题和网络连接问题

## 浏览器支持

- Chrome >= 88
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 许可证

MIT License
