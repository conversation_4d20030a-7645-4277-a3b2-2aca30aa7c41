#!/bin/bash

# MES前端生产模式验证脚本

echo "🔍 验证MES前端生产模式配置..."
echo ""

# 检查环境变量配置
echo "📋 检查环境变量配置..."
if [ -f ".env.production" ]; then
    echo "✅ .env.production 文件存在"
    if grep -q "VITE_API_URL=/api" .env.production; then
        echo "✅ VITE_API_URL 配置正确 (/api)"
    else
        echo "❌ VITE_API_URL 配置错误，应该设置为 /api"
        echo "   当前配置："
        grep "VITE_API_URL" .env.production || echo "   未找到 VITE_API_URL 配置"
        exit 1
    fi
else
    echo "❌ .env.production 文件不存在"
    exit 1
fi

# 检查构建产物
echo ""
echo "📦 检查构建产物..."
if [ -d "dist" ]; then
    echo "✅ dist 目录存在"
    if [ -f "dist/index.html" ]; then
        echo "✅ index.html 存在"
    else
        echo "❌ index.html 不存在，请运行 npm run build:prod"
        exit 1
    fi
    
    # 检查是否包含直连后端的代码
    FOUND_9001=$(find dist -name "*.js" -exec grep -l ":9001" {} \; 2>/dev/null | head -1)
    if [ -n "$FOUND_9001" ]; then
        echo "❌ 构建产物中仍包含直连后端的代码 (:9001)"
        echo "   文件: $FOUND_9001"
        echo "   请确保 .env.production 中 VITE_API_URL=/api，然后重新构建"
        exit 1
    else
        echo "✅ 构建产物中没有直连后端的代码"
    fi
else
    echo "❌ dist 目录不存在，请运行 npm run build:prod"
    exit 1
fi

# 检查代理服务器文件
echo ""
echo "🔧 检查代理服务器..."
if [ -f "simple-proxy-fixed.cjs" ]; then
    echo "✅ 代理服务器文件存在"
else
    echo "❌ simple-proxy-fixed.cjs 不存在"
    exit 1
fi

# 检查后端服务
echo ""
echo "🔍 检查后端服务..."
if curl -s http://localhost:9001/api/health > /dev/null 2>&1; then
    echo "✅ 后端服务运行正常"
else
    echo "⚠️  后端服务未运行，请先启动后端服务"
    echo "   在项目根目录运行: cargo run"
fi

# 检查前端代理服务
echo ""
echo "🌐 检查前端代理服务..."
if curl -s http://localhost:3080 > /dev/null 2>&1; then
    echo "✅ 前端代理服务运行正常"
    
    # 测试API代理
    echo ""
    echo "🔄 测试API代理..."
    if curl -s -X POST http://localhost:3080/api/auth/login \
        -H "Content-Type: application/json" \
        -d '{"username":"admin","password":"admin123"}' > /dev/null 2>&1; then
        echo "✅ API代理工作正常"
    else
        echo "❌ API代理测试失败"
        echo "   请检查后端服务是否正常运行"
        exit 1
    fi
else
    echo "⚠️  前端代理服务未运行"
    echo "   运行以下命令启动："
    echo "   npm run serve:proxy"
    echo "   或"
    echo "   ./start-production.sh"
fi

echo ""
echo "🎉 验证完成！"
echo ""
echo "📱 前端地址: http://localhost:3080"
echo "🔗 API代理: http://localhost:3080/api -> http://localhost:9001/api"
echo ""
echo "💡 如果遇到问题，请查看 TROUBLESHOOTING.md 文档"
