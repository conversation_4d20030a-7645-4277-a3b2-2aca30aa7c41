# MES前端故障排除指南

## 生产模式API连接问题

### 问题描述
生产模式下前端无法连接到后端API，出现 `ERR_CONNECTION_REFUSED` 错误。

### 根本原因
前端在生产模式下错误地尝试直接连接到后端服务器，而不是通过代理服务器。主要原因是 `.env.production` 文件中设置了直连后端的URL。

### 解决方案
✅ **已修复**：
1. 修改了 `.env.production` 文件中的 `VITE_API_URL` 为 `/api`
2. 前端现在在开发模式和生产模式下都使用相对路径 `/api`，通过代理服务器转发请求

### 技术细节

#### 修复前的问题
```bash
# .env.production (错误配置)
VITE_API_URL=http://[::1]:9001/api  # 直连后端，绕过代理
```

#### 修复后的配置
```bash
# .env.production (正确配置)
VITE_API_URL=/api  # 使用相对路径，通过代理转发
```

#### 代码逻辑
```javascript
// API基础URL获取逻辑
const getApiBaseUrl = () => {
  if (import.meta.env.VITE_API_URL) {
    return import.meta.env.VITE_API_URL;  // 使用环境变量
  }
  return '/api';  // 默认使用相对路径代理
};
```

## 常见问题和解决方案

### 1. 前端无法启动

**症状**：
- 运行 `npm run serve:proxy` 失败
- 端口被占用错误

**解决方案**：
```bash
# 检查端口占用
lsof -i :3080
# 或
netstat -tulpn | grep 3080

# 杀死占用进程
kill -9 <PID>

# 或使用不同端口
FRONTEND_PORT=3081 npm run serve:proxy
```

### 2. API请求失败

**症状**：
- 登录失败
- API返回404或500错误

**检查步骤**：
1. 确认后端服务运行正常：
   ```bash
   curl http://localhost:9001/api/auth/login
   ```

2. 确认代理服务器正常：
   ```bash
   curl http://localhost:3080/api/auth/login
   ```

3. 检查代理服务器日志是否显示请求转发

### 3. 静态文件404错误

**症状**：
- CSS/JS文件加载失败
- 页面样式丢失

**解决方案**：
1. 确认构建产物存在：
   ```bash
   ls -la dist/
   ```

2. 重新构建：
   ```bash
   npm run build:prod
   ```

3. 重启代理服务器

### 4. 路由404错误

**症状**：
- 刷新页面后出现404
- 直接访问子路由失败

**原因**：SPA路由配置问题

**解决方案**：
代理服务器已配置fallback到index.html，如果仍有问题，检查：
1. 代理服务器配置是否正确
2. 路由路径是否正确

### 5. CORS错误

**症状**：
- 浏览器控制台显示CORS错误
- API请求被阻止

**解决方案**：
代理服务器已配置CORS头，如果仍有问题：
1. 检查代理服务器是否正常运行
2. 确认请求通过代理而不是直连后端

## 调试技巧

### 1. 检查网络请求
1. 打开浏览器开发者工具
2. 切换到Network标签
3. 查看API请求的URL和状态
4. 确认请求发送到 `http://localhost:3080/api/...` 而不是 `http://localhost:9001/api/...`

### 2. 查看代理日志
代理服务器会输出详细的请求日志：
```
🔄 代理请求: POST /api/auth/login -> http://localhost:9001/api/auth/login
```

### 3. 测试API连接
```bash
# 测试后端直连
curl -X POST http://localhost:9001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 测试代理连接
curl -X POST http://localhost:3080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

## 环境变量配置

### 开发模式
```bash
# .env.development
VITE_API_URL=/api  # 使用Vite代理
```

### 生产模式
```bash
# 环境变量
FRONTEND_PORT=3080      # 前端端口
BACKEND_PORT=9001       # 后端端口
VITE_API_URL=/api       # API基础URL（可选）
```

## 性能监控

### 1. 响应时间监控
- 监控API请求响应时间
- 监控静态文件加载时间

### 2. 错误监控
- 监控JavaScript错误
- 监控API错误率

### 3. 资源使用监控
- 监控内存使用
- 监控CPU使用

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 错误信息和堆栈跟踪
2. 浏览器开发者工具的Network标签截图
3. 代理服务器日志
4. 系统环境信息（Node.js版本、操作系统等）
