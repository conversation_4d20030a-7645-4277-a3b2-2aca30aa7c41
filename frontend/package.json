{"name": "mes-frontend", "private": true, "version": "1.0.0", "type": "module", "description": "Manufacturing Execution System Frontend", "scripts": {"dev": "vite", "build": "tsc && vite build", "build-skip-check": "vite build", "build:prod": "npm run type-check && vite build --mode production", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "type-check": "tsc --noEmit", "serve": "python3 -m http.server 8080 --directory dist", "serve:win": "python -m http.server 8080 --directory dist", "serve:proxy": "node simple-proxy-fixed.cjs", "serve:proxy-old": "node simple-proxy.cjs", "start:prod": "npm run build:prod && npm run serve", "clean": "rm -rf dist node_modules/.vite", "test:api": "curl -X POST http://localhost:3080/api/auth/login -H 'Content-Type: application/json' -d '{\"username\":\"admin\",\"password\":\"admin123\"}'", "test:backend": "curl -X POST http://localhost:9001/api/auth/login -H 'Content-Type: application/json' -d '{\"username\":\"admin\",\"password\":\"admin123\"}'", "verify:production": "./verify-production.sh", "docker:build": "docker build -t mes-frontend .", "docker:run": "docker run -p 8080:80 mes-frontend"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.3.2", "@types/react-window": "^1.8.8", "antd": "^5.12.8", "axios": "^1.6.2", "date-fns": "^4.1.0", "dayjs": "^1.11.10", "express": "^5.1.0", "http-proxy": "^1.18.1", "http-proxy-middleware": "^3.0.5", "i18next": "^25.3.2", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-i18next": "^15.6.1", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "react-window": "^1.8.11", "recharts": "^2.8.0", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^24.0.14", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "terser": "^5.43.1", "typescript": "^5.2.2", "vite": "^5.0.8", "webpack-bundle-analyzer": "^4.10.2"}, "engines": {"node": ">=18.0.0"}}