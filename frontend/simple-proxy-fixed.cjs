#!/usr/bin/env node

/**
 * 修复版本的简单代理服务器
 * 用于生产环境下代理API请求到后端服务
 */

const http = require('http');
const httpProxy = require('http-proxy');
const fs = require('fs');
const path = require('path');
const url = require('url');

// 配置
const FRONTEND_PORT = process.env.FRONTEND_PORT || 3080;
const BACKEND_PORT = process.env.MES_PORT || process.env.BACKEND_PORT || 9001;
const BACKEND_URL = `http://localhost:${BACKEND_PORT}`;
const DIST_DIR = path.join(__dirname, 'dist');

console.log(`🚀 启动前端代理服务器...`);
console.log(`📁 静态文件目录: ${DIST_DIR}`);
console.log(`🔗 API代理目标: ${BACKEND_URL}`);
console.log(`🌐 前端端口: ${FRONTEND_PORT}`);

// 创建代理服务器
const proxy = httpProxy.createProxyServer({});

// MIME类型映射
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'text/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.ico': 'image/x-icon'
};

// 获取MIME类型
function getMimeType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  return mimeTypes[ext] || 'application/octet-stream';
}

// 服务静态文件
function serveStaticFile(res, filePath) {
  fs.readFile(filePath, (err, data) => {
    if (err) {
      res.writeHead(404, { 'Content-Type': 'text/plain' });
      res.end('File not found');
      return;
    }
    
    const mimeType = getMimeType(filePath);
    res.writeHead(200, { 
      'Content-Type': mimeType,
      'Cache-Control': 'public, max-age=86400'
    });
    res.end(data);
  });
}

// 创建HTTP服务器
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;

  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // 处理OPTIONS请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // API请求代理到后端
  if (pathname.startsWith('/api/')) {
    console.log(`🔄 代理请求: ${req.method} ${pathname} -> ${BACKEND_URL}${pathname}`);
    
    proxy.web(req, res, {
      target: BACKEND_URL,
      changeOrigin: true,
      timeout: 30000
    }, (err) => {
      console.error('❌ 代理错误:', err.message);
      if (!res.headersSent) {
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          error: 'Proxy Error',
          message: '无法连接到后端服务',
          backend: BACKEND_URL
        }));
      }
    });
    return;
  }

  // 静态文件服务
  let filePath = path.join(DIST_DIR, pathname === '/' ? 'index.html' : pathname);
  
  // 检查文件是否存在
  fs.access(filePath, fs.constants.F_OK, (err) => {
    if (err) {
      // 文件不存在，对于SPA路由返回index.html
      if (pathname !== '/' && !pathname.includes('.')) {
        filePath = path.join(DIST_DIR, 'index.html');
        serveStaticFile(res, filePath);
      } else {
        res.writeHead(404, { 'Content-Type': 'text/plain' });
        res.end('File not found');
      }
    } else {
      serveStaticFile(res, filePath);
    }
  });
});

// 启动服务器
server.listen(FRONTEND_PORT, () => {
  console.log('');
  console.log('🎉 前端代理服务器启动成功！');
  console.log('');
  console.log(`📱 前端地址: http://localhost:${FRONTEND_PORT}`);
  console.log(`🔗 API代理: http://localhost:${FRONTEND_PORT}/api -> ${BACKEND_URL}/api`);
  console.log('');
  console.log('按 Ctrl+C 停止服务');
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('🛑 收到SIGTERM信号，正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 收到SIGINT信号，正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});

// 未捕获的异常处理
process.on('uncaughtException', (err) => {
  console.error('❌ 未捕获的异常:', err);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});
