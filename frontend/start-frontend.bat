@echo off
setlocal enabledelayedexpansion

REM MES系统前端Windows启动脚本

echo ========================================
echo MES系统前端启动脚本 (Windows)
echo ========================================
echo.

REM 设置变量
set SCRIPT_DIR=%~dp0
set DIST_DIR=%SCRIPT_DIR%dist
set NODE_MODULES=%SCRIPT_DIR%node_modules

REM 检查Node.js
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo [错误] Node.js未安装，请先安装Node.js
    pause
    exit /b 1
)

REM 检查npm
where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo [错误] npm未安装，请先安装npm
    pause
    exit /b 1
)

echo [信息] Node.js版本:
node --version
echo [信息] npm版本:
npm --version
echo.

REM 检查是否有构建文件
if exist "%DIST_DIR%\index.html" (
    echo [信息] 发现生产构建文件，启动生产模式...
    echo [信息] 访问地址: http://localhost:8080
    echo [信息] 按 Ctrl+C 停止服务
    echo.
    
    cd /d "%DIST_DIR%"
    
    REM 尝试使用Python启动静态服务器
    where python >nul 2>nul
    if %errorlevel% equ 0 (
        echo [信息] 使用Python HTTP服务器...
        python -m http.server 8080
    ) else (
        echo [信息] Python未安装，使用npx serve...
        cd /d "%SCRIPT_DIR%"
        npx serve dist -l 8080
    )
) else (
    echo [信息] 未发现构建文件，启动开发模式...
    
    REM 检查依赖
    if not exist "%NODE_MODULES%" (
        echo [信息] 安装依赖...
        npm install
    )
    
    echo [信息] 启动Vite开发服务器...
    echo [信息] 访问地址: http://localhost:3000
    echo [信息] 按 Ctrl+C 停止服务
    echo.
    
    npm run dev
)

pause
