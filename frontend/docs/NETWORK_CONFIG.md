# MES 系统网络配置指南

## 🌐 外部访问配置

### 问题描述
当通过外部域名（如 `ql.hjch.cf`）访问 Vite 开发服务器时，可能会遇到以下错误：
```
Blocked request. This host ("ql.hjch.cf") is not allowed.
To allow this host, add "ql.hjch.cf" to `server.allowedHosts` in vite.config.js.
```

### 解决方案

#### 1. Vite 配置更新
已在 `vite.config.ts` 中添加 `allowedHosts` 配置：

```typescript
export default defineConfig({
  server: {
    host: '::', // 允许外部访问 (IPv6 双栈)
    port: 3000,
    allowedHosts: [
      'localhost',
      '127.0.0.1',
      '::1',
      'ql.hjch.cf',
      '.hjch.cf', // 允许所有 hjch.cf 子域名
      'all', // 允许所有主机（开发环境使用）
    ],
    proxy: {
      '/api': {
        target: 'http://localhost:9000',
        changeOrigin: true,
        secure: false,
      },
    },
  },
  preview: {
    host: '::', // 允许外部访问 (IPv6 双栈)
    port: 9999,
    allowedHosts: [
      'localhost',
      '127.0.0.1',
      '::1',
      'ql.hjch.cf',
      '.hjch.cf', // 允许所有 hjch.cf 子域名
      'all', // 允许所有主机（开发环境使用）
    ],
  },
});
```

#### 2. 重启开发服务器
配置更新后，需要重启 Vite 开发服务器：

```bash
# 停止当前服务器 (Ctrl+C)
# 然后重新启动
npm run dev
# 或
yarn dev
```

### 🔧 配置说明

#### allowedHosts 选项
- `'localhost'`: 允许本地主机访问
- `'127.0.0.1'`: 允许本地 IPv4 地址访问
- `'::1'`: 允许本地 IPv6 地址访问
- `'ql.hjch.cf'`: 允许特定域名访问
- `'.hjch.cf'`: 允许所有 hjch.cf 子域名访问
- `'all'`: 允许所有主机访问（仅开发环境推荐）

#### host 配置
- `'::'`: IPv6 双栈模式，同时支持 IPv4 和 IPv6
- `'0.0.0.0'`: 仅 IPv4，允许外部访问
- `'localhost'`: 仅本地访问

### 🚀 启动命令

#### 开发模式
```bash
# 启动开发服务器
npm run dev

# 或使用 yarn
yarn dev

# 服务器将在以下地址可用：
# - http://localhost:3000
# - http://127.0.0.1:3000
# - http://[::1]:3000
# - http://ql.hjch.cf:3000 (如果域名解析正确)
```

#### 预览模式
```bash
# 构建并预览
npm run build
npm run preview

# 或使用 yarn
yarn build
yarn preview

# 预览服务器将在端口 9999 启动
```

### 🔒 安全考虑

#### 开发环境
- 使用 `'all'` 允许所有主机访问，方便开发和测试
- 确保开发服务器不暴露在公网上

#### 生产环境
- 移除 `'all'` 选项
- 只允许必要的域名
- 使用 HTTPS 和适当的安全头

### 🌍 域名解析

确保域名 `ql.hjch.cf` 正确解析到运行 Vite 服务器的机器：

```bash
# 检查域名解析
nslookup ql.hjch.cf

# 或使用 dig
dig ql.hjch.cf

# 检查端口连通性
telnet ql.hjch.cf 3000
```

### 🐛 常见问题

#### 1. 仍然无法访问
- 检查防火墙设置，确保端口 3000 开放
- 确认域名解析正确
- 重启 Vite 开发服务器

#### 2. API 请求失败
- 检查后端服务是否运行在正确端口
- 确认代理配置正确
- 检查 CORS 设置

#### 3. 移动端测试
- 确保移动设备和开发机器在同一网络
- 使用机器的 IP 地址而不是 localhost
- 检查移动端浏览器的网络设置

### 📱 移动端访问

#### 获取本机 IP 地址
```bash
# Windows
ipconfig

# macOS/Linux
ifconfig
# 或
ip addr show
```

#### 移动端访问地址
```
# 使用本机 IP 地址
http://*************:3000

# 使用域名（如果配置了 DNS）
http://ql.hjch.cf:3000
```

### 🔄 热重载

Vite 的热重载功能在外部访问时也能正常工作：
- 文件修改后自动刷新
- 保持开发状态
- 支持 HMR (Hot Module Replacement)

### 📊 性能监控

在外部访问时，可以使用以下工具监控性能：
- Chrome DevTools
- Lighthouse
- Web Vitals 扩展

### 🔧 高级配置

#### 自定义端口
```typescript
server: {
  port: process.env.PORT || 3000,
  // 其他配置...
}
```

#### 环境变量
```bash
# .env.local
VITE_PORT=3000
VITE_HOST=0.0.0.0
```

#### HTTPS 配置
```typescript
server: {
  https: {
    key: fs.readFileSync('path/to/key.pem'),
    cert: fs.readFileSync('path/to/cert.pem'),
  },
  // 其他配置...
}
```

### 📝 总结

通过以上配置，MES 系统现在支持：
- ✅ 外部域名访问
- ✅ 移动端设备访问
- ✅ IPv4 和 IPv6 双栈支持
- ✅ 开发和预览模式
- ✅ 热重载功能

记住在配置更新后重启开发服务器，以确保新配置生效！
