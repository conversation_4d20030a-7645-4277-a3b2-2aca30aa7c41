# MES 系统移动端适配指南

## 📱 概述

本文档描述了 MES 系统的移动端适配实现，包括响应式设计、触摸优化、移动端导航等功能。

## 🎯 适配目标

- **响应式布局**: 适配各种屏幕尺寸（手机、平板、桌面）
- **触摸友好**: 优化触摸交互体验
- **性能优化**: 针对移动设备的性能优化
- **用户体验**: 提供原生应用般的使用体验

## 🏗️ 技术实现

### 1. 响应式布局系统

#### 断点设置
```css
/* 移动端 */
@media (max-width: 768px) {
  /* 移动端样式 */
}

/* 桌面端 */
@media (min-width: 769px) {
  /* 桌面端样式 */
}
```

#### 布局组件 (Layout.tsx)
- **移动端**: 顶部导航栏 + 侧边抽屉菜单 + 底部导航栏
- **桌面端**: 传统的侧边栏 + 顶部栏布局

### 2. 移动端组件库

#### MobileTable 组件
- 将传统表格转换为卡片列表
- 支持主要字段和次要字段配置
- 移动端友好的操作菜单

```tsx
<MobileTable
  columns={columns}
  dataSource={data}
  primaryFields={['name']}
  secondaryFields={['status', 'type']}
  actions={[
    { key: 'edit', label: '编辑', icon: <EditOutlined /> },
    { key: 'delete', label: '删除', danger: true }
  ]}
/>
```

#### MobileForm 组件
- 自动适配移动端和桌面端显示模式
- 移动端使用抽屉，桌面端使用模态框
- 优化的表单项间距和按钮布局

```tsx
<MobileForm
  form={form}
  title="表单标题"
  visible={visible}
  onFinish={handleSubmit}
  onCancel={handleCancel}
>
  <Form.Item name="field" label="字段">
    <Input />
  </Form.Item>
</MobileForm>
```

#### MobileInput 组件
- 防止 iOS 缩放的字体大小设置
- 移动端优化的输入框高度
- 支持多种输入类型（文本、密码、搜索、数字等）

```tsx
<MobileInput
  type="text"
  placeholder="请输入内容"
  clearable
  autoFocus
/>
```

### 3. 触摸手势支持

#### useTouchGestures Hook
支持多种触摸手势：
- 点击 (Tap)
- 双击 (Double Tap)
- 长按 (Long Press)
- 拖拽 (Pan)
- 缩放 (Pinch)
- 旋转 (Rotate)
- 滑动 (Swipe)

```tsx
const { bindGestures } = useTouchGestures({
  onTap: (point) => console.log('点击', point),
  onPan: (delta, velocity) => console.log('拖拽', delta),
  onPinch: (scale, center) => console.log('缩放', scale),
});

useEffect(() => {
  return bindGestures(elementRef.current);
}, [bindGestures]);
```

### 4. 移动端导航

#### 底部导航栏 (MobileBottomNav)
- 快速访问主要功能
- 滚动时自动隐藏/显示
- 支持角色权限控制

#### 手势导航 (MobileGestureNav)
- 支持滑动手势导航
- 左右滑动切换页面
- 上下滑动刷新/加载

### 5. 性能优化

#### 懒加载图片 (LazyImage)
- Intersection Observer API 实现
- 支持 WebP 格式优化
- 移动端图片质量调整

#### 虚拟滚动 (VirtualScroll)
- 长列表性能优化
- 只渲染可见区域的项目
- 支持动态高度

#### 性能监控 (useMobilePerformance)
- Web Vitals 指标监控
- 加载时间统计
- 性能问题诊断

## 📋 使用指南

### 1. 开发新页面

创建移动端友好的页面：

```tsx
import React from 'react';
import { Card, Button } from 'antd';
import MobileTable from '@/components/MobileTable';

const MyPage: React.FC = () => {
  return (
    <div style={{ padding: '16px' }}>
      <Card title="页面标题">
        {/* 使用移动端组件 */}
        <MobileTable {...props} />
      </Card>
    </div>
  );
};
```

### 2. 样式编写规范

```css
/* 使用移动端优先的响应式设计 */
.my-component {
  /* 移动端样式 */
  padding: 12px;
  font-size: 14px;
}

@media (min-width: 769px) {
  .my-component {
    /* 桌面端样式 */
    padding: 24px;
    font-size: 16px;
  }
}
```

### 3. 触摸事件处理

```tsx
// 为交互组件添加触摸支持
const MyInteractiveComponent = () => {
  const { bindGestures } = useTouchGestures({
    onTap: handleTap,
    onPan: handlePan,
  });

  return (
    <div ref={(el) => el && bindGestures(el)}>
      {/* 组件内容 */}
    </div>
  );
};
```

## 🧪 测试指南

### 1. 测试页面
访问 `/mobile-test` 页面查看各种移动端组件的效果。

### 2. 测试设备
- **手机**: iPhone、Android 各种尺寸
- **平板**: iPad、Android 平板
- **桌面**: 不同分辨率的显示器

### 3. 测试项目
- [ ] 响应式布局在各种屏幕尺寸下正常显示
- [ ] 触摸操作响应正常
- [ ] 表格在移动端显示为卡片列表
- [ ] 表单在移动端使用抽屉显示
- [ ] 底部导航栏功能正常
- [ ] 图片懒加载工作正常
- [ ] 性能指标在合理范围内

## 🔧 配置选项

### 1. 断点配置
在 `tailwind.config.js` 中配置响应式断点：

```javascript
module.exports = {
  theme: {
    screens: {
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
    }
  }
}
```

### 2. 移动端检测
```tsx
const [isMobile, setIsMobile] = useState(false);

useEffect(() => {
  const checkIsMobile = () => {
    setIsMobile(window.innerWidth <= 768);
  };

  checkIsMobile();
  window.addEventListener('resize', checkIsMobile);
  return () => window.removeEventListener('resize', checkIsMobile);
}, []);
```

## 🚀 最佳实践

### 1. 设计原则
- **移动端优先**: 先设计移动端，再适配桌面端
- **触摸友好**: 按钮和点击区域至少 44px
- **简洁明了**: 移动端界面保持简洁
- **性能优先**: 优化加载速度和交互响应

### 2. 开发建议
- 使用提供的移动端组件
- 遵循响应式设计规范
- 测试各种设备和屏幕尺寸
- 关注性能指标

### 3. 常见问题
- **iOS 缩放问题**: 使用 16px 字体大小
- **触摸延迟**: 使用 `touch-action: manipulation`
- **滚动性能**: 使用 `-webkit-overflow-scrolling: touch`
- **安全区域**: 使用 `env(safe-area-inset-*)`

## 📊 性能指标

### 目标指标
- **首次内容绘制 (FCP)**: < 1.5s
- **最大内容绘制 (LCP)**: < 2.5s
- **首次输入延迟 (FID)**: < 100ms
- **累积布局偏移 (CLS)**: < 0.1

### 监控方法
使用 `useMobilePerformance` Hook 监控性能指标：

```tsx
const performance = useMobilePerformance();
console.log('性能指标:', performance);
```

## 🔄 持续改进

### 1. 用户反馈
- 收集移动端用户使用反馈
- 分析用户行为数据
- 持续优化用户体验

### 2. 技术更新
- 跟进移动端技术发展
- 更新组件库和工具
- 优化性能和兼容性

### 3. 测试覆盖
- 扩大测试设备范围
- 增加自动化测试
- 定期性能测试
