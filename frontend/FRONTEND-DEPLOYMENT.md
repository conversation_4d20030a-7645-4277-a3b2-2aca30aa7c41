# MES系统前端部署指南

本文档介绍如何构建和部署MES系统前端。

## 📦 构建前端

### 开发环境要求
- Node.js 16+ 
- npm 8+
- 现代浏览器支持

### 构建命令
```bash
# 安装依赖
npm install

# 开发模式
npm run dev

# 生产构建
npm run build

# 生产构建 (跳过类型检查)
npm run build-skip-check

# 预览生产构建
npm run preview

# 类型检查
npm run type-check

# 代码检查
npm run lint
```

## 🚀 启动脚本

### 1. 快速启动 (推荐)
```bash
# 自动检测模式启动
./quick-start-frontend.sh
```
- 如果存在 `dist/` 目录，启动生产模式 (端口8080)
- 否则启动开发模式 (端口3000)

### 2. 完整启动脚本
```bash
# 开发模式
./start-frontend.sh dev
./start-frontend.sh dev -p 3001        # 指定端口
./start-frontend.sh dev -o             # 自动打开浏览器

# 生产模式
./start-frontend.sh prod
./start-frontend.sh prod -p 8080       # 指定端口

# 构建
./start-frontend.sh build

# 预览
./start-frontend.sh preview
```

### 3. npm脚本
```bash
# 开发模式
npm run dev

# 生产构建并启动
npm run start:prod

# 仅启动静态服务器 (需要先构建)
npm run serve           # Linux/Mac
npm run serve:win       # Windows

# 清理缓存
npm run clean
```

### 4. Windows启动
```cmd
# Windows批处理文件
start-frontend.bat
```

## 🐳 Docker部署

### 构建Docker镜像
```bash
# 构建镜像
docker build -t mes-frontend .

# 或使用npm脚本
npm run docker:build
```

### 运行Docker容器
```bash
# 运行容器
docker run -p 8080:80 mes-frontend

# 或使用npm脚本
npm run docker:run

# 后台运行
docker run -d -p 8080:80 --name mes-frontend mes-frontend
```

### Docker Compose
```yaml
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - "8080:80"
    depends_on:
      - backend
    environment:
      - VITE_API_URL=http://backend:9001
```

## 🌐 生产环境部署

### 1. 静态文件服务器

#### Nginx配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/mes-frontend;
    index index.html;

    # 启用gzip压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://localhost:9001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

#### Apache配置
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/mes-frontend
    
    # 启用压缩
    LoadModule deflate_module modules/mod_deflate.so
    <Location />
        SetOutputFilter DEFLATE
    </Location>
    
    # SPA路由支持
    <Directory "/var/www/mes-frontend">
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
</VirtualHost>
```

### 2. CDN部署

#### 上传到CDN
```bash
# 构建生产版本
npm run build

# 上传到CDN (示例)
aws s3 sync dist/ s3://your-bucket-name --delete
```

#### 配置CDN缓存
- HTML文件: 不缓存或短时间缓存
- JS/CSS文件: 长时间缓存 (文件名包含hash)
- 图片/字体: 长时间缓存

## 🔧 环境配置

### 环境变量
创建 `.env` 文件：
```env
# API配置
VITE_API_URL=http://localhost:9001
VITE_API_TIMEOUT=10000

# 应用配置
VITE_APP_TITLE=MES制造执行系统
VITE_APP_VERSION=1.0.0

# 开发配置
VITE_DEV_PORT=3000
VITE_DEV_HOST=localhost
```

### 不同环境配置
```bash
# 开发环境
.env.development

# 生产环境
.env.production

# 测试环境
.env.test
```

## 📊 性能优化

### 1. 构建优化
- 代码分割 (已配置)
- Tree shaking (已启用)
- 资源压缩 (已启用)
- 图片优化

### 2. 运行时优化
- 懒加载路由
- 组件懒加载
- 虚拟滚动 (大列表)
- 缓存策略

### 3. 网络优化
- CDN加速
- HTTP/2
- 资源预加载
- 服务端渲染 (可选)

## 🔍 监控和调试

### 开发工具
```bash
# 开发模式 (热重载)
npm run dev

# 类型检查
npm run type-check

# 代码检查
npm run lint

# 构建分析
npm run build -- --analyze
```

### 生产监控
- 错误监控 (Sentry)
- 性能监控 (Web Vitals)
- 用户行为分析
- 日志收集

## 🚨 故障排除

### 常见问题

1. **构建失败**
```bash
# 清理缓存
npm run clean
npm install

# 跳过类型检查构建
npm run build-skip-check
```

2. **端口被占用**
```bash
# 查看端口占用
lsof -i :3000
netstat -tulpn | grep :3000

# 杀死进程
kill -9 <PID>
```

3. **路由404问题**
- 确保服务器配置了SPA路由支持
- 检查 `try_files` 配置

4. **API连接失败**
- 检查后端服务是否运行
- 验证API URL配置
- 检查CORS设置

### 日志查看
```bash
# 开发模式日志
# 浏览器控制台

# 生产模式日志
# 服务器访问日志
tail -f /var/log/nginx/access.log

# 错误日志
tail -f /var/log/nginx/error.log
```

## 📱 移动端支持

### 响应式设计
- 已配置Ant Design响应式组件
- 支持移动端触摸操作
- 自适应布局

### PWA支持 (可选)
```bash
# 添加PWA插件
npm install vite-plugin-pwa -D

# 配置service worker
# 离线缓存
# 应用安装
```

## 🔐 安全配置

### 内容安全策略 (CSP)
```html
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';">
```

### HTTPS配置
```bash
# 开发环境HTTPS
npm run dev -- --https

# 生产环境
# 配置SSL证书
# 强制HTTPS重定向
```

## 📞 支持

### 开发模式
- 热重载: ✅
- 源码映射: ✅
- 错误提示: ✅
- 开发工具: ✅

### 生产模式
- 代码压缩: ✅
- 资源优化: ✅
- 缓存策略: ✅
- 错误处理: ✅

如有问题，请查看：
- 浏览器控制台
- 网络请求
- 服务器日志
- 构建日志
