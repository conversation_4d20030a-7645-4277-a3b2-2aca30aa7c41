import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Popconfirm,
  Typography,
  Card,
  Tag,
  Select,
  Switch,
  InputNumber,
  Divider,
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, ImportOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import { useMessage } from '@/hooks/useMessage';
import DataImport from '@/components/DataImport';
import PartProjectsList from '@/components/PartProjectsList';
import type { Part, CreatePartRequest, CreateProjectBomRequest } from '@/types/api';

const { Title, Text } = Typography;
const { TextArea } = Input;

const Parts: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingPart, setEditingPart] = useState<Part | null>(null);
  const [addToProject, setAddToProject] = useState(false);
  const [isImportVisible, setIsImportVisible] = useState(false);
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const { success, error } = useMessage();
  const navigate = useNavigate();

  const { data: parts, isLoading } = useQuery(
    'parts',
    () => apiClient.getParts()
  );

  const { data: projects } = useQuery(
    'projects',
    () => apiClient.getProjects()
  );

  const createMutation = useMutation(
    async (data: { partData: CreatePartRequest; quantity?: number; projectId?: number }) => {
      const part = await apiClient.createPart(data.partData);

      // 如果需要添加到项目BOM
      if (data.quantity && data.projectId) {
        const bomData: CreateProjectBomRequest = {
          part_id: part.id,
          quantity: data.quantity
        };
        await apiClient.createProjectBom(data.projectId, bomData);
      }

      return part;
    },
    {
      onSuccess: (_, variables) => {
        if (variables.quantity && variables.projectId) {
          success('零件创建成功并已添加到项目BOM');
        } else {
          success('零件创建成功');
        }
        queryClient.invalidateQueries('parts');
        queryClient.invalidateQueries('projects');
        setIsModalVisible(false);
        setAddToProject(false);
        form.resetFields();
      },
      onError: () => {
        error('零件创建失败');
      },
    }
  );

  const updateMutation = useMutation(
    ({ id, data }: { id: number; data: Partial<CreatePartRequest> }) =>
      apiClient.updatePart(id, data),
    {
      onSuccess: () => {
        success('零件更新成功');
        queryClient.invalidateQueries('parts');
        setIsModalVisible(false);
        setEditingPart(null);
        form.resetFields();
      },
    }
  );

  const deleteMutation = useMutation(
    (id: number) => apiClient.deletePart(id),
    {
      onSuccess: () => {
        success('零件删除成功');
        queryClient.invalidateQueries('parts');
      },
    }
  );

  const handleCreate = () => {
    setEditingPart(null);
    setIsModalVisible(true);
    setAddToProject(false);
    form.resetFields();
  };

  const handleEdit = (part: Part) => {
    setEditingPart(part);
    setIsModalVisible(true);
    form.setFieldsValue(part);
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (editingPart) {
        // 编辑模式，只更新零件信息
        const { project_id, quantity, ...partData } = values;
        updateMutation.mutate({ id: editingPart.id, data: partData });
      } else {
        // 创建模式
        const { project_id, quantity, ...partData } = values;

        createMutation.mutate({
          partData,
          quantity: addToProject ? quantity : undefined,
          projectId: addToProject ? project_id : undefined
        });
      }
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '零件编号',
      dataIndex: 'part_number',
      key: 'part_number',
      render: (text: string) => <strong>{text}</strong>,
    },
    {
      title: '零件名称',
      dataIndex: 'part_name',
      key: 'part_name',
      render: (text: string) => text || <Tag color="default">未命名</Tag>,
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      render: (text: string) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: '规格说明',
      dataIndex: 'specifications',
      key: 'specifications',
      render: (text: string) => (
        <Text ellipsis={{ tooltip: text }} style={{ maxWidth: 200 }}>
          {text || '无'}
        </Text>
      ),
    },
    {
      title: '所属项目',
      key: 'projects',
      width: 200,
      render: (_: any, record: Part) => (
        <PartProjectsList
          partId={record.id}
          partNumber={record.part_number}
        />
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_: any, record: Part) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个零件吗？"
            onConfirm={() => deleteMutation.mutate(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div className="page-header">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={2} style={{ margin: 0 }}>
            零件管理
          </Title>
          <Space>
            <Button
              icon={<ImportOutlined />}
              onClick={() => setIsImportVisible(true)}
            >
              导入零件
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreate}
            >
              新建零件
            </Button>
          </Space>
        </div>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={parts}
          rowKey="id"
          loading={isLoading}
          pagination={{
            total: parts?.length || 0,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <Modal
        title={editingPart ? '编辑零件' : '新建零件'}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingPart(null);
          setAddToProject(false);
          form.resetFields();
        }}
        confirmLoading={createMutation.isLoading || updateMutation.isLoading}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          autoComplete="off"
        >
          <Form.Item
            name="part_number"
            label="零件编号"
            rules={[
              { required: true, message: '请输入零件编号' },
              { min: 2, message: '零件编号至少2个字符' },
            ]}
          >
            <Input placeholder="请输入零件编号" />
          </Form.Item>

          <Form.Item
            name="part_name"
            label="零件名称"
          >
            <Input placeholder="请输入零件名称（可选）" />
          </Form.Item>

          <Form.Item
            name="version"
            label="版本"
            rules={[
              { required: true, message: '请输入版本号' },
            ]}
          >
            <Input placeholder="例如：v1.0" />
          </Form.Item>

          <Form.Item
            name="specifications"
            label="规格说明"
          >
            <TextArea
              rows={4}
              placeholder="请输入零件规格说明（可选）"
            />
          </Form.Item>

          {!editingPart && (
            <>
              <Divider />
              <Form.Item label="项目关联">
                <Switch
                  checked={addToProject}
                  onChange={setAddToProject}
                  checkedChildren="添加到项目"
                  unCheckedChildren="仅创建零件"
                />
              </Form.Item>

              {addToProject && (
                <>
                  <Form.Item
                    name="project_id"
                    label="选择项目"
                    rules={[
                      { required: addToProject, message: '请选择项目' },
                    ]}
                  >
                    <Select
                      placeholder="请选择要添加到的项目"
                      showSearch
                      optionFilterProp="children"
                      filterOption={(input, option) =>
                        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                      }
                      options={projects?.map(project => ({
                        value: project.id,
                        label: `${project.project_name}${project.customer_name ? ` (${project.customer_name})` : ''}`,
                      }))}
                    />
                  </Form.Item>

                  <Form.Item
                    name="quantity"
                    label="BOM数量"
                    rules={[
                      { required: addToProject, message: '请输入BOM数量' },
                      { type: 'number', min: 1, message: '数量必须大于0' },
                    ]}
                  >
                    <InputNumber
                      min={1}
                      placeholder="请输入在项目中的数量"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </>
              )}
            </>
          )}
        </Form>
      </Modal>

      <DataImport
        visible={isImportVisible}
        onCancel={() => setIsImportVisible(false)}
        moduleType="parts"
        moduleName="零件"
        onSuccess={() => {
          queryClient.invalidateQueries('parts');
          setIsImportVisible(false);
        }}
      />
    </div>
  );
};

export default Parts;
