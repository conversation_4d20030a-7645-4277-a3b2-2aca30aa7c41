import React from 'react';
import { Card, Space, Tag, Typography, Divider } from 'antd';
import { useAuthStore } from '@/store/auth';
import { usePermissions } from '@/hooks/usePermissions';
import RoleBasedAccess from '@/components/RoleBasedAccess';

const { Title, Text } = Typography;

const PermissionTest: React.FC = () => {
  const { user } = useAuthStore();
  const { hasPageAccess, hasOperationPermission, getAccessiblePages } = usePermissions();

  if (!user) {
    return <div>用户未登录</div>;
  }

  const userRoles = user.roles || [];
  const userSkills = user.skills || [];
  const accessibleMenus = getAccessiblePages();

  const testPages = [
    '/dashboard',
    '/projects', 
    '/parts',
    '/machines',
    '/work-orders',
    '/plan-tasks',
    '/production-center',
    '/quality',
    '/bom',
    '/routings',
    '/users',
    '/role-permissions',
    '/system-config'
  ];

  const testFeatures = [
    'CREATE_PROJECT',
    'EDIT_PROJECT', 
    'DELETE_PROJECT',
    'MANAGE_USERS',
    'MANAGE_ROLES',
    'EXECUTE_TASK',
    'START_TASK',
    'COMPLETE_TASK'
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>权限系统测试页面</Title>
      
      <Card title="用户信息" style={{ marginBottom: '16px' }}>
        <Space direction="vertical">
          <Text><strong>用户名:</strong> {user.username}</Text>
          <Text><strong>姓名:</strong> {user.full_name}</Text>
          <div>
            <Text><strong>角色:</strong> </Text>
            {userRoles.map(role => (
              <Tag key={role} color="blue">{role}</Tag>
            ))}
          </div>
          <div>
            <Text><strong>技能:</strong> </Text>
            {userSkills.map(skill => (
              <Tag key={skill} color="green">{skill}</Tag>
            ))}
          </div>
        </Space>
      </Card>

      <Card title="页面访问权限" style={{ marginBottom: '16px' }}>
        <Space wrap>
          {testPages.map(page => {
            const hasAccess = hasPageAccess(page);
            return (
              <Tag key={page} color={hasAccess ? 'green' : 'red'}>
                {page} {hasAccess ? '✓' : '✗'}
              </Tag>
            );
          })}
        </Space>
      </Card>

      <Card title="功能权限" style={{ marginBottom: '16px' }}>
        <Space wrap>
          {testFeatures.map(feature => {
            const hasAccess = hasOperationPermission(feature);
            return (
              <Tag key={feature} color={hasAccess ? 'green' : 'red'}>
                {feature} {hasAccess ? '✓' : '✗'}
              </Tag>
            );
          })}
        </Space>
      </Card>

      <Card title="可访问菜单" style={{ marginBottom: '16px' }}>
        <Space wrap>
          {accessibleMenus.map(menu => (
            <Tag key={menu} color="blue">{menu}</Tag>
          ))}
        </Space>
      </Card>

      <Card title="角色访问控制组件测试">
        <Space direction="vertical">
          <div>
            <Text strong>管理员专用内容:</Text>
            <RoleBasedAccess allowedRoles={['admin']} fallback={<Text type="secondary">无权限访问</Text>}>
              <Tag color="gold">这是管理员才能看到的内容</Tag>
            </RoleBasedAccess>
          </div>
          
          <div>
            <Text strong>计划员专用内容:</Text>
            <RoleBasedAccess allowedRoles={['planner']} fallback={<Text type="secondary">无权限访问</Text>}>
              <Tag color="blue">这是计划员才能看到的内容</Tag>
            </RoleBasedAccess>
          </div>
          
          <div>
            <Text strong>操作员专用内容:</Text>
            <RoleBasedAccess allowedRoles={['operator']} fallback={<Text type="secondary">无权限访问</Text>}>
              <Tag color="orange">这是操作员才能看到的内容</Tag>
            </RoleBasedAccess>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default PermissionTest;
