import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Typography,
  Space,
  Tabs,
  Checkbox,
  Divider,
  Tag,
  Row,
  Col,
  Alert,
  App
} from 'antd';
import {
  SettingOutlined,
  EditOutlined,
  PlusOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useAuthStore } from '@/store/auth';
import { usePermissions } from '@/hooks/usePermissions';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

interface Permission {
  id: number;
  permission_code: string;
  permission_name: string;
  description?: string;
  category: string;
  is_active: boolean;
  created_at: string;
}

interface PermissionInfo {
  id: number;
  permission_code: string;
  permission_name: string;
  description?: string;
  category: string;
  granted: boolean;
}

interface RoleWithPermissions {
  id: number;
  role_name: string;
  description?: string;
  is_active: boolean;
  role_type: string;
  created_at: string;
  permissions: PermissionInfo[];
}

interface RolePermissionUpdate {
  permission_id: number;
  granted: boolean;
}

interface Role {
  id: number;
  role_name: string;
  role_type?: string;
}

interface RolesResponse {
  roles: Role[];
}

// 自定义防抖函数
const useDebounce = (callback: Function, delay: number) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  return useCallback((...args: any[]) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]);
};

const PermissionConfig: React.FC = () => {
  const { message } = App.useApp();
  const { user } = useAuthStore();
  const { hasOperationPermission } = usePermissions();
  const queryClient = useQueryClient();
  const [selectedRole, setSelectedRole] = useState<number | null>(null);
  const [isPermissionModalVisible, setIsPermissionModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 检查权限
  if (!user || !hasOperationPermission('MANAGE_PERMISSIONS')) {
    return (
      <Alert
        message="权限不足"
        description="您没有权限访问权限配置功能。"
        type="error"
        showIcon
      />
    );
  }

  // 获取所有权限
  const { data: permissions, isLoading: permissionsLoading } = useQuery<Permission[]>(
    'permissions',
    async () => {
      const response = await fetch('/api/permissions', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });
      if (!response.ok) {
        throw new Error('Failed to fetch permissions');
      }
      return response.json();
    }
  );

  // 获取所有角色
  const { data: rolesData, isLoading: rolesLoading } = useQuery(
    'roles',
    async () => {
      const response = await fetch('/api/auth/roles', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });
      if (!response.ok) {
        throw new Error('Failed to fetch roles');
      }
      return response.json();
    }
  );

  const roles = rolesData?.roles || [];

  // 获取角色权限
  const { data: rolePermissions, isLoading: rolePermissionsLoading } = useQuery<RoleWithPermissions>(
    ['role-permissions', selectedRole],
    async () => {
      if (!selectedRole) return null;
      const response = await fetch(`/api/roles/${selectedRole}/permissions`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });
      if (!response.ok) {
        throw new Error('Failed to fetch role permissions');
      }
      return response.json();
    },
    {
      enabled: !!selectedRole,
    }
  );

  // 更新角色权限
  const updateRolePermissionsMutation = useMutation(
    async ({ roleId, permissions }: { roleId: number; permissions: RolePermissionUpdate[] }) => {
      const response = await fetch(`/api/roles/${roleId}/permissions`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({ permissions }),
      });
      if (!response.ok) {
        throw new Error('Failed to update role permissions');
      }
      return response.json();
    },
    {
      onSuccess: () => {
        message.success('角色权限更新成功');
        queryClient.invalidateQueries(['role-permissions', selectedRole]);
        // 重新获取最新的权限数据以确保同步
        queryClient.refetchQueries(['role-permissions', selectedRole]);
      },
      onError: (error: any) => {
        message.error(`更新失败: ${error.message}`);
        // 发生错误时，重新同步本地状态
        if (rolePermissions?.permissions) {
          setLocalPermissions(rolePermissions.permissions);
        }
      },
    }
  );

  // 创建新权限
  const createPermissionMutation = useMutation(
    async (permissionData: any) => {
      const response = await fetch('/api/permissions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(permissionData),
      });
      if (!response.ok) {
        throw new Error('Failed to create permission');
      }
      return response.json();
    },
    {
      onSuccess: () => {
        message.success('权限创建成功');
        queryClient.invalidateQueries('permissions');
        setIsPermissionModalVisible(false);
        form.resetFields();
      },
      onError: (error: any) => {
        message.error(`创建失败: ${error.message}`);
      },
    }
  );

  // 权限表格列定义
  const permissionColumns = [
    {
      title: '权限代码',
      dataIndex: 'permission_code',
      key: 'permission_code',
      width: 200,
    },
    {
      title: '权限名称',
      dataIndex: 'permission_name',
      key: 'permission_name',
      width: 200,
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category: string) => {
        const categoryColors: { [key: string]: string } = {
          page: 'blue',
          create: 'green',
          edit: 'orange',
          delete: 'red',
          operation: 'purple',
          management: 'gold',
        };
        return <Tag color={categoryColors[category] || 'default'}>{category}</Tag>;
      },
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
  ];

  // 本地权限状态管理
  const [localPermissions, setLocalPermissions] = useState<PermissionInfo[]>([]);

  // 同步服务器权限到本地状态
  useEffect(() => {
    if (rolePermissions?.permissions) {
      setLocalPermissions(rolePermissions.permissions);
    }
  }, [rolePermissions]);

  // 防抖更新权限
  const debouncedUpdatePermissions = useDebounce((roleId: number, permissions: RolePermissionUpdate[]) => {
    updateRolePermissionsMutation.mutate({
      roleId,
      permissions,
    });
  }, 500);

  // 处理权限变更
  const handlePermissionChange = (permissionId: number, granted: boolean) => {
    if (!selectedRole || !rolePermissions) return;

    // 立即更新本地状态以提供即时反馈
    const updatedPermissions = localPermissions.map(p =>
      p.id === permissionId ? { ...p, granted } : p
    );
    setLocalPermissions(updatedPermissions);

    // 防抖发送API请求
    const permissionUpdates: RolePermissionUpdate[] = updatedPermissions.map(p => ({
      permission_id: p.id,
      granted: p.granted,
    }));

    debouncedUpdatePermissions(selectedRole, permissionUpdates);
  };

  // 批量权限操作
  const handleBatchPermissionChange = (category: string, granted: boolean) => {
    if (!selectedRole || !rolePermissions) return;

    // 立即更新本地状态
    const updatedPermissions = localPermissions.map(p =>
      p.category === category ? { ...p, granted } : p
    );
    setLocalPermissions(updatedPermissions);

    // 防抖发送API请求
    const permissionUpdates: RolePermissionUpdate[] = updatedPermissions.map(p => ({
      permission_id: p.id,
      granted: p.granted,
    }));

    debouncedUpdatePermissions(selectedRole, permissionUpdates);
  };

  // 创建权限
  const handleCreatePermission = (values: any) => {
    createPermissionMutation.mutate(values);
  };

  // 按分类分组权限
  const groupPermissionsByCategory = (permissions: PermissionInfo[]) => {
    const grouped: { [key: string]: PermissionInfo[] } = {};
    permissions.forEach(permission => {
      if (!grouped[permission.category]) {
        grouped[permission.category] = [];
      }
      grouped[permission.category].push(permission);
    });
    return grouped;
  };

  const categoryNames: { [key: string]: string } = {
    page: '页面访问',
    create: '创建权限',
    edit: '编辑权限',
    delete: '删除权限',
    operation: '操作权限',
    management: '管理权限',
  };

  return (
    <div>
      <div className="page-header">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title level={2} style={{ margin: 0 }}>
              权限配置管理
            </Title>
            <Typography.Paragraph style={{ margin: 0, color: '#666' }}>
              配置系统角色的详细权限，支持细粒度的权限控制
            </Typography.Paragraph>
          </div>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setIsPermissionModalVisible(true)}
            >
              新建权限
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => {
                queryClient.invalidateQueries('permissions');
                queryClient.invalidateQueries('roles');
                if (selectedRole) {
                  queryClient.invalidateQueries(['role-permissions', selectedRole]);
                }
              }}
            >
              刷新
            </Button>
          </Space>
        </div>
      </div>

      <Row gutter={16}>
        {/* 左侧：角色选择 */}
        <Col span={8}>
          <Card title="选择角色" size="small">
            <div style={{ marginBottom: 16 }}>
              <Select
                style={{ width: '100%' }}
                placeholder="请选择要配置的角色"
                value={selectedRole}
                onChange={setSelectedRole}
                loading={rolesLoading}
              >
                {roles?.map((role: any) => (
                  <Option key={role.id} value={role.id}>
                    {role.role_name}
                    {role.role_type === 'system' && <Tag color="blue" style={{ marginLeft: 8 }}>系统</Tag>}
                  </Option>
                ))}
              </Select>
            </div>
            
            {selectedRole && rolePermissions && (
              <div>
                <Text strong>角色信息</Text>
                <div style={{ marginTop: 8 }}>
                  <p><Text type="secondary">角色名称：</Text>{rolePermissions.role_name}</p>
                  <p><Text type="secondary">角色类型：</Text>
                    <Tag color={rolePermissions.role_type === 'system' ? 'blue' : 'green'}>
                      {rolePermissions.role_type === 'system' ? '系统角色' : '自定义角色'}
                    </Tag>
                  </p>
                  {rolePermissions.description && (
                    <p><Text type="secondary">描述：</Text>{rolePermissions.description}</p>
                  )}
                </div>
              </div>
            )}
          </Card>
        </Col>

        {/* 右侧：权限配置 */}
        <Col span={16}>
          <Tabs
            defaultActiveKey="role-permissions"
            items={[
              {
                key: 'role-permissions',
                label: '角色权限配置',
                children: selectedRole && rolePermissions && localPermissions.length > 0 ? (
                  <Card
                    title={`${rolePermissions.role_name} 权限配置`}
                    size="small"
                    extra={
                      updateRolePermissionsMutation.isLoading && (
                        <Text type="secondary">保存中...</Text>
                      )
                    }
                  >
                    {Object.entries(groupPermissionsByCategory(localPermissions)).map(([category, perms]) => (
                      <div key={category} style={{ marginBottom: 24 }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
                          <Title level={5} style={{ margin: 0 }}>
                            {categoryNames[category] || category}
                          </Title>
                          <Space>
                            <Button
                              size="small"
                              onClick={() => handleBatchPermissionChange(category, true)}
                              disabled={updateRolePermissionsMutation.isLoading}
                            >
                              全选
                            </Button>
                            <Button
                              size="small"
                              onClick={() => handleBatchPermissionChange(category, false)}
                              disabled={updateRolePermissionsMutation.isLoading}
                            >
                              全不选
                            </Button>
                          </Space>
                        </div>
                        <Row gutter={[16, 8]}>
                          {perms.map(permission => {
                            // 从本地状态中找到对应的权限
                            const localPerm = localPermissions.find(p => p.id === permission.id);
                            return (
                              <Col span={12} key={permission.id}>
                                <Checkbox
                                  checked={localPerm?.granted || false}
                                  onChange={(e) => handlePermissionChange(permission.id, e.target.checked)}
                                  disabled={updateRolePermissionsMutation.isLoading}
                                >
                                  <div>
                                    <div>{permission.permission_name}</div>
                                    <Text type="secondary" style={{ fontSize: '12px' }}>
                                      {permission.permission_code}
                                    </Text>
                                  </div>
                                </Checkbox>
                              </Col>
                            );
                          })}
                        </Row>
                        <Divider />
                      </div>
                    ))}
                  </Card>
                ) : (
                  <Card>
                    <div style={{ textAlign: 'center', padding: '40px 0' }}>
                      <Text type="secondary">请选择一个角色来配置权限</Text>
                    </div>
                  </Card>
                )
              },
              {
                key: 'permissions',
                label: '权限管理',
                children: (
                  <Card title="系统权限列表" size="small">
                    <Table
                      columns={permissionColumns}
                      dataSource={permissions}
                      rowKey="id"
                      loading={permissionsLoading}
                      size="small"
                      pagination={{
                        pageSize: 10,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条记录`,
                      }}
                    />
                  </Card>
                ),
              },
            ]}
          />
        </Col>
      </Row>

      {/* 创建权限模态框 */}
      <Modal
        title="创建新权限"
        open={isPermissionModalVisible}
        onCancel={() => {
          setIsPermissionModalVisible(false);
          form.resetFields();
        }}
        footer={[
          <Button key="cancel" onClick={() => setIsPermissionModalVisible(false)}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={createPermissionMutation.isLoading}
            onClick={() => form.submit()}
          >
            创建
          </Button>,
        ]}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreatePermission}
        >
          <Form.Item
            name="permission_code"
            label="权限代码"
            rules={[
              { required: true, message: '请输入权限代码' },
              { min: 2, max: 100, message: '权限代码长度应在2-100字符之间' },
            ]}
          >
            <Input placeholder="例如：CREATE_PROJECT" />
          </Form.Item>

          <Form.Item
            name="permission_name"
            label="权限名称"
            rules={[
              { required: true, message: '请输入权限名称' },
              { min: 2, max: 255, message: '权限名称长度应在2-255字符之间' },
            ]}
          >
            <Input placeholder="例如：创建项目" />
          </Form.Item>

          <Form.Item
            name="category"
            label="权限分类"
            rules={[{ required: true, message: '请选择权限分类' }]}
          >
            <Select placeholder="请选择分类">
              <Option value="page">页面访问</Option>
              <Option value="create">创建权限</Option>
              <Option value="edit">编辑权限</Option>
              <Option value="delete">删除权限</Option>
              <Option value="operation">操作权限</Option>
              <Option value="management">管理权限</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="权限描述"
          >
            <Input.TextArea rows={3} placeholder="权限的详细描述" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default PermissionConfig;
