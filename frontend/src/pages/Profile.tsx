import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Typography,
  Space,
  Divider,
  Tabs,
  Alert,
  Modal,
  App,
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  EditOutlined,
  SaveOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
  ToolOutlined,
} from '@ant-design/icons';
import { useMutation, useQueryClient } from 'react-query';
import { useAuthStore } from '@/store/auth';
import { apiClient } from '@/lib/api';
import type { UpdateProfileRequest, ChangePasswordRequest } from '@/types/api';
import { validationRules } from '@/utils/validation';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const Profile: React.FC = () => {
  const { message } = App.useApp();
  const { user, setUser } = useAuthStore();
  const queryClient = useQueryClient();
  const [profileForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [isEditingProfile, setIsEditingProfile] = useState(false);

  // 更新个人资料
  const updateProfileMutation = useMutation(
    (data: UpdateProfileRequest) => apiClient.updateProfile(data),
    {
      onSuccess: (updatedUser) => {
        message.success('个人资料更新成功');
        setUser({ ...user!, ...updatedUser });
        setIsEditingProfile(false);
        queryClient.invalidateQueries('current-user');
      },
      onError: (error: any) => {
        message.error(`更新失败: ${error.response?.data?.message || error.message}`);
      },
    }
  );

  // 修改密码
  const changePasswordMutation = useMutation(
    (data: ChangePasswordRequest) => apiClient.changePassword(data),
    {
      onSuccess: () => {
        message.success('密码修改成功');
        passwordForm.resetFields();
      },
      onError: (error: any) => {
        message.error(`密码修改失败: ${error.response?.data?.message || error.message}`);
      },
    }
  );

  const handleProfileSubmit = async () => {
    try {
      const values = await profileForm.validateFields();
      updateProfileMutation.mutate(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handlePasswordSubmit = async () => {
    try {
      const values = await passwordForm.validateFields();
      changePasswordMutation.mutate(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleEditProfile = () => {
    setIsEditingProfile(true);
    profileForm.setFieldsValue({
      full_name: user?.full_name || '',
    });
  };

  const handleCancelEdit = () => {
    setIsEditingProfile(false);
    profileForm.resetFields();
  };

  if (!user) {
    return (
      <div style={{ padding: 24 }}>
        <Alert message="用户信息加载中..." type="info" />
      </div>
    );
  }

  return (
    <div style={{ padding: 24, maxWidth: 800, margin: '0 auto' }}>
      <div className="page-header" style={{ marginBottom: 24 }}>
        <Title level={2}>
          <UserOutlined style={{ marginRight: 8 }} />
          个人资料
        </Title>
        <Text type="secondary">管理您的个人信息和账户设置</Text>
      </div>

      <Tabs defaultActiveKey="profile" type="card">
        <TabPane
          tab={
            <span>
              <UserOutlined />
              基本信息
            </span>
          }
          key="profile"
        >
          <Card>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
              <Title level={4} style={{ margin: 0 }}>个人信息</Title>
              {!isEditingProfile && (
                <Button
                  type="primary"
                  icon={<EditOutlined />}
                  onClick={handleEditProfile}
                >
                  编辑
                </Button>
              )}
            </div>

            {!isEditingProfile ? (
              <div>
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <div>
                    <Text strong>用户名：</Text>
                    <Text>{user.username}</Text>
                  </div>
                  <div>
                    <Text strong>姓名：</Text>
                    <Text>{user.full_name || '未设置'}</Text>
                  </div>
                  <div>
                    <Text strong>角色：</Text>
                    <Text>{user.roles?.join(', ') || '无'}</Text>
                  </div>
                  <div>
                    <Text strong>技能组：</Text>
                    <Text>{user.skills?.join(', ') || '无'}</Text>
                  </div>
                  <div>
                    <Text strong>账户状态：</Text>
                    <Text type={user.is_active ? 'success' : 'danger'}>
                      {user.is_active ? '活跃' : '已禁用'}
                    </Text>
                  </div>
                </Space>
              </div>
            ) : (
              <Form
                form={profileForm}
                layout="vertical"
                onFinish={handleProfileSubmit}
              >
                <Form.Item
                  label="用户名"
                >
                  <Input value={user.username} disabled />
                </Form.Item>

                <Form.Item
                  name="full_name"
                  label="姓名"
                  rules={[
                    { max: 50, message: '姓名不能超过50个字符' },
                  ]}
                >
                  <Input placeholder="请输入您的真实姓名" />
                </Form.Item>

                <Form.Item>
                  <Space>
                    <Button
                      type="primary"
                      htmlType="submit"
                      icon={<SaveOutlined />}
                      loading={updateProfileMutation.isLoading}
                    >
                      保存
                    </Button>
                    <Button onClick={handleCancelEdit}>
                      取消
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            )}
          </Card>
        </TabPane>

        <TabPane
          tab={
            <span>
              <LockOutlined />
              密码修改
            </span>
          }
          key="password"
        >
          <Card>
            <Title level={4}>修改密码</Title>
            <Alert
              message="密码安全提示"
              description="为了您的账户安全，请定期更换密码，并使用包含字母、数字的强密码。"
              type="info"
              showIcon
              style={{ marginBottom: 24 }}
            />

            <Form
              form={passwordForm}
              layout="vertical"
              onFinish={handlePasswordSubmit}
              style={{ maxWidth: 400 }}
            >
              <Form.Item
                name="current_password"
                label="当前密码"
                rules={[
                  { required: true, message: '请输入当前密码' },
                ]}
              >
                <Input.Password
                  placeholder="请输入当前密码"
                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                />
              </Form.Item>

              <Form.Item
                name="new_password"
                label="新密码"
                rules={[
                  { required: true, message: '请输入新密码' },
                  { min: 6, message: '密码至少6个字符' },
                ]}
              >
                <Input.Password
                  placeholder="请输入新密码"
                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                />
              </Form.Item>

              <Form.Item
                name="confirm_password"
                label="确认新密码"
                dependencies={['new_password']}
                rules={[
                  { required: true, message: '请确认新密码' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('new_password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('两次输入的密码不一致'));
                    },
                  }),
                ]}
              >
                <Input.Password
                  placeholder="请再次输入新密码"
                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<LockOutlined />}
                  loading={changePasswordMutation.isLoading}
                >
                  修改密码
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        <TabPane
          tab={
            <span>
              <ToolOutlined />
              设备绑定
            </span>
          }
          key="devices"
        >
          <Card>
            <Title level={4}>设备绑定管理</Title>
            <Alert
              message="设备绑定说明"
              description="操作员可以绑定常用的设备，绑定后可以在仪表板中快速查看相关任务和状态。"
              type="info"
              showIcon
              style={{ marginBottom: 24 }}
            />
            
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <Text type="secondary">
                设备绑定功能已集成在操作员仪表板中，请前往仪表板进行设备绑定管理。
              </Text>
            </div>
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default Profile;
