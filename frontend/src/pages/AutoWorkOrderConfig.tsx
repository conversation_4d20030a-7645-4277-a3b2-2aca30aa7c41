import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Select,
  InputNumber,
  Switch,
  Popconfirm,
  Tag,
  Typography,
  Alert,
  App,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SettingOutlined,
  PlayCircleOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import type {
  AutoWorkOrderConfig,
  CreateAutoWorkOrderConfigRequest,
  UpdateAutoWorkOrderConfigRequest,
} from '@/types/api';

const { Title, Text } = Typography;

const AutoWorkOrderConfigPage: React.FC = () => {
  const { message } = App.useApp();
  const queryClient = useQueryClient();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState<AutoWorkOrderConfig | null>(null);
  const [form] = Form.useForm();

  // 获取配置列表
  const { data: configsData, isLoading } = useQuery(
    'auto-work-order-configs',
    () => apiClient.getAutoWorkOrderConfigs(),
    {
      refetchInterval: 30000, // 30秒刷新一次
    }
  );

  // 获取项目列表（用于选择特定项目）
  const { data: projectsData } = useQuery(
    'projects-for-auto-config',
    () => apiClient.getProjects({ limit: 100 })
  );

  // 创建配置
  const createConfigMutation = useMutation(
    (data: CreateAutoWorkOrderConfigRequest) => apiClient.createAutoWorkOrderConfig(data),
    {
      onSuccess: () => {
        message.success('自动工单配置创建成功');
        queryClient.invalidateQueries('auto-work-order-configs');
        setIsModalVisible(false);
        form.resetFields();
      },
      onError: (error: any) => {
        message.error(`创建失败: ${error.response?.data?.message || error.message}`);
      },
    }
  );

  // 更新配置
  const updateConfigMutation = useMutation(
    ({ configId, data }: { configId: number; data: UpdateAutoWorkOrderConfigRequest }) =>
      apiClient.updateAutoWorkOrderConfig(configId, data),
    {
      onSuccess: () => {
        message.success('自动工单配置更新成功');
        queryClient.invalidateQueries('auto-work-order-configs');
        setIsModalVisible(false);
        setEditingConfig(null);
        form.resetFields();
      },
      onError: (error: any) => {
        message.error(`更新失败: ${error.response?.data?.message || error.message}`);
      },
    }
  );

  // 删除配置
  const deleteConfigMutation = useMutation(
    (configId: number) => apiClient.deleteAutoWorkOrderConfig(configId),
    {
      onSuccess: () => {
        message.success('自动工单配置删除成功');
        queryClient.invalidateQueries('auto-work-order-configs');
      },
      onError: (error: any) => {
        message.error(`删除失败: ${error.response?.data?.message || error.message}`);
      },
    }
  );

  const handleCreate = () => {
    setEditingConfig(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (config: AutoWorkOrderConfig) => {
    setEditingConfig(config);
    form.setFieldsValue({
      trigger_type: config.trigger_type,
      project_id: config.project_id,
      is_enabled: config.is_enabled,
      default_quantity: config.default_quantity,
      default_due_days: config.default_due_days,
      auto_create_plan_tasks: config.auto_create_plan_tasks,
    });
    setIsModalVisible(true);
  };

  const handleSubmit = (values: any) => {
    if (editingConfig) {
      updateConfigMutation.mutate({
        configId: editingConfig.id,
        data: {
          is_enabled: values.is_enabled,
          default_quantity: values.default_quantity,
          default_due_days: values.default_due_days,
          auto_create_plan_tasks: values.auto_create_plan_tasks,
        },
      });
    } else {
      createConfigMutation.mutate(values);
    }
  };

  const getTriggerTypeText = (type: string) => {
    switch (type) {
      case 'part_created':
        return '零件创建';
      case 'routing_created':
        return '工艺创建';
      case 'bom_added':
        return 'BOM添加';
      default:
        return type;
    }
  };

  const getTriggerTypeColor = (type: string) => {
    switch (type) {
      case 'part_created':
        return 'blue';
      case 'routing_created':
        return 'green';
      case 'bom_added':
        return 'orange';
      default:
        return 'default';
    }
  };

  const getProjectName = (projectId: number | null) => {
    if (!projectId) return '全局配置';
    const project = projectsData?.find((p: any) => p.id === projectId);
    return project ? project.project_name : `项目 ${projectId}`;
  };

  const columns = [
    {
      title: '触发类型',
      dataIndex: 'trigger_type',
      key: 'trigger_type',
      render: (type: string) => (
        <Tag color={getTriggerTypeColor(type)}>{getTriggerTypeText(type)}</Tag>
      ),
    },
    {
      title: '应用范围',
      dataIndex: 'project_id',
      key: 'project_id',
      render: (projectId: number | null) => (
        <Text>{getProjectName(projectId)}</Text>
      ),
    },
    {
      title: '状态',
      dataIndex: 'is_enabled',
      key: 'is_enabled',
      render: (enabled: boolean) => (
        <Tag color={enabled ? 'success' : 'default'}>
          {enabled ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '默认数量',
      dataIndex: 'default_quantity',
      key: 'default_quantity',
      render: (quantity: number | null) => quantity || '使用BOM数量',
    },
    {
      title: '默认交期(天)',
      dataIndex: 'default_due_days',
      key: 'default_due_days',
      render: (days: number | null) => days || '无',
    },
    {
      title: '自动创建计划',
      dataIndex: 'auto_create_plan_tasks',
      key: 'auto_create_plan_tasks',
      render: (auto: boolean) => (
        <Tag color={auto ? 'success' : 'default'}>
          {auto ? '是' : '否'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: AutoWorkOrderConfig) => (
        <Space>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个配置吗？"
            onConfirm={() => deleteConfigMutation.mutate(record.id)}
          >
            <Button
              type="text"
              icon={<DeleteOutlined />}
              danger
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title level={4} style={{ margin: 0 }}>
              <SettingOutlined /> 自动工单创建配置
            </Title>
            <Text type="secondary">
              配置在添加零件、工艺或BOM时自动创建工单的规则
            </Text>
          </div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreate}
          >
            新增配置
          </Button>
        </div>

        <Alert
          message="功能说明"
          description={
            <div>
              <p>• <strong>零件创建</strong>：在创建新零件时自动为包含该零件的BOM项目创建工单</p>
              <p>• <strong>工艺创建</strong>：在为零件创建工艺路线时自动创建工单</p>
              <p>• <strong>BOM添加</strong>：在项目中添加BOM项目时自动创建工单</p>
              <p>• 全局配置对所有项目生效，项目特定配置优先级更高</p>
            </div>
          }
          type="info"
          showIcon
          style={{ marginBottom: '16px' }}
        />

        <Table
          columns={columns}
          dataSource={configsData?.configs || []}
          rowKey="id"
          loading={isLoading}
          pagination={{
            total: configsData?.total_count || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <Modal
        title={editingConfig ? '编辑自动工单配置' : '新增自动工单配置'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingConfig(null);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        confirmLoading={createConfigMutation.isLoading || updateConfigMutation.isLoading}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="trigger_type"
            label="触发类型"
            rules={[{ required: true, message: '请选择触发类型' }]}
          >
            <Select
              placeholder="请选择触发类型"
              disabled={!!editingConfig} // 编辑时不允许修改触发类型
            >
              <Select.Option value="part_created">零件创建</Select.Option>
              <Select.Option value="routing_created">工艺创建</Select.Option>
              <Select.Option value="bom_added">BOM添加</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="project_id"
            label="应用项目"
            help="留空表示全局配置，对所有项目生效"
          >
            <Select
              placeholder="选择特定项目（可选）"
              allowClear
              disabled={!!editingConfig} // 编辑时不允许修改项目
            >
              {projectsData?.map((project: any) => (
                <Select.Option key={project.id} value={project.id}>
                  {project.project_name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="is_enabled"
            label="启用状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>

          <Form.Item
            name="default_quantity"
            label="默认工单数量"
            help="留空表示使用BOM中的数量"
          >
            <InputNumber
              placeholder="默认数量"
              min={1}
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="default_due_days"
            label="默认交期天数"
            help="从当前日期开始计算的天数"
          >
            <InputNumber
              placeholder="交期天数"
              min={1}
              max={365}
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="auto_create_plan_tasks"
            label="自动创建计划任务"
            valuePropName="checked"
            initialValue={false}
          >
            <Switch 
              checkedChildren="是" 
              unCheckedChildren="否"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AutoWorkOrderConfigPage;
