import React, { useState } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  Space,
  Popconfirm,
  Card,
  Typography,
  Row,
  Col,
  Tag,
  Divider,
  Alert,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ExportOutlined,
  ImportOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import { useMessage } from '@/hooks/useMessage';
import type { ProjectBom, CreateProjectBomRequest, Project, Part } from '@/types/api';

const { Title, Text } = Typography;
const { Option } = Select;

interface BOMItem extends ProjectBom {
  part_name?: string;
  part_number?: string;
  specifications?: string;
}

const BOM: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingBom, setEditingBom] = useState<BOMItem | null>(null);
  const [selectedProject, setSelectedProject] = useState<number | null>(null);
  const [searchText, setSearchText] = useState('');
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const { success, error, warning } = useMessage();

  // Fetch projects
  const { data: projects = [] } = useQuery('projects', () => apiClient.getProjects());

  // Fetch parts
  const { data: parts = [] } = useQuery('parts', () => apiClient.getParts());

  // Fetch BOM items for selected project
  const { data: bomItems = [], isLoading } = useQuery(
    ['project-bom', selectedProject],
    () => selectedProject ? apiClient.getProjectBom(selectedProject) : Promise.resolve([]),
    {
      enabled: !!selectedProject,
    }
  );

  // Create BOM item mutation
  const createBomMutation = useMutation(
    (data: CreateProjectBomRequest & { project_id: number }) =>
      apiClient.createProjectBom(data.project_id, data),
    {
      onSuccess: () => {
        setIsModalVisible(false);
        form.resetFields();
        queryClient.invalidateQueries(['project-bom', selectedProject]);
        // 使用setTimeout避免并发模式警告
        setTimeout(() => success('BOM项目添加成功'), 0);
      },
      onError: () => {
        setTimeout(() => error('添加BOM项目失败'), 0);
      },
    }
  );

  // Update BOM item mutation
  const updateBomMutation = useMutation(
    (data: { id: number; updates: Partial<CreateProjectBomRequest> }) =>
      apiClient.updateProjectBom(data.id, data.updates),
    {
      onSuccess: () => {
        setIsModalVisible(false);
        form.resetFields();
        setEditingBom(null);
        queryClient.invalidateQueries(['project-bom', selectedProject]);
        setTimeout(() => success('BOM项目更新成功'), 0);
      },
      onError: () => {
        setTimeout(() => error('更新BOM项目失败'), 0);
      },
    }
  );

  // Delete BOM item mutation
  const deleteBomMutation = useMutation(
    (id: number) => apiClient.deleteProjectBom(id),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['project-bom', selectedProject]);
        setTimeout(() => success('BOM项目删除成功'), 0);
      },
      onError: () => {
        setTimeout(() => error('删除BOM项目失败'), 0);
      },
    }
  );

  const handleAdd = () => {
    if (!selectedProject) {
      setTimeout(() => warning('请先选择项目'), 0);
      return;
    }
    setEditingBom(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (record: BOMItem) => {
    setEditingBom(record);
    form.setFieldsValue({
      part_id: record.part_id,
      quantity: record.quantity,
    });
    setIsModalVisible(true);
  };

  const handleDelete = (id: number) => {
    deleteBomMutation.mutate(id);
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingBom) {
        updateBomMutation.mutate({
          id: editingBom.id,
          updates: values,
        });
      } else {
        createBomMutation.mutate({
          project_id: selectedProject!,
          ...values,
        });
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const columns = [
    {
      title: '零件编号',
      dataIndex: 'part_number',
      key: 'part_number',
      render: (_: any, record: BOMItem) => {
        const part = parts.find(p => p.id === record.part_id);
        return part?.part_number || '-';
      },
    },
    {
      title: '零件名称',
      dataIndex: 'part_name',
      key: 'part_name',
      render: (_: any, record: BOMItem) => {
        const part = parts.find(p => p.id === record.part_id);
        return part?.part_name || '-';
      },
    },
    {
      title: '规格说明',
      dataIndex: 'specifications',
      key: 'specifications',
      render: (_: any, record: BOMItem) => {
        const part = parts.find(p => p.id === record.part_id);
        return part?.specifications || '-';
      },
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (quantity: number) => (
        <Tag color="blue">{quantity}</Tag>
      ),
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      render: (_: any, record: BOMItem) => {
        const part = parts.find(p => p.id === record.part_id);
        return part?.version || '-';
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: BOMItem) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个BOM项目吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const filteredBomItems = bomItems.filter((item: BOMItem) => {
    if (!searchText) return true;
    const part = parts.find(p => p.id === item.part_id);
    return (
      part?.part_number?.toLowerCase().includes(searchText.toLowerCase()) ||
      part?.part_name?.toLowerCase().includes(searchText.toLowerCase()) ||
      part?.specifications?.toLowerCase().includes(searchText.toLowerCase())
    );
  });

  const selectedProjectData = projects.find(p => p.id === selectedProject);

  return (
    <div>
      <div className="page-header">
        <Title level={2} style={{ margin: 0 }}>
          BOM管理
        </Title>
      </div>

      {/* 新功能提示 */}
      <Alert
        message="新功能提示"
        description={
          <div>
            现在您可以在项目详情页面中直接管理BOM，体验更加流畅！
            <Button
              type="link"
              onClick={() => window.open('/projects', '_blank')}
              style={{ padding: 0, marginLeft: 8 }}
            >
              前往项目管理 →
            </Button>
          </div>
        }
        type="info"
        showIcon
        closable
        style={{ marginBottom: 16 }}
      />

      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={8}>
            <Text strong>选择项目：</Text>
            <Select
              style={{ width: '100%', marginTop: 8 }}
              placeholder="请选择项目"
              value={selectedProject}
              onChange={setSelectedProject}
              showSearch
              optionFilterProp="children"
            >
              {projects.map((project: Project) => (
                <Option key={project.id} value={project.id}>
                  {project.project_name}
                  {project.customer_name && ` (${project.customer_name})`}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={8}>
            <Text strong>搜索零件：</Text>
            <Input
              style={{ marginTop: 8 }}
              placeholder="搜索零件编号、名称或规格"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
            />
          </Col>
          <Col span={8}>
            <div style={{ marginTop: 24 }}>
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                  disabled={!selectedProject}
                >
                  添加BOM项目
                </Button>
                <Button icon={<ExportOutlined />} disabled={!selectedProject}>
                  导出BOM
                </Button>
                <Button icon={<ImportOutlined />} disabled={!selectedProject}>
                  导入BOM
                </Button>
              </Space>
            </div>
          </Col>
        </Row>

        {selectedProjectData && (
          <>
            <Divider />
            <Row gutter={16}>
              <Col span={12}>
                <Text strong>项目名称：</Text>
                <Text style={{ marginLeft: 8 }}>{selectedProjectData.project_name}</Text>
              </Col>
              <Col span={12}>
                <Text strong>客户名称：</Text>
                <Text style={{ marginLeft: 8 }}>{selectedProjectData.customer_name || '-'}</Text>
              </Col>
            </Row>
          </>
        )}
      </Card>

      <Card>
        <Table
          columns={columns}
          dataSource={filteredBomItems}
          rowKey="id"
          loading={isLoading}
          pagination={{
            total: filteredBomItems.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <Modal
        title={editingBom ? '编辑BOM项目' : '添加BOM项目'}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
          setEditingBom(null);
        }}
        confirmLoading={createBomMutation.isLoading || updateBomMutation.isLoading}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            quantity: 1,
          }}
        >
          <Form.Item
            name="part_id"
            label="选择零件"
            rules={[{ required: true, message: '请选择零件' }]}
          >
            <Select
              placeholder="请选择零件"
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
              }
            >
              {parts.map((part: Part) => (
                <Option key={part.id} value={part.id}>
                  {part.part_number} - {part.part_name || '未命名'}
                  {part.specifications && ` (${part.specifications})`}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="quantity"
            label="数量"
            rules={[
              { required: true, message: '请输入数量' },
              { type: 'number', min: 1, message: '数量必须大于0' },
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入数量"
              min={1}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default BOM;
