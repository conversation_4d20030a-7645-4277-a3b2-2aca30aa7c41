import React, { useState, useMemo } from 'react';
import { Row, Col, Card, Typography, Button, Space, Table, Tag, Tabs, Modal, Divider, Alert, App } from 'antd';
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  CheckCircleOutlined, 
  ClockCircleOutlined, 
  ToolOutlined,
  SettingOutlined,
  SafetyCertificateOutlined,
  Bar<PERSON><PERSON>Outlined,
  EyeOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import type { ExecutionLog, PlanTaskWithDetails } from '@/types/api';
import { useAuthStore } from '@/store/auth';
import { usePermissions } from '@/hooks/usePermissions';
import RoleBasedAccess from '@/components/RoleBasedAccess';
import TaskConfirmation from '@/components/operator/TaskConfirmation';
import MachineStatusUpdate from '@/components/operator/MachineStatusUpdate';
import QualityDataEntry from '@/components/operator/QualityDataEntry';
import TaskCompletion from '@/components/operator/TaskCompletion';
// import UserDebugInfo from '@/components/UserDebugInfo';
import dayjs from '@/utils/dayjs';

const { Title, Text } = Typography;

/**
 * 生产执行中心 - 统一的生产执行页面
 * 根据用户角色显示不同的功能模块
 */
const ProductionCenter: React.FC = () => {
  const { message } = App.useApp();
  const [activeTab, setActiveTab] = useState('my-tasks');
  const [selectedTask, setSelectedTask] = useState<PlanTaskWithDetails | null>(null);
  const [isTaskModalVisible, setIsTaskModalVisible] = useState(false);
  const queryClient = useQueryClient();
  const { user } = useAuthStore();
  const { hasOperationPermission } = usePermissions();

  // 判断用户角色和权限
  const isOperator = hasOperationPermission('START_TASK') || hasOperationPermission('COMPLETE_TASK');
  const isManager = user?.roles?.includes('admin') ||
                   hasOperationPermission('EDIT_PLAN_TASK') ||
                   hasOperationPermission('CREATE_PLAN_TASK');

  // 获取计划分配模式配置
  const { data: planAssignmentConfig, isLoading: isConfigLoading } = useQuery(
    'plan-assignment-mode',
    () => apiClient.getPlanAssignmentMode(),
    {
      staleTime: 5 * 60 * 1000, // 5分钟缓存
    }
  );

  const { data: executionLogs, isLoading: logsLoading } = useQuery(
    'execution-logs',
    () => apiClient.getExecutionLogs()
  );

  const { data: myTasks, isLoading: tasksLoading } = useQuery(
    'my-skill-group-tasks',
    () => apiClient.getMySkillGroupTasks(),
    {
      enabled: !!user?.id, // 确保用户已登录且有ID
      refetchInterval: 30000, // 30秒刷新一次
    }
  );

  // 任务执行操作
  const startTaskMutation = useMutation(
    (taskId: number) => apiClient.startTask({ plan_task_id: taskId }),
    {
      onSuccess: () => {
        message.success('任务已开始');
        queryClient.invalidateQueries('my-skill-group-tasks');
        queryClient.invalidateQueries('execution-logs');
      },
      onError: () => {
        message.error('开始任务失败');
      },
    }
  );

  const completeTaskMutation = useMutation(
    (taskId: number) => apiClient.completeTask({ plan_task_id: taskId }),
    {
      onSuccess: () => {
        message.success('任务已完成');
        queryClient.invalidateQueries('my-skill-group-tasks');
        queryClient.invalidateQueries('execution-logs');
      },
      onError: () => {
        message.error('完成任务失败');
      },
    }
  );

  const pauseTaskMutation = useMutation(
    (taskId: number) => apiClient.pauseTask({ plan_task_id: taskId }),
    {
      onSuccess: () => {
        message.success('任务已暂停');
        queryClient.invalidateQueries('my-skill-group-tasks');
        queryClient.invalidateQueries('execution-logs');
      },
      onError: () => {
        message.error('暂停任务失败');
      },
    }
  );

  const getEventTypeTag = (eventType: string) => {
    const typeMap: Record<string, { color: string; text: string }> = {
      start: { color: 'blue', text: '开始' },
      pause: { color: 'orange', text: '暂停' },
      resume: { color: 'cyan', text: '恢复' },
      complete: { color: 'green', text: '完成' },
      cancel: { color: 'red', text: '取消' },
    };
    const config = typeMap[eventType] || { color: 'default', text: eventType };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getStatusTag = (status: string) => {
    const statusMap: Record<string, { color: string; text: string }> = {
      planned: { color: 'default', text: '计划中' },
      scheduled: { color: 'blue', text: '已排程' },
      in_progress: { color: 'processing', text: '进行中' },
      completed: { color: 'success', text: '已完成' },
      cancelled: { color: 'error', text: '已取消' },
    };
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const handleTaskAction = (task: PlanTaskWithDetails, action: string) => {
    Modal.confirm({
      title: `确认${action}任务`,
      content: `确定要${action}任务 "${task.process_name}" 吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        switch (action) {
          case '开始':
            startTaskMutation.mutate(task.id);
            break;
          case '完成':
            completeTaskMutation.mutate(task.id);
            break;
          case '暂停':
            pauseTaskMutation.mutate(task.id);
            break;
        }
      },
    });
  };

  const handleViewTask = (task: PlanTaskWithDetails) => {
    setSelectedTask(task);
    setIsTaskModalVisible(true);
  };

  // 获取唯一值用于筛选
  const getUniqueValues = (dataSource: PlanTaskWithDetails[], field: string) => {
    const values = dataSource?.map(item => item[field as keyof PlanTaskWithDetails])
      .filter(value => value !== null && value !== undefined && value !== '')
      .map(value => String(value));
    return [...new Set(values)].sort();
  };

  // 生成筛选选项
  const generateFilterOptions = (values: string[]) => {
    return values.map(value => ({
      text: value,
      value: value,
    }));
  };

  // 我的任务表格列定义
  const myTasksColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '项目',
      dataIndex: 'project_name',
      key: 'project_name',
      filters: generateFilterOptions(getUniqueValues(myTasks || [], 'project_name')),
      onFilter: (value: any, record: PlanTaskWithDetails) =>
        record.project_name === value,
      filterSearch: true,
      width: 120,
    },
    {
      title: '零件',
      dataIndex: 'part_number',
      key: 'part_number',
      filters: generateFilterOptions(getUniqueValues(myTasks || [], 'part_number')),
      onFilter: (value: any, record: PlanTaskWithDetails) =>
        record.part_number === value,
      filterSearch: true,
      width: 120,
    },
    {
      title: '工艺',
      dataIndex: 'process_name',
      key: 'process_name',
      filters: generateFilterOptions(getUniqueValues(myTasks || [], 'process_name')),
      onFilter: (value: any, record: PlanTaskWithDetails) =>
        record.process_name === value,
      filterSearch: true,
      width: 150,
    },
    {
      title: '技能组',
      dataIndex: 'skill_group_name',
      key: 'skill_group_name',
      filters: generateFilterOptions(getUniqueValues(myTasks || [], 'skill_group_name')),
      onFilter: (value: any, record: PlanTaskWithDetails) =>
        record.skill_group_name === value,
      filterSearch: true,
      width: 100,
    },
    {
      title: '设备',
      dataIndex: 'machine_name',
      key: 'machine_name',
      filters: planAssignmentConfig?.mode === 'machine' ?
        generateFilterOptions(getUniqueValues(myTasks || [], 'machine_name')) :
        undefined,
      onFilter: planAssignmentConfig?.mode === 'machine' ?
        (value: any, record: PlanTaskWithDetails) =>
          record.machine_name === value :
        undefined,
      filterSearch: planAssignmentConfig?.mode === 'machine',
      width: 100,
      render: (text: string, record: PlanTaskWithDetails) => {
        if (planAssignmentConfig?.mode === 'machine') {
          return text || '未分配';
        }
        return '-'; // 技能组模式下不显示设备
      },
    },
    {
      title: '计划开始',
      dataIndex: 'planned_start',
      key: 'planned_start',
      width: 120,
      render: (text: string) => dayjs(text).format('MM-DD HH:mm'),
    },
    {
      title: '计划结束',
      dataIndex: 'planned_end',
      key: 'planned_end',
      width: 120,
      render: (text: string) => dayjs(text).format('MM-DD HH:mm'),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      filters: [
        { text: '计划中', value: 'planned' },
        { text: '进行中', value: 'in_progress' },
        { text: '已完成', value: 'completed' },
        { text: '已暂停', value: 'paused' },
        { text: '已取消', value: 'cancelled' },
      ],
      onFilter: (value: any, record: PlanTaskWithDetails) =>
        record.status === value,
      width: 100,
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_: any, record: PlanTaskWithDetails) => (
        <Space>
          <Button
            size="small"
            onClick={() => handleViewTask(record)}
          >
            查看
          </Button>
          {record.status === 'planned' || record.status === 'scheduled' ? (
            <Button
              type="primary"
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={() => handleTaskAction(record, '开始')}
              loading={startTaskMutation.isLoading}
            >
              开始
            </Button>
          ) : null}
          {record.status === 'in_progress' ? (
            <>
              <Button
                size="small"
                icon={<PauseCircleOutlined />}
                onClick={() => handleTaskAction(record, '暂停')}
                loading={pauseTaskMutation.isLoading}
              >
                暂停
              </Button>
              <Button
                type="primary"
                size="small"
                icon={<CheckCircleOutlined />}
                onClick={() => handleTaskAction(record, '完成')}
                loading={completeTaskMutation.isLoading}
                style={{ backgroundColor: '#52c41a' }}
              >
                完成
              </Button>
            </>
          ) : null}
        </Space>
      ),
    },
  ];

  // 执行日志表格列定义
  const logColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '任务ID',
      dataIndex: 'plan_task_id',
      key: 'plan_task_id',
    },
    {
      title: '设备ID',
      dataIndex: 'machine_id',
      key: 'machine_id',
      render: (text: number) => text || '未指定',
    },
    {
      title: '操作员',
      dataIndex: 'user_id',
      key: 'user_id',
    },
    {
      title: '事件类型',
      dataIndex: 'event_type',
      key: 'event_type',
      render: (eventType: string) => getEventTypeTag(eventType),
    },
    {
      title: '事件时间',
      dataIndex: 'event_time',
      key: 'event_time',
      render: (text: string) => dayjs(text).format('MM-DD HH:mm:ss'),
    },
    {
      title: '备注',
      dataIndex: 'notes',
      key: 'notes',
      render: (text: string) => (
        <Text ellipsis={{ tooltip: text }} style={{ maxWidth: 150 }}>
          {text || '无'}
        </Text>
      ),
    },
  ];

  // 根据角色生成标签页配置
  const tabItems = useMemo(() => {
    const items = [];

    // 操作员视图 - 任务执行相关
    if (isOperator) {
      items.push(
        {
          key: 'my-tasks',
          label: (
            <Space>
              <PlayCircleOutlined />
              {planAssignmentConfig?.mode === 'machine' ? '我的设备任务' : '我的任务'}
              {planAssignmentConfig?.mode === 'machine' && (
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  (3天内)
                </Text>
              )}
              {planAssignmentConfig?.mode === 'skill_group' && (
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  (3天内)
                </Text>
              )}
            </Space>
          ),
          children: (
            <div>
              {myTasks && myTasks.length === 0 && !tasksLoading && (
                <Alert
                  message="暂无可执行的任务"
                  description={
                    <div>
                      <p>可能的原因：</p>
                      <ul style={{ marginBottom: 0, paddingLeft: 20 }}>
                        <li>您还没有被分配技能组，请联系管理员分配适当的技能组</li>
                        <li>当前没有分配给您技能组的任务</li>
                        <li>所有任务都已完成或不在3天时间范围内</li>
                      </ul>
                    </div>
                  }
                  type="info"
                  showIcon
                  style={{ marginBottom: 16 }}
                />
              )}
              <Table
                columns={myTasksColumns}
                dataSource={myTasks}
                rowKey="id"
                loading={tasksLoading}
                pagination={{
                  total: myTasks?.length || 0,
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total) => `共 ${total} 条任务`,
                }}
                scroll={{ x: 1200 }}
                locale={{
                  emptyText: '暂无任务',
                }}
              />
            </div>
          )
        },
        {
          key: 'task-confirm',
          label: (
            <Space>
              <CheckCircleOutlined />
              任务确认
            </Space>
          ),
          children: <TaskConfirmation />
        },
        {
          key: 'machine-status',
          label: (
            <Space>
              <SettingOutlined />
              设备状态
            </Space>
          ),
          children: <MachineStatusUpdate />
        },
        {
          key: 'quality-check',
          label: (
            <Space>
              <SafetyCertificateOutlined />
              质量检查
            </Space>
          ),
          children: <QualityDataEntry />
        },
        {
          key: 'task-completion',
          label: (
            <Space>
              <CheckCircleOutlined />
              完工确认
            </Space>
          ),
          children: <TaskCompletion />
        }
      );
    }

    // 管理员视图 - 监控跟踪相关
    if (isManager) {
      items.push(
        {
          key: 'execution-tracking',
          label: (
            <Space>
              <EyeOutlined />
              执行跟踪
            </Space>
          ),
          children: (
            <Table
              columns={myTasksColumns}
              dataSource={myTasks}
              rowKey="id"
              loading={tasksLoading}
              pagination={{
                total: myTasks?.length || 0,
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条任务`,
              }}
              scroll={{ x: 1200 }}
            />
          )
        },
        {
          key: 'execution-logs',
          label: (
            <Space>
              <BarChartOutlined />
              执行日志
            </Space>
          ),
          children: (
            <Table
              columns={logColumns}
              dataSource={executionLogs}
              rowKey="id"
              loading={logsLoading}
              pagination={{
                total: executionLogs?.length || 0,
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          )
        }
      );
    }

    return items;
  }, [isOperator, isManager, myTasks, tasksLoading, executionLogs, logsLoading, myTasksColumns, logColumns]);

  // 设置默认标签页
  React.useEffect(() => {
    if (tabItems.length > 0 && !tabItems.find(item => item.key === activeTab)) {
      setActiveTab(tabItems[0].key);
    }
  }, [tabItems, activeTab]);

  // 添加调试信息组件（临时）
  // Debug info removed for production
  // const showDebugInfo = true;

  return (
    <div>
      {/* Debug info removed for production */}
      {/* {showDebugInfo && <UserDebugInfo />} */}

      <div className="page-header">
        <Title level={2} style={{ margin: 0 }}>
          生产执行中心
        </Title>
        <div style={{ display: 'flex', alignItems: 'center', gap: 16, marginTop: 8 }}>
          <Typography.Paragraph style={{ margin: 0, color: '#666' }}>
            {isOperator && '任务执行、设备操作、质量录入'}
            {isManager && '生产监控、执行跟踪、数据分析'}
            {!isOperator && !isManager && '生产执行相关功能'}
          </Typography.Paragraph>
          {!isConfigLoading && planAssignmentConfig && (
            <div style={{ fontSize: '14px', color: '#666' }}>
              <SettingOutlined style={{ marginRight: 4 }} />
              任务分配模式: {planAssignmentConfig.mode === 'skill_group' ? '技能组模式' : '设备模式'}
              {planAssignmentConfig.mode === 'skill_group' && (
                <Text type="secondary" style={{ marginLeft: 8, fontSize: '12px' }}>
                  (显示前2天至后3天技能组任务)
                </Text>
              )}
              {planAssignmentConfig.mode === 'machine' && (
                <Text type="secondary" style={{ marginLeft: 8, fontSize: '12px' }}>
                  (显示前2天至后3天设备任务)
                </Text>
              )}
              {!planAssignmentConfig.enabled && (
                <Tag color="orange" style={{ marginLeft: 8 }}>配置已禁用</Tag>
              )}
            </div>
          )}
        </div>
      </div>

      {/* 统计卡片 - 根据角色显示不同内容 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <ClockCircleOutlined style={{ fontSize: 32, color: '#1890ff', marginBottom: 8 }} />
              <div>
                <Text strong>{isOperator ? '待执行任务' : '计划任务'}</Text>
              </div>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>
                {myTasks?.filter(t => t.status === 'planned' || t.status === 'scheduled').length || 0}
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <PlayCircleOutlined style={{ fontSize: 32, color: '#faad14', marginBottom: 8 }} />
              <div>
                <Text strong>进行中任务</Text>
              </div>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#faad14' }}>
                {myTasks?.filter(t => t.status === 'in_progress').length || 0}
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <CheckCircleOutlined style={{ fontSize: 32, color: '#52c41a', marginBottom: 8 }} />
              <div>
                <Text strong>今日完成</Text>
              </div>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#52c41a' }}>
                {myTasks?.filter(t => t.status === 'completed').length || 0}
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <ToolOutlined style={{ fontSize: 32, color: '#722ed1', marginBottom: 8 }} />
              <div>
                <Text strong>{isOperator ? '我的技能组' : '活跃技能组'}</Text>
              </div>
              <div style={{ fontSize: 16, fontWeight: 'bold', color: '#722ed1' }}>
                {user?.skills && user.skills.length > 0 ? user.skills.join(', ') : '未分配'}
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
          tabBarStyle={{ marginBottom: 24 }}
        />
      </Card>

      {/* 任务详情模态框 */}
      <Modal
        title="任务详情"
        open={isTaskModalVisible}
        onCancel={() => {
          setIsTaskModalVisible(false);
          setSelectedTask(null);
        }}
        footer={[
          <Button key="close" onClick={() => setIsTaskModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedTask && (
          <div>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Card size="small" title="基本信息">
                  <p><strong>任务ID：</strong>{selectedTask.id}</p>
                  <p><strong>项目：</strong>{selectedTask.project_name}</p>
                  <p><strong>零件：</strong>{selectedTask.part_number}</p>
                  <p><strong>工艺：</strong>{selectedTask.process_name}</p>
                  <p><strong>技能组：</strong>{selectedTask.skill_group_name}</p>
                  <p><strong>状态：</strong>{getStatusTag(selectedTask.status)}</p>
                </Card>
              </Col>
              <Col span={12}>
                <Card size="small" title="时间信息">
                  <p><strong>计划开始：</strong>{dayjs(selectedTask.planned_start).format('YYYY-MM-DD HH:mm')}</p>
                  <p><strong>计划结束：</strong>{dayjs(selectedTask.planned_end).format('YYYY-MM-DD HH:mm')}</p>
                  <p><strong>标准工时：</strong>{selectedTask.standard_hours || '未设置'} 小时</p>
                  <p><strong>工单数量：</strong>{selectedTask.work_order_quantity}</p>
                </Card>
              </Col>
            </Row>

            {selectedTask.work_instructions && (
              <Card size="small" title="作业指导书" style={{ marginTop: 16 }}>
                <div style={{
                  padding: 12,
                  backgroundColor: '#f5f5f5',
                  borderRadius: 6,
                  whiteSpace: 'pre-wrap'
                }}>
                  {selectedTask.work_instructions}
                </div>
              </Card>
            )}

            <Divider />

            <div style={{ textAlign: 'center' }}>
              {selectedTask.status === 'planned' || selectedTask.status === 'scheduled' ? (
                <Button
                  type="primary"
                  size="large"
                  icon={<PlayCircleOutlined />}
                  onClick={() => {
                    handleTaskAction(selectedTask, '开始');
                    setIsTaskModalVisible(false);
                  }}
                  loading={startTaskMutation.isLoading}
                >
                  开始任务
                </Button>
              ) : null}

              {selectedTask.status === 'in_progress' ? (
                <Space>
                  <Button
                    size="large"
                    icon={<PauseCircleOutlined />}
                    onClick={() => {
                      handleTaskAction(selectedTask, '暂停');
                      setIsTaskModalVisible(false);
                    }}
                    loading={pauseTaskMutation.isLoading}
                  >
                    暂停任务
                  </Button>
                  <Button
                    type="primary"
                    size="large"
                    icon={<CheckCircleOutlined />}
                    onClick={() => {
                      handleTaskAction(selectedTask, '完成');
                      setIsTaskModalVisible(false);
                    }}
                    loading={completeTaskMutation.isLoading}
                    style={{ backgroundColor: '#52c41a' }}
                  >
                    完成任务
                  </Button>
                </Space>
              ) : null}
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ProductionCenter;
