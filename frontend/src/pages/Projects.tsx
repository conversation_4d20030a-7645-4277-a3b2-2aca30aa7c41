import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Popconfirm,
  Typography,
  Card,
  Tag,
  Row,
  Col,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ImportOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useNavigate } from 'react-router-dom';
import { apiClient } from '@/lib/api';
import { useMessage } from '@/hooks/useMessage';
import DataImport from '@/components/DataImport';
import ProjectStatusTag from '@/components/ProjectStatusTag';
import ProjectStatusSelector from '@/components/ProjectStatusSelector';
import type {
  Project,
  CreateProjectRequest,
  UpdateProjectRequest,
  ProjectQuery,
} from '@/types/api';
import dayjs from '@/utils/dayjs';

const { Title } = Typography;

const Projects: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [isImportVisible, setIsImportVisible] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string | undefined>(undefined);
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { success, error } = useMessage();

  // Build query parameters
  const queryParams: ProjectQuery = {
    ...(statusFilter && { status: statusFilter }),
  };

  const { data: projects, isLoading } = useQuery(
    ['projects', queryParams],
    () => {
      if (Object.keys(queryParams).length > 0) {
        return apiClient.searchProjects(queryParams).then(result => result.projects);
      }
      return apiClient.getProjects();
    },
    { refetchInterval: 30000 }
  );

  const createMutation = useMutation(
    (data: CreateProjectRequest) => apiClient.createProject(data),
    {
      onSuccess: () => {
        success('项目创建成功');
        queryClient.invalidateQueries(['projects']);
        setIsModalVisible(false);
        form.resetFields();
      },
      onError: () => {
        error('项目创建失败');
      },
    }
  );

  const updateMutation = useMutation(
    ({ id, data }: { id: number; data: UpdateProjectRequest }) =>
      apiClient.updateProject(id, data),
    {
      onSuccess: () => {
        success('项目更新成功');
        queryClient.invalidateQueries(['projects']);
        setIsModalVisible(false);
        setEditingProject(null);
        form.resetFields();
      },
      onError: () => {
        error('项目更新失败');
      },
    }
  );

  const deleteMutation = useMutation(
    (id: number) => apiClient.deleteProject(id),
    {
      onSuccess: () => {
        success('项目删除成功');
        queryClient.invalidateQueries(['projects']);
      },
      onError: () => {
        error('项目删除失败');
      },
    }
  );

  const handleCreate = () => {
    setEditingProject(null);
    setIsModalVisible(true);
    form.resetFields();
  };

  const handleEdit = (project: Project) => {
    setEditingProject(project);
    setIsModalVisible(true);
    form.setFieldsValue({
      project_name: project.project_name,
      customer_name: project.customer_name,
    });
  };

  const handleDelete = (id: number) => {
    deleteMutation.mutate(id);
  };

  const handleViewDetail = (project: Project) => {
    navigate(`/projects/${project.id}`);
  };

  const handleImport = () => {
    setIsImportVisible(true);
  };

  const handleImportSuccess = () => {
    queryClient.invalidateQueries('projects');
    setIsImportVisible(false);
  };

  // Get status tag with appropriate color and icon
  const getStatusTag = (status: string) => {
    return <ProjectStatusTag status={status} />;
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (editingProject) {
        updateMutation.mutate({ id: editingProject.id, data: values });
      } else {
        console.log('Creating project with data:', values);
        createMutation.mutate(values);
      }
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '项目名称',
      dataIndex: 'project_name',
      key: 'project_name',
      render: (text: string) => <strong>{text}</strong>,
    },
    {
      title: '客户名称',
      dataIndex: 'customer_name',
      key: 'customer_name',
      render: (text: string) => text || <Tag color="default">未指定</Tag>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
      width: 120,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_: any, record: Project) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            查看详情
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个项目吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div className="page-header">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={2} style={{ margin: 0 }}>
            项目管理
          </Title>
          <Space>
            <Button
              icon={<ImportOutlined />}
              onClick={handleImport}
            >
              导入项目
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreate}
            >
              新建项目
            </Button>
          </Space>
        </div>
      </div>

      {/* Filters */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={6}>
            <ProjectStatusSelector
              placeholder="筛选状态"
              allowClear
              value={statusFilter}
              onChange={setStatusFilter}
              style={{ width: '100%' }}
            />
          </Col>
        </Row>
      </Card>

      <Card>
        <Table
          columns={columns}
          dataSource={projects}
          rowKey="id"
          loading={isLoading}
          pagination={{
            total: projects?.length || 0,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <Modal
        title={editingProject ? '编辑项目' : '新建项目'}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingProject(null);
          form.resetFields();
        }}
        confirmLoading={createMutation.isLoading || updateMutation.isLoading}
      >
        <Form
          form={form}
          layout="vertical"
          autoComplete="off"
        >
          <Form.Item
            name="project_name"
            label="项目名称"
            rules={[
              { required: true, message: '请输入项目名称' },
              { min: 2, message: '项目名称至少2个字符' },
            ]}
          >
            <Input placeholder="请输入项目名称" />
          </Form.Item>

          <Form.Item
            name="customer_name"
            label="客户名称"
          >
            <Input placeholder="请输入客户名称（可选）" />
          </Form.Item>

          <Form.Item
            name="status"
            label="项目状态"
            initialValue="normal"
          >
            <ProjectStatusSelector placeholder="请选择项目状态" />
          </Form.Item>
        </Form>
      </Modal>

      <DataImport
        visible={isImportVisible}
        onCancel={() => setIsImportVisible(false)}
        moduleType="projects"
        moduleName="项目"
        onSuccess={handleImportSuccess}
      />
    </div>
  );
};

export default Projects;
