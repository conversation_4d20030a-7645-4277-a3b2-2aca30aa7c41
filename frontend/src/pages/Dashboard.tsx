import React from 'react';
import { Row, Col, Card, Statistic, Typography, Spin, Alert } from 'antd';
import {
  ProjectOutlined,
  FileTextOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  SettingOutlined,
  SafetyCertificateOutlined,
} from '@ant-design/icons';
import { useQuery } from 'react-query';
import { apiClient } from '@/lib/api';
import { useAuthStore } from '@/store/auth';
import { hasRole, ROLES } from '@/utils/permissions';
import { useI18n } from '@/hooks/useI18n';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import OperatorDashboard from '@/components/dashboards/OperatorDashboard';
import QualityInspectorDashboard from '@/components/dashboards/QualityInspectorDashboard';
import PlannerDashboard from '@/components/dashboards/PlannerDashboard';
// import UserDebugInfo from '@/components/UserDebugInfo';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const { user, isAuthenticated, isLoading } = useAuthStore();
  const { dashboard } = useI18n();

  // 添加调试信息组件（临时）
  const showDebugInfo = true;

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <Spin size="large" />
      </div>
    );
  }

  // 如果未认证，显示提示信息
  if (!isAuthenticated || !user) {
    return (
      <div style={{ padding: '20px' }}>
        <Alert
          message="认证状态异常"
          description="请重新登录以访问系统功能。"
          type="warning"
          showIcon
          action={
            <a href="/login" style={{ textDecoration: 'none' }}>
              前往登录
            </a>
          }
        />
      </div>
    );
  }

  // 根据用户角色显示不同的仪表板
  if (user && user.roles) {
    const userRoles = user.roles;

    // 操作员仪表板
    if (hasRole(userRoles, ROLES.OPERATOR) && !hasRole(userRoles, ROLES.ADMIN)) {
      return (
        <div>
          {/* Debug info removed for production */}
          {/* {showDebugInfo && <UserDebugInfo />} */}
          <OperatorDashboard />
        </div>
      );
    }

    // 质量检验员仪表板
    if (hasRole(userRoles, ROLES.QUALITY_INSPECTOR) && !hasRole(userRoles, ROLES.ADMIN)) {
      return (
        <div>
          {/* Debug info removed for production */}
          {/* {showDebugInfo && <UserDebugInfo />} */}
          <QualityInspectorDashboard />
        </div>
      );
    }

    // 生产计划员仪表板
    if (hasRole(userRoles, ROLES.PLANNER) && !hasRole(userRoles, ROLES.ADMIN)) {
      return (
        <div>
          {/* Debug info removed for production */}
          {/* {showDebugInfo && <UserDebugInfo />} */}
          <PlannerDashboard />
        </div>
      );
    }
  }

  // 默认仪表板（管理员、工艺工程师等）
  const { data: overview, isLoading: overviewLoading, error: overviewError } = useQuery(
    'dashboard-overview',
    () => apiClient.getDashboardOverview(),
    {
      refetchInterval: 30000, // Refresh every 30 seconds
      onSuccess: (data) => {
        // console.log('Dashboard data loaded:', data);
      },
      onError: (error) => {
        // console.error('Dashboard data loading error:', error);
      }
    }
  );

  const { data: productionSummary, isLoading: summaryLoading } = useQuery(
    'production-summary',
    () => apiClient.getProductionSummary(),
    { refetchInterval: 30000 }
  );

  const { data: weeklyTaskStats, isLoading: weeklyStatsLoading } = useQuery(
    'weekly-task-stats',
    () => apiClient.getWeeklyTaskStats(),
    { refetchInterval: 60000 } // Refresh every minute
  );

  // Work order data for charts
  const workOrderData = [
    { name: '待处理', value: overview?.work_order_status?.pending_orders || 0, color: '#ffa940' },
    { name: '进行中', value: overview?.work_order_status?.in_progress_orders || 0, color: '#1890ff' },
    { name: '已完成', value: overview?.work_order_status?.completed_orders || 0, color: '#52c41a' },
    { name: '已计划', value: overview?.work_order_status?.planned_orders || 0, color: '#722ed1' },
  ];

  // 使用真实的本周任务统计数据，如果数据还在加载则使用空数组
  const weeklyData = weeklyTaskStats?.daily_stats?.map(stat => ({
    name: stat.day_name,
    完成任务: stat.completed_tasks,
    计划任务: stat.planned_tasks,
    完成率: stat.completion_rate,
  })) || [];

  if (overviewError) {
    return (
      <Alert
        message="数据加载失败"
        description="无法获取仪表板数据，请检查网络连接或稍后重试"
        type="error"
        showIcon
      />
    );
  }

  return (
    <div>
      {/* Debug info removed for production */}
      {/* {showDebugInfo && <UserDebugInfo />} */}

      <div className="page-header">
        <Title level={2} style={{ margin: 0 }}>
          生产仪表板
        </Title>
      </div>

      <Spin spinning={overviewLoading || summaryLoading}>
        {overviewError ? (
          <Card title="❌ 数据加载错误" style={{ marginBottom: 16 }} size="small">
            <Alert
              message="仪表板数据加载失败"
              description={`错误信息: ${String(overviewError)}`}
              type="error"
              showIcon
            />
          </Card>
        ) : null}

        {overview && (
          <Card title="📊 调试信息" style={{ marginBottom: 16 }} size="small">
            <Alert
              message="数据加载成功"
              description={`设备利用率: ${overview.machine_status?.utilization_rate || 0}%, 质量合格率: ${overview.quality_metrics?.quality_rate || 0}%`}
              type="success"
              showIcon
            />
            <details style={{ marginTop: 10 }}>
              <summary style={{ cursor: 'pointer', color: '#1890ff' }}>查看原始数据</summary>
              <pre style={{ fontSize: '12px', maxHeight: '200px', overflow: 'auto', marginTop: 10, background: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
                {JSON.stringify(overview, null, 2)}
              </pre>
            </details>
          </Card>
        )}

        {!overview && !overviewLoading && !overviewError && (
          <Card title="⚠️ 无数据" style={{ marginBottom: 16 }} size="small">
            <Alert
              message="未获取到仪表板数据"
              description="请检查网络连接和API服务状态"
              type="warning"
              showIcon
            />
          </Card>
        )}

        {/* 统计卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title={dashboard.totalWorkOrders()}
                value={overview?.production_summary?.total_work_orders || 0}
                prefix={<ProjectOutlined style={{ color: '#1890ff' }} />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title={dashboard.inProgressOrders()}
                value={overview?.work_order_status?.in_progress_orders || 0}
                prefix={<FileTextOutlined style={{ color: '#52c41a' }} />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title={dashboard.pendingTasks()}
                value={overview?.production_summary?.tasks_pending || 0}
                prefix={<CalendarOutlined style={{ color: '#faad14' }} />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="今日完成"
                value={overview?.production_summary?.tasks_completed_today || 0}
                prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 效率指标 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} lg={8}>
            <Card>
              <Statistic
                title={dashboard.machineUtilization()}
                value={overview?.machine_status?.utilization_rate || 0}
                suffix="%"
                prefix={<SettingOutlined style={{ color: '#722ed1' }} />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={8}>
            <Card>
              <Statistic
                title={dashboard.qualityRate()}
                value={overview?.quality_metrics?.quality_rate || 0}
                suffix="%"
                prefix={<SafetyCertificateOutlined style={{ color: '#52c41a' }} />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={8}>
            <Card>
              <Statistic
                title={dashboard.productionEfficiency()}
                value={overview?.production_summary?.overall_efficiency || 0}
                suffix="%"
                prefix={<CheckCircleOutlined style={{ color: '#1890ff' }} />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 图表 */}
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={16}>
            <Card
              title={
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span>本周任务完成情况</span>
                  {weeklyTaskStats && (
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      总完成率: {weeklyTaskStats.overall_completion_rate.toFixed(1)}%
                    </Text>
                  )}
                </div>
              }
              style={{ height: 400 }}
              loading={weeklyStatsLoading}
            >
              {weeklyData.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={weeklyData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip
                      formatter={(value, name) => [
                        value,
                        name === '计划任务' ? '计划任务' : '完成任务'
                      ]}
                      labelFormatter={(label) => `${label}`}
                    />
                    <Bar dataKey="计划任务" fill="#91d5ff" name="计划任务" />
                    <Bar dataKey="完成任务" fill="#1890ff" name="完成任务" />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div style={{
                  height: 300,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#999'
                }}>
                  {weeklyStatsLoading ? '数据加载中...' : '暂无数据'}
                </div>
              )}
            </Card>
          </Col>
          <Col xs={24} lg={8}>
            <Card title="工单状态分布" style={{ height: 400 }}>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={workOrderData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                    label={({ name, value }) => `${name}: ${value}`}
                  >
                    {workOrderData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </Card>
          </Col>
        </Row>
      </Spin>
    </div>
  );
};

export default Dashboard;
