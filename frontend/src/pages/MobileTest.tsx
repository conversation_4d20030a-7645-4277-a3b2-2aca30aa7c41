import React, { useState } from 'react';
import { 
  Card, 
  Button, 
  Space, 
  Typography, 
  Form, 
  Input, 
  Select, 
  DatePicker, 
  Switch,
  Slider,
  Rate,
  Tag,
  Progress,
  Alert,
  Divider,
  Row,
  Col,
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  SearchOutlined,
  HeartOutlined,
  StarOutlined,
  LikeOutlined,
} from '@ant-design/icons';
import MobileTable from '@/components/MobileTable';
import MobileForm, { MobileFormItem, MobileFormGroup } from '@/components/MobileForm';
import MobileInput, { MobileSelect } from '@/components/MobileInput';
import LazyImage, { MobileImage, useMobilePerformance } from '@/components/LazyImage';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

/**
 * 移动端适配测试页面
 * 用于测试和验证各种移动端组件的效果
 */
const MobileTest: React.FC = () => {
  const [form] = Form.useForm();
  const [tableData] = useState([
    { id: 1, name: '测试项目1', status: 'active', type: 'A类', date: '2024-01-15' },
    { id: 2, name: '测试项目2', status: 'inactive', type: 'B类', date: '2024-01-16' },
    { id: 3, name: '测试项目3', status: 'pending', type: 'C类', date: '2024-01-17' },
    { id: 4, name: '测试项目4', status: 'active', type: 'A类', date: '2024-01-18' },
    { id: 5, name: '测试项目5', status: 'inactive', type: 'B类', date: '2024-01-19' },
  ]);
  const [formVisible, setFormVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [selectValue, setSelectValue] = useState('');
  
  const performance = useMobilePerformance();

  // 表格列配置
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const colors = {
          active: 'green',
          inactive: 'red',
          pending: 'orange',
        };
        return <Tag color={colors[status as keyof typeof colors]}>{status}</Tag>;
      },
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
    },
  ];

  // 表格操作
  const tableActions = [
    {
      key: 'edit',
      label: '编辑',
      icon: <EditOutlined />,
      onClick: (record: any) => console.log('编辑', record),
    },
    {
      key: 'delete',
      label: '删除',
      icon: <DeleteOutlined />,
      danger: true,
      onClick: (record: any) => console.log('删除', record),
    },
  ];

  const selectOptions = [
    { label: '选项1', value: 'option1' },
    { label: '选项2', value: 'option2' },
    { label: '选项3', value: 'option3' },
    { label: '选项4', value: 'option4' },
  ];

  return (
    <div style={{ padding: '16px', paddingBottom: '80px' }}>
      <Title level={2}>移动端适配测试</Title>
      <Paragraph>
        这个页面用于测试各种移动端组件的适配效果。请在不同尺寸的设备上查看效果。
      </Paragraph>

      {/* 性能指标 */}
      <Card title="性能指标" style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Text strong>加载时间:</Text>
            <br />
            <Text>{performance.loadTime.toFixed(2)}ms</Text>
          </Col>
          <Col span={12}>
            <Text strong>首次内容绘制:</Text>
            <br />
            <Text>{performance.firstContentfulPaint.toFixed(2)}ms</Text>
          </Col>
          <Col span={12}>
            <Text strong>最大内容绘制:</Text>
            <br />
            <Text>{performance.largestContentfulPaint.toFixed(2)}ms</Text>
          </Col>
          <Col span={12}>
            <Text strong>累积布局偏移:</Text>
            <br />
            <Text>{performance.cumulativeLayoutShift.toFixed(4)}</Text>
          </Col>
        </Row>
      </Card>

      {/* 移动端输入组件测试 */}
      <Card title="移动端输入组件" style={{ marginBottom: '16px' }}>
        <Space direction="vertical" style={{ width: '100%' }} size="middle">
          <div>
            <Text strong>文本输入:</Text>
            <MobileInput
              type="text"
              value={inputValue}
              onChange={setInputValue}
              placeholder="请输入文本"
              clearable
            />
          </div>
          
          <div>
            <Text strong>搜索输入:</Text>
            <MobileInput
              type="search"
              placeholder="搜索内容"
            />
          </div>
          
          <div>
            <Text strong>密码输入:</Text>
            <MobileInput
              type="password"
              placeholder="请输入密码"
            />
          </div>
          
          <div>
            <Text strong>数字输入:</Text>
            <MobileInput
              type="number"
              placeholder="请输入数字"
            />
          </div>
          
          <div>
            <Text strong>选择器:</Text>
            <MobileSelect
              value={selectValue}
              onChange={setSelectValue}
              options={selectOptions}
              placeholder="请选择选项"
            />
          </div>
        </Space>
      </Card>

      {/* 移动端表格测试 */}
      <Card title="移动端表格" style={{ marginBottom: '16px' }}>
        <MobileTable
          columns={columns}
          dataSource={tableData}
          rowKey="id"
          actions={tableActions}
          primaryFields={['name']}
          secondaryFields={['status', 'type', 'date']}
          pagination={{
            total: tableData.length,
            pageSize: 3,
          }}
        />
      </Card>

      {/* 移动端表单测试 */}
      <Card title="移动端表单" style={{ marginBottom: '16px' }}>
        <Button 
          type="primary" 
          onClick={() => setFormVisible(true)}
          block
        >
          打开移动端表单
        </Button>
        
        <MobileForm
          form={form}
          title="测试表单"
          visible={formVisible}
          onFinish={(values) => {
            console.log('表单提交:', values);
            setFormVisible(false);
          }}
          onCancel={() => setFormVisible(false)}
        >
          <MobileFormGroup title="基本信息">
            <Form.Item name="name" label="姓名" rules={[{ required: true }]}>
              <Input placeholder="请输入姓名" />
            </Form.Item>
            
            <Form.Item name="email" label="邮箱">
              <Input type="email" placeholder="请输入邮箱" />
            </Form.Item>
            
            <Form.Item name="phone" label="电话">
              <Input type="tel" placeholder="请输入电话" />
            </Form.Item>
          </MobileFormGroup>
          
          <MobileFormGroup title="详细信息" collapsible>
            <Form.Item name="category" label="分类">
              <Select placeholder="请选择分类">
                <Option value="A">分类A</Option>
                <Option value="B">分类B</Option>
                <Option value="C">分类C</Option>
              </Select>
            </Form.Item>
            
            <Form.Item name="date" label="日期">
              <DatePicker style={{ width: '100%' }} />
            </Form.Item>
            
            <Form.Item name="enabled" label="启用" valuePropName="checked">
              <Switch />
            </Form.Item>
            
            <Form.Item name="priority" label="优先级">
              <Slider min={1} max={10} marks={{ 1: '低', 5: '中', 10: '高' }} />
            </Form.Item>
            
            <Form.Item name="rating" label="评分">
              <Rate />
            </Form.Item>
          </MobileFormGroup>
        </MobileForm>
      </Card>

      {/* 移动端图片测试 */}
      <Card title="移动端图片" style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Text strong>懒加载图片:</Text>
            <LazyImage
              src="https://picsum.photos/300/200?random=1"
              alt="测试图片1"
              width="100%"
              height="120px"
            />
          </Col>
          <Col span={12}>
            <Text strong>移动端优化图片:</Text>
            <MobileImage
              src="https://picsum.photos/300/200?random=2"
              alt="测试图片2"
              width="100%"
              height="120px"
              quality="medium"
            />
          </Col>
        </Row>
      </Card>

      {/* 移动端交互测试 */}
      <Card title="移动端交互" style={{ marginBottom: '16px' }}>
        <Space direction="vertical" style={{ width: '100%' }} size="middle">
          <Alert
            message="触摸提示"
            description="在移动设备上，您可以使用触摸手势进行交互。尝试点击、长按、滑动等操作。"
            type="info"
            showIcon
          />
          
          <div>
            <Text strong>进度条:</Text>
            <Progress percent={75} status="active" />
          </div>
          
          <div>
            <Text strong>操作按钮:</Text>
            <Space wrap>
              <Button type="primary" icon={<PlusOutlined />}>
                添加
              </Button>
              <Button icon={<EditOutlined />}>
                编辑
              </Button>
              <Button danger icon={<DeleteOutlined />}>
                删除
              </Button>
              <Button type="text" icon={<SearchOutlined />}>
                搜索
              </Button>
            </Space>
          </div>
          
          <div>
            <Text strong>图标按钮:</Text>
            <Space size="large">
              <Button shape="circle" icon={<HeartOutlined />} />
              <Button shape="circle" icon={<StarOutlined />} />
              <Button shape="circle" icon={<LikeOutlined />} />
            </Space>
          </div>
        </Space>
      </Card>

      <Divider />
      
      <Text type="secondary" style={{ fontSize: '12px' }}>
        移动端适配测试页面 - 请在不同设备上测试效果
      </Text>
    </div>
  );
};

export default MobileTest;
