import React, { useState } from 'react';
import { Card, Typography, Button, Space, App } from 'antd';
import { addDays } from 'date-fns';
import GanttChart from '@/components/GanttChart';
import type { PlanTaskWithDetails } from '@/types/api';

const { Title, Text } = Typography;

const GanttDemo: React.FC = () => {
  const { message } = App.useApp();
  // 模拟数据
  const [tasks, setTasks] = useState<PlanTaskWithDetails[]>([
    {
      id: 1,
      work_order_id: 1001,
      routing_step_id: 1,
      skill_group_id: 1,
      machine_id: 1,
      planned_start: new Date().toISOString(),
      planned_end: addDays(new Date(), 3).toISOString(),
      status: 'pending',
      work_order_quantity: 100,
      work_order_status: 'active',
      work_order_due_date: undefined,
      project_id: 1,
      project_name: '项目A',
      customer_name: '客户A',
      part_id: 1,
      part_number: 'P001',
      part_name: '零件A',
      version: 'v1.0',
      step_number: 1,
      process_name: '铣削',
      work_instructions: '铣削加工说明',
      standard_hours: 8.0,
      skill_group_name: '机械加工组',
      machine_name: '铣床001',
    },
    {
      id: 2,
      work_order_id: 1002,
      routing_step_id: 2,
      skill_group_id: 2,
      machine_id: 2,
      planned_start: addDays(new Date(), 1).toISOString(),
      planned_end: addDays(new Date(), 4).toISOString(),
      status: 'in_progress',
      work_order_quantity: 50,
      work_order_status: 'active',
      work_order_due_date: undefined,
      project_id: 2,
      project_name: '项目B',
      customer_name: '客户B',
      part_id: 2,
      part_number: 'P002',
      part_name: '零件B',
      version: 'v1.0',
      step_number: 1,
      process_name: '车削',
      work_instructions: '车削加工说明',
      standard_hours: 6.0,
      skill_group_name: '车削组',
      machine_name: '车床001',
    },
    {
      id: 3,
      work_order_id: 1003,
      routing_step_id: 3,
      skill_group_id: 1,
      machine_id: 3,
      planned_start: addDays(new Date(), 2).toISOString(),
      planned_end: addDays(new Date(), 6).toISOString(),
      status: 'completed',
      work_order_quantity: 75,
      work_order_status: 'active',
      work_order_due_date: undefined,
      project_id: 3,
      project_name: '项目C',
      customer_name: '客户C',
      part_id: 3,
      part_number: 'P003',
      part_name: '零件C',
      version: 'v1.0',
      step_number: 1,
      process_name: '钻孔',
      work_instructions: '钻孔加工说明',
      standard_hours: 4.0,
      skill_group_name: '机械加工组',
      machine_name: '钻床001',
    },
  ]);

  const handleTaskUpdate = (taskId: number, newStart: Date, newEnd: Date) => {
    setTasks(prevTasks => 
      prevTasks.map(task => 
        task.id === taskId 
          ? { 
              ...task, 
              planned_start: newStart.toISOString(),
              planned_end: newEnd.toISOString()
            }
          : task
      )
    );
    message.success(`任务 ${taskId} 时间已更新`);
  };

  const resetTasks = () => {
    setTasks(prevTasks => 
      prevTasks.map((task, index) => ({
        ...task,
        planned_start: addDays(new Date(), index).toISOString(),
        planned_end: addDays(new Date(), index + 3).toISOString(),
      }))
    );
    message.info('任务时间已重置');
  };

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>甘特图拖拽演示</Title>
        <Text type="secondary">
          这是一个可拖拽的甘特图演示。您可以：
        </Text>
        <ul style={{ marginTop: '8px' }}>
          <li>拖拽任务条来移动整个任务</li>
          <li>拖拽任务条两端来调整任务持续时间</li>
          <li>悬停在任务条上查看详细信息</li>
        </ul>
        
        <Space style={{ marginTop: '16px' }}>
          <Button onClick={resetTasks}>
            重置任务时间
          </Button>
        </Space>
      </div>

      <GanttChart
        tasks={tasks}
        title="可拖拽甘特图演示"
        height={400}
        editable={true}
        onTaskUpdate={handleTaskUpdate}
      />

      <Card style={{ marginTop: '24px' }} title="使用说明">
        <div>
          <Title level={4}>拖拽功能：</Title>
          <ul>
            <li><strong>移动任务：</strong>点击并拖拽任务条的中间部分来移动整个任务</li>
            <li><strong>调整开始时间：</strong>悬停在任务条左边缘，出现蓝色手柄时拖拽</li>
            <li><strong>调整结束时间：</strong>悬停在任务条右边缘，出现蓝色手柄时拖拽</li>
          </ul>
          
          <Title level={4}>状态说明：</Title>
          <ul>
            <li><span style={{ color: '#d9d9d9' }}>灰色</span>：待开始</li>
            <li><span style={{ color: '#1890ff' }}>蓝色</span>：进行中</li>
            <li><span style={{ color: '#52c41a' }}>绿色</span>：已完成</li>
            <li><span style={{ color: '#ff4d4f' }}>红色</span>：已取消</li>
          </ul>
        </div>
      </Card>
    </div>
  );
};

export default GanttDemo;
