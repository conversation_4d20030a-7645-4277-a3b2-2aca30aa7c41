import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Typography, Table, Tag, Space, Button, Modal, Form, Select, Checkbox, Divider, App } from 'antd';
import { EditOutlined, EyeOutlined, SettingOutlined } from '@ant-design/icons';
import {
  ROLES,
  ROLE_MENU_CONFIG,
  PAGE_PERMISSIONS,
  FEATURE_PERMISSIONS,
  getUserMenuItems
} from '@/utils/permissions';
import { useI18n } from '@/hooks/useI18n';
import { getRoleDisplayText, getRoleDescription, getPermissionDisplayText } from '@/utils/textUtils';

const { Title, Text } = Typography;
const { Option } = Select;

interface RoleConfig {
  role: string;
  displayName: string;
  description: string;
  menus: string[];
  features: string[];
}

const RolePermissions: React.FC = () => {
  const navigate = useNavigate();
  const { message } = App.useApp();
  const [selectedRole, setSelectedRole] = useState<string | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const { roles, permissions } = useI18n();

  // 角色显示名称映射（使用国际化文本）
  const roleDisplayNames = {
    [ROLES.ADMIN]: getRoleDisplayText(ROLES.ADMIN),
    [ROLES.PROCESS_ENGINEER]: getRoleDisplayText(ROLES.PROCESS_ENGINEER),
    [ROLES.PLANNER]: getRoleDisplayText(ROLES.PLANNER),
    [ROLES.OPERATOR]: getRoleDisplayText(ROLES.OPERATOR),
    [ROLES.QUALITY_INSPECTOR]: getRoleDisplayText(ROLES.QUALITY_INSPECTOR),
    [ROLES.VIEWER]: getRoleDisplayText(ROLES.VIEWER)
  };

  // 角色描述（使用国际化文本）
  const roleDescriptions = {
    [ROLES.ADMIN]: getRoleDescription(ROLES.ADMIN),
    [ROLES.PROCESS_ENGINEER]: getRoleDescription(ROLES.PROCESS_ENGINEER),
    [ROLES.PLANNER]: getRoleDescription(ROLES.PLANNER),
    [ROLES.OPERATOR]: getRoleDescription(ROLES.OPERATOR),
    [ROLES.QUALITY_INSPECTOR]: getRoleDescription(ROLES.QUALITY_INSPECTOR),
    [ROLES.VIEWER]: getRoleDescription(ROLES.VIEWER)
  };

  // 菜单显示名称映射
  const menuDisplayNames = {
    '/dashboard': '仪表板',
    '/projects': '项目管理',
    '/parts': '零件管理',
    '/machines': '设备管理',
    '/work-orders': '工单管理',
    '/plan-tasks': '生产计划',
    '/execution': '生产执行',
    '/operator-execution': '操作员执行',
    '/quality': '质量管理',
    '/bom': 'BOM管理',
    '/routings': '工艺路径',
    '/users': '用户管理',
    '/role-permissions': '角色权限',
    '/api-test': 'API测试',
    '/database': '数据库管理',
    '/api-debug': 'API调试'
  };

  // 功能显示名称映射（使用国际化文本）
  const getFeatureDisplayName = (featureKey: string): string => {
    return getPermissionDisplayText(featureKey) || featureKey;
  };

  // 获取角色的菜单权限
  const getRoleMenus = (role: string): string[] => {
    if (role === ROLES.ADMIN) {
      return Object.keys(PAGE_PERMISSIONS);
    }
    return ROLE_MENU_CONFIG[role as keyof typeof ROLE_MENU_CONFIG] as string[] || [];
  };

  // 获取角色的功能权限
  const getRoleFeatures = (role: string): string[] => {
    const features: string[] = [];
    Object.entries(FEATURE_PERMISSIONS).forEach(([feature, allowedRoles]) => {
      if (allowedRoles.includes(role as any)) {
        features.push(feature);
      }
    });
    return features;
  };

  // 角色数据
  const roleData: RoleConfig[] = Object.values(ROLES).map(role => ({
    role,
    displayName: roleDisplayNames[role as keyof typeof roleDisplayNames],
    description: roleDescriptions[role as keyof typeof roleDescriptions],
    menus: getRoleMenus(role),
    features: getRoleFeatures(role)
  }));

  const handleViewRole = (role: string) => {
    setSelectedRole(role);
    setIsModalVisible(true);
  };

  const columns = [
    {
      title: '角色',
      dataIndex: 'displayName',
      key: 'displayName',
      width: 150,
      render: (text: string, record: RoleConfig) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.role}
          </Text>
        </div>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '可访问菜单',
      dataIndex: 'menus',
      key: 'menus',
      width: 200,
      render: (menus: string[]) => (
        <div>
          {menus.slice(0, 3).map(menu => (
            <Tag key={menu} style={{ marginBottom: 2, fontSize: '12px' }}>
              {menuDisplayNames[menu as keyof typeof menuDisplayNames] || menu}
            </Tag>
          ))}
          {menus.length > 3 && (
            <Tag color="blue" style={{ fontSize: '12px' }}>
              +{menus.length - 3}个
            </Tag>
          )}
        </div>
      ),
    },
    {
      title: '功能权限',
      dataIndex: 'features',
      key: 'features',
      width: 200,
      render: (features: string[]) => (
        <div>
          {features.slice(0, 3).map(feature => (
            <Tag key={feature} color="green" style={{ marginBottom: 2, fontSize: '12px' }}>
              {getFeatureDisplayName(feature)}
            </Tag>
          ))}
          {features.length > 3 && (
            <Tag color="green" style={{ fontSize: '12px' }}>
              +{features.length - 3}个
            </Tag>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_: any, record: RoleConfig) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewRole(record.role)}
          >
            查看详情
          </Button>
        </Space>
      ),
    },
  ];

  const selectedRoleData = roleData.find(r => r.role === selectedRole);

  return (
    <div>
      <div className="page-header">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title level={2} style={{ margin: 0 }}>
              角色权限管理
            </Title>
            <Typography.Paragraph style={{ margin: 0, color: '#666' }}>
              查看和管理系统角色的菜单访问权限和功能权限
            </Typography.Paragraph>
          </div>
          <Button
            type="primary"
            icon={<SettingOutlined />}
            onClick={() => navigate('/permission-config')}
          >
            权限配置
          </Button>
        </div>
      </div>

      {/* 权限说明卡片 */}
      <Card style={{ marginBottom: 24 }} title="权限设计说明">
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: 16 }}>
          <div>
            <Text strong style={{ color: '#1890ff' }}>计划与执行分离</Text>
            <ul style={{ marginTop: 8, paddingLeft: 20 }}>
              <li>计划员负责任务调度和时间安排</li>
              <li>操作员决定何时开始执行任务</li>
              <li>操作员只能操作分配给自己技能组的任务</li>
            </ul>
          </div>
          <div>
            <Text strong style={{ color: '#52c41a' }}>角色权限控制</Text>
            <ul style={{ marginTop: 8, paddingLeft: 20 }}>
              <li>操作员无法访问设备管理页面</li>
              <li>操作员无法修改设备状态</li>
              <li>只有管理员可以管理用户和角色</li>
            </ul>
          </div>
          <div>
            <Text strong style={{ color: '#faad14' }}>安全设计</Text>
            <ul style={{ marginTop: 8, paddingLeft: 20 }}>
              <li>基于角色的访问控制 (RBAC)</li>
              <li>菜单级别和功能级别双重权限控制</li>
              <li>最小权限原则</li>
            </ul>
          </div>
        </div>
      </Card>

      {/* 角色权限表格 */}
      <Card title="系统角色权限概览">
        <Table
          columns={columns}
          dataSource={roleData}
          rowKey="role"
          pagination={false}
          size="middle"
        />
      </Card>

      {/* 角色详情模态框 */}
      <Modal
        title={`角色详情 - ${selectedRoleData?.displayName}`}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          setSelectedRole(null);
        }}
        footer={[
          <Button key="close" onClick={() => setIsModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedRoleData && (
          <div>
            <div style={{ marginBottom: 20 }}>
              <Text strong>角色描述：</Text>
              <p style={{ marginTop: 8 }}>{selectedRoleData.description}</p>
            </div>

            <Divider />

            <div style={{ marginBottom: 20 }}>
              <Text strong>可访问菜单 ({selectedRoleData.menus.length}个)：</Text>
              <div style={{ marginTop: 8 }}>
                {selectedRoleData.menus.map(menu => (
                  <Tag key={menu} style={{ marginBottom: 4 }}>
                    {menuDisplayNames[menu as keyof typeof menuDisplayNames] || menu}
                  </Tag>
                ))}
              </div>
            </div>

            <Divider />

            <div>
              <Text strong>功能权限 ({selectedRoleData.features.length}个)：</Text>
              <div style={{ marginTop: 8 }}>
                {selectedRoleData.features.map(feature => (
                  <Tag key={feature} color="green" style={{ marginBottom: 4 }}>
                    {getFeatureDisplayName(feature)}
                  </Tag>
                ))}
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default RolePermissions;
