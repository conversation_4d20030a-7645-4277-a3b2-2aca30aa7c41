import React, { useState } from 'react';
import {
  Card,
  Progress,
  Row,
  Col,
  Statistic,
  Tag,
  Typography,
  Table,
  Button,
  Modal,
  Steps,
  Descriptions,
  Space,
  Divider,
} from 'antd';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  ToolOutlined,
  PlayCircleOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Step } = Steps;

// 模拟数据
const mockProjectData = {
  overall_progress: {
    total_parts: 5,
    completed_parts: 1,
    in_progress_parts: 3,
    not_started_parts: 1,
    overall_completion_percentage: 35.0,
  },
  parts_status: [
    {
      part_id: 1,
      part_number: "001",
      part_name: "1#",
      bom_quantity: 2,
      total_work_orders: 4,
      completed_work_orders: 2,
      in_progress_work_orders: 1,
      pending_work_orders: 1,
      cancelled_work_orders: 0,
      completion_percentage: 75.0,
      status: "in_progress",
    },
    {
      part_id: 2,
      part_number: "002",
      part_name: "2#",
      bom_quantity: 3,
      total_work_orders: 6,
      completed_work_orders: 6,
      in_progress_work_orders: 0,
      pending_work_orders: 0,
      cancelled_work_orders: 0,
      completion_percentage: 100.0,
      status: "completed",
    },
    {
      part_id: 3,
      part_number: "003",
      part_name: "3#",
      bom_quantity: 1,
      total_work_orders: 2,
      completed_work_orders: 0,
      in_progress_work_orders: 1,
      pending_work_orders: 1,
      cancelled_work_orders: 0,
      completion_percentage: 25.0,
      status: "in_progress",
    },
  ],
  work_orders_summary: {
    total_work_orders: 12,
    completed_orders: 8,
    in_progress_orders: 2,
    planned_orders: 1,
    pending_orders: 1,
    overdue_orders: 0,
  },
};

const mockRoutingData = {
  1: {
    part_routing: {
      part_id: 1,
      part_number: "001",
      part_name: "1#",
      version: "v1.0",
      routing_steps: [
        {
          id: 1,
          step_number: 1,
          process_name: "下料",
          work_instructions: "按图纸要求下料",
          standard_hours: 2.0,
        },
        {
          id: 2,
          step_number: 2,
          process_name: "粗加工",
          work_instructions: "粗加工外形",
          standard_hours: 4.0,
        },
        {
          id: 3,
          step_number: 3,
          process_name: "精加工",
          work_instructions: "精加工到位",
          standard_hours: 3.0,
        },
        {
          id: 4,
          step_number: 4,
          process_name: "检验",
          work_instructions: "质量检验",
          standard_hours: 1.0,
        },
      ],
    },
  },
  2: {
    part_routing: {
      part_id: 2,
      part_number: "002",
      part_name: "2#",
      version: "v1.0",
      routing_steps: [
        {
          id: 5,
          step_number: 1,
          process_name: "下料",
          work_instructions: "按图纸要求下料",
          standard_hours: 1.5,
        },
        {
          id: 6,
          step_number: 2,
          process_name: "加工",
          work_instructions: "机械加工",
          standard_hours: 3.0,
        },
        {
          id: 7,
          step_number: 3,
          process_name: "检验",
          work_instructions: "质量检验",
          standard_hours: 0.5,
        },
      ],
    },
  },
};

const mockTaskData = {
  1: {
    plan_tasks: [
      { routing_step_id: 1, status: 'completed' },
      { routing_step_id: 2, status: 'completed' },
      { routing_step_id: 3, status: 'in_progress' },
      { routing_step_id: 4, status: 'planned' },
    ],
  },
  2: {
    plan_tasks: [
      { routing_step_id: 5, status: 'completed' },
      { routing_step_id: 6, status: 'completed' },
      { routing_step_id: 7, status: 'completed' },
    ],
  },
};

interface PartDetailModalProps {
  visible: boolean;
  onClose: () => void;
  partId: number | null;
  partInfo: any;
}

const PartDetailModal: React.FC<PartDetailModalProps> = ({ visible, onClose, partId, partInfo }) => {
  const partRouting = partId ? mockRoutingData[partId as keyof typeof mockRoutingData] : null;
  const planTasks = partId ? mockTaskData[partId as keyof typeof mockTaskData] : null;

  const getProcessStepStatus = (stepId: number) => {
    if (!planTasks?.plan_tasks) return 'wait';
    
    const stepTasks = planTasks.plan_tasks.filter((task: any) => task.routing_step_id === stepId);
    if (stepTasks.length === 0) return 'wait';
    
    const completedTasks = stepTasks.filter((task: any) => task.status === 'completed');
    const inProgressTasks = stepTasks.filter((task: any) => task.status === 'in_progress');
    
    if (completedTasks.length === stepTasks.length) return 'finish';
    if (inProgressTasks.length > 0) return 'process';
    return 'wait';
  };

  const getProcessStepIcon = (status: string) => {
    switch (status) {
      case 'finish':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'process':
        return <PlayCircleOutlined style={{ color: '#1890ff' }} />;
      case 'wait':
        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  return (
    <Modal
      title={
        <Space>
          <ToolOutlined />
          零件工艺进度详情
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>
      ]}
      width={800}
      style={{ top: 20 }}
    >
      {partInfo && (
        <div>
          {/* 零件基本信息 */}
          <Card size="small" style={{ marginBottom: 16 }}>
            <Descriptions title="零件信息" column={2} size="small">
              <Descriptions.Item label="零件编号">{partInfo.part_number}</Descriptions.Item>
              <Descriptions.Item label="零件名称">{partInfo.part_name || '-'}</Descriptions.Item>
              <Descriptions.Item label="BOM数量">{partInfo.bom_quantity}</Descriptions.Item>
              <Descriptions.Item label="完成度">
                <Progress 
                  percent={Math.round(partInfo.completion_percentage)} 
                  size="small" 
                  status={partInfo.completion_percentage === 100 ? 'success' : 'active'}
                />
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* 工单状态概览 */}
          <Card size="small" style={{ marginBottom: 16 }}>
            <Title level={5}>工单状态</Title>
            <Row gutter={16}>
              <Col span={6}>
                <Statistic title="总工单" value={partInfo.total_work_orders} suffix="个" />
              </Col>
              <Col span={6}>
                <Statistic 
                  title="已完成" 
                  value={partInfo.completed_work_orders} 
                  suffix="个"
                  valueStyle={{ color: '#3f8600' }}
                />
              </Col>
              <Col span={6}>
                <Statistic 
                  title="进行中" 
                  value={partInfo.in_progress_work_orders} 
                  suffix="个"
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={6}>
                <Statistic 
                  title="待处理" 
                  value={partInfo.pending_work_orders} 
                  suffix="个"
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
            </Row>
          </Card>

          {/* 工艺流程进度 */}
          <Card size="small">
            <Title level={5}>工艺流程进度</Title>
            {partRouting?.part_routing?.routing_steps && partRouting.part_routing.routing_steps.length > 0 ? (
              <Steps
                direction="vertical"
                size="small"
                current={-1}
                style={{ marginTop: 16 }}
              >
                {partRouting.part_routing.routing_steps.map((step: any, index: number) => {
                  const status = getProcessStepStatus(step.id);
                  return (
                    <Step
                      key={step.id}
                      title={
                        <Space>
                          <Text strong>工序 {step.step_number}</Text>
                          <Text>{step.process_name}</Text>
                        </Space>
                      }
                      description={
                        <div>
                          <Text type="secondary">{step.work_instructions || '无工作指导'}</Text>
                          {step.standard_hours && (
                            <div>
                              <Text type="secondary">标准工时: {step.standard_hours}h</Text>
                            </div>
                          )}
                        </div>
                      }
                      status={status}
                      icon={getProcessStepIcon(status)}
                    />
                  );
                })}
              </Steps>
            ) : (
              <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
                暂无工艺信息
              </div>
            )}
          </Card>
        </div>
      )}
    </Modal>
  );
};

const PartProgressDemo: React.FC = () => {
  const [selectedPart, setSelectedPart] = useState<{ id: number; info: any } | null>(null);

  const handlePartClick = (record: any) => {
    setSelectedPart({
      id: record.part_id,
      info: record
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'in_progress':
        return <ClockCircleOutlined style={{ color: '#1890ff' }} />;
      default:
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in_progress':
        return 'processing';
      default:
        return 'warning';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'in_progress':
        return '进行中';
      default:
        return '未开始';
    }
  };

  const partsColumns = [
    {
      title: '零件编号',
      dataIndex: 'part_number',
      key: 'part_number',
      render: (text: string, record: any) => (
        <Button 
          type="link" 
          onClick={() => handlePartClick(record)}
          style={{ padding: 0, height: 'auto' }}
        >
          <Text strong style={{ color: '#1890ff' }}>{text}</Text>
        </Button>
      ),
    },
    {
      title: '零件名称',
      dataIndex: 'part_name',
      key: 'part_name',
      render: (text: string) => text || '-',
    },
    {
      title: 'BOM数量',
      dataIndex: 'bom_quantity',
      key: 'bom_quantity',
    },
    {
      title: '工单状态',
      key: 'work_orders',
      render: (_: any, record: any) => (
        <div>
          <div>
            <Text strong>总计: </Text>
            <Text>{record.total_work_orders}</Text>
          </div>
          {record.completed_work_orders > 0 && (
            <Tag color="success">已完成: {record.completed_work_orders}</Tag>
          )}
          {record.in_progress_work_orders > 0 && (
            <Tag color="processing">进行中: {record.in_progress_work_orders}</Tag>
          )}
          {record.pending_work_orders > 0 && (
            <Tag color="warning">待处理: {record.pending_work_orders}</Tag>
          )}
        </div>
      ),
    },
    {
      title: '完成度',
      key: 'completion',
      render: (_: any, record: any) => (
        <div>
          <Progress
            percent={Math.round(record.completion_percentage)}
            size="small"
            status={record.completion_percentage === 100 ? 'success' : 'active'}
          />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {Math.round(record.completion_percentage)}%
          </Text>
        </div>
      ),
    },
    {
      title: '工艺状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_: any, record: any) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handlePartClick(record)}
            size="small"
          >
            详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div className="page-header">
        <Title level={2} style={{ margin: 0 }}>
          零件进度追踪演示
        </Title>
        <Text type="secondary">
          点击零件编号或详情按钮查看工艺进度详情
        </Text>
      </div>

      {/* 整体进度概览 */}
      <Card
        title="项目进度概览"
        style={{ marginBottom: 16 }}
      >
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Statistic
              title="整体完成度"
              value={Math.round(mockProjectData.overall_progress.overall_completion_percentage)}
              suffix="%"
              valueStyle={{ color: '#1890ff' }}
            />
            <Progress
              percent={Math.round(mockProjectData.overall_progress.overall_completion_percentage)}
              status="active"
              style={{ marginTop: 8 }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="总零件数"
              value={mockProjectData.overall_progress.total_parts}
              suffix="个"
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="已完成零件"
              value={mockProjectData.overall_progress.completed_parts}
              suffix="个"
              valueStyle={{ color: '#3f8600' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="进行中零件"
              value={mockProjectData.overall_progress.in_progress_parts}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
        </Row>
      </Card>

      {/* 零件详细状态 */}
      <Card 
        title={
          <Space>
            <ToolOutlined />
            零件完成状态详情
            <Text type="secondary" style={{ fontSize: '14px', fontWeight: 'normal' }}>
              (点击零件编号或详情按钮查看工艺进度)
            </Text>
          </Space>
        }
      >
        <Table
          columns={partsColumns}
          dataSource={mockProjectData.parts_status}
          rowKey="part_id"
          pagination={false}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* 零件详情模态框 */}
      <PartDetailModal
        visible={!!selectedPart}
        onClose={() => setSelectedPart(null)}
        partId={selectedPart?.id || null}
        partInfo={selectedPart?.info}
      />
    </div>
  );
};

export default PartProgressDemo;
