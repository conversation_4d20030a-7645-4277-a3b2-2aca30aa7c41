import React from 'react';
import { Card, Typography, Tag, Space, Divider } from 'antd';
import { useAuthStore } from '@/store/auth';
import { usePermissions } from '@/hooks/usePermissions';

const { Title, Text } = Typography;

const PermissionDebug: React.FC = () => {
  const { user } = useAuthStore();
  const { 
    permissions, 
    grantedPermissions, 
    loading, 
    error, 
    hasPageAccess, 
    hasOperationPermission,
    getAccessiblePages 
  } = usePermissions();

  if (!user) {
    return <div>用户未登录</div>;
  }

  const accessiblePages = getAccessiblePages();

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>权限调试页面</Title>
      
      <Card title="用户信息" style={{ marginBottom: '16px' }}>
        <Space direction="vertical">
          <Text><strong>用户名:</strong> {user.username}</Text>
          <Text><strong>用户ID:</strong> {user.id}</Text>
          <div>
            <Text><strong>角色:</strong> </Text>
            {user.roles?.map(role => (
              <Tag key={role} color="blue">{role}</Tag>
            ))}
          </div>
          <div>
            <Text><strong>技能:</strong> </Text>
            {user.skills?.map(skill => (
              <Tag key={skill} color="green">{skill}</Tag>
            ))}
          </div>
        </Space>
      </Card>

      <Card title="权限加载状态" style={{ marginBottom: '16px' }}>
        <Space direction="vertical">
          <Text><strong>加载中:</strong> {loading ? '是' : '否'}</Text>
          <Text><strong>错误:</strong> {error || '无'}</Text>
        </Space>
      </Card>

      <Card title="原始权限数据" style={{ marginBottom: '16px' }}>
        <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
          {JSON.stringify(permissions, null, 2)}
        </pre>
      </Card>

      <Card title="处理后的权限列表" style={{ marginBottom: '16px' }}>
        <Space wrap>
          {grantedPermissions.map(permission => (
            <Tag key={permission} color="green">{permission}</Tag>
          ))}
        </Space>
      </Card>

      <Card title="页面访问权限测试" style={{ marginBottom: '16px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text><strong>仪表盘 (/dashboard):</strong> </Text>
            <Tag color={hasPageAccess('/dashboard') ? 'green' : 'red'}>
              {hasPageAccess('/dashboard') ? '有权限' : '无权限'}
            </Tag>
          </div>
          <div>
            <Text><strong>零件管理 (/parts):</strong> </Text>
            <Tag color={hasPageAccess('/parts') ? 'green' : 'red'}>
              {hasPageAccess('/parts') ? '有权限' : '无权限'}
            </Tag>
          </div>
          <div>
            <Text><strong>BOM管理 (/bom):</strong> </Text>
            <Tag color={hasPageAccess('/bom') ? 'green' : 'red'}>
              {hasPageAccess('/bom') ? '有权限' : '无权限'}
            </Tag>
          </div>
          <div>
            <Text><strong>项目管理 (/projects):</strong> </Text>
            <Tag color={hasPageAccess('/projects') ? 'green' : 'red'}>
              {hasPageAccess('/projects') ? '有权限' : '无权限'}
            </Tag>
          </div>
        </Space>
      </Card>

      <Card title="操作权限测试" style={{ marginBottom: '16px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text><strong>开始任务 (START_TASK):</strong> </Text>
            <Tag color={hasOperationPermission('START_TASK') ? 'green' : 'red'}>
              {hasOperationPermission('START_TASK') ? '有权限' : '无权限'}
            </Tag>
          </div>
          <div>
            <Text><strong>完成任务 (COMPLETE_TASK):</strong> </Text>
            <Tag color={hasOperationPermission('COMPLETE_TASK') ? 'green' : 'red'}>
              {hasOperationPermission('COMPLETE_TASK') ? '有权限' : '无权限'}
            </Tag>
          </div>
        </Space>
      </Card>

      <Card title="可访问页面列表" style={{ marginBottom: '16px' }}>
        <Space wrap>
          {accessiblePages.map(page => (
            <Tag key={page} color="blue">{page}</Tag>
          ))}
        </Space>
        {accessiblePages.length === 0 && (
          <Text type="danger">没有可访问的页面</Text>
        )}
      </Card>
    </div>
  );
};

export default PermissionDebug;
