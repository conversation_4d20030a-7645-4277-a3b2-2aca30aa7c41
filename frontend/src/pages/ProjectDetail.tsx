import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Button,
  Space,
  Breadcrumb,
  Row,
  Col,
  Spin,
  Alert,
  Modal,
  Form,
  Input,
  Statistic,
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  ProjectOutlined,
  ToolOutlined,
  UserOutlined,
  CalendarOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import { useMessage } from '@/hooks/useMessage';
import BOMManager from '@/components/BOMManager';
import ProjectProgress from '@/components/ProjectProgress';
import type { Project, CreateProjectRequest } from '@/types/api';
import dayjs from '@/utils/dayjs';

const { Title, Text } = Typography;

const ProjectDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { success, error } = useMessage();
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const projectId = parseInt(id || '0', 10);

  // Fetch project details
  const { data: project, isLoading, error: projectError } = useQuery(
    ['project', projectId],
    () => apiClient.getProjectById(projectId),
    {
      enabled: !!projectId,
      onError: () => {
        error('获取项目详情失败');
      },
    }
  );

  // Fetch BOM items for statistics
  const { data: bomItems = [] } = useQuery(
    ['project-bom', projectId],
    () => apiClient.getProjectBom(projectId),
    {
      enabled: !!projectId,
    }
  );

  // Update project mutation
  const updateProjectMutation = useMutation(
    (data: Partial<CreateProjectRequest>) => apiClient.updateProject(projectId, data),
    {
      onSuccess: () => {
        success('项目更新成功');
        queryClient.invalidateQueries(['project', projectId]);
        setIsEditModalVisible(false);
        form.resetFields();
      },
      onError: () => {
        error('项目更新失败');
      },
    }
  );

  const handleBack = () => {
    navigate('/projects');
  };

  const handleEdit = () => {
    if (project) {
      form.setFieldsValue({
        project_name: project.project_name,
        customer_name: project.customer_name,
      });
      setIsEditModalVisible(true);
    }
  };

  const handleEditSubmit = async () => {
    try {
      const values = await form.validateFields();
      updateProjectMutation.mutate(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  if (isLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (projectError || !project) {
    return (
      <div style={{ padding: '20px' }}>
        <Alert
          message="项目不存在"
          description="请检查项目ID是否正确，或者项目可能已被删除。"
          type="error"
          showIcon
          action={
            <Button size="small" onClick={handleBack}>
              返回项目列表
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div>
      {/* 面包屑导航 */}
      <Breadcrumb style={{ marginBottom: 16 }}>
        <Breadcrumb.Item>
          <Button type="link" onClick={handleBack} style={{ padding: 0 }}>
            项目管理
          </Button>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{project.project_name}</Breadcrumb.Item>
      </Breadcrumb>

      {/* 页面头部 */}
      <div className="page-header">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={handleBack}
              style={{ marginRight: 16 }}
            >
              返回
            </Button>
            <Title level={2} style={{ margin: 0 }}>
              项目详情
            </Title>
          </div>
          <Space>
            <Button
              type="default"
              icon={<EditOutlined />}
              onClick={handleEdit}
            >
              编辑项目
            </Button>
          </Space>
        </div>
      </div>

      {/* 项目基本信息 */}
      <Card
        title={
          <Space>
            <ProjectOutlined />
            项目信息
          </Space>
        }
        style={{ marginBottom: 16 }}
      >
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <div>
              <Text strong>项目名称：</Text>
              <div style={{ marginTop: 4 }}>
                <Text style={{ fontSize: 16 }}>{project.project_name}</Text>
              </div>
            </div>
          </Col>
          <Col span={6}>
            <div>
              <Text strong>客户名称：</Text>
              <div style={{ marginTop: 4 }}>
                <Text style={{ fontSize: 16 }}>
                  <UserOutlined style={{ marginRight: 4 }} />
                  {project.customer_name || '-'}
                </Text>
              </div>
            </div>
          </Col>
          <Col span={6}>
            <div>
              <Text strong>创建时间：</Text>
              <div style={{ marginTop: 4 }}>
                <Text style={{ fontSize: 16 }}>
                  <CalendarOutlined style={{ marginRight: 4 }} />
                  {dayjs(project.created_at).format('YYYY-MM-DD')}
                </Text>
              </div>
            </div>
          </Col>
          <Col span={6}>
            <Statistic
              title="BOM零件数量"
              value={bomItems.length}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
        </Row>
      </Card>

      {/* 项目进度状态 */}
      <ProjectProgress projectId={projectId} />

      {/* BOM管理区域 */}
      <Card
        title={
          <Space>
            <ToolOutlined />
            项目BOM
          </Space>
        }
        style={{ marginTop: 16 }}
      >
        <BOMManager projectId={projectId} />
      </Card>

      {/* 编辑项目模态框 */}
      <Modal
        title="编辑项目"
        open={isEditModalVisible}
        onOk={handleEditSubmit}
        onCancel={() => {
          setIsEditModalVisible(false);
          form.resetFields();
        }}
        confirmLoading={updateProjectMutation.isLoading}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="project_name"
            label="项目名称"
            rules={[
              { required: true, message: '请输入项目名称' },
              { max: 255, message: '项目名称不能超过255个字符' },
            ]}
          >
            <Input placeholder="请输入项目名称" />
          </Form.Item>

          <Form.Item
            name="customer_name"
            label="客户名称"
            rules={[
              { max: 255, message: '客户名称不能超过255个字符' },
            ]}
          >
            <Input placeholder="请输入客户名称（可选）" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProjectDetail;
