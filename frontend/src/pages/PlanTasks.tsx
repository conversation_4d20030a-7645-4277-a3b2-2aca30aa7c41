import React, { useState, useEffect, useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  Table,
  Typography,
  Card,
  Tag,
  Button,
  Space,
  Tabs,
  Modal,
  Form,
  DatePicker,
  Select,
  Alert,
  Tooltip,
  InputNumber,
  Divider,
  Switch,
  App,
  // Divider
} from 'antd';

const { Option } = Select;
import {
  CalendarOutlined,
  BarChartOutlined,
  TableOutlined,
  PlusOutlined,
  ReloadOutlined,
  SettingOutlined,
  BulbOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  ProjectOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import { getSkillGroupDisplayName } from '@/utils/roleUtils';
import type { PlanTaskWithDetails } from '@/types/api';
import GanttChart from '@/components/GanttChart';
import WorkOrderSelector from '@/components/WorkOrderSelector';
import TimeEstimateHelper from '@/components/TimeEstimateHelper';
import { usePermissions } from '@/hooks/usePermissions';
import { useAuthStore } from '@/store/auth';
import dayjs from '@/utils/dayjs';

const { Title, Text } = Typography;

const PlanTasks: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState('table');
  const [isRescheduleModalVisible, setIsRescheduleModalVisible] = useState(false);
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [selectedTask, setSelectedTask] = useState<PlanTaskWithDetails | null>(null);
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [createForm] = Form.useForm();
  const queryClient = useQueryClient();

  // 从URL参数获取筛选条件
  const workOrderIdFromUrl = searchParams.get('work_order_id');
  const highlightTaskId = searchParams.get('highlight_task');

  // Form实例准备状态
  const [isFormReady, setIsFormReady] = useState(false);
  const [isCreateFormReady, setIsCreateFormReady] = useState(false);

  // 计划分配模式状态
  const [assignmentMode, setAssignmentMode] = useState<'skill_group' | 'machine'>('skill_group');

  // 获取当前用户信息和权限
  const { user } = useAuthStore();
  const { hasOperationPermission } = usePermissions();
  const userRoles = user?.roles || [];

  // 权限检查
  const canCreateTask = hasOperationPermission('CREATE_PLAN_TASK');
  const canEditTask = hasOperationPermission('EDIT_PLAN_TASK');
  const isPlanner = hasOperationPermission('EDIT_PLAN_TASK') || user?.roles?.includes('admin');

  // 获取计划分配模式配置
  const { data: planAssignmentConfig } = useQuery(
    'plan-assignment-mode',
    () => apiClient.getPlanAssignmentMode(),
    {
      enabled: isPlanner,
      onSuccess: (data) => {
        setAssignmentMode(data.mode);
      }
    }
  );

  // 设置计划分配模式
  const setPlanAssignmentModeMutation = useMutation(
    (mode: 'skill_group' | 'machine') => apiClient.setPlanAssignmentMode({ mode }),
    {
      onSuccess: () => {
        message.success('计划分配模式更新成功');
        queryClient.invalidateQueries('plan-assignment-mode');
      },
      onError: (error: any) => {
        message.error(`更新失败: ${error.response?.data?.error || error.message}`);
      },
    }
  );





  // 构建查询参数
  const planTasksQueryParams = useMemo(() => {
    const params: any = {};
    if (workOrderIdFromUrl) {
      params.work_order_id = parseInt(workOrderIdFromUrl);
    }
    return params;
  }, [workOrderIdFromUrl]);

  const { data: planTasks, isLoading } = useQuery(
    ['plan-tasks', planTasksQueryParams],
    () => apiClient.getPlanTasks(planTasksQueryParams)
  );

  const { data: workOrders } = useQuery(
    'work-orders',
    () => apiClient.getWorkOrders()
  );

  const { data: skillGroups } = useQuery(
    'skill-groups',
    () => apiClient.getSkillGroups()
  );

  const { data: projects } = useQuery(
    'projects',
    () => apiClient.getProjects()
  );

  const { data: routings, refetch: refetchRoutings } = useQuery(
    'routings',
    () => apiClient.getRoutings(),
    {
      refetchOnWindowFocus: true,
      staleTime: 0, // 数据立即过期，确保总是获取最新数据
    }
  );

  // 计划分配模式配置已在上面定义

  // 获取设备列表
  const { data: machines } = useQuery(
    'machines',
    () => apiClient.getMachines()
  );

  // 状态管理：选择的工单和对应的零件ID
  const [selectedWorkOrderId, setSelectedWorkOrderId] = useState<number | null>(null);
  const [selectedPartId, setSelectedPartId] = useState<number | null>(null);
  const [selectedSkillGroupId, setSelectedSkillGroupId] = useState<number | null>(null);

  // 时间估算相关状态
  const [selectedRoutingStep, setSelectedRoutingStep] = useState<any>(null);
  const [timeEstimateVisible, setTimeEstimateVisible] = useState(false);
  const [customDuration, setCustomDuration] = useState<number | null>(null);
  const [timeDeviationWarning, setTimeDeviationWarning] = useState<string>('');

  // 批量创建相关状态
  const [isBatchCreateModalVisible, setIsBatchCreateModalVisible] = useState(false);
  const [batchCreateForm] = Form.useForm();
  const [batchCreationType, setBatchCreationType] = useState<'part' | 'project' | 'work_order'>('project');
  const [selectedTargetId, setSelectedTargetId] = useState<number | null>(null);

  // 获取零件数据用于批量创建
  const { data: parts } = useQuery(
    'parts',
    () => apiClient.getParts({ limit: 1000 } as any),
    {
      enabled: batchCreationType === 'part' || isBatchCreateModalVisible
    }
  );

  // 处理URL参数，高亮显示任务
  useEffect(() => {
    if (highlightTaskId && planTasks) {
      // 如果有高亮任务ID，切换到表格视图
      setActiveTab('table');

      // 可以在这里添加滚动到指定任务的逻辑
      setTimeout(() => {
        const taskElement = document.querySelector(`[data-task-id="${highlightTaskId}"]`);
        if (taskElement) {
          taskElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);
    }
  }, [highlightTaskId, planTasks]);

  // 监控Form准备状态
  useEffect(() => {
    setIsFormReady(isRescheduleModalVisible);
  }, [isRescheduleModalVisible]);

  useEffect(() => {
    setIsCreateFormReady(isCreateModalVisible);
  }, [isCreateModalVisible]);

  // 根据选择的工单获取对应的零件ID
  useEffect(() => {
    if (selectedWorkOrderId && workOrders) {
      const selectedWorkOrder = workOrders.find(wo => wo.id === selectedWorkOrderId);
      if (selectedWorkOrder) {
        setSelectedPartId(selectedWorkOrder.part_id || null);
      }
    } else {
      setSelectedPartId(null);
    }
  }, [selectedWorkOrderId, workOrders]);

  // 根据选择的零件过滤工艺步骤
  const filteredRoutings = useMemo(() => {
    if (!selectedPartId || !routings) {
      return [];
    }
    return routings.filter(routing => routing.part_id === selectedPartId);
  }, [selectedPartId, routings]);

  // 根据选择的技能组过滤设备
  const filteredMachines = useMemo(() => {
    if (!selectedSkillGroupId || !machines || planAssignmentConfig?.mode !== 'machine') {
      return [];
    }
    return machines.filter(machine => machine.skill_group_id === selectedSkillGroupId);
  }, [selectedSkillGroupId, machines, planAssignmentConfig?.mode]);

  // 智能时间计算函数
  const calculateSuggestedTimes = (routingStep: any, startTime?: dayjs.Dayjs) => {
    if (!routingStep || !routingStep.standard_hours) {
      return null;
    }

    const baseStartTime = startTime || dayjs().add(1, 'hour').startOf('hour');
    const durationHours = routingStep.standard_hours;
    const suggestedEndTime = baseStartTime.add(durationHours, 'hour');

    return {
      suggested_start: baseStartTime,
      suggested_end: suggestedEndTime,
      duration_hours: durationHours,
      routing_step: routingStep
    };
  };

  // 应用建议时间到表单
  const applySuggestedTimes = (suggestion: any) => {
    if (!suggestion || !isCreateModalVisible || !isCreateFormReady) return;

    createForm.setFieldsValue({
      planned_start: suggestion.suggested_start,
      planned_end: suggestion.suggested_end,
    });
    setTimeEstimateVisible(false);
    setTimeDeviationWarning('');
  };

  // 检查时间偏差
  const checkTimeDeviation = (startTime: dayjs.Dayjs, endTime: dayjs.Dayjs, standardHours?: number) => {
    if (!standardHours || !startTime || !endTime) {
      setTimeDeviationWarning('');
      return;
    }

    const actualHours = endTime.diff(startTime, 'hour', true);
    const deviationPercent = Math.abs((actualHours - standardHours) / standardHours) * 100;

    if (deviationPercent > 50) {
      setTimeDeviationWarning(`⚠️ 计划时间与标准工时偏差较大：实际${actualHours.toFixed(1)}小时 vs 标准${standardHours}小时 (偏差${deviationPercent.toFixed(0)}%)`);
    } else if (deviationPercent > 20) {
      setTimeDeviationWarning(`⚡ 计划时间与标准工时有偏差：实际${actualHours.toFixed(1)}小时 vs 标准${standardHours}小时 (偏差${deviationPercent.toFixed(0)}%)`);
    } else {
      setTimeDeviationWarning('');
    }
  };

  const updateTaskMutation = useMutation(
    ({ id, data }: { id: number; data: any }) => apiClient.updatePlanTask(id, data),
    {
      onSuccess: () => {
        message.success('任务更新成功');
        queryClient.invalidateQueries('plan-tasks');
        setIsRescheduleModalVisible(false);
        setSelectedTask(null);
        form.resetFields();
      },
      onError: () => {
        message.error('任务更新失败');
      },
    }
  );

  const createTaskMutation = useMutation(
    (data: any) => apiClient.createPlanTask(data),
    {
      onSuccess: () => {
        message.success('计划任务创建成功');
        queryClient.invalidateQueries('plan-tasks');
        setIsCreateModalVisible(false);
        createForm.resetFields();
        setSelectedWorkOrderId(null);
        setSelectedPartId(null);
        setSelectedSkillGroupId(null);
      },
      onError: () => {
        message.error('计划任务创建失败');
      },
    }
  );

  // 批量创建计划任务
  const batchCreateTasksMutation = useMutation(
    (data: any) => apiClient.batchCreatePlanTasks(data),
    {
      onSuccess: (result) => {
        message.success(`成功创建 ${result.count} 个计划任务`);
        queryClient.invalidateQueries('plan-tasks');
        setIsBatchCreateModalVisible(false);
        batchCreateForm.resetFields();
        setSelectedTargetId(null);
        setBatchCreationType('project');
      },
      onError: (error: any) => {
        message.error(`批量创建失败: ${error.response?.data?.message || error.message}`);
      },
    }
  );



  const handleReschedule = (task: PlanTaskWithDetails) => {
    setSelectedTask(task);
    setIsRescheduleModalVisible(true);
  };

  // 当重新调度Modal打开且选中任务时，设置表单值
  useEffect(() => {
    if (isRescheduleModalVisible && selectedTask) {
      const startTime = selectedTask.planned_start ? dayjs(selectedTask.planned_start) : null;
      const standardHours = selectedTask.standard_hours || 1;
      const endTime = startTime ? startTime.add(standardHours, 'hour') : null;

      form.setFieldsValue({
        skill_group_id: selectedTask.skill_group_id,
        machine_id: selectedTask.machine_id,
        planned_start: startTime,
        planned_end: endTime, // 根据标准工时计算的结束时间
      });
    }
  }, [isRescheduleModalVisible, selectedTask, form]);

  // 移除handleStartTask - 任务开始应该由操作员在执行页面进行

  const handleRescheduleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (selectedTask) {
        const startTime = values.planned_start;

        if (!startTime) {
          message.error('请选择计划开始时间');
          return;
        }

        // 根据标准工时自动计算结束时间
        const standardHours = selectedTask.standard_hours || 1; // 默认1小时
        const endTime = startTime.add(standardHours, 'hour');

        // 验证计算出的时间是否合理
        const diffMinutes = endTime.diff(startTime, 'minute');
        if (diffMinutes < 30) {
          message.warning(`标准工时过短（${standardHours}小时），建议检查工艺设置`);
        }

        updateTaskMutation.mutate({
          id: selectedTask.id,
          data: {
            skill_group_id: values.skill_group_id,
            machine_id: values.machine_id,
            planned_start: startTime.toISOString(),
            planned_end: endTime.toISOString(),
          }
        });
      }
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handleCreateSubmit = async () => {
    try {
      const values = await createForm.validateFields();

      // 验证时间间隔至少30分钟
      const startTime = values.planned_start;
      const endTime = values.planned_end;
      if (startTime && endTime) {
        const diffMinutes = endTime.diff(startTime, 'minute');
        if (diffMinutes < 30) {
          message.error('计划结束时间必须比开始时间至少晚30分钟');
          return;
        }

        // 检查时间偏差并给出警告
        if (selectedRoutingStep?.standard_hours) {
          const actualHours = endTime.diff(startTime, 'hour', true);
          const standardHours = selectedRoutingStep.standard_hours;
          const deviationPercent = Math.abs((actualHours - standardHours) / standardHours) * 100;

          if (deviationPercent > 50) {
            const confirmed = await new Promise((resolve) => {
              Modal.confirm({
                title: '时间偏差较大',
                content: (
                  <div>
                    <p>计划时间与标准工时偏差较大：</p>
                    <p>• 计划时间：<strong>{actualHours.toFixed(1)}小时</strong></p>
                    <p>• 标准工时：<strong>{standardHours}小时</strong></p>
                    <p>• 偏差：<strong style={{ color: '#ff4d4f' }}>{deviationPercent.toFixed(0)}%</strong></p>
                    <p style={{ marginTop: 12, color: '#666' }}>是否确认创建此计划任务？</p>
                  </div>
                ),
                icon: <WarningOutlined style={{ color: '#ff4d4f' }} />,
                okText: '确认创建',
                cancelText: '重新调整',
                onOk: () => resolve(true),
                onCancel: () => resolve(false),
              });
            });

            if (!confirmed) {
              return;
            }
          }
        }
      }

      createTaskMutation.mutate({
        work_order_id: values.work_order_id,
        routing_step_id: values.routing_step_id,
        skill_group_id: values.skill_group_id,
        machine_id: planAssignmentConfig?.mode === 'machine' ? values.machine_id : undefined,
        planned_start: values.planned_start?.toISOString(),
        planned_end: values.planned_end?.toISOString(),
      });
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  // 批量创建计划任务处理函数
  const handleBatchCreateSubmit = async () => {
    try {
      const values = await batchCreateForm.validateFields();

      if (!selectedTargetId) {
        const targetName = batchCreationType === 'part' ? '零件' :
                          batchCreationType === 'project' ? '项目' : '工单';
        message.error(`请选择${targetName}`);
        return;
      }

      if (!values.start_date) {
        message.error('请选择计划开始时间');
        return;
      }

      const requestData = {
        creation_type: batchCreationType,
        target_id: selectedTargetId,
        start_date: values.start_date.toISOString(),
        skill_group_assignments: values.skill_group_assignments || null,
        machine_assignments: planAssignmentConfig?.mode === 'machine' ? values.machine_assignments || null : null,
        auto_schedule: values.auto_schedule !== false, // 默认启用自动排程
      };

      batchCreateTasksMutation.mutate(requestData);
    } catch (error) {
      console.error('Batch create validation failed:', error);
    }
  };

  const getStatusTag = (status: string) => {
    const statusMap: Record<string, { color: string; text: string }> = {
      pending: { color: 'default', text: '待开始' },
      'in-progress': { color: 'blue', text: '进行中' },
      completed: { color: 'green', text: '已完成' },
      paused: { color: 'orange', text: '已暂停' },
      cancelled: { color: 'red', text: '已取消' },
    };
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 获取唯一值用于筛选
  const getUniqueValues = (dataSource: PlanTaskWithDetails[], field: string) => {
    const values = dataSource?.map(item => item[field as keyof PlanTaskWithDetails])
      .filter(value => value !== null && value !== undefined && value !== '')
      .map(value => String(value));
    return [...new Set(values)].sort();
  };

  // 生成筛选选项
  const generateFilterOptions = (values: string[]) => {
    return values.map(value => ({
      text: value,
      value: value,
    }));
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '工单ID',
      dataIndex: 'work_order_id',
      key: 'work_order_id',
    },
    {
      title: '项目',
      dataIndex: 'project_name',
      key: 'project_name',
      filters: generateFilterOptions(getUniqueValues(planTasks || [], 'project_name')),
      onFilter: (value: any, record: PlanTaskWithDetails) =>
        record.project_name === value,
      filterSearch: true,
      width: 120,
    },
    {
      title: '零件',
      dataIndex: 'part_number',
      key: 'part_number',
      filters: generateFilterOptions(getUniqueValues(planTasks || [], 'part_number')),
      onFilter: (value: any, record: PlanTaskWithDetails) =>
        record.part_number === value,
      filterSearch: true,
      width: 120,
    },
    {
      title: '工艺',
      dataIndex: 'process_name',
      key: 'process_name',
      filters: generateFilterOptions(getUniqueValues(planTasks || [], 'process_name')),
      onFilter: (value: any, record: PlanTaskWithDetails) =>
        record.process_name === value,
      filterSearch: true,
      width: 150,
    },
    {
      title: assignmentMode === 'machine' ? '技能组' : '分配技能组',
      dataIndex: 'skill_group_name',
      key: 'skill_group_name',
      filters: generateFilterOptions(getUniqueValues(planTasks || [], 'skill_group_name')),
      onFilter: (value: any, record: PlanTaskWithDetails) =>
        record.skill_group_name === value,
      filterSearch: true,
      render: (text: string) => {
        if (!text) return '未分配';
        const skillGroup = skillGroups?.find(sg => sg.group_name === text);
        return skillGroup ? getSkillGroupDisplayName(skillGroup) : text;
      },
      width: 120,
    },
    {
      title: '设备',
      dataIndex: 'machine_name',
      key: 'machine_name',
      filters: assignmentMode === 'machine' ?
        generateFilterOptions(getUniqueValues(planTasks || [], 'machine_name')) :
        undefined,
      onFilter: assignmentMode === 'machine' ?
        (value: any, record: PlanTaskWithDetails) =>
          record.machine_name === value :
        undefined,
      filterSearch: assignmentMode === 'machine',
      render: (text: string, record: PlanTaskWithDetails) => {
        if (assignmentMode === 'machine') {
          return text || '未分配';
        }
        return <span style={{ color: '#999' }}>由操作员选择</span>; // 技能组模式下显示提示
      },
      width: 120,
    },
    {
      title: '计划开始',
      dataIndex: 'planned_start',
      key: 'planned_start',
      render: (text: string) => dayjs(text).format('MM-DD HH:mm'),
    },
    {
      title: '计划结束',
      dataIndex: 'planned_end',
      key: 'planned_end',
      render: (text: string) => dayjs(text).format('MM-DD HH:mm'),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      filters: [
        { text: '计划中', value: 'planned' },
        { text: '进行中', value: 'in_progress' },
        { text: '已完成', value: 'completed' },
        { text: '已暂停', value: 'paused' },
        { text: '已取消', value: 'cancelled' },
      ],
      onFilter: (value: any, record: PlanTaskWithDetails) =>
        record.status === value,
      render: (status: string) => getStatusTag(status),
      width: 100,
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_: any, record: PlanTaskWithDetails) => (
        <Space>
          {canEditTask && (
            <Button
              type="link"
              icon={<CalendarOutlined />}
              onClick={() => handleReschedule(record)}
            >
              调度
            </Button>
          )}
          {/* 注意：开始任务应该由操作员在执行页面进行，这里移除开始按钮 */}
          {/* 计划员只负责调度，不负责执行 */}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div className="page-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <div>
          <Title level={2} style={{ margin: 0 }}>
            生产计划
          </Title>
          {isPlanner && planAssignmentConfig && (
            <div style={{ marginTop: 12, display: 'flex', alignItems: 'center', gap: 16 }}>
              <div style={{ fontSize: '14px', color: '#666' }}>
                <SettingOutlined style={{ marginRight: 4 }} />
                任务分配模式:
              </div>
              <Select
                value={assignmentMode}
                style={{ width: 150 }}
                onChange={(value) => {
                  setAssignmentMode(value);
                  setPlanAssignmentModeMutation.mutate(value);
                }}
                disabled={setPlanAssignmentModeMutation.isLoading}
              >
                <Select.Option value="skill_group">技能组模式</Select.Option>
                <Select.Option value="machine">设备模式</Select.Option>
              </Select>
              <div style={{ fontSize: '12px', color: '#999' }}>
                {assignmentMode === 'skill_group'
                  ? '任务分配给技能组，操作员选择设备执行'
                  : '任务直接分配给具体设备'}
              </div>
              {planAssignmentConfig && !planAssignmentConfig.enabled && (
                <Tag color="orange">配置已禁用</Tag>
              )}
            </div>
          )}
        </div>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => {
              refetchRoutings();
              queryClient.invalidateQueries('plan-tasks');
              queryClient.invalidateQueries('work-orders');
              queryClient.invalidateQueries('plan-assignment-mode');
              message.success('数据已刷新');
            }}
          >
            刷新数据
          </Button>
          {canCreateTask && (
            <>
              <Button
                icon={<PlusOutlined />}
                onClick={() => setIsBatchCreateModalVisible(true)}
              >
                批量创建
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setIsCreateModalVisible(true)}
              >
                添加计划
              </Button>
            </>
          )}
        </Space>
      </div>

      {/* 分配模式说明 */}
      {isPlanner && (
        <Card
          size="small"
          style={{ marginBottom: 16, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f' }}
        >
          <div style={{ fontSize: '13px', color: '#52c41a' }}>
            <strong>📋 分配模式说明：</strong>
            {assignmentMode === 'skill_group' ? (
              <span>
                技能组模式 - 任务分配给技能组，操作员在执行时可以选择技能组内的任何可用设备进行操作，提供更大的灵活性。
              </span>
            ) : (
              <span>
                设备模式 - 任务直接分配给具体设备，操作员必须使用指定的设备执行任务，适用于有特殊要求的工艺。
              </span>
            )}
          </div>
        </Card>
      )}

      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'table',
              label: (
                <span>
                  <TableOutlined />
                  列表视图
                </span>
              ),
              children: (
                <Table
                  columns={columns}
                  dataSource={planTasks}
                  rowKey="id"
                  loading={isLoading}
                  pagination={{
                    total: planTasks?.length || 0,
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条记录`,
                  }}
                  rowClassName={(record) =>
                    highlightTaskId && record.id.toString() === highlightTaskId
                      ? 'highlighted-task-row'
                      : ''
                  }
                  onRow={(record) => ({
                    'data-task-id': record.id.toString(),
                  } as any)}
                />
              ),
            },
            {
              key: 'gantt',
              label: (
                <span>
                  <BarChartOutlined />
                  甘特图
                </span>
              ),
              children: (
                <GanttChart
                  tasks={planTasks || []}
                  title="生产计划甘特图"
                  height={500}
                  editable={true}
                  onTaskUpdate={async (taskId, newStart, newEnd) => {
                    try {
                      await updateTaskMutation.mutateAsync({
                        id: taskId,
                        data: {
                          planned_start: newStart.toISOString(),
                          planned_end: newEnd.toISOString(),
                        }
                      });
                    } catch (error) {
                      console.error('更新任务失败:', error);
                      throw error;
                    }
                  }}
                />
              ),
            },
          ]}
        />
      </Card>

      {/* 任务调度模态框 */}
      <Modal
        title="任务调度"
        open={isRescheduleModalVisible}
        onOk={handleRescheduleSubmit}
        onCancel={() => {
          setIsRescheduleModalVisible(false);
          setSelectedTask(null);
          form.resetFields();
        }}
        confirmLoading={updateTaskMutation.isLoading}
        okText="确定"
        cancelText="取消"
      >
        {selectedTask && (
          <div style={{ marginBottom: 16 }}>
            <p><strong>任务：</strong>{selectedTask.process_name}</p>
            <p><strong>项目：</strong>{selectedTask.project_name}</p>
            <p><strong>零件：</strong>{selectedTask.part_number}</p>
          </div>
        )}

        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="skill_group_id"
            label="技能组"
            rules={[{ required: true, message: '请选择技能组' }]}
          >
            <Select
              placeholder="请选择技能组"
              style={{ width: '100%' }}
              onChange={(value) => {
                // 当技能组改变时，清空设备选择
                form.setFieldValue('machine_id', undefined);
              }}
            >
              {skillGroups?.map(group => (
                <Option key={group.id} value={group.id}>
                  {group.group_name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="machine_id"
            label="指定设备"
            tooltip="可选择具体设备，不选择则分配给技能组"
          >
            <Select
              placeholder="请选择设备（可选）"
              allowClear
              style={{ width: '100%' }}
            >
              {machines?.filter(machine => {
                if (!isFormReady) return false;
                try {
                  return machine.skill_group_id === form.getFieldValue('skill_group_id');
                } catch {
                  return false;
                }
              }).map(machine => (
                <Option key={machine.id} value={machine.id}>
                  {machine.machine_name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="planned_start"
            label="计划开始时间"
            rules={[{ required: true, message: '请选择计划开始时间' }]}
          >
            <DatePicker
              showTime={{
                minuteStep: 30,
                hideDisabledOptions: true,
                format: 'HH:mm',
              }}
              style={{ width: '100%' }}
              format="YYYY-MM-DD HH:mm"
              placeholder="选择计划开始时间"
              onChange={(startTime) => {
                if (startTime && selectedTask) {
                  // 根据工艺标准工时自动计算结束时间
                  const standardHours = selectedTask.standard_hours || 1; // 默认1小时
                  const endTime = startTime.add(standardHours, 'hour');
                  form.setFieldValue('planned_end', endTime);
                }
              }}
            />
          </Form.Item>

          <Form.Item
            name="planned_end"
            label={
              <Space>
                <span>计划结束时间</span>
                {selectedTask?.standard_hours && (
                  <Tag color="blue">
                    标准工时: {selectedTask.standard_hours}h
                  </Tag>
                )}
              </Space>
            }
          >
            <DatePicker
              showTime={{
                minuteStep: 30,
                hideDisabledOptions: true,
                format: 'HH:mm',
              }}
              style={{ width: '100%' }}
              format="YYYY-MM-DD HH:mm"
              placeholder="根据标准工时自动计算"
              disabled={true}
              title="结束时间根据开始时间和标准工时自动计算"
            />
          </Form.Item>

          {/* 显示工时信息 */}
          {selectedTask && (
            <div style={{
              marginBottom: '16px',
              padding: '8px 12px',
              backgroundColor: '#f6ffed',
              border: '1px solid #b7eb8f',
              borderRadius: '4px',
              fontSize: '12px'
            }}>
              <Space direction="vertical" size="small">
                <Text type="secondary">
                  <ClockCircleOutlined /> 工艺信息: {selectedTask.process_name}
                </Text>
                <Text type="secondary">
                  📏 标准工时: {selectedTask.standard_hours || '未设置'} 小时
                </Text>
                <Text type="secondary">
                  ⚡ 结束时间将根据开始时间自动计算
                </Text>
              </Space>
            </div>
          )}
        </Form>
      </Modal>

      {/* 创建计划任务模态框 */}
      <Modal
        title="创建计划任务"
        open={isCreateModalVisible}
        onOk={handleCreateSubmit}
        onCancel={() => {
          setIsCreateModalVisible(false);
          createForm.resetFields();
          setSelectedWorkOrderId(null);
          setSelectedPartId(null);
          setSelectedSkillGroupId(null);
        }}
        confirmLoading={createTaskMutation.isLoading}
        okText="创建"
        cancelText="取消"
        width={600}
      >
        <Form
          form={createForm}
          layout="vertical"
        >
          <Form.Item
            name="work_order_id"
            label="工单"
            rules={[{ required: true, message: '请选择工单' }]}
          >
            <WorkOrderSelector
              placeholder="请选择工单"
              onChange={(workOrderId, workOrder) => {
                setSelectedWorkOrderId(workOrderId);
                setSelectedPartId(workOrder.part_id || null);
                // 清空工艺步骤选择
                createForm.setFieldsValue({ routing_step_id: undefined });
              }}
              showWorkOrderInfo={true}
              mode="project-based"
            />
          </Form.Item>

          <Form.Item
            name="routing_step_id"
            label="工艺步骤"
            rules={[{ required: true, message: '请选择工艺步骤' }]}
          >
            <Select
              placeholder={selectedWorkOrderId ? "请选择工艺步骤" : "请先选择工单"}
              showSearch
              disabled={!selectedWorkOrderId}
              onChange={(routingStepId) => {
                const selectedRouting = filteredRoutings?.find(r => r.id === routingStepId);
                setSelectedRoutingStep(selectedRouting);

                // 自动设置工艺路径中预设的技能组
                if (selectedRouting?.skill_group_id) {
                  createForm.setFieldsValue({ skill_group_id: selectedRouting.skill_group_id });
                  setSelectedSkillGroupId(selectedRouting.skill_group_id);
                  // 清空设备选择，因为技能组变了
                  if (planAssignmentConfig?.mode === 'machine') {
                    createForm.setFieldsValue({ machine_id: undefined });
                  }
                }

                // 如果有标准工时，显示时间估算助手
                if (selectedRouting?.standard_hours) {
                  setTimeEstimateVisible(true);
                  const suggestion = calculateSuggestedTimes(selectedRouting);
                  if (suggestion) {
                    // 自动填充建议时间
                    applySuggestedTimes(suggestion);
                  }
                } else {
                  setTimeEstimateVisible(false);
                }
              }}
            >
              {filteredRoutings?.map(routing => {
                const skillGroup = skillGroups?.find(sg => sg.id === routing.skill_group_id);
                return (
                  <Select.Option key={routing.id} value={routing.id}>
                    <div>
                      <span>步骤{routing.step_number}: {routing.process_name}</span>
                      {routing.standard_hours && (
                        <span style={{ color: '#1890ff', marginLeft: 8, fontSize: '12px' }}>
                          <ClockCircleOutlined /> {routing.standard_hours}小时
                        </span>
                      )}
                      {skillGroup && (
                        <span style={{ color: '#52c41a', marginLeft: 8, fontSize: '12px' }}>
                          🎯 {skillGroup.group_name}
                        </span>
                      )}
                      {routing.work_instructions && (
                        <div style={{ color: '#999', fontSize: '12px', marginTop: 2 }}>
                          {routing.work_instructions.substring(0, 50)}
                          {routing.work_instructions.length > 50 ? '...' : ''}
                        </div>
                      )}
                    </div>
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>

          {/* 时间估算助手 */}
          {isCreateModalVisible && isCreateFormReady && (
            <TimeEstimateHelper
              routingStep={selectedRoutingStep}
              visible={timeEstimateVisible && !!selectedRoutingStep}
              currentStartTime={createForm.getFieldValue('planned_start')}
              currentEndTime={createForm.getFieldValue('planned_end')}
              onApplyTime={(startTime, endTime) => {
                createForm.setFieldsValue({
                  planned_start: startTime,
                  planned_end: endTime,
                });
                // 重新检查偏差
                if (selectedRoutingStep?.standard_hours) {
                  checkTimeDeviation(startTime, endTime, selectedRoutingStep.standard_hours);
                }
              }}
            />
          )}

          <Form.Item
            name="skill_group_id"
            label={assignmentMode === 'machine' ? '技能组（用于筛选设备）' : '分配技能组'}
            rules={[{ required: true, message: '请选择技能组' }]}
            tooltip="选择工艺步骤时会自动设置工艺路径中预设的技能组"
          >
            <Select
              placeholder="请选择技能组"
              onChange={(value) => {
                setSelectedSkillGroupId(value);
                // 清空设备选择
                if (planAssignmentConfig?.mode === 'machine') {
                  createForm.setFieldsValue({ machine_id: undefined });
                }
              }}
            >
              {skillGroups?.map(group => (
                <Select.Option key={group.id} value={group.id}>
                  {group.group_name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          {/* 设备选择器 - 仅在设备分配模式下显示 */}
          {planAssignmentConfig?.mode === 'machine' && (
            <Form.Item
              name="machine_id"
              label="设备"
              rules={[{ required: true, message: '请选择设备' }]}
            >
              <Select
                placeholder={selectedSkillGroupId ? "请选择设备" : "请先选择技能组"}
                disabled={!selectedSkillGroupId}
                showSearch
                optionFilterProp="children"
              >
                {filteredMachines?.map(machine => (
                  <Select.Option key={machine.id} value={machine.id}>
                    {machine.machine_name}
                    <span style={{ color: '#999', marginLeft: 8 }}>
                      ({machine.status === 'available' ? '可用' :
                        machine.status === 'in_use' ? '使用中' :
                        machine.status === 'maintenance' ? '维护中' : machine.status})
                    </span>
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          )}

          <Form.Item
            name="planned_start"
            label={
              <span>
                计划开始时间
                {selectedRoutingStep?.standard_hours && (
                  <Tooltip title={`标准工时：${selectedRoutingStep.standard_hours}小时`}>
                    <ClockCircleOutlined style={{ marginLeft: 8, color: '#1890ff' }} />
                  </Tooltip>
                )}
              </span>
            }
            rules={[{ required: true, message: '请选择计划开始时间' }]}
          >
            <DatePicker
              showTime={{
                minuteStep: 30,
                hideDisabledOptions: true,
                format: 'HH:mm',
              }}
              style={{ width: '100%' }}
              format="YYYY-MM-DD HH:mm"
              placeholder="选择计划开始时间"
              onChange={(startTime) => {
                const endTime = createForm.getFieldValue('planned_end');
                if (startTime && endTime && selectedRoutingStep?.standard_hours) {
                  checkTimeDeviation(startTime, endTime, selectedRoutingStep.standard_hours);
                }
              }}
            />
          </Form.Item>

          <Form.Item
            name="planned_end"
            label="计划结束时间"
            rules={[{ required: true, message: '请选择计划结束时间' }]}
          >
            <DatePicker
              showTime={{
                minuteStep: 30,
                hideDisabledOptions: true,
                format: 'HH:mm',
              }}
              style={{ width: '100%' }}
              format="YYYY-MM-DD HH:mm"
              placeholder="选择计划结束时间"
              onChange={(endTime) => {
                const startTime = createForm.getFieldValue('planned_start');
                if (startTime && endTime && selectedRoutingStep?.standard_hours) {
                  checkTimeDeviation(startTime, endTime, selectedRoutingStep.standard_hours);
                }
              }}
            />
          </Form.Item>

          {/* 时间偏差警告 */}
          {timeDeviationWarning && (
            <Alert
              message="时间偏差提醒"
              description={timeDeviationWarning}
              type={timeDeviationWarning.includes('⚠️') ? 'warning' : 'info'}
              showIcon
              style={{ marginBottom: 16 }}
              icon={timeDeviationWarning.includes('⚠️') ? <WarningOutlined /> : <ClockCircleOutlined />}
            />
          )}

          {/* 快速时间调整工具 */}
          {selectedRoutingStep?.standard_hours && (
            <Card size="small" style={{ marginBottom: 16, backgroundColor: '#f8f9fa' }}>
              <div style={{ fontSize: '12px', color: '#666', marginBottom: 8 }}>
                <BulbOutlined /> 快速调整：基于标准工时 {selectedRoutingStep.standard_hours}小时
              </div>
              <Space>
                <Button
                  size="small"
                  onClick={() => {
                    const suggestion = calculateSuggestedTimes(selectedRoutingStep);
                    if (suggestion) applySuggestedTimes(suggestion);
                  }}
                >
                  使用标准时间
                </Button>
                <Button
                  size="small"
                  onClick={() => {
                    const suggestion = calculateSuggestedTimes(selectedRoutingStep, dayjs().add(2, 'hour'));
                    if (suggestion) applySuggestedTimes(suggestion);
                  }}
                >
                  2小时后开始
                </Button>
                <Button
                  size="small"
                  onClick={() => {
                    const suggestion = calculateSuggestedTimes(selectedRoutingStep, dayjs().add(1, 'day').hour(8).minute(0));
                    if (suggestion) applySuggestedTimes(suggestion);
                  }}
                >
                  明天8点开始
                </Button>
              </Space>
            </Card>
          )}
        </Form>
      </Modal>

      {/* 批量创建计划任务模态框 */}
      <Modal
        title="批量创建计划任务"
        open={isBatchCreateModalVisible}
        onOk={handleBatchCreateSubmit}
        onCancel={() => {
          setIsBatchCreateModalVisible(false);
          batchCreateForm.resetFields();
          setSelectedTargetId(null);
          setBatchCreationType('project');
        }}
        confirmLoading={batchCreateTasksMutation.isLoading}
        okText="批量创建"
        cancelText="取消"
        width={900}
      >
        <Alert
          message="智能批量创建"
          description="支持按项目、零件或工单批量创建计划任务，系统将自动为相关的所有工艺步骤创建计划任务，并基于标准工时智能安排时间。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Form
          form={batchCreateForm}
          layout="vertical"
        >
          {/* 创建方式选择 */}
          <Form.Item
            label="创建方式"
            style={{ marginBottom: 16 }}
          >
            <Card size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                <div style={{ marginBottom: 8, fontWeight: 'bold' }}>选择批量创建的范围：</div>
                <Space wrap>
                  <Button
                    type={batchCreationType === 'project' ? 'primary' : 'default'}
                    icon={<ProjectOutlined />}
                    onClick={() => {
                      setBatchCreationType('project');
                      setSelectedTargetId(null);
                      batchCreateForm.resetFields(['target_id']);
                    }}
                  >
                    按项目创建
                  </Button>
                  <Button
                    type={batchCreationType === 'part' ? 'primary' : 'default'}
                    icon={<SettingOutlined />}
                    onClick={() => {
                      setBatchCreationType('part');
                      setSelectedTargetId(null);
                      batchCreateForm.resetFields(['target_id']);
                    }}
                  >
                    按零件创建
                  </Button>
                  <Button
                    type={batchCreationType === 'work_order' ? 'primary' : 'default'}
                    icon={<CalendarOutlined />}
                    onClick={() => {
                      setBatchCreationType('work_order');
                      setSelectedTargetId(null);
                      batchCreateForm.resetFields(['target_id']);
                    }}
                  >
                    按工单创建
                  </Button>
                </Space>
                <div style={{ fontSize: '12px', color: '#666', marginTop: 8 }}>
                  {batchCreationType === 'project' && '• 为选择项目的所有待处理工单创建计划任务'}
                  {batchCreationType === 'part' && '• 为选择零件的所有待处理工单创建计划任务'}
                  {batchCreationType === 'work_order' && '• 为选择的单个工单创建计划任务'}
                </div>
              </Space>
            </Card>
          </Form.Item>

          {/* 目标选择 */}
          <Form.Item
            name="target_id"
            label={
              batchCreationType === 'project' ? '选择项目' :
              batchCreationType === 'part' ? '选择零件' : '选择工单'
            }
            rules={[{ required: true, message: `请选择${
              batchCreationType === 'project' ? '项目' :
              batchCreationType === 'part' ? '零件' : '工单'
            }` }]}
          >
            {batchCreationType === 'project' && (
              <Select
                placeholder="请选择要批量创建计划任务的项目"
                showSearch
                filterOption={(input, option) =>
                  (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                }
                onChange={(value) => setSelectedTargetId(value)}
              >
                {projects?.map(project => (
                  <Select.Option key={project.id} value={project.id}>
                    {project.project_name}
                    {project.customer_name && (
                      <span style={{ color: '#999', marginLeft: 8 }}>
                        - {project.customer_name}
                      </span>
                    )}
                  </Select.Option>
                ))}
              </Select>
            )}

            {batchCreationType === 'part' && (
              <Select
                placeholder="请选择要批量创建计划任务的零件"
                showSearch
                filterOption={(input, option) =>
                  (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                }
                onChange={(value) => setSelectedTargetId(value)}
                loading={!parts}
                notFoundContent={!parts ? "加载中..." : "暂无零件数据"}
              >
                {parts?.map(part => (
                  <Select.Option key={part.id} value={part.id}>
                    {part.part_number} - {part.part_name}
                    <span style={{ color: '#999', marginLeft: 8 }}>
                      (版本: {part.version})
                    </span>
                  </Select.Option>
                ))}
              </Select>
            )}

            {batchCreationType === 'work_order' && (
              <WorkOrderSelector
                placeholder="请选择要批量创建计划任务的工单"
                onChange={(workOrderId) => setSelectedTargetId(workOrderId)}
                showWorkOrderInfo={true}
                mode="project-based"
              />
            )}
          </Form.Item>

          <Form.Item
            name="start_date"
            label="计划开始时间"
            rules={[{ required: true, message: '请选择计划开始时间' }]}
          >
            <DatePicker
              showTime={{
                minuteStep: 30,
                hideDisabledOptions: true,
                format: 'HH:mm',
              }}
              style={{ width: '100%' }}
              format="YYYY-MM-DD HH:mm"
              placeholder="选择第一个工序的开始时间"
            />
          </Form.Item>

          {/* 技能组分配设置 */}
          <Divider orientation="left">技能组分配</Divider>
          <Alert
            message="智能技能组分配"
            description="系统将根据工艺名称自动匹配对应的技能组。例如：CNC Machining工艺自动分配给CNC加工技能组，Grinding工艺分配给磨削技能组等。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Form.Item
            label="分配模式"
            style={{ marginBottom: 16 }}
          >
            <div style={{ fontSize: '14px', color: '#666' }}>
              <div style={{ marginBottom: 8 }}>
                <strong>🤖 智能分配模式（推荐）</strong>
              </div>
              <div style={{ paddingLeft: 16, fontSize: '13px' }}>
                • 系统根据工艺名称自动匹配技能组<br/>
                • CNC Machining → CNC加工技能组<br/>
                • Grinding → 磨削技能组<br/>
                • Milling → 铣削技能组<br/>
                • 其他工艺按名称相似度匹配
              </div>
            </div>
          </Form.Item>

          {/* 自动排程选项 */}
          <Divider orientation="left">排程设置</Divider>
          <Form.Item
            name="auto_schedule"
            valuePropName="checked"
            initialValue={true}
          >
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <Switch defaultChecked />
              <span>启用自动排程（工序间无缝衔接）</span>
              <Tooltip title="启用后，后续工序将自动安排在前一工序完成后立即开始">
                <InfoCircleOutlined style={{ color: '#999' }} />
              </Tooltip>
            </div>
          </Form.Item>

          {selectedTargetId && (
            <Alert
              message="批量创建说明"
              description={
                <div>
                  <p>• 系统将根据工艺路线的步骤顺序自动创建计划任务</p>
                  <p>• 每个工序的时间基于标准工时自动计算</p>
                  <p>• 技能组将根据工艺名称智能匹配分配</p>
                  <p>• {batchCreationType === 'project' ? '项目内所有零件的工单将按零件编号排序' :
                       batchCreationType === 'part' ? '该零件的所有工单将按交期排序' :
                       '该工单的所有工艺步骤将按步骤顺序排列'}</p>
                  <p>• 创建后可在列表中单独调整每个任务的时间和分配</p>
                </div>
              }
              type="success"
              showIcon
              style={{ marginTop: 16 }}
            />
          )}
        </Form>
      </Modal>
    </div>
  );
};

export default PlanTasks;
