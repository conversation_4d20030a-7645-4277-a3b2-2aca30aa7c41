import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  TimePicker,
  Checkbox,
  Space,
  Tag,
  message,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Alert,
  Tooltip,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  SettingOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

interface ShiftTemplate {
  id: number;
  template_name: string;
  schedule_type: string;
  start_hour: number;
  start_minute: number;
  end_hour: number;
  end_minute: number;
  duration_hours: number;
  work_days: number[];
  break_periods?: any;
  description?: string;
  is_system_template: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface PlanGroup {
  id: number;
  group_name: string;
  group_code: string;
  description?: string;
  priority: number;
  is_active: boolean;
  created_by?: number;
  created_at: string;
  updated_at: string;
}

const ShiftManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('templates');
  const [templateModalVisible, setTemplateModalVisible] = useState(false);
  const [planGroupModalVisible, setPlanGroupModalVisible] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<ShiftTemplate | null>(null);
  const [editingPlanGroup, setPlanGroup] = useState<PlanGroup | null>(null);
  const [templateForm] = Form.useForm();
  const [planGroupForm] = Form.useForm();
  const queryClient = useQueryClient();

  // 获取班次模板
  const { data: templatesData, isLoading: templatesLoading } = useQuery(
    'shift-templates',
    () => apiClient.getShiftTemplates(true),
    {
      select: (data) => data.templates,
    }
  );

  // 获取计划组
  const { data: planGroupsData, isLoading: planGroupsLoading } = useQuery(
    'plan-groups',
    () => apiClient.getPlanGroups(true),
    {
      select: (data) => data.groups,
    }
  );

  // 创建班次模板
  const createTemplateMutation = useMutation(
    (data: any) => apiClient.createShiftTemplate(data),
    {
      onSuccess: () => {
        message.success('班次模板创建成功');
        setTemplateModalVisible(false);
        templateForm.resetFields();
        queryClient.invalidateQueries('shift-templates');
      },
      onError: (error: any) => {
        message.error(error.response?.data?.message || '创建失败');
      },
    }
  );

  // 更新班次模板
  const updateTemplateMutation = useMutation(
    ({ id, data }: { id: number; data: any }) =>
      apiClient.updateShiftTemplate(id, data),
    {
      onSuccess: () => {
        message.success('班次模板更新成功');
        setTemplateModalVisible(false);
        templateForm.resetFields();
        setEditingTemplate(null);
        queryClient.invalidateQueries('shift-templates');
      },
      onError: (error: any) => {
        message.error(error.response?.data?.message || '更新失败');
      },
    }
  );

  // 删除班次模板
  const deleteTemplateMutation = useMutation(
    (id: number) => apiClient.deleteShiftTemplate(id),
    {
      onSuccess: () => {
        message.success('班次模板删除成功');
        queryClient.invalidateQueries('shift-templates');
      },
      onError: (error: any) => {
        message.error(error.response?.data?.message || '删除失败');
      },
    }
  );

  // 创建计划组
  const createPlanGroupMutation = useMutation(
    (data: any) => apiClient.createPlanGroup(data),
    {
      onSuccess: () => {
        message.success('计划组创建成功');
        setPlanGroupModalVisible(false);
        planGroupForm.resetFields();
        queryClient.invalidateQueries('plan-groups');
      },
      onError: (error: any) => {
        message.error(error.response?.data?.message || '创建失败');
      },
    }
  );

  // 班次模板表格列
  const templateColumns = [
    {
      title: '模板名称',
      dataIndex: 'template_name',
      key: 'template_name',
      render: (text: string, record: ShiftTemplate) => (
        <Space>
          <span>{text}</span>
          {record.is_system_template && (
            <Tag color="blue">系统</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '班次类型',
      dataIndex: 'schedule_type',
      key: 'schedule_type',
      render: (type: string) => {
        const typeMap: Record<string, { text: string; color: string }> = {
          '7x24': { text: '7×24小时', color: 'red' },
          '7x12': { text: '7×12小时', color: 'orange' },
          '5x8': { text: '5×8小时', color: 'green' },
          'custom': { text: '自定义', color: 'purple' },
        };
        const config = typeMap[type] || { text: type, color: 'default' };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '工作时间',
      key: 'work_time',
      render: (record: ShiftTemplate) => {
        const startTime = `${record.start_hour.toString().padStart(2, '0')}:${record.start_minute.toString().padStart(2, '0')}`;
        const endTime = `${record.end_hour.toString().padStart(2, '0')}:${record.end_minute.toString().padStart(2, '0')}`;
        return `${startTime} - ${endTime}`;
      },
    },
    {
      title: '时长',
      dataIndex: 'duration_hours',
      key: 'duration_hours',
      render: (hours: number) => `${hours}小时`,
    },
    {
      title: '工作日',
      dataIndex: 'work_days',
      key: 'work_days',
      render: (days: number[]) => {
        const dayNames = ['一', '二', '三', '四', '五', '六', '日'];
        return days.map(day => dayNames[day - 1]).join('、');
      },
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (active: boolean) => (
        <Tag color={active ? 'green' : 'red'}>
          {active ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: ShiftTemplate) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEditTemplate(record)}
            disabled={record.is_system_template}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个班次模板吗？"
            onConfirm={() => deleteTemplateMutation.mutate(record.id)}
            disabled={record.is_system_template}
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              disabled={record.is_system_template}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 计划组表格列
  const planGroupColumns = [
    {
      title: '组名',
      dataIndex: 'group_name',
      key: 'group_name',
    },
    {
      title: '代码',
      dataIndex: 'group_code',
      key: 'group_code',
      render: (code: string) => <Tag>{code}</Tag>,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: number) => (
        <Tag color={priority === 0 ? 'red' : priority === 1 ? 'orange' : 'blue'}>
          {priority === 0 ? '最高' : priority === 1 ? '高' : '普通'}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (active: boolean) => (
        <Tag color={active ? 'green' : 'red'}>
          {active ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: PlanGroup) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEditPlanGroup(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            icon={<SettingOutlined />}
            onClick={() => handleConfigureShifts(record)}
          >
            配置班次
          </Button>
        </Space>
      ),
    },
  ];

  // 处理编辑班次模板
  const handleEditTemplate = (template: ShiftTemplate) => {
    setEditingTemplate(template);
    templateForm.setFieldsValue({
      template_name: template.template_name,
      schedule_type: template.schedule_type,
      start_time: dayjs().hour(template.start_hour).minute(template.start_minute),
      end_time: dayjs().hour(template.end_hour).minute(template.end_minute),
      work_days: template.work_days,
      description: template.description,
    });
    setTemplateModalVisible(true);
  };

  // 处理编辑计划组
  const handleEditPlanGroup = (group: PlanGroup) => {
    setPlanGroup(group);
    planGroupForm.setFieldsValue({
      group_name: group.group_name,
      group_code: group.group_code,
      description: group.description,
      priority: group.priority,
    });
    setPlanGroupModalVisible(true);
  };

  // 处理配置班次
  const handleConfigureShifts = (group: PlanGroup) => {
    // TODO: 打开班次配置对话框
    message.info(`配置 ${group.group_name} 的班次安排`);
  };

  // 提交班次模板表单
  const handleTemplateSubmit = async (values: any) => {
    const formData = {
      template_name: values.template_name,
      schedule_type: values.schedule_type,
      start_hour: values.start_time.hour(),
      start_minute: values.start_time.minute(),
      end_hour: values.end_time.hour(),
      end_minute: values.end_time.minute(),
      work_days: values.work_days,
      description: values.description,
    };

    if (editingTemplate) {
      updateTemplateMutation.mutate({ id: editingTemplate.id, data: formData });
    } else {
      createTemplateMutation.mutate(formData);
    }
  };

  // 提交计划组表单
  const handlePlanGroupSubmit = async (values: any) => {
    if (editingPlanGroup) {
      // TODO: 实现更新计划组
      message.info('更新计划组功能待实现');
    } else {
      createPlanGroupMutation.mutate(values);
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card title="班次管理" extra={
        <Space>
          <Tooltip title="7×12小时和7×24小时工作模式管理">
            <ClockCircleOutlined />
          </Tooltip>
        </Space>
      }>
        {/* 统计信息 */}
        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={6}>
            <Statistic
              title="班次模板"
              value={templatesData?.length || 0}
              prefix={<ClockCircleOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="计划组"
              value={planGroupsData?.length || 0}
              prefix={<TeamOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="7×24模式"
              value={templatesData?.filter((t: ShiftTemplate) => t.schedule_type === '7x24').length || 0}
              valueStyle={{ color: '#cf1322' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="7×12模式"
              value={templatesData?.filter((t: ShiftTemplate) => t.schedule_type === '7x12').length || 0}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Col>
        </Row>

        <Alert
          message="班次管理说明"
          description="7×24小时模式适用于连续生产线，7×12小时模式适用于两班制工作。可以为不同的计划组配置不同的班次模式。"
          type="info"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="班次模板" key="templates">
            <div style={{ marginBottom: '16px' }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setEditingTemplate(null);
                  templateForm.resetFields();
                  setTemplateModalVisible(true);
                }}
              >
                新建班次模板
              </Button>
            </div>
            <Table
              columns={templateColumns}
              dataSource={templatesData}
              loading={templatesLoading}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>

          <TabPane tab="计划组" key="plan-groups">
            <div style={{ marginBottom: '16px' }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setPlanGroup(null);
                  planGroupForm.resetFields();
                  setPlanGroupModalVisible(true);
                }}
              >
                新建计划组
              </Button>
            </div>
            <Table
              columns={planGroupColumns}
              dataSource={planGroupsData}
              loading={planGroupsLoading}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 班次模板编辑对话框 */}
      <Modal
        title={editingTemplate ? '编辑班次模板' : '新建班次模板'}
        open={templateModalVisible}
        onCancel={() => {
          setTemplateModalVisible(false);
          setEditingTemplate(null);
          templateForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={templateForm}
          layout="vertical"
          onFinish={handleTemplateSubmit}
        >
          <Form.Item
            name="template_name"
            label="模板名称"
            rules={[{ required: true, message: '请输入模板名称' }]}
          >
            <Input placeholder="例如：白班(7×12)" />
          </Form.Item>

          <Form.Item
            name="schedule_type"
            label="班次类型"
            rules={[{ required: true, message: '请选择班次类型' }]}
          >
            <Select placeholder="选择班次类型">
              <Option value="7x24">7×24小时连续</Option>
              <Option value="7x12">7×12小时两班制</Option>
              <Option value="5x8">5×8小时标准</Option>
              <Option value="custom">自定义</Option>
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="start_time"
                label="开始时间"
                rules={[{ required: true, message: '请选择开始时间' }]}
              >
                <TimePicker format="HH:mm" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="end_time"
                label="结束时间"
                rules={[{ required: true, message: '请选择结束时间' }]}
              >
                <TimePicker format="HH:mm" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="work_days"
            label="工作日"
            rules={[{ required: true, message: '请选择工作日' }]}
          >
            <Checkbox.Group>
              <Row>
                <Col span={8}><Checkbox value={1}>周一</Checkbox></Col>
                <Col span={8}><Checkbox value={2}>周二</Checkbox></Col>
                <Col span={8}><Checkbox value={3}>周三</Checkbox></Col>
                <Col span={8}><Checkbox value={4}>周四</Checkbox></Col>
                <Col span={8}><Checkbox value={5}>周五</Checkbox></Col>
                <Col span={8}><Checkbox value={6}>周六</Checkbox></Col>
                <Col span={8}><Checkbox value={7}>周日</Checkbox></Col>
              </Row>
            </Checkbox.Group>
          </Form.Item>

          <Form.Item name="description" label="描述">
            <TextArea rows={3} placeholder="班次模板描述" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingTemplate ? '更新' : '创建'}
              </Button>
              <Button onClick={() => setTemplateModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 计划组编辑对话框 */}
      <Modal
        title={editingPlanGroup ? '编辑计划组' : '新建计划组'}
        open={planGroupModalVisible}
        onCancel={() => {
          setPlanGroupModalVisible(false);
          setPlanGroup(null);
          planGroupForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={planGroupForm}
          layout="vertical"
          onFinish={handlePlanGroupSubmit}
        >
          <Form.Item
            name="group_name"
            label="组名"
            rules={[{ required: true, message: '请输入组名' }]}
          >
            <Input placeholder="例如：生产计划组" />
          </Form.Item>

          <Form.Item
            name="group_code"
            label="代码"
            rules={[{ required: true, message: '请输入代码' }]}
          >
            <Input placeholder="例如：PROD" style={{ textTransform: 'uppercase' }} />
          </Form.Item>

          <Form.Item
            name="priority"
            label="优先级"
            rules={[{ required: true, message: '请选择优先级' }]}
          >
            <Select placeholder="选择优先级">
              <Option value={0}>最高</Option>
              <Option value={1}>高</Option>
              <Option value={2}>普通</Option>
              <Option value={3}>低</Option>
            </Select>
          </Form.Item>

          <Form.Item name="description" label="描述">
            <TextArea rows={3} placeholder="计划组描述" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingPlanGroup ? '更新' : '创建'}
              </Button>
              <Button onClick={() => setPlanGroupModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ShiftManagement;
