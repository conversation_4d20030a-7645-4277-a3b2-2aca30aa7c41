/**
 * 文本测试页面
 * 用于验证国际化和文本管理系统的功能
 */

import React from 'react';
import { Card, Row, Col, Typography, Tag, Space, Divider, Button } from 'antd';
import { useI18n } from '@/hooks/useI18n';
import { 
  getTaskStatusText, 
  getMachineStatusText, 
  getRoleDisplayText, 
  getSkillDisplayText,
  getTaskStatusOptions,
  getMachineStatusOptions 
} from '@/utils/textUtils';

const { Title, Text } = Typography;

const TextTest: React.FC = () => {
  const { 
    common, 
    dashboard, 
    roles, 
    skills, 
    status, 
    equipment, 
    permissions,
    changeLanguage,
    currentLanguage 
  } = useI18n();

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>文本管理系统测试页面</Title>
      <Text type="secondary">当前语言: {currentLanguage}</Text>
      
      <Divider />

      {/* 通用文本测试 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card title="通用文本" size="small">
            <Space wrap>
              <Button type="primary">{common.create()}</Button>
              <Button>{common.edit()}</Button>
              <Button danger>{common.delete()}</Button>
              <Button>{common.save()}</Button>
              <Button>{common.cancel()}</Button>
              <Button>{common.confirm()}</Button>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 仪表盘文本测试 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={12}>
          <Card title="仪表盘文本" size="small">
            <Space direction="vertical">
              <Text>{dashboard.operatorDashboard()}</Text>
              <Text>{dashboard.qualityDashboard()}</Text>
              <Text>{dashboard.plannerDashboard()}</Text>
              <Text>{dashboard.welcomeMessage('张三')}</Text>
              <Text>{dashboard.dailyWorkHours(8.5)}</Text>
            </Space>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="统计指标文本" size="small">
            <Space direction="vertical">
              <Text>{dashboard.completedTasksToday()}</Text>
              <Text>{dashboard.qualityRate()}</Text>
              <Text>{dashboard.workEfficiency()}</Text>
              <Text>{dashboard.machineUtilization()}</Text>
              <Text>{dashboard.productionEfficiency()}</Text>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 角色和技能文本测试 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={12}>
          <Card title="角色文本" size="small">
            <Space wrap>
              <Tag color="blue">{getRoleDisplayText('admin')}</Tag>
              <Tag color="green">{getRoleDisplayText('operator')}</Tag>
              <Tag color="orange">{getRoleDisplayText('planner')}</Tag>
              <Tag color="purple">{getRoleDisplayText('quality_inspector')}</Tag>
              <Tag color="cyan">{getRoleDisplayText('process_engineer')}</Tag>
              <Tag color="red">{getRoleDisplayText('viewer')}</Tag>
            </Space>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="技能组文本" size="small">
            <Space wrap>
              <Tag color="cyan">{getSkillDisplayText('CNC Machining')}</Tag>
              <Tag color="cyan">{getSkillDisplayText('Milling')}</Tag>
              <Tag color="cyan">{getSkillDisplayText('Turning')}</Tag>
              <Tag color="cyan">{getSkillDisplayText('Assembly')}</Tag>
              <Tag color="cyan">{getSkillDisplayText('Quality Control')}</Tag>
              <Tag color="cyan">{getSkillDisplayText('Packaging')}</Tag>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 状态文本测试 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={12}>
          <Card title="任务状态" size="small">
            <Space wrap>
              {getTaskStatusOptions().map(option => (
                <Tag key={option.value} color={option.color}>
                  {option.label}
                </Tag>
              ))}
            </Space>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="设备状态" size="small">
            <Space wrap>
              {getMachineStatusOptions().map(option => (
                <Tag key={option.value} color={option.color}>
                  {option.label}
                </Tag>
              ))}
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 设备文本测试 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card title="设备文本" size="small">
            <Space wrap>
              <Text>{equipment.cncEquipment()}</Text>
              <Text>{equipment.millingMachine()}</Text>
              <Text>{equipment.lathe()}</Text>
              <Text>{equipment.grinder()}</Text>
              <Divider type="vertical" />
              <Text>{equipment.total()}: 10</Text>
              <Text>{equipment.running()}: 8</Text>
              <Text>{equipment.idle()}: 1</Text>
              <Text>{equipment.maintenance()}: 1</Text>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 权限文本测试 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card title="权限功能文本" size="small">
            <Space wrap>
              <Tag>{permissions.createProject()}</Tag>
              <Tag>{permissions.editProject()}</Tag>
              <Tag>{permissions.deleteProject()}</Tag>
              <Tag>{permissions.createMachine()}</Tag>
              <Tag>{permissions.updateMachineStatus()}</Tag>
              <Tag>{permissions.startTask()}</Tag>
              <Tag>{permissions.completeTask()}</Tag>
              <Tag>{permissions.manageUsers()}</Tag>
              <Tag>{permissions.manageRoles()}</Tag>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 语言切换测试 */}
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card title="语言切换测试" size="small">
            <Space>
              <Button 
                type={currentLanguage === 'zh-CN' ? 'primary' : 'default'}
                onClick={() => changeLanguage('zh-CN')}
              >
                中文
              </Button>
              <Button 
                type={currentLanguage === 'en-US' ? 'primary' : 'default'}
                onClick={() => changeLanguage('en-US')}
              >
                English
              </Button>
            </Space>
            <Divider />
            <Text type="secondary">
              切换语言后，页面上的所有文本都应该相应更新。
              这包括按钮文本、标签、状态显示等。
            </Text>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default TextTest;
