import React, { useState } from 'react';
import {
  Card,
  Typography,
  Tabs,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Tag,
  Divider,
  Alert,
  Popconfirm,
  App,
} from 'antd';
import {
  SettingOutlined,
  PlusOutlined,
  EditOutlined,
  ReloadOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import type { SystemConfig, CreateSystemConfigRequest, UpdateSystemConfigRequest } from '@/types/api';
import { usePermissions } from '@/hooks/usePermissions';
import { useAuthStore } from '@/store/auth';
import TokenStatus from '@/components/TokenStatus';
import dayjs from '@/utils/dayjs';

const { Title, Text } = Typography;
const { TextArea } = Input;

const SystemConfigPage: React.FC = () => {
  const { message } = App.useApp();
  const [activeTab, setActiveTab] = useState('planning');
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState<SystemConfig | null>(null);
  const [form] = Form.useForm();
  const [createForm] = Form.useForm();
  const queryClient = useQueryClient();

  // 获取当前用户信息和权限
  const { user } = useAuthStore();
  const userRoles = user?.roles || [];

  // 权限检查 - 只有管理员可以访问系统配置
  const isAdmin = userRoles.includes('admin');
  const canManageConfig = isAdmin;

  // 获取系统配置
  const { data: configsByCategory, isLoading } = useQuery(
    'system-configs-by-category',
    () => apiClient.getConfigsByCategory(),
    {
      enabled: canManageConfig,
    }
  );

  // 计划分配模式配置已移至生产计划页面

  // 创建配置
  const createConfigMutation = useMutation(
    (data: CreateSystemConfigRequest) => apiClient.createConfig(data),
    {
      onSuccess: () => {
        message.success('配置创建成功');
        queryClient.invalidateQueries('system-configs-by-category');
        setIsCreateModalVisible(false);
        createForm.resetFields();
      },
      onError: (error: any) => {
        message.error(`创建失败: ${error.response?.data?.error || error.message}`);
      },
    }
  );

  // 更新配置
  const updateConfigMutation = useMutation(
    ({ key, data }: { key: string; data: UpdateSystemConfigRequest }) =>
      apiClient.updateConfig(key, data),
    {
      onSuccess: () => {
        message.success('配置更新成功');
        queryClient.invalidateQueries('system-configs-by-category');
        setIsEditModalVisible(false);
        setEditingConfig(null);
        form.resetFields();
      },
      onError: (error: any) => {
        message.error(`更新失败: ${error.response?.data?.error || error.message}`);
      },
    }
  );

  // 计划分配模式设置已移至生产计划页面

  const handleEdit = (config: SystemConfig) => {
    setEditingConfig(config);
    setIsEditModalVisible(true);
    form.setFieldsValue({
      config_value: config.config_value,
      config_type: config.config_type,
      description: config.description,
      category: config.category,
      is_active: config.is_active,
    });
  };

  const handleCreateSubmit = async () => {
    try {
      const values = await createForm.validateFields();
      createConfigMutation.mutate(values);
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handleEditSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (editingConfig) {
        updateConfigMutation.mutate({
          key: editingConfig.config_key,
          data: values,
        });
      }
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const getConfigTypeTag = (type: string) => {
    const typeMap: Record<string, { color: string; text: string }> = {
      string: { color: 'blue', text: '字符串' },
      boolean: { color: 'green', text: '布尔值' },
      integer: { color: 'orange', text: '整数' },
      decimal: { color: 'purple', text: '小数' },
      json: { color: 'red', text: 'JSON' },
    };
    const config = typeMap[type] || { color: 'default', text: type };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const renderConfigValue = (value: string, type: string) => {
    if (type === 'boolean') {
      return value === 'true' ? <Tag color="green">是</Tag> : <Tag color="red">否</Tag>;
    }
    if (type === 'json') {
      try {
        return <pre style={{ margin: 0, fontSize: '12px' }}>{JSON.stringify(JSON.parse(value), null, 2)}</pre>;
      } catch {
        return value;
      }
    }
    return value;
  };

  const columns = [
    {
      title: '配置键',
      dataIndex: 'config_key',
      key: 'config_key',
      width: 200,
    },
    {
      title: '配置值',
      dataIndex: 'config_value',
      key: 'config_value',
      render: (value: string, record: SystemConfig) => renderConfigValue(value, record.config_type),
    },
    {
      title: '类型',
      dataIndex: 'config_type',
      key: 'config_type',
      width: 100,
      render: (type: string) => getConfigTypeTag(type),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 80,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 150,
      render: (text: string) => dayjs(text).format('MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: (_: any, record: SystemConfig) => (
        <Button
          type="link"
          icon={<EditOutlined />}
          onClick={() => handleEdit(record)}
          size="small"
        >
          编辑
        </Button>
      ),
    },
  ];

  if (!canManageConfig) {
    return (
      <div>
        <Alert
          message="权限不足"
          description="只有管理员和计划员可以访问系统配置页面"
          type="error"
          showIcon
        />
      </div>
    );
  }

  return (
    <div>
      <div className="page-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Title level={2} style={{ margin: 0 }}>
          <SettingOutlined style={{ marginRight: 8 }} />
          系统配置
        </Title>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => {
              queryClient.invalidateQueries('system-configs-by-category');
              // queryClient.invalidateQueries('plan-assignment-mode'); // 已移至生产计划页面
              message.success('配置已刷新');
            }}
          >
            刷新
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setIsCreateModalVisible(true)}
          >
            新建配置
          </Button>
        </Space>
      </div>

      {/* Token状态显示 */}
      <Card style={{ marginBottom: 16 }}>
        <Title level={4}>认证状态</Title>
        <TokenStatus showDetails={true} />
      </Card>

      {/* 计划分配模式配置已移至生产计划页面 */}
      <Card style={{ marginBottom: 16 }}>
        <Title level={4}>系统说明</Title>
        <div style={{ fontSize: '14px', color: '#666' }}>
          <p>📋 <strong>计划分配模式设置</strong>已移至"生产计划"页面，计划人员可以在制定计划时直接选择任务分配模式。</p>
          <p>🔧 此页面用于管理其他系统配置项，如时间设置、界面配置等。</p>
        </div>
      </Card>

      {/* 配置列表 */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={Object.entries(configsByCategory || {}).map(([category, configs]) => ({
            key: category,
            label: getCategoryDisplayName(category),
            children: (
              <Table
                columns={columns}
                dataSource={configs}
                rowKey="id"
                loading={isLoading}
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total) => `共 ${total} 条配置`,
                }}
              />
            ),
          }))}
        />
      </Card>

      {/* 创建配置模态框 */}
      <Modal
        title="新建配置"
        open={isCreateModalVisible}
        onOk={handleCreateSubmit}
        onCancel={() => {
          setIsCreateModalVisible(false);
          createForm.resetFields();
        }}
        confirmLoading={createConfigMutation.isLoading}
        okText="创建"
        cancelText="取消"
        width={600}
      >
        <Form
          form={createForm}
          layout="vertical"
        >
          <Form.Item
            name="config_key"
            label="配置键"
            rules={[
              { required: true, message: '请输入配置键' },
              { pattern: /^[a-z_]+$/, message: '配置键只能包含小写字母和下划线' },
            ]}
          >
            <Input placeholder="例如: new_feature_enabled" />
          </Form.Item>

          <Form.Item
            name="config_value"
            label="配置值"
            rules={[{ required: true, message: '请输入配置值' }]}
          >
            <TextArea rows={3} placeholder="请输入配置值" />
          </Form.Item>

          <Form.Item
            name="config_type"
            label="配置类型"
            rules={[{ required: true, message: '请选择配置类型' }]}
          >
            <Select placeholder="请选择配置类型">
              <Select.Option value="string">字符串</Select.Option>
              <Select.Option value="boolean">布尔值</Select.Option>
              <Select.Option value="integer">整数</Select.Option>
              <Select.Option value="decimal">小数</Select.Option>
              <Select.Option value="json">JSON</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="category"
            label="配置类别"
            rules={[{ required: true, message: '请选择配置类别' }]}
          >
            <Select placeholder="请选择配置类别">
              <Select.Option value="planning">计划管理</Select.Option>
              <Select.Option value="general">通用设置</Select.Option>
              <Select.Option value="ui">界面配置</Select.Option>
              <Select.Option value="system">系统设置</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={2} placeholder="请输入配置描述（可选）" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑配置模态框 */}
      <Modal
        title="编辑配置"
        open={isEditModalVisible}
        onOk={handleEditSubmit}
        onCancel={() => {
          setIsEditModalVisible(false);
          setEditingConfig(null);
          form.resetFields();
        }}
        confirmLoading={updateConfigMutation.isLoading}
        okText="保存"
        cancelText="取消"
        width={600}
      >
        {editingConfig && (
          <div style={{ marginBottom: 16 }}>
            <Text strong>配置键: </Text>
            <Text code>{editingConfig.config_key}</Text>
          </div>
        )}

        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="config_value"
            label="配置值"
            rules={[{ required: true, message: '请输入配置值' }]}
          >
            <TextArea rows={3} placeholder="请输入配置值" />
          </Form.Item>

          <Form.Item
            name="config_type"
            label="配置类型"
            rules={[{ required: true, message: '请选择配置类型' }]}
          >
            <Select placeholder="请选择配置类型">
              <Select.Option value="string">字符串</Select.Option>
              <Select.Option value="boolean">布尔值</Select.Option>
              <Select.Option value="integer">整数</Select.Option>
              <Select.Option value="decimal">小数</Select.Option>
              <Select.Option value="json">JSON</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="category"
            label="配置类别"
            rules={[{ required: true, message: '请选择配置类别' }]}
          >
            <Select placeholder="请选择配置类别">
              <Select.Option value="planning">计划管理</Select.Option>
              <Select.Option value="general">通用设置</Select.Option>
              <Select.Option value="ui">界面配置</Select.Option>
              <Select.Option value="system">系统设置</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={2} placeholder="请输入配置描述（可选）" />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

// 获取类别显示名称
const getCategoryDisplayName = (category: string) => {
  const categoryMap: Record<string, string> = {
    planning: '计划管理',
    general: '通用设置',
    ui: '界面配置',
    system: '系统设置',
  };
  return categoryMap[category] || category;
};

export default SystemConfigPage;
