import { useState, useEffect, useMemo } from 'react';
import { useAuthStore } from '@/store/auth';
import { apiClient } from '@/lib/api';

// 权限代码到页面路径的映射
const PERMISSION_TO_PAGE_MAP: Record<string, string> = {
  'PAGE_DASHBOARD': '/dashboard',
  'PAGE_PROJECTS': '/projects',
  'PAGE_PARTS': '/parts',
  'PAGE_MACHINES': '/machines',
  'PAGE_WORK_ORDERS': '/work-orders',
  'PAGE_PLAN_TASKS': '/plan-tasks',
  'PAGE_EXECUTION': '/production-center',
  'PAGE_OPERATOR_EXECUTION': '/production-center',
  'PAGE_QUALITY': '/quality',
  'PAGE_BOM': '/bom',
  'PAGE_ROUTINGS': '/routings',
  'PAGE_USERS': '/users',
  'PAGE_ROLE_PERMISSIONS': '/role-permissions',
};

// 页面路径到权限代码的映射
const PAGE_TO_PERMISSION_MAP: Record<string, string> = Object.fromEntries(
  Object.entries(PERMISSION_TO_PAGE_MAP).map(([permission, page]) => [page, permission])
);

interface UserPermission {
  id: number;
  permission_code: string;
  permission_name: string;
  description: string;
  category: string;
  granted: boolean;
}

interface UserPermissions {
  [roleId: string]: string[] | UserPermission[];
}

/**
 * 权限管理Hook
 * 基于后端权限API的动态权限检查
 */
export const usePermissions = () => {
  const { user, isAuthenticated } = useAuthStore();
  const [permissions, setPermissions] = useState<UserPermissions>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取用户权限
  const fetchUserPermissions = async () => {
    if (!user || !isAuthenticated) {
      setPermissions({});
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // 使用新的用户权限API端点
      const userPermissionsResponse = await apiClient.getUserPermissions();
      const rolePermissions: UserPermissions = userPermissionsResponse.permissions || {};

      setPermissions(rolePermissions);
    } catch (err) {
      console.error('Failed to fetch user permissions:', err);
      setError('获取权限信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 当用户信息变化时重新获取权限
  useEffect(() => {
    fetchUserPermissions();
  }, [user, isAuthenticated]);

  // 获取所有已授权的权限
  const grantedPermissions = useMemo(() => {
    const allPermissions: string[] = [];
    Object.values(permissions).forEach(rolePermissions => {
      // 处理后端返回的字符串数组格式
      if (Array.isArray(rolePermissions)) {
        // 检查数组元素类型
        if (rolePermissions.length > 0 && typeof rolePermissions[0] === 'string') {
          allPermissions.push(...(rolePermissions as string[]));
        } else {
          // 处理对象数组格式（向后兼容）
          allPermissions.push(...(rolePermissions as UserPermission[]).filter(p => p.granted).map(p => p.permission_code));
        }
      }
    });
    return [...new Set(allPermissions)]; // 去重
  }, [permissions]);

  // 检查是否有指定权限
  const hasPermission = (permissionCode: string): boolean => {
    return grantedPermissions.includes(permissionCode);
  };

  // 检查是否有任一权限
  const hasAnyPermission = (permissionCodes: string[]): boolean => {
    return permissionCodes.some(code => hasPermission(code));
  };

  // 检查页面访问权限
  const hasPageAccess = (pagePath: string): boolean => {
    // 管理员有所有权限
    if (user?.roles?.includes('admin')) {
      return true;
    }

    const permissionCode = PAGE_TO_PERMISSION_MAP[pagePath];
    if (!permissionCode) {
      return false;
    }

    return hasPermission(permissionCode);
  };

  // 获取用户可访问的页面列表
  const getAccessiblePages = (): string[] => {
    // 管理员可以访问所有页面
    if (user?.roles?.includes('admin')) {
      return Object.keys(PAGE_TO_PERMISSION_MAP);
    }

    const accessiblePages: string[] = [];
    
    grantedPermissions.forEach(permission => {
      const pagePath = PERMISSION_TO_PAGE_MAP[permission];
      if (pagePath) {
        accessiblePages.push(pagePath);
      }
    });

    return [...new Set(accessiblePages)];
  };

  // 检查操作权限
  const hasOperationPermission = (operationCode: string): boolean => {
    return hasPermission(operationCode);
  };

  // 获取权限按类别分组
  const getPermissionsByCategory = () => {
    const categorized: Record<string, string[]> = {};

    grantedPermissions.forEach(permission => {
      // 根据权限代码推断类别
      let category = 'other';
      if (permission.startsWith('PAGE_')) {
        category = 'page';
      } else if (permission.includes('_TASK')) {
        category = 'operation';
      } else if (permission.startsWith('CREATE_') || permission.startsWith('EDIT_') || permission.startsWith('DELETE_')) {
        category = 'crud';
      } else if (permission.startsWith('MANAGE_')) {
        category = 'management';
      }

      if (!categorized[category]) {
        categorized[category] = [];
      }
      categorized[category].push(permission);
    });

    return categorized;
  };

  return {
    permissions,
    grantedPermissions,
    loading,
    error,
    hasPermission,
    hasAnyPermission,
    hasPageAccess,
    hasOperationPermission,
    getAccessiblePages,
    getPermissionsByCategory,
    refetch: fetchUserPermissions,
  };
};

export default usePermissions;
