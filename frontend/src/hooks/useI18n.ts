/**
 * 国际化Hook
 * 提供便捷的国际化文本获取方法
 */

import { useTranslation } from 'react-i18next';

export const useI18n = () => {
  const { t, i18n } = useTranslation();

  // 通用文本
  const common = {
    create: () => t('common.create'),
    edit: () => t('common.edit'),
    delete: () => t('common.delete'),
    save: () => t('common.save'),
    cancel: () => t('common.cancel'),
    confirm: () => t('common.confirm'),
    submit: () => t('common.submit'),
    reset: () => t('common.reset'),
    search: () => t('common.search'),
    filter: () => t('common.filter'),
    export: () => t('common.export'),
    import: () => t('common.import'),
    refresh: () => t('common.refresh'),
    loading: () => t('common.loading'),
    noData: () => t('common.noData'),
    success: () => t('common.success'),
    error: () => t('common.error'),
    warning: () => t('common.warning'),
    info: () => t('common.info'),
    totalRecords: (total: number) => t('common.totalRecords', { total }),
    pageSize: () => t('common.pageSize'),
    requiredField: () => t('common.requiredField'),
    invalidFormat: () => t('common.invalidFormat'),
    deleteConfirm: () => t('common.deleteConfirm'),
    saveConfirm: () => t('common.saveConfirm'),
  };

  // 仪表盘文本
  const dashboard = {
    dashboard: () => t('dashboard.dashboard'),
    overview: () => t('dashboard.overview'),
    statistics: () => t('dashboard.statistics'),
    metrics: () => t('dashboard.metrics'),
    operatorDashboard: () => t('dashboard.operatorDashboard'),
    welcomeMessage: (name: string) => t('dashboard.welcomeMessage', { name }),
    skillGroups: () => t('dashboard.skillGroups'),
    unassigned: () => t('dashboard.unassigned'),
    dailyWorkHours: (hours: number) => t('dashboard.dailyWorkHours', { hours }),
    completedTasksToday: () => t('dashboard.completedTasksToday'),
    qualityRate: () => t('dashboard.qualityRate'),
    workEfficiency: () => t('dashboard.workEfficiency'),
    workHours: () => t('dashboard.workHours'),
    myMachines: () => t('dashboard.myMachines'),
    bindMachine: () => t('dashboard.bindMachine'),
    qualityDashboard: () => t('dashboard.qualityDashboard'),
    inspectionOverview: () => t('dashboard.inspectionOverview'),
    qualityAlert: () => t('dashboard.qualityAlert'),
    qualityAlertMessage: (rate: number) => t('dashboard.qualityAlertMessage', { rate }),
    plannerDashboard: () => t('dashboard.plannerDashboard'),
    planOverview: () => t('dashboard.planOverview'),
    urgentTasks: () => t('dashboard.urgentTasks'),
    equipmentUtilization: () => t('dashboard.equipmentUtilization'),
    totalWorkOrders: () => t('dashboard.totalWorkOrders'),
    inProgressOrders: () => t('dashboard.inProgressOrders'),
    pendingTasks: () => t('dashboard.pendingTasks'),
    machineUtilization: () => t('dashboard.machineUtilization'),
    productionEfficiency: () => t('dashboard.productionEfficiency'),
  };

  // 角色文本
  const roles = {
    admin: () => t('roles.admin'),
    processEngineer: () => t('roles.processEngineer'),
    planner: () => t('roles.planner'),
    operator: () => t('roles.operator'),
    qualityInspector: () => t('roles.qualityInspector'),
    viewer: () => t('roles.viewer'),
    adminDesc: () => t('roles.adminDesc'),
    processEngineerDesc: () => t('roles.processEngineerDesc'),
    plannerDesc: () => t('roles.plannerDesc'),
    operatorDesc: () => t('roles.operatorDesc'),
    qualityInspectorDesc: () => t('roles.qualityInspectorDesc'),
    viewerDesc: () => t('roles.viewerDesc'),
    roleManagement: () => t('roles.roleManagement'),
    userManagement: () => t('roles.userManagement'),
    rolePermissions: () => t('roles.rolePermissions'),
    noRole: () => t('roles.noRole'),
  };

  // 技能组文本
  const skills = {
    cncMachining: () => t('skills.cncMachining'),
    milling: () => t('skills.milling'),
    turning: () => t('skills.turning'),
    grinding: () => t('skills.grinding'),
    assembly: () => t('skills.assembly'),
    qualityControl: () => t('skills.qualityControl'),
    packaging: () => t('skills.packaging'),
    skillManagement: () => t('skills.skillManagement'),
    noSkills: () => t('skills.noSkills'),
    skillGroups: () => t('skills.skillGroups'),
  };

  // 状态文本
  const status = {
    task: {
      planned: () => t('status.task.planned'),
      scheduled: () => t('status.task.scheduled'),
      inProgress: () => t('status.task.inProgress'),
      completed: () => t('status.task.completed'),
      cancelled: () => t('status.task.cancelled'),
      onHold: () => t('status.task.onHold'),
    },
    machine: {
      running: () => t('status.machine.running'),
      idle: () => t('status.machine.idle'),
      maintenance: () => t('status.machine.maintenance'),
      error: () => t('status.machine.error'),
      setup: () => t('status.machine.setup'),
    },
    order: {
      pending: () => t('status.order.pending'),
      planned: () => t('status.order.planned'),
      inProgress: () => t('status.order.inProgress'),
      completed: () => t('status.order.completed'),
      cancelled: () => t('status.order.cancelled'),
    },
    inspection: {
      pending: () => t('status.inspection.pending'),
      inProgress: () => t('status.inspection.inProgress'),
      completed: () => t('status.inspection.completed'),
      cancelled: () => t('status.inspection.cancelled'),
      pass: () => t('status.inspection.pass'),
      fail: () => t('status.inspection.fail'),
      conditional: () => t('status.inspection.conditional'),
      resultPending: () => t('status.inspection.resultPending'),
    },
  };

  // 设备文本
  const equipment = {
    cncEquipment: () => t('equipment.cncEquipment'),
    millingMachine: () => t('equipment.millingMachine'),
    lathe: () => t('equipment.lathe'),
    grinder: () => t('equipment.grinder'),
    total: () => t('equipment.total'),
    running: () => t('equipment.running'),
    idle: () => t('equipment.idle'),
    maintenance: () => t('equipment.maintenance'),
    utilization: () => t('equipment.utilization'),
  };

  // 权限文本
  const permissions = {
    createProject: () => t('permissions.createProject'),
    createPart: () => t('permissions.createPart'),
    createMachine: () => t('permissions.createMachine'),
    createWorkOrder: () => t('permissions.createWorkOrder'),
    createPlanTask: () => t('permissions.createPlanTask'),
    createRouting: () => t('permissions.createRouting'),
    editProject: () => t('permissions.editProject'),
    editPart: () => t('permissions.editPart'),
    editMachine: () => t('permissions.editMachine'),
    editWorkOrder: () => t('permissions.editWorkOrder'),
    editPlanTask: () => t('permissions.editPlanTask'),
    editRouting: () => t('permissions.editRouting'),
    deleteProject: () => t('permissions.deleteProject'),
    deletePart: () => t('permissions.deletePart'),
    deleteMachine: () => t('permissions.deleteMachine'),
    deleteWorkOrder: () => t('permissions.deleteWorkOrder'),
    deletePlanTask: () => t('permissions.deletePlanTask'),
    deleteRouting: () => t('permissions.deleteRouting'),
    updateMachineStatus: () => t('permissions.updateMachineStatus'),
    submitQualityData: () => t('permissions.submitQualityData'),
    executeTask: () => t('permissions.executeTask'),
    startTask: () => t('permissions.startTask'),
    completeTask: () => t('permissions.completeTask'),
    pauseTask: () => t('permissions.pauseTask'),
    manageUsers: () => t('permissions.manageUsers'),
    manageRoles: () => t('permissions.manageRoles'),
    manageSkills: () => t('permissions.manageSkills'),
  };

  // 语言切换
  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  // 获取当前语言
  const currentLanguage = i18n.language;

  return {
    t,
    common,
    dashboard,
    roles,
    skills,
    status,
    equipment,
    permissions,
    changeLanguage,
    currentLanguage,
  };
};
