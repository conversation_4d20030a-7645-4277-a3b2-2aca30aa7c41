import { useEffect, useRef, useCallback } from 'react';

interface TouchPoint {
  x: number;
  y: number;
  timestamp: number;
}

interface GestureState {
  isActive: boolean;
  startPoint: TouchPoint | null;
  currentPoint: TouchPoint | null;
  lastPoint: TouchPoint | null;
  velocity: { x: number; y: number };
  scale: number;
  rotation: number;
}

interface TouchGestureOptions {
  onTap?: (point: TouchPoint) => void;
  onDoubleTap?: (point: TouchPoint) => void;
  onLongPress?: (point: TouchPoint) => void;
  onPan?: (delta: { x: number; y: number }, velocity: { x: number; y: number }) => void;
  onPanStart?: (point: TouchPoint) => void;
  onPanEnd?: (point: TouchPoint, velocity: { x: number; y: number }) => void;
  onPinch?: (scale: number, center: TouchPoint) => void;
  onPinchStart?: (center: TouchPoint) => void;
  onPinchEnd?: (center: TouchPoint) => void;
  onRotate?: (rotation: number, center: TouchPoint) => void;
  onSwipe?: (direction: 'left' | 'right' | 'up' | 'down', velocity: { x: number; y: number }) => void;
  
  // 配置选项
  tapTimeout?: number;
  doubleTapTimeout?: number;
  longPressTimeout?: number;
  swipeThreshold?: number;
  panThreshold?: number;
  preventDefaultTouch?: boolean;
}

/**
 * 触摸手势识别Hook
 * 支持点击、双击、长按、拖拽、缩放、旋转、滑动等手势
 */
export const useTouchGestures = (options: TouchGestureOptions = {}) => {
  const {
    onTap,
    onDoubleTap,
    onLongPress,
    onPan,
    onPanStart,
    onPanEnd,
    onPinch,
    onPinchStart,
    onPinchEnd,
    onRotate,
    onSwipe,
    tapTimeout = 300,
    doubleTapTimeout = 300,
    longPressTimeout = 500,
    swipeThreshold = 50,
    panThreshold = 10,
    preventDefaultTouch = true,
  } = options;

  const gestureState = useRef<GestureState>({
    isActive: false,
    startPoint: null,
    currentPoint: null,
    lastPoint: null,
    velocity: { x: 0, y: 0 },
    scale: 1,
    rotation: 0,
  });

  const tapTimer = useRef<NodeJS.Timeout | null>(null);
  const longPressTimer = useRef<NodeJS.Timeout | null>(null);
  const lastTapTime = useRef<number>(0);
  const touchCount = useRef<number>(0);
  const initialDistance = useRef<number>(0);
  const initialAngle = useRef<number>(0);

  // 计算两点距离
  const getDistance = useCallback((touch1: Touch, touch2: Touch) => {
    const dx = touch1.clientX - touch2.clientX;
    const dy = touch1.clientY - touch2.clientY;
    return Math.sqrt(dx * dx + dy * dy);
  }, []);

  // 计算两点角度
  const getAngle = useCallback((touch1: Touch, touch2: Touch) => {
    const dx = touch1.clientX - touch2.clientX;
    const dy = touch1.clientY - touch2.clientY;
    return Math.atan2(dy, dx) * 180 / Math.PI;
  }, []);

  // 计算两点中心
  const getCenter = useCallback((touch1: Touch, touch2: Touch): TouchPoint => {
    return {
      x: (touch1.clientX + touch2.clientX) / 2,
      y: (touch1.clientY + touch2.clientY) / 2,
      timestamp: Date.now(),
    };
  }, []);

  // 计算速度
  const calculateVelocity = useCallback((current: TouchPoint, last: TouchPoint) => {
    const timeDelta = current.timestamp - last.timestamp;
    if (timeDelta === 0) return { x: 0, y: 0 };
    
    return {
      x: (current.x - last.x) / timeDelta,
      y: (current.y - last.y) / timeDelta,
    };
  }, []);

  // 处理触摸开始
  const handleTouchStart = useCallback((event: TouchEvent) => {
    if (preventDefaultTouch) {
      event.preventDefault();
    }

    const touches = event.touches;
    touchCount.current = touches.length;

    const point: TouchPoint = {
      x: touches[0].clientX,
      y: touches[0].clientY,
      timestamp: Date.now(),
    };

    gestureState.current = {
      ...gestureState.current,
      isActive: true,
      startPoint: point,
      currentPoint: point,
      lastPoint: point,
    };

    // 清除之前的定时器
    if (tapTimer.current) {
      clearTimeout(tapTimer.current);
      tapTimer.current = null;
    }

    // 设置长按定时器
    if (onLongPress && touches.length === 1) {
      longPressTimer.current = setTimeout(() => {
        onLongPress(point);
        longPressTimer.current = null;
      }, longPressTimeout);
    }

    // 多点触摸处理
    if (touches.length === 2) {
      initialDistance.current = getDistance(touches[0], touches[1]);
      initialAngle.current = getAngle(touches[0], touches[1]);
      gestureState.current.scale = 1;
      gestureState.current.rotation = 0;

      const center = getCenter(touches[0], touches[1]);
      onPinchStart?.(center);
    }

    // 单点触摸开始拖拽
    if (touches.length === 1) {
      onPanStart?.(point);
    }
  }, [onLongPress, onPinchStart, onPanStart, longPressTimeout, preventDefaultTouch, getDistance, getAngle, getCenter]);

  // 处理触摸移动
  const handleTouchMove = useCallback((event: TouchEvent) => {
    if (preventDefaultTouch) {
      event.preventDefault();
    }

    if (!gestureState.current.isActive) return;

    const touches = event.touches;
    const point: TouchPoint = {
      x: touches[0].clientX,
      y: touches[0].clientY,
      timestamp: Date.now(),
    };

    const { startPoint, lastPoint } = gestureState.current;
    if (!startPoint || !lastPoint) return;

    // 清除长按定时器（移动时取消长按）
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }

    // 计算移动距离
    const deltaX = point.x - lastPoint.x;
    const deltaY = point.y - lastPoint.y;
    const totalDeltaX = point.x - startPoint.x;
    const totalDeltaY = point.y - startPoint.y;
    const totalDistance = Math.sqrt(totalDeltaX * totalDeltaX + totalDeltaY * totalDeltaY);

    // 更新状态
    gestureState.current.lastPoint = gestureState.current.currentPoint;
    gestureState.current.currentPoint = point;
    gestureState.current.velocity = calculateVelocity(point, lastPoint);

    // 多点触摸处理（缩放和旋转）
    if (touches.length === 2) {
      const currentDistance = getDistance(touches[0], touches[1]);
      const currentAngle = getAngle(touches[0], touches[1]);
      const center = getCenter(touches[0], touches[1]);

      // 缩放
      const scale = currentDistance / initialDistance.current;
      gestureState.current.scale = scale;
      onPinch?.(scale, center);

      // 旋转
      const rotation = currentAngle - initialAngle.current;
      gestureState.current.rotation = rotation;
      onRotate?.(rotation, center);
    }

    // 单点触摸处理（拖拽）
    if (touches.length === 1 && totalDistance > panThreshold) {
      onPan?.({ x: deltaX, y: deltaY }, gestureState.current.velocity);
    }
  }, [onPan, onPinch, onRotate, panThreshold, preventDefaultTouch, calculateVelocity, getDistance, getAngle, getCenter]);

  // 处理触摸结束
  const handleTouchEnd = useCallback((event: TouchEvent) => {
    if (preventDefaultTouch) {
      event.preventDefault();
    }

    const { startPoint, currentPoint, velocity } = gestureState.current;
    if (!startPoint || !currentPoint) return;

    // 清除定时器
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }

    const totalDeltaX = currentPoint.x - startPoint.x;
    const totalDeltaY = currentPoint.y - startPoint.y;
    const totalDistance = Math.sqrt(totalDeltaX * totalDeltaX + totalDeltaY * totalDeltaY);

    // 多点触摸结束
    if (touchCount.current === 2) {
      const center = getCenter(event.changedTouches[0], event.changedTouches[1] || event.changedTouches[0]);
      onPinchEnd?.(center);
    }

    // 单点触摸结束
    if (touchCount.current === 1) {
      onPanEnd?.(currentPoint, velocity);

      // 判断是否为滑动
      if (totalDistance > swipeThreshold) {
        const absX = Math.abs(totalDeltaX);
        const absY = Math.abs(totalDeltaY);
        
        if (absX > absY) {
          // 水平滑动
          const direction = totalDeltaX > 0 ? 'right' : 'left';
          onSwipe?.(direction, velocity);
        } else {
          // 垂直滑动
          const direction = totalDeltaY > 0 ? 'down' : 'up';
          onSwipe?.(direction, velocity);
        }
      } else if (totalDistance < panThreshold) {
        // 判断是否为点击
        const now = Date.now();
        const timeSinceLastTap = now - lastTapTime.current;

        if (timeSinceLastTap < doubleTapTimeout && onDoubleTap) {
          // 双击
          onDoubleTap(currentPoint);
          lastTapTime.current = 0;
        } else {
          // 单击
          lastTapTime.current = now;
          tapTimer.current = setTimeout(() => {
            onTap?.(currentPoint);
            tapTimer.current = null;
          }, tapTimeout);
        }
      }
    }

    // 重置状态
    gestureState.current = {
      isActive: false,
      startPoint: null,
      currentPoint: null,
      lastPoint: null,
      velocity: { x: 0, y: 0 },
      scale: 1,
      rotation: 0,
    };
  }, [onTap, onDoubleTap, onSwipe, onPanEnd, onPinchEnd, swipeThreshold, panThreshold, doubleTapTimeout, tapTimeout, preventDefaultTouch, getCenter]);

  // 绑定事件监听器
  const bindGestures = useCallback((element: HTMLElement | null) => {
    if (!element) return;

    element.addEventListener('touchstart', handleTouchStart, { passive: false });
    element.addEventListener('touchmove', handleTouchMove, { passive: false });
    element.addEventListener('touchend', handleTouchEnd, { passive: false });

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  }, [handleTouchStart, handleTouchMove, handleTouchEnd]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (tapTimer.current) {
        clearTimeout(tapTimer.current);
      }
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current);
      }
    };
  }, []);

  return {
    bindGestures,
    gestureState: gestureState.current,
  };
};

export default useTouchGestures;
