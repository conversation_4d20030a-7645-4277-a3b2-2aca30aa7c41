import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiClient } from '@/lib/api';
import { TokenManager } from '@/utils/tokenManager';
import type { User, LoginRequest } from '@/types/api';

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => void;
  getCurrentUser: () => Promise<void>;
  setLoading: (loading: boolean) => void;
  setUser: (user: User) => void;
  checkTokenValidity: () => boolean;
  initializeAuth: () => void;
  refreshToken: () => Promise<boolean>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,

      login: async (credentials: LoginRequest) => {
        try {
          set({ isLoading: true });
          console.log('Auth store: Starting login...');
          const response = await apiClient.login(credentials);
          console.log('Auth store: Login response received');

          // 使用TokenManager存储token
          TokenManager.setToken(response.token);

          set({
            user: response.user,
            token: response.token,
            isAuthenticated: true,
            isLoading: false,
          });
          console.log('Auth store: State updated, isAuthenticated:', true);
        } catch (error) {
          console.error('Auth store: Login failed:', error);
          set({ isLoading: false });
          throw error;
        }
      },

      logout: () => {
        console.log('Auth store: Logging out...');
        TokenManager.clearToken();
        set({
          user: null,
          token: null,
          isAuthenticated: false,
        });
      },

      getCurrentUser: async () => {
        try {
          const token = TokenManager.getValidToken();
          if (!token) {
            console.log('Auth store: No valid token found');
            get().logout();
            return;
          }

          set({ isLoading: true });
          console.log('Auth store: Getting current user...');
          const user = await apiClient.getCurrentUser();

          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
          });
          console.log('Auth store: Current user retrieved successfully');
        } catch (error) {
          console.error('Auth store: Failed to get current user:', error);
          // If getting current user fails, clear auth state
          get().logout();
          set({ isLoading: false });
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setUser: (user: User) => {
        set({ user });
      },

      checkTokenValidity: () => {
        const token = TokenManager.getValidToken();
        const isValid = !!token;

        if (!isValid && get().isAuthenticated) {
          console.log('Auth store: Token expired, logging out...');
          get().logout();
        }

        return isValid;
      },

      initializeAuth: () => {
        console.log('Auth store: Initializing authentication...');

        // 清理过期的token
        const wasExpired = TokenManager.cleanupExpiredToken();
        if (wasExpired) {
          console.log('Auth store: Expired token cleaned up');
          set({
            user: null,
            token: null,
            isAuthenticated: false,
          });
          return;
        }

        // 检查是否有有效token
        const token = TokenManager.getValidToken();
        if (token && !get().isAuthenticated) {
          console.log('Auth store: Valid token found, getting current user...');
          get().getCurrentUser();
        } else if (!token) {
          console.log('Auth store: No valid token found');
          set({
            user: null,
            token: null,
            isAuthenticated: false,
          });
        }
      },

      refreshToken: async () => {
        try {
          console.log('Auth store: Refreshing token...');
          const response = await apiClient.refreshToken();

          // 使用TokenManager存储新token
          TokenManager.setToken(response.token);

          set({
            token: response.token,
          });

          console.log('Auth store: Token refreshed successfully');
          return true;
        } catch (error) {
          console.error('Auth store: Token refresh failed:', error);
          // 如果刷新失败，登出用户
          get().logout();
          return false;
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        token: state.token,
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
