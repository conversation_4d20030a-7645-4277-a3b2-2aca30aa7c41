/**
 * 文本常量定义
 * 统一管理系统中的所有UI文本，便于维护和国际化
 */

// 通用文本
export const COMMON_TEXTS = {
  // 操作按钮
  CREATE: '创建',
  EDIT: '编辑',
  DELETE: '删除',
  SAVE: '保存',
  CANCEL: '取消',
  CONFIRM: '确认',
  SUBMIT: '提交',
  RESET: '重置',
  SEARCH: '搜索',
  FILTER: '筛选',
  EXPORT: '导出',
  IMPORT: '导入',
  REFRESH: '刷新',
  
  // 状态文本
  LOADING: '加载中...',
  NO_DATA: '暂无数据',
  SUCCESS: '成功',
  ERROR: '错误',
  WARNING: '警告',
  INFO: '信息',
  
  // 分页
  TOTAL_RECORDS: '共 {total} 条记录',
  PAGE_SIZE: '每页条数',
  
  // 表单验证
  REQUIRED_FIELD: '此字段为必填项',
  INVALID_FORMAT: '格式不正确',
  
  // 确认对话框
  DELETE_CONFIRM: '确定要删除吗？',
  SAVE_CONFIRM: '确定要保存吗？',
} as const;

// 仪表盘相关文本
export const DASHBOARD_TEXTS = {
  // 通用仪表盘
  DASHBOARD: '仪表盘',
  OVERVIEW: '概览',
  STATISTICS: '统计',
  METRICS: '指标',
  
  // 操作员仪表盘
  OPERATOR_DASHBOARD: '我的工作台',
  WELCOME_MESSAGE: '欢迎，{name}',
  SKILL_GROUPS: '技能组',
  UNASSIGNED: '未分配',
  DAILY_WORK_HOURS: '今日工作时间：{hours}小时',
  
  // 统计指标
  COMPLETED_TASKS_TODAY: '今日完成任务',
  QUALITY_RATE: '质量合格率',
  WORK_EFFICIENCY: '工作效率',
  WORK_HOURS: '工作时长',
  MY_MACHINES: '我的设备',
  BIND_MACHINE: '绑定设备',
  
  // 质检员仪表盘
  QUALITY_DASHBOARD: '质量管理仪表板',
  INSPECTION_OVERVIEW: '今日检验概况 | 合格率目标：≥98%',
  QUALITY_ALERT: '质量警报',
  QUALITY_ALERT_MESSAGE: '今日合格率 {rate}% 低于目标值 98%，请关注质量问题',
  
  // 计划员仪表盘
  PLANNER_DASHBOARD: '生产计划仪表板',
  PLAN_OVERVIEW: '今日计划执行概况 | 设备利用率目标：≥85%',
  URGENT_TASKS: '紧急任务跟踪',
  EQUIPMENT_UTILIZATION: '设备利用率概览',
  
  // 默认仪表盘指标
  TOTAL_WORK_ORDERS: '总工单数',
  IN_PROGRESS_ORDERS: '进行中工单',
  PENDING_TASKS: '待处理任务',
  MACHINE_UTILIZATION: '设备利用率',
  PRODUCTION_EFFICIENCY: '生产效率',
} as const;

// 角色相关文本
export const ROLE_TEXTS = {
  // 角色名称
  ADMIN: '系统管理员',
  PROCESS_ENGINEER: '工艺工程师',
  PLANNER: '生产计划员',
  OPERATOR: '操作员',
  QUALITY_INSPECTOR: '质检员',
  VIEWER: '查看者',
  
  // 角色描述
  ADMIN_DESC: '拥有系统所有权限，可以管理用户、角色和系统配置',
  PROCESS_ENGINEER_DESC: '负责工艺设计、路径规划和技术文档管理',
  PLANNER_DESC: '负责生产计划制定、任务调度和资源分配',
  OPERATOR_DESC: '负责具体生产执行，只能开始/暂停/完成分配给自己的任务',
  QUALITY_INSPECTOR_DESC: '负责质量检验和质量数据管理',
  VIEWER_DESC: '只能查看仪表板，无操作权限',
  
  // 角色管理
  ROLE_MANAGEMENT: '角色管理',
  USER_MANAGEMENT: '用户管理',
  ROLE_PERMISSIONS: '角色权限',
  NO_ROLE: '无角色',
} as const;

// 技能组相关文本
export const SKILL_TEXTS = {
  // 技能组名称映射
  CNC_MACHINING: 'CNC加工',
  MILLING: '铣削加工',
  TURNING: '车削加工',
  GRINDING: '磨削加工',
  ASSEMBLY: '装配',
  QUALITY_CONTROL: '质量控制',
  PACKAGING: '包装',
  
  // 技能管理
  SKILL_MANAGEMENT: '技能组管理',
  NO_SKILLS: '无技能',
  SKILL_GROUPS: '技能组',
} as const;

// 状态相关文本
export const STATUS_TEXTS = {
  // 任务状态
  TASK_PLANNED: '已计划',
  TASK_SCHEDULED: '已调度',
  TASK_IN_PROGRESS: '进行中',
  TASK_COMPLETED: '已完成',
  TASK_CANCELLED: '已取消',
  TASK_ON_HOLD: '暂停',
  
  // 设备状态
  MACHINE_RUNNING: '运行中',
  MACHINE_IDLE: '空闲',
  MACHINE_MAINTENANCE: '维护中',
  MACHINE_ERROR: '故障',
  MACHINE_SETUP: '调试中',
  
  // 工单状态
  ORDER_PENDING: '待处理',
  ORDER_PLANNED: '已计划',
  ORDER_IN_PROGRESS: '进行中',
  ORDER_COMPLETED: '已完成',
  ORDER_CANCELLED: '已取消',
  
  // 质检状态
  INSPECTION_PENDING: '待检验',
  INSPECTION_IN_PROGRESS: '检验中',
  INSPECTION_COMPLETED: '已完成',
  INSPECTION_CANCELLED: '已取消',
  
  // 质检结果
  INSPECTION_PASS: '合格',
  INSPECTION_FAIL: '不合格',
  INSPECTION_CONDITIONAL: '有条件通过',
  INSPECTION_RESULT_PENDING: '待确定',
} as const;

// 设备相关文本
export const EQUIPMENT_TEXTS = {
  CNC_EQUIPMENT: 'CNC设备',
  MILLING_MACHINE: '铣床',
  LATHE: '车床',
  GRINDER: '磨床',
  
  TOTAL: '总数',
  RUNNING: '运行',
  IDLE: '空闲',
  MAINTENANCE: '维护',
  UTILIZATION: '利用率',
} as const;

// 权限功能文本
export const PERMISSION_TEXTS = {
  CREATE_PROJECT: '创建项目',
  CREATE_PART: '创建零件',
  CREATE_MACHINE: '创建设备',
  CREATE_WORK_ORDER: '创建工单',
  CREATE_PLAN_TASK: '创建计划任务',
  CREATE_ROUTING: '创建工艺路径',
  
  EDIT_PROJECT: '编辑项目',
  EDIT_PART: '编辑零件',
  EDIT_MACHINE: '编辑设备',
  EDIT_WORK_ORDER: '编辑工单',
  EDIT_PLAN_TASK: '编辑计划任务',
  EDIT_ROUTING: '编辑工艺路径',
  
  DELETE_PROJECT: '删除项目',
  DELETE_PART: '删除零件',
  DELETE_MACHINE: '删除设备',
  DELETE_WORK_ORDER: '删除工单',
  DELETE_PLAN_TASK: '删除计划任务',
  DELETE_ROUTING: '删除工艺路径',
  
  UPDATE_MACHINE_STATUS: '更新设备状态',
  SUBMIT_QUALITY_DATA: '提交质量数据',
  EXECUTE_TASK: '执行任务',
  START_TASK: '开始任务',
  COMPLETE_TASK: '完成任务',
  PAUSE_TASK: '暂停任务',
  
  MANAGE_USERS: '管理用户',
  MANAGE_ROLES: '管理角色',
  MANAGE_SKILLS: '管理技能组',
} as const;
