/**
 * Token管理工具类
 * 处理JWT token的存储、验证、过期检测和自动清理
 */

interface TokenPayload {
  sub: string;
  username: string;
  roles: string[];
  skills: string[];
  exp: number;
  iat: number;
}

export class TokenManager {
  private static readonly TOKEN_KEY = 'token';
  private static readonly TOKEN_EXPIRY_KEY = 'token_expiry';
  private static readonly REFRESH_THRESHOLD = 5 * 60 * 1000; // 5分钟前刷新
  private static refreshPromise: Promise<string> | null = null;

  /**
   * 解析JWT token获取payload
   */
  static parseToken(token: string): TokenPayload | null {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        return null;
      }

      const payload = JSON.parse(atob(parts[1]));
      return payload;
    } catch (error) {
      console.error('Failed to parse token:', error);
      return null;
    }
  }

  /**
   * 检查token是否过期
   */
  static isTokenExpired(token: string): boolean {
    const payload = this.parseToken(token);
    if (!payload) {
      return true;
    }

    const now = Math.floor(Date.now() / 1000);
    return payload.exp <= now;
  }

  /**
   * 检查token是否即将过期（需要刷新）
   */
  static shouldRefreshToken(token: string): boolean {
    const payload = this.parseToken(token);
    if (!payload) {
      return false;
    }

    const now = Math.floor(Date.now() / 1000);
    const timeUntilExpiry = (payload.exp - now) * 1000;
    return timeUntilExpiry <= this.REFRESH_THRESHOLD;
  }

  /**
   * 存储token和过期时间
   */
  static setToken(token: string): void {
    const payload = this.parseToken(token);
    if (!payload) {
      throw new Error('Invalid token format');
    }

    localStorage.setItem(this.TOKEN_KEY, token);
    localStorage.setItem(this.TOKEN_EXPIRY_KEY, payload.exp.toString());
  }

  /**
   * 获取token
   */
  static getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  /**
   * 获取有效的token（自动检查过期）
   */
  static getValidToken(): string | null {
    const token = this.getToken();
    if (!token) {
      return null;
    }

    if (this.isTokenExpired(token)) {
      this.clearToken();
      return null;
    }

    return token;
  }

  /**
   * 清除token和相关数据
   */
  static clearToken(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.TOKEN_EXPIRY_KEY);
  }

  /**
   * 获取token过期时间
   */
  static getTokenExpiry(): number | null {
    const expiry = localStorage.getItem(this.TOKEN_EXPIRY_KEY);
    return expiry ? parseInt(expiry, 10) : null;
  }

  /**
   * 获取token剩余有效时间（毫秒）
   */
  static getTokenRemainingTime(): number {
    const expiry = this.getTokenExpiry();
    if (!expiry) {
      return 0;
    }

    const now = Math.floor(Date.now() / 1000);
    const remaining = (expiry - now) * 1000;
    return Math.max(0, remaining);
  }

  /**
   * 检查localStorage中是否有过期的token并清理
   */
  static cleanupExpiredToken(): boolean {
    const token = this.getToken();
    if (!token) {
      return false;
    }

    if (this.isTokenExpired(token)) {
      this.clearToken();
      return true;
    }

    return false;
  }

  /**
   * 设置token过期监听器
   */
  static setupTokenExpiryWatcher(onExpiry: () => void): () => void {
    const checkInterval = 60 * 1000; // 每分钟检查一次
    
    const intervalId = setInterval(() => {
      const token = this.getToken();
      if (token && this.isTokenExpired(token)) {
        this.clearToken();
        onExpiry();
      }
    }, checkInterval);

    // 返回清理函数
    return () => clearInterval(intervalId);
  }

  /**
   * 自动刷新token（如果需要）
   */
  static async autoRefreshToken(refreshFn: () => Promise<string>): Promise<string | null> {
    const token = this.getToken();
    if (!token) {
      return null;
    }

    if (this.isTokenExpired(token)) {
      this.clearToken();
      return null;
    }

    if (!this.shouldRefreshToken(token)) {
      return token;
    }

    // 如果已经有刷新请求在进行中，等待它完成
    if (this.refreshPromise) {
      try {
        return await this.refreshPromise;
      } catch (error) {
        console.error('Token refresh failed:', error);
        this.refreshPromise = null;
        return token; // 返回原token
      }
    }

    // 开始新的刷新请求
    this.refreshPromise = this.performTokenRefresh(refreshFn);

    try {
      const newToken = await this.refreshPromise;
      this.refreshPromise = null;
      return newToken;
    } catch (error) {
      console.error('Token refresh failed:', error);
      this.refreshPromise = null;
      return token; // 返回原token
    }
  }

  /**
   * 执行token刷新
   */
  private static async performTokenRefresh(refreshFn: () => Promise<string>): Promise<string> {
    console.log('TokenManager: Refreshing token...');

    try {
      const newToken = await refreshFn();
      this.setToken(newToken);
      console.log('TokenManager: Token refreshed successfully');
      return newToken;
    } catch (error) {
      console.error('TokenManager: Token refresh failed:', error);
      throw error;
    }
  }

  /**
   * 获取token信息用于调试
   */
  static getTokenInfo(): {
    hasToken: boolean;
    isExpired: boolean;
    shouldRefresh: boolean;
    remainingTime: number;
    payload: TokenPayload | null;
  } {
    const token = this.getToken();

    if (!token) {
      return {
        hasToken: false,
        isExpired: true,
        shouldRefresh: false,
        remainingTime: 0,
        payload: null,
      };
    }

    const payload = this.parseToken(token);
    const isExpired = this.isTokenExpired(token);
    const shouldRefresh = !isExpired && this.shouldRefreshToken(token);
    const remainingTime = this.getTokenRemainingTime();

    return {
      hasToken: true,
      isExpired,
      shouldRefresh,
      remainingTime,
      payload,
    };
  }
}

export default TokenManager;
