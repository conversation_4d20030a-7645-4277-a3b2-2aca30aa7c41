/**
 * 文本处理工具函数
 * 统一处理各种状态、角色、技能组等的文本映射
 */

import { STATUS_TEXTS, ROLE_TEXTS, SKILL_TEXTS, PERMISSION_TEXTS } from '@/constants/texts';

/**
 * 获取任务状态的显示文本
 */
export const getTaskStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'planned': STATUS_TEXTS.TASK_PLANNED,
    'scheduled': STATUS_TEXTS.TASK_SCHEDULED,
    'in_progress': STATUS_TEXTS.TASK_IN_PROGRESS,
    'completed': STATUS_TEXTS.TASK_COMPLETED,
    'cancelled': STATUS_TEXTS.TASK_CANCELLED,
    'on_hold': STATUS_TEXTS.TASK_ON_HOLD,
  };
  return statusMap[status.toLowerCase()] || status;
};

/**
 * 获取任务状态的颜色
 */
export const getTaskStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    'planned': '#d9d9d9',
    'scheduled': '#faad14',
    'in_progress': '#1890ff',
    'completed': '#52c41a',
    'cancelled': '#ff4d4f',
    'on_hold': '#722ed1',
  };
  return colorMap[status.toLowerCase()] || '#d9d9d9';
};

/**
 * 获取任务状态的Ant Design标签类型
 */
export const getTaskStatusTagType = (status: string): string => {
  const tagTypeMap: Record<string, string> = {
    'planned': 'default',
    'scheduled': 'warning',
    'in_progress': 'processing',
    'completed': 'success',
    'cancelled': 'error',
    'on_hold': 'purple',
  };
  return tagTypeMap[status.toLowerCase()] || 'default';
};

/**
 * 获取设备状态的显示文本
 */
export const getMachineStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'running': STATUS_TEXTS.MACHINE_RUNNING,
    'idle': STATUS_TEXTS.MACHINE_IDLE,
    'maintenance': STATUS_TEXTS.MACHINE_MAINTENANCE,
    'error': STATUS_TEXTS.MACHINE_ERROR,
    'setup': STATUS_TEXTS.MACHINE_SETUP,
  };
  return statusMap[status.toLowerCase()] || status;
};

/**
 * 获取设备状态的颜色
 */
export const getMachineStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    'running': '#52c41a',
    'idle': '#faad14',
    'maintenance': '#ff4d4f',
    'error': '#ff4d4f',
    'setup': '#1890ff',
  };
  return colorMap[status.toLowerCase()] || '#d9d9d9';
};

/**
 * 获取设备状态的Ant Design标签类型
 */
export const getMachineStatusTagType = (status: string): string => {
  const tagTypeMap: Record<string, string> = {
    'running': 'processing',
    'idle': 'default',
    'maintenance': 'warning',
    'error': 'error',
    'setup': 'processing',
  };
  return tagTypeMap[status.toLowerCase()] || 'default';
};

/**
 * 获取工单状态的显示文本
 */
export const getWorkOrderStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'pending': STATUS_TEXTS.ORDER_PENDING,
    'planned': STATUS_TEXTS.ORDER_PLANNED,
    'in_progress': STATUS_TEXTS.ORDER_IN_PROGRESS,
    'completed': STATUS_TEXTS.ORDER_COMPLETED,
    'cancelled': STATUS_TEXTS.ORDER_CANCELLED,
  };
  return statusMap[status.toLowerCase()] || status;
};

/**
 * 获取质检状态的显示文本
 */
export const getInspectionStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'pending': STATUS_TEXTS.INSPECTION_PENDING,
    'in_progress': STATUS_TEXTS.INSPECTION_IN_PROGRESS,
    'completed': STATUS_TEXTS.INSPECTION_COMPLETED,
    'cancelled': STATUS_TEXTS.INSPECTION_CANCELLED,
  };
  return statusMap[status.toLowerCase()] || status;
};

/**
 * 获取质检结果的显示文本
 */
export const getInspectionResultText = (result: string): string => {
  const resultMap: Record<string, string> = {
    'pass': STATUS_TEXTS.INSPECTION_PASS,
    'fail': STATUS_TEXTS.INSPECTION_FAIL,
    'conditional': STATUS_TEXTS.INSPECTION_CONDITIONAL,
    'pending': STATUS_TEXTS.INSPECTION_RESULT_PENDING,
  };
  return resultMap[result.toLowerCase()] || result;
};

/**
 * 获取角色的显示名称
 */
export const getRoleDisplayText = (roleKey: string): string => {
  const roleMap: Record<string, string> = {
    'admin': ROLE_TEXTS.ADMIN,
    'process_engineer': ROLE_TEXTS.PROCESS_ENGINEER,
    'planner': ROLE_TEXTS.PLANNER,
    'operator': ROLE_TEXTS.OPERATOR,
    'quality_inspector': ROLE_TEXTS.QUALITY_INSPECTOR,
    'viewer': ROLE_TEXTS.VIEWER,
  };
  return roleMap[roleKey.toLowerCase()] || roleKey;
};

/**
 * 获取角色的描述
 */
export const getRoleDescription = (roleKey: string): string => {
  const descMap: Record<string, string> = {
    'admin': ROLE_TEXTS.ADMIN_DESC,
    'process_engineer': ROLE_TEXTS.PROCESS_ENGINEER_DESC,
    'planner': ROLE_TEXTS.PLANNER_DESC,
    'operator': ROLE_TEXTS.OPERATOR_DESC,
    'quality_inspector': ROLE_TEXTS.QUALITY_INSPECTOR_DESC,
    'viewer': ROLE_TEXTS.VIEWER_DESC,
  };
  return descMap[roleKey.toLowerCase()] || '';
};

/**
 * 获取技能组的显示名称
 */
export const getSkillDisplayText = (skillKey: string): string => {
  const skillMap: Record<string, string> = {
    'CNC Machining': SKILL_TEXTS.CNC_MACHINING,
    'Milling': SKILL_TEXTS.MILLING,
    'Turning': SKILL_TEXTS.TURNING,
    'Grinding': SKILL_TEXTS.GRINDING,
    'Assembly': SKILL_TEXTS.ASSEMBLY,
    'Quality Control': SKILL_TEXTS.QUALITY_CONTROL,
    'Packaging': SKILL_TEXTS.PACKAGING,
  };
  return skillMap[skillKey] || skillKey;
};

/**
 * 获取权限功能的显示名称
 */
export const getPermissionDisplayText = (permissionKey: string): string => {
  const permissionMap: Record<string, string> = {
    'CREATE_PROJECT': PERMISSION_TEXTS.CREATE_PROJECT,
    'CREATE_PART': PERMISSION_TEXTS.CREATE_PART,
    'CREATE_MACHINE': PERMISSION_TEXTS.CREATE_MACHINE,
    'CREATE_WORK_ORDER': PERMISSION_TEXTS.CREATE_WORK_ORDER,
    'CREATE_PLAN_TASK': PERMISSION_TEXTS.CREATE_PLAN_TASK,
    'CREATE_ROUTING': PERMISSION_TEXTS.CREATE_ROUTING,
    'EDIT_PROJECT': PERMISSION_TEXTS.EDIT_PROJECT,
    'EDIT_PART': PERMISSION_TEXTS.EDIT_PART,
    'EDIT_MACHINE': PERMISSION_TEXTS.EDIT_MACHINE,
    'EDIT_WORK_ORDER': PERMISSION_TEXTS.EDIT_WORK_ORDER,
    'EDIT_PLAN_TASK': PERMISSION_TEXTS.EDIT_PLAN_TASK,
    'EDIT_ROUTING': PERMISSION_TEXTS.EDIT_ROUTING,
    'DELETE_PROJECT': PERMISSION_TEXTS.DELETE_PROJECT,
    'DELETE_PART': PERMISSION_TEXTS.DELETE_PART,
    'DELETE_MACHINE': PERMISSION_TEXTS.DELETE_MACHINE,
    'DELETE_WORK_ORDER': PERMISSION_TEXTS.DELETE_WORK_ORDER,
    'DELETE_PLAN_TASK': PERMISSION_TEXTS.DELETE_PLAN_TASK,
    'DELETE_ROUTING': PERMISSION_TEXTS.DELETE_ROUTING,
    'UPDATE_MACHINE_STATUS': PERMISSION_TEXTS.UPDATE_MACHINE_STATUS,
    'SUBMIT_QUALITY_DATA': PERMISSION_TEXTS.SUBMIT_QUALITY_DATA,
    'EXECUTE_TASK': PERMISSION_TEXTS.EXECUTE_TASK,
    'START_TASK': PERMISSION_TEXTS.START_TASK,
    'COMPLETE_TASK': PERMISSION_TEXTS.COMPLETE_TASK,
    'PAUSE_TASK': PERMISSION_TEXTS.PAUSE_TASK,
    'MANAGE_USERS': PERMISSION_TEXTS.MANAGE_USERS,
    'MANAGE_ROLES': PERMISSION_TEXTS.MANAGE_ROLES,
    'MANAGE_SKILLS': PERMISSION_TEXTS.MANAGE_SKILLS,
  };
  return permissionMap[permissionKey] || permissionKey;
};

/**
 * 格式化文本模板（简单的字符串替换）
 */
export const formatText = (template: string, params: Record<string, any>): string => {
  let result = template;
  Object.keys(params).forEach(key => {
    const placeholder = `{${key}}`;
    result = result.replace(new RegExp(placeholder, 'g'), String(params[key]));
  });
  return result;
};

/**
 * 获取所有有效的任务状态选项
 */
export const getTaskStatusOptions = () => [
  { value: 'planned', label: STATUS_TEXTS.TASK_PLANNED, color: getTaskStatusColor('planned') },
  { value: 'scheduled', label: STATUS_TEXTS.TASK_SCHEDULED, color: getTaskStatusColor('scheduled') },
  { value: 'in_progress', label: STATUS_TEXTS.TASK_IN_PROGRESS, color: getTaskStatusColor('in_progress') },
  { value: 'completed', label: STATUS_TEXTS.TASK_COMPLETED, color: getTaskStatusColor('completed') },
  { value: 'cancelled', label: STATUS_TEXTS.TASK_CANCELLED, color: getTaskStatusColor('cancelled') },
  { value: 'on_hold', label: STATUS_TEXTS.TASK_ON_HOLD, color: getTaskStatusColor('on_hold') },
];

/**
 * 获取所有有效的设备状态选项
 */
export const getMachineStatusOptions = () => [
  { value: 'running', label: STATUS_TEXTS.MACHINE_RUNNING, color: getMachineStatusColor('running') },
  { value: 'idle', label: STATUS_TEXTS.MACHINE_IDLE, color: getMachineStatusColor('idle') },
  { value: 'maintenance', label: STATUS_TEXTS.MACHINE_MAINTENANCE, color: getMachineStatusColor('maintenance') },
  { value: 'error', label: STATUS_TEXTS.MACHINE_ERROR, color: getMachineStatusColor('error') },
  { value: 'setup', label: STATUS_TEXTS.MACHINE_SETUP, color: getMachineStatusColor('setup') },
];
