import { Role, SkillGroup } from '@/types/api';
import { getRoleDisplayText, getSkillDisplayText } from './textUtils';

/**
 * 角色和技能组显示工具函数
 * 用于统一处理角色和技能组的显示名称，避免硬编码
 */

/**
 * 获取角色的显示名称
 * 优先使用 display_name，如果没有则使用 role_name，最后尝试从文本映射获取
 */
export const getRoleDisplayName = (role: Role | string): string => {
  if (typeof role === 'string') {
    // 如果传入的是字符串，尝试从文本映射获取，否则返回原值
    return getRoleDisplayText(role) || role;
  }

  return role.display_name || role.role_name || getRoleDisplayText(role.role_name);
};

/**
 * 获取技能组的显示名称
 * 优先使用 display_name，如果没有则使用 group_name，最后尝试从文本映射获取
 */
export const getSkillGroupDisplayName = (skillGroup: SkillGroup | string): string => {
  if (typeof skillGroup === 'string') {
    // 如果传入的是字符串，尝试从文本映射获取，否则返回原值
    return getSkillDisplayText(skillGroup) || skillGroup;
  }

  return skillGroup.display_name || skillGroup.group_name || getSkillDisplayText(skillGroup.group_name);
};

/**
 * 根据角色名称从角色列表中查找角色对象
 */
export const findRoleByName = (roles: Role[], roleName: string): Role | undefined => {
  return roles.find(role => role.role_name === roleName);
};

/**
 * 根据技能组名称从技能组列表中查找技能组对象
 */
export const findSkillGroupByName = (skillGroups: SkillGroup[], groupName: string): SkillGroup | undefined => {
  return skillGroups.find(sg => sg.group_name === groupName);
};

/**
 * 获取角色的描述信息
 */
export const getRoleDescription = (role: Role): string => {
  return role.description || '暂无描述';
};

/**
 * 获取技能组的描述信息
 */
export const getSkillGroupDescription = (skillGroup: SkillGroup): string => {
  return skillGroup.description || '暂无描述';
};

/**
 * 判断是否为系统角色
 */
export const isSystemRole = (role: Role): boolean => {
  return role.role_type === 'system';
};

/**
 * 获取系统角色标签
 */
export const getSystemRoleTag = (role: Role): { color: string; text: string } => {
  if (isSystemRole(role)) {
    return { color: 'red', text: '系统角色' };
  }
  return { color: 'blue', text: '自定义角色' };
};

/**
 * 判断是否为系统技能组
 * 基于预定义的系统技能组列表
 */
export const isSystemSkillGroup = (skillGroup: SkillGroup): boolean => {
  const systemSkillGroups = [
    'CNC Machining', 
    'Milling', 
    'Turning', 
    'Grinding', 
    'Assembly', 
    'Quality Control', 
    'Packaging'
  ];
  return systemSkillGroups.includes(skillGroup.group_name);
};

/**
 * 获取系统技能组标签
 */
export const getSystemSkillGroupTag = (skillGroup: SkillGroup): { color: string; text: string } => {
  if (isSystemSkillGroup(skillGroup)) {
    return { color: 'red', text: '系统技能组' };
  }
  return { color: 'green', text: '自定义技能组' };
};

/**
 * 将用户的角色名称数组转换为角色对象数组
 */
export const mapUserRolesToRoleObjects = (userRoles: string[], allRoles: Role[]): Role[] => {
  return userRoles
    .map(roleName => findRoleByName(allRoles, roleName))
    .filter((role): role is Role => role !== undefined);
};

/**
 * 将用户的技能组名称数组转换为技能组对象数组
 */
export const mapUserSkillsToSkillGroupObjects = (userSkills: string[], allSkillGroups: SkillGroup[]): SkillGroup[] => {
  return userSkills
    .map(skillName => findSkillGroupByName(allSkillGroups, skillName))
    .filter((skillGroup): skillGroup is SkillGroup => skillGroup !== undefined);
};

/**
 * 获取角色的显示名称列表（用于表格显示）
 */
export const getRoleDisplayNames = (userRoles: string[], allRoles: Role[]): string[] => {
  const roleObjects = mapUserRolesToRoleObjects(userRoles, allRoles);
  return roleObjects.map(role => getRoleDisplayName(role));
};

/**
 * 获取技能组的显示名称列表（用于表格显示）
 */
export const getSkillGroupDisplayNames = (userSkills: string[], allSkillGroups: SkillGroup[]): string[] => {
  const skillGroupObjects = mapUserSkillsToSkillGroupObjects(userSkills, allSkillGroups);
  return skillGroupObjects.map(sg => getSkillGroupDisplayName(sg));
};
