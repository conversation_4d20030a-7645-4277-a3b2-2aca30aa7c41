// 角色权限配置
export const ROLES = {
  ADMIN: 'admin',
  PROCESS_ENGINEER: 'process_engineer',
  PLANNER: 'planner',
  OPERATOR: 'operator',
  QUALITY_INSPECTOR: 'quality_inspector',
  VIEWER: 'viewer'
} as const;

// 技能组配置
export const SKILLS = {
  CNC_MACHINING: 'CNC Machining',
  MILLING: 'Milling',
  TURNING: 'Turning',
  GRINDING: 'Grinding',
  ASSEMBLY: 'Assembly',
  QUALITY_CONTROL: 'Quality Control',
  PACKAGING: 'Packaging'
} as const;

// 角色菜单配置
export const ROLE_MENU_CONFIG = {
  [ROLES.OPERATOR]: [
    '/dashboard',
    '/production-center',  // 新的统一生产执行中心
    '/quality'     // 可以提交质量数据
  ],
  [ROLES.QUALITY_INSPECTOR]: [
    '/dashboard',
    '/quality',
    '/production-center',
    '/work-orders'
  ],
  [ROLES.PLANNER]: [
    '/dashboard',
    '/projects',
    '/parts',
    '/machines',
    '/work-orders',
    '/plan-tasks',  // 计划员负责任务调度
    '/production-center',   // 可以查看执行状态
    '/bom',
    '/routings'
  ],
  [ROLES.PROCESS_ENGINEER]: [
    '/dashboard',
    '/projects',
    '/parts',
    '/machines',
    '/work-orders',
    '/plan-tasks',
    '/production-center',
    '/quality',
    '/bom',
    '/routings'
  ],
  [ROLES.ADMIN]: 'all', // 所有菜单
  [ROLES.VIEWER]: [
    '/dashboard'
  ]
};

// 页面权限配置
export const PAGE_PERMISSIONS = {
  '/dashboard': [ROLES.ADMIN, ROLES.PROCESS_ENGINEER, ROLES.PLANNER, ROLES.OPERATOR, ROLES.QUALITY_INSPECTOR, ROLES.VIEWER],
  '/projects': [ROLES.ADMIN, ROLES.PROCESS_ENGINEER, ROLES.PLANNER],
  '/parts': [ROLES.ADMIN, ROLES.PROCESS_ENGINEER, ROLES.PLANNER],
  '/machines': [ROLES.ADMIN, ROLES.PROCESS_ENGINEER, ROLES.PLANNER], // 移除操作员的设备管理权限
  '/work-orders': [ROLES.ADMIN, ROLES.PROCESS_ENGINEER, ROLES.PLANNER, ROLES.QUALITY_INSPECTOR],
  '/plan-tasks': [ROLES.ADMIN, ROLES.PROCESS_ENGINEER, ROLES.PLANNER], // 只有计划相关角色可以访问
  '/production-center': [ROLES.ADMIN, ROLES.PROCESS_ENGINEER, ROLES.PLANNER, ROLES.OPERATOR, ROLES.QUALITY_INSPECTOR], // 新的统一生产执行中心
  '/execution': [ROLES.ADMIN, ROLES.PROCESS_ENGINEER, ROLES.PLANNER, ROLES.OPERATOR, ROLES.QUALITY_INSPECTOR], // 保留旧页面用于过渡
  '/operator-execution': [ROLES.ADMIN, ROLES.OPERATOR], // 保留旧页面用于过渡
  '/quality': [ROLES.ADMIN, ROLES.PROCESS_ENGINEER, ROLES.QUALITY_INSPECTOR, ROLES.OPERATOR],
  '/bom': [ROLES.ADMIN, ROLES.PROCESS_ENGINEER, ROLES.PLANNER],
  '/routings': [ROLES.ADMIN, ROLES.PROCESS_ENGINEER, ROLES.PLANNER],
  '/users': [ROLES.ADMIN],
  '/role-permissions': [ROLES.ADMIN], // 新增角色权限管理页面
  '/permission-config': [ROLES.ADMIN], // 权限配置页面
  '/permission-config-simple': [ROLES.ADMIN], // 简化权限配置页面
  '/system-config': [ROLES.ADMIN], // 系统配置页面 - 计划分配模式已移至生产计划页面
  '/permission-test': [ROLES.ADMIN], // 权限测试页面
  '/api-test': [ROLES.ADMIN],
  '/database': [ROLES.ADMIN],
  '/api-debug': [ROLES.ADMIN]
};

// 功能权限配置
export const FEATURE_PERMISSIONS = {
  // 创建权限
  CREATE_PROJECT: [ROLES.ADMIN, ROLES.PROCESS_ENGINEER],
  CREATE_PART: [ROLES.ADMIN, ROLES.PROCESS_ENGINEER],
  CREATE_MACHINE: [ROLES.ADMIN, ROLES.PROCESS_ENGINEER],
  CREATE_WORK_ORDER: [ROLES.ADMIN, ROLES.PROCESS_ENGINEER, ROLES.PLANNER],
  CREATE_PLAN_TASK: [ROLES.ADMIN, ROLES.PROCESS_ENGINEER, ROLES.PLANNER],
  CREATE_ROUTING: [ROLES.ADMIN, ROLES.PROCESS_ENGINEER],
  
  // 编辑权限
  EDIT_PROJECT: [ROLES.ADMIN, ROLES.PROCESS_ENGINEER],
  EDIT_PART: [ROLES.ADMIN, ROLES.PROCESS_ENGINEER],
  EDIT_MACHINE: [ROLES.ADMIN, ROLES.PROCESS_ENGINEER],
  EDIT_WORK_ORDER: [ROLES.ADMIN, ROLES.PROCESS_ENGINEER, ROLES.PLANNER],
  EDIT_PLAN_TASK: [ROLES.ADMIN, ROLES.PROCESS_ENGINEER, ROLES.PLANNER],
  EDIT_ROUTING: [ROLES.ADMIN, ROLES.PROCESS_ENGINEER],
  
  // 删除权限
  DELETE_PROJECT: [ROLES.ADMIN],
  DELETE_PART: [ROLES.ADMIN],
  DELETE_MACHINE: [ROLES.ADMIN],
  DELETE_WORK_ORDER: [ROLES.ADMIN, ROLES.PROCESS_ENGINEER],
  DELETE_PLAN_TASK: [ROLES.ADMIN, ROLES.PROCESS_ENGINEER],
  DELETE_ROUTING: [ROLES.ADMIN, ROLES.PROCESS_ENGINEER],
  
  // 操作权限
  UPDATE_MACHINE_STATUS: [ROLES.ADMIN, ROLES.PROCESS_ENGINEER, ROLES.PLANNER], // 移除操作员的设备状态更新权限
  SUBMIT_QUALITY_DATA: [ROLES.ADMIN, ROLES.QUALITY_INSPECTOR, ROLES.OPERATOR],
  EXECUTE_TASK: [ROLES.ADMIN, ROLES.OPERATOR], // 只有操作员可以执行任务
  START_TASK: [ROLES.ADMIN, ROLES.OPERATOR], // 只有操作员可以开始任务
  COMPLETE_TASK: [ROLES.ADMIN, ROLES.OPERATOR], // 只有操作员可以完成任务
  PAUSE_TASK: [ROLES.ADMIN, ROLES.OPERATOR], // 只有操作员可以暂停任务
  
  // 管理权限
  MANAGE_USERS: [ROLES.ADMIN],
  MANAGE_ROLES: [ROLES.ADMIN],
  MANAGE_SKILLS: [ROLES.ADMIN],
  MANAGE_PERMISSIONS: [ROLES.ADMIN]
};

/**
 * 检查用户是否有指定角色
 */
export const hasRole = (userRoles: string[], requiredRole: string): boolean => {
  return userRoles.includes(requiredRole);
};

/**
 * 检查用户是否有任一指定角色
 */
export const hasAnyRole = (userRoles: string[], requiredRoles: string[]): boolean => {
  return requiredRoles.some(role => userRoles.includes(role));
};

/**
 * 检查用户是否有所有指定角色
 */
export const hasAllRoles = (userRoles: string[], requiredRoles: string[]): boolean => {
  return requiredRoles.every(role => userRoles.includes(role));
};

/**
 * 检查用户是否有指定技能
 */
export const hasSkill = (userSkills: string[], requiredSkill: string): boolean => {
  return userSkills.includes(requiredSkill);
};

/**
 * 检查用户是否有任一指定技能
 */
export const hasAnySkill = (userSkills: string[], requiredSkills: string[]): boolean => {
  return requiredSkills.some(skill => userSkills.includes(skill));
};

/**
 * 检查用户是否有页面访问权限
 */
export const hasPageAccess = (userRoles: string[], page: string): boolean => {
  const allowedRoles = PAGE_PERMISSIONS[page as keyof typeof PAGE_PERMISSIONS];
  if (!allowedRoles) return false;
  return hasAnyRole(userRoles, allowedRoles);
};

/**
 * 检查用户是否有功能权限
 */
export const hasFeatureAccess = (userRoles: string[], feature: string): boolean => {
  const allowedRoles = FEATURE_PERMISSIONS[feature as keyof typeof FEATURE_PERMISSIONS];
  if (!allowedRoles) return false;
  return hasAnyRole(userRoles, allowedRoles);
};

/**
 * 获取用户可访问的菜单项
 */
export const getUserMenuItems = (userRoles: string[]): string[] => {
  // 管理员可以访问所有菜单
  if (hasRole(userRoles, ROLES.ADMIN)) {
    return Object.keys(PAGE_PERMISSIONS);
  }
  
  // 其他角色根据配置获取菜单
  const accessibleMenus: string[] = [];
  
  for (const role of userRoles) {
    const roleMenus = ROLE_MENU_CONFIG[role as keyof typeof ROLE_MENU_CONFIG];
    if (roleMenus === 'all') {
      return Object.keys(PAGE_PERMISSIONS);
    } else if (Array.isArray(roleMenus)) {
      accessibleMenus.push(...roleMenus);
    }
  }
  
  // 去重并返回
  return [...new Set(accessibleMenus)];
};
