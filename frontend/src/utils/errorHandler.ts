import { message, notification } from 'antd';
import { AxiosError } from 'axios';

// 错误响应接口
export interface ApiErrorResponse {
  error: {
    code: string;
    message: string;
    details?: string;
  };
  timestamp: string;
  path?: string;
}

// 错误类型枚举
export enum ErrorCode {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  BUSINESS_LOGIC_ERROR = 'BUSINESS_LOGIC_ERROR',
  CONFLICT_ERROR = 'CONFLICT_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
}

// 错误消息映射
const ERROR_MESSAGES: Record<string, string> = {
  [ErrorCode.VALIDATION_ERROR]: '输入数据验证失败',
  [ErrorCode.RESOURCE_NOT_FOUND]: '请求的资源不存在',
  [ErrorCode.UNAUTHORIZED]: '请先登录',
  [ErrorCode.FORBIDDEN]: '权限不足',
  [ErrorCode.INTERNAL_SERVER_ERROR]: '服务器内部错误',
  [ErrorCode.DATABASE_ERROR]: '数据库操作失败',
  [ErrorCode.BUSINESS_LOGIC_ERROR]: '业务逻辑错误',
  [ErrorCode.CONFLICT_ERROR]: '数据冲突',
  [ErrorCode.NETWORK_ERROR]: '网络连接失败',
  [ErrorCode.TIMEOUT_ERROR]: '请求超时，请稍后重试',
  [ErrorCode.RATE_LIMIT_ERROR]: '请求过于频繁，请稍后再试',
  [ErrorCode.SERVICE_UNAVAILABLE]: '服务暂时不可用',
};

// 错误处理选项
interface ErrorHandlerOptions {
  showMessage?: boolean;
  showNotification?: boolean;
  redirectOnAuth?: boolean;
  customMessage?: string;
  enableRetry?: boolean;
  retryCount?: number;
  retryDelay?: number;
  onRetry?: () => void;
  logError?: boolean;
}

// 默认错误处理选项
const DEFAULT_OPTIONS: ErrorHandlerOptions = {
  showMessage: true,
  showNotification: false,
  redirectOnAuth: true,
};

/**
 * 统一的API错误处理函数
 */
export const handleApiError = (
  error: any,
  options: ErrorHandlerOptions = {}
): void => {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  // 合并选项
  if (error.response?.data?.error) {
    // 标准API错误响应
    const apiError: ApiErrorResponse = error.response.data;
    handleStandardError(apiError, opts);
  } else if (error.code === 'NETWORK_ERROR' || error.message === 'Network Error') {
    // 网络错误
    handleNetworkError(opts);
  } else if (error.response?.status) {
    // HTTP状态码错误
    handleHttpStatusError(error.response.status, error.response.statusText, opts);
  } else {
    // 未知错误
    handleUnknownError(error, opts);
  }
};

/**
 * 处理标准API错误
 */
const handleStandardError = (
  apiError: ApiErrorResponse,
  options: ErrorHandlerOptions
): void => {
  const { error } = apiError;
  
  // 根据错误代码处理
  switch (error.code) {
    case ErrorCode.UNAUTHORIZED:
      handleUnauthorizedError(error.message, options);
      break;
      
    case ErrorCode.FORBIDDEN:
      showError('权限不足', error.message, options);
      break;
      
    case ErrorCode.VALIDATION_ERROR:
      handleValidationError(error, options);
      break;
      
    case ErrorCode.RESOURCE_NOT_FOUND:
      showError('资源不存在', error.message, options);
      break;
      
    case ErrorCode.CONFLICT_ERROR:
      showError('数据冲突', error.message, options);
      break;
      
    case ErrorCode.DATABASE_ERROR:
      showError('数据库错误', '数据库操作失败，请稍后重试', options);
      break;
      
    case ErrorCode.BUSINESS_LOGIC_ERROR:
      showError('业务错误', error.message, options);
      break;
      
    default:
      showError('系统错误', error.message || '未知错误', options);
  }
};

/**
 * 处理验证错误
 */
const handleValidationError = (
  error: ApiErrorResponse['error'],
  options: ErrorHandlerOptions
): void => {
  const title = '输入验证失败';
  const content = error.details || error.message;
  
  if (options.showNotification) {
    notification.error({
      message: title,
      description: content,
      duration: 5,
    });
  } else if (options.showMessage) {
    message.error(`${title}: ${content}`);
  }
};

/**
 * 处理未授权错误
 */
const handleUnauthorizedError = (
  errorMessage: string,
  options: ErrorHandlerOptions
): void => {
  showError('未授权', errorMessage || '请先登录', options);
  
  if (options.redirectOnAuth) {
    // 清除本地存储的认证信息
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    
    // 重定向到登录页
    setTimeout(() => {
      window.location.href = '#/login';
    }, 1500);
  }
};

/**
 * 处理网络错误
 */
const handleNetworkError = (options: ErrorHandlerOptions): void => {
  showError(
    '网络错误',
    '网络连接失败，请检查网络设置后重试',
    options
  );
};

/**
 * 处理HTTP状态码错误
 */
const handleHttpStatusError = (
  status: number,
  statusText: string,
  options: ErrorHandlerOptions
): void => {
  let title = 'HTTP错误';
  let message = `${status} ${statusText}`;
  
  switch (status) {
    case 400:
      title = '请求错误';
      message = '请求参数有误';
      break;
    case 401:
      handleUnauthorizedError('登录已过期，请重新登录', options);
      return;
    case 403:
      title = '权限不足';
      message = '您没有权限执行此操作';
      break;
    case 404:
      title = '资源不存在';
      message = '请求的资源不存在';
      break;
    case 500:
      title = '服务器错误';
      message = '服务器内部错误，请稍后重试';
      break;
    case 502:
      title = '网关错误';
      message = '服务暂时不可用，请稍后重试';
      break;
    case 503:
      title = '服务不可用';
      message = '服务暂时不可用，请稍后重试';
      break;
  }
  
  showError(title, message, options);
};

/**
 * 处理未知错误
 */
const handleUnknownError = (error: any, options: ErrorHandlerOptions): void => {
  console.error('未知错误:', error);
  showError(
    '未知错误',
    options.customMessage || '发生了未知错误，请稍后重试',
    options
  );
};

/**
 * 显示错误信息
 */
const showError = (
  title: string,
  content: string,
  options: ErrorHandlerOptions
): void => {
  if (options.showNotification) {
    notification.error({
      message: title,
      description: content,
      duration: 4,
    });
  } else if (options.showMessage) {
    message.error(options.customMessage || `${title}: ${content}`);
  }
};

/**
 * 创建带错误处理的请求拦截器
 */
export const createErrorInterceptor = (customOptions?: ErrorHandlerOptions) => {
  return (error: AxiosError) => {
    handleApiError(error, customOptions);
    return Promise.reject(error);
  };
};

/**
 * 便捷的错误处理函数
 */
export const showSuccess = (msg: string) => {
  message.success(msg);
};

export const showWarning = (msg: string) => {
  message.warning(msg);
};

export const showInfo = (msg: string) => {
  message.info(msg);
};

export const showNotification = (
  type: 'success' | 'error' | 'warning' | 'info',
  title: string,
  description?: string
) => {
  notification[type]({
    message: title,
    description,
    duration: 4,
  });
};
