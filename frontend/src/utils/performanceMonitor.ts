// 性能监控工具

interface PerformanceMetrics {
  pageLoadTime: number;
  domContentLoaded: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  firstInputDelay: number;
  cumulativeLayoutShift: number;
}

class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {};
  private observers: PerformanceObserver[] = [];

  constructor() {
    this.initializeMonitoring();
  }

  private initializeMonitoring() {
    // 监控页面加载时间
    this.measurePageLoadTime();
    
    // 监控Core Web Vitals
    this.measureWebVitals();
    
    // 监控资源加载
    this.measureResourceLoading();
  }

  private measurePageLoadTime() {
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      this.metrics.pageLoadTime = navigation.loadEventEnd - navigation.fetchStart;
      this.metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.fetchStart;
      
      this.reportMetrics('page-load', {
        pageLoadTime: this.metrics.pageLoadTime,
        domContentLoaded: this.metrics.domContentLoaded,
      });
    });
  }

  private measureWebVitals() {
    // First Contentful Paint (FCP)
    if ('PerformanceObserver' in window) {
      const fcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
        if (fcpEntry) {
          this.metrics.firstContentfulPaint = fcpEntry.startTime;
          this.reportMetrics('fcp', { value: fcpEntry.startTime });
        }
      });
      fcpObserver.observe({ entryTypes: ['paint'] });
      this.observers.push(fcpObserver);

      // Largest Contentful Paint (LCP)
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.metrics.largestContentfulPaint = lastEntry.startTime;
        this.reportMetrics('lcp', { value: lastEntry.startTime });
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.push(lcpObserver);

      // First Input Delay (FID)
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          this.metrics.firstInputDelay = entry.processingStart - entry.startTime;
          this.reportMetrics('fid', { value: this.metrics.firstInputDelay });
        });
      });
      fidObserver.observe({ entryTypes: ['first-input'] });
      this.observers.push(fidObserver);

      // Cumulative Layout Shift (CLS)
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        this.metrics.cumulativeLayoutShift = clsValue;
        this.reportMetrics('cls', { value: clsValue });
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
      this.observers.push(clsObserver);
    }
  }

  private measureResourceLoading() {
    if ('PerformanceObserver' in window) {
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'resource') {
            const resourceEntry = entry as PerformanceResourceTiming;
            
            // 监控大型资源
            if (resourceEntry.transferSize > 100000) { // 100KB以上
              this.reportMetrics('large-resource', {
                name: resourceEntry.name,
                size: resourceEntry.transferSize,
                duration: resourceEntry.duration,
              });
            }
            
            // 监控慢速资源
            if (resourceEntry.duration > 1000) { // 1秒以上
              this.reportMetrics('slow-resource', {
                name: resourceEntry.name,
                duration: resourceEntry.duration,
              });
            }
          }
        });
      });
      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.push(resourceObserver);
    }
  }

  private reportMetrics(type: string, data: any) {
    // 在开发环境下输出到控制台
    if (process.env.NODE_ENV === 'development') {
      console.group(`🚀 Performance Metric: ${type}`);
      console.log('Data:', data);
      console.log('Timestamp:', new Date().toISOString());
      console.groupEnd();
    }

    // 在生产环境下可以发送到分析服务
    if (process.env.NODE_ENV === 'production') {
      // 这里可以集成Google Analytics、Sentry等服务
      // 例如：gtag('event', 'performance_metric', { metric_type: type, ...data });
    }
  }

  // 手动测量函数执行时间
  public measureFunction<T>(name: string, fn: () => T): T {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    
    this.reportMetrics('function-execution', {
      name,
      duration: end - start,
    });
    
    return result;
  }

  // 手动测量异步函数执行时间
  public async measureAsyncFunction<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = performance.now();
    const result = await fn();
    const end = performance.now();
    
    this.reportMetrics('async-function-execution', {
      name,
      duration: end - start,
    });
    
    return result;
  }

  // 获取当前性能指标
  public getMetrics(): Partial<PerformanceMetrics> {
    return { ...this.metrics };
  }

  // 清理观察者
  public cleanup() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor();

// 导出实例和类型
export default performanceMonitor;
export type { PerformanceMetrics };

// 便捷的测量函数
export const measurePerformance = {
  function: performanceMonitor.measureFunction.bind(performanceMonitor),
  asyncFunction: performanceMonitor.measureAsyncFunction.bind(performanceMonitor),
  getMetrics: performanceMonitor.getMetrics.bind(performanceMonitor),
};
