import React, { useEffect, lazy, Suspense } from 'react';
import { HashRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, App as AntApp, theme, Spin } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { QueryClient, QueryClientProvider } from 'react-query';
import dayjs from './utils/dayjs';
import { useAuthStore } from '@/store/auth';
import { TokenManager } from '@/utils/tokenManager';
import Layout from '@/components/Layout';
import Login from '@/pages/Login';

// 路由级别的代码分割 - 懒加载页面组件
const Dashboard = lazy(() => import('@/pages/Dashboard'));
const Projects = lazy(() => import('@/pages/Projects'));
const ProjectDetail = lazy(() => import('@/pages/ProjectDetail'));
const Parts = lazy(() => import('@/pages/Parts'));
const Machines = lazy(() => import('@/pages/Machines'));
const WorkOrders = lazy(() => import('@/pages/WorkOrders'));
const PlanTasks = lazy(() => import('@/pages/PlanTasks'));
const Quality = lazy(() => import('@/pages/Quality'));
const Users = lazy(() => import('@/pages/Users'));
const Profile = lazy(() => import('@/pages/Profile'));
const RolePermissions = lazy(() => import('@/pages/RolePermissions'));
const PermissionConfig = lazy(() => import('@/pages/PermissionConfig'));
const PermissionConfigSimple = lazy(() => import('@/pages/PermissionConfigSimple'));
const BOM = lazy(() => import('@/pages/BOM'));
const Routings = lazy(() => import('@/pages/Routings'));
const AutoWorkOrderConfig = lazy(() => import('@/pages/AutoWorkOrderConfig'));
const TextTest = lazy(() => import('@/pages/TextTest'));
// Debug/Test imports - commented out for production
// import ApiTest from '@/pages/ApiTest';
// import DatabaseViewer from '@/pages/DatabaseViewer';
// import ApiDebug from '@/pages/ApiDebug';
// import OperatorExecution from '@/pages/OperatorExecution'; // 旧的操作员执行页面，已被生产执行中心替代
// 继续懒加载其他页面组件
const ProductionCenter = lazy(() => import('@/pages/ProductionCenter'));
const SystemConfig = lazy(() => import('@/pages/SystemConfig'));
const MobileTest = lazy(() => import('@/pages/MobileTest'));
const PartProgressDemo = lazy(() => import('@/pages/PartProgressDemo'));
const PermissionTest = lazy(() => import('@/pages/PermissionTest'));
const PermissionDebug = lazy(() => import('@/pages/PermissionDebug'));
const ShiftManagement = lazy(() => import('@/pages/ShiftManagement'));
import LoadingSpinner from '@/components/LoadingSpinner';
import ErrorBoundary from '@/components/ErrorBoundary';
import ProtectedRouteComponent from '@/components/ProtectedRoute';
import RoutePreloader from '@/components/RoutePreloader';
import performanceMonitor from '@/utils/performanceMonitor';
import './App.css';

// 优化的QueryClient配置
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,      // 5分钟内数据视为新鲜
      cacheTime: 10 * 60 * 1000,     // 10分钟缓存时间
      refetchOnWindowFocus: false,    // 窗口聚焦时不自动刷新
      refetchOnReconnect: true,       // 网络重连时刷新
      retry: 3,                       // 失败重试3次
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      retry: 1,
    },
  },
});

const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuthStore();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

const App: React.FC = () => {
  const { isAuthenticated, initializeAuth, isLoading, checkTokenValidity } = useAuthStore();

  useEffect(() => {
    // 初始化认证状态
    initializeAuth();

    // 设置token过期监听器
    const cleanup = TokenManager.setupTokenExpiryWatcher(() => {
      console.log('App: Token expired, user will be logged out');
    });

    // 定期检查token有效性
    const validityCheckInterval = setInterval(() => {
      checkTokenValidity();
    }, 60000); // 每分钟检查一次

    return () => {
      cleanup();
      clearInterval(validityCheckInterval);
    };
  }, []); // 空依赖数组，只在组件挂载时执行一次

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ConfigProvider
          locale={zhCN}
          theme={{
            algorithm: theme.defaultAlgorithm,
            token: {
              colorPrimary: '#1890ff',
              borderRadius: 6,
            },
          }}
        >
          <AntApp
            message={{
              maxCount: 3,
              duration: 3,
            }}
          >
            <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
              <RoutePreloader />
              <Routes>
                <Route path="/login" element={<Login />} />
                <Route
                  path="/*"
                  element={
                    <ProtectedRoute>
                      <Layout>
                        <ErrorBoundary>
                          <Suspense fallback={<div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}><Spin size="large" /></div>}>
                            <Routes>
                            <Route path="/" element={<Navigate to="/dashboard" replace />} />
                            <Route path="/dashboard" element={
                              <ProtectedRouteComponent>
                                <Dashboard />
                              </ProtectedRouteComponent>
                            } />
                            <Route path="/projects" element={
                              <ProtectedRouteComponent>
                                <Projects />
                              </ProtectedRouteComponent>
                            } />
                            <Route path="/projects/:id" element={
                              <ProtectedRouteComponent>
                                <ProjectDetail />
                              </ProtectedRouteComponent>
                            } />
                            <Route path="/parts" element={
                              <ProtectedRouteComponent>
                                <Parts />
                              </ProtectedRouteComponent>
                            } />
                            <Route path="/machines" element={
                              <ProtectedRouteComponent>
                                <Machines />
                              </ProtectedRouteComponent>
                            } />
                            <Route path="/work-orders" element={
                              <ProtectedRouteComponent>
                                <WorkOrders />
                              </ProtectedRouteComponent>
                            } />
                            <Route path="/plan-tasks" element={
                              <ProtectedRouteComponent>
                                <PlanTasks />
                              </ProtectedRouteComponent>
                            } />
                            {/* 旧的执行页面已被生产执行中心替代 */}
                            {/* <Route path="/execution" element={<Execution />} /> */}
                            {/* <Route path="/operator-execution" element={<OperatorExecution />} /> */}
                            <Route path="/production-center" element={
                              <ProtectedRouteComponent>
                                <ProductionCenter />
                              </ProtectedRouteComponent>
                            } />
                            <Route path="/quality" element={
                              <ProtectedRouteComponent>
                                <Quality />
                              </ProtectedRouteComponent>
                            } />
                            <Route path="/bom" element={
                              <ProtectedRouteComponent>
                                <BOM />
                              </ProtectedRouteComponent>
                            } />
                            <Route path="/routings" element={
                              <ProtectedRouteComponent>
                                <Routings />
                              </ProtectedRouteComponent>
                            } />
                            <Route path="/users" element={
                              <ProtectedRouteComponent>
                                <Users />
                              </ProtectedRouteComponent>
                            } />
                            <Route path="/profile" element={<Profile />} />
                            <Route path="/role-permissions" element={
                              <ProtectedRouteComponent>
                                <RolePermissions />
                              </ProtectedRouteComponent>
                            } />
                            <Route path="/permission-config" element={
                              <ProtectedRouteComponent>
                                <PermissionConfig />
                              </ProtectedRouteComponent>
                            } />
                            <Route path="/permission-config-simple" element={
                              <ProtectedRouteComponent>
                                <PermissionConfigSimple />
                              </ProtectedRouteComponent>
                            } />
                            <Route path="/system-config" element={
                              <ProtectedRouteComponent>
                                <SystemConfig />
                              </ProtectedRouteComponent>
                            } />
                            <Route path="/auto-work-order-config" element={
                              <ProtectedRouteComponent>
                                <AutoWorkOrderConfig />
                              </ProtectedRouteComponent>
                            } />
                            <Route path="/shift-management" element={
                              <ProtectedRouteComponent>
                                <ShiftManagement />
                              </ProtectedRouteComponent>
                            } />
                            <Route path="/mobile-test" element={<MobileTest />} />
                            <Route path="/text-test" element={<TextTest />} />
                            <Route path="/part-progress-demo" element={<PartProgressDemo />} />
                            <Route path="/permission-test" element={<PermissionTest />} />
                            <Route path="/permission-debug" element={<PermissionDebug />} />
                            {/* Debug/Test routes - commented out for production */}
                            {/* <Route path="/api-test" element={<ApiTest />} /> */}
                            {/* <Route path="/database" element={<DatabaseViewer />} /> */}
                            {/* <Route path="/api-debug" element={<ApiDebug />} /> */}
                            {/* <Route path="/part-selection-demo" element={<PartSelectionDemo />} /> */}
                          </Routes>
                          </Suspense>
                        </ErrorBoundary>
                      </Layout>
                    </ProtectedRoute>
                  }
                />
              </Routes>
            </Router>
          </AntApp>
        </ConfigProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;
