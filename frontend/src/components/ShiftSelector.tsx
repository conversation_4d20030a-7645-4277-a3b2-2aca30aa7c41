import React, { useState, useEffect } from 'react';
import {
  Card,
  Select,
  Row,
  Col,
  Tag,
  Space,
  Alert,
  Tooltip,
  Button,
  Modal,
  Timeline,
  Descriptions,
} from 'antd';
import {
  ClockCircleOutlined,
  InfoCircleOutlined,
  CalendarOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { useQuery } from 'react-query';
import { apiClient } from '@/lib/api';
import dayjs from 'dayjs';

const { Option } = Select;

interface ShiftTemplate {
  id: number;
  template_name: string;
  schedule_type: string;
  start_hour: number;
  start_minute: number;
  end_hour: number;
  end_minute: number;
  duration_hours: number;
  work_days: number[];
  description?: string;
  is_active: boolean;
}

interface PlanGroup {
  id: number;
  group_name: string;
  group_code: string;
  description?: string;
  priority: number;
  is_active: boolean;
}

interface ShiftSelectorProps {
  value?: {
    planGroupId?: number;
    shiftTemplateId?: number;
  };
  onChange?: (value: {
    planGroupId?: number;
    shiftTemplateId?: number;
  }) => void;
  disabled?: boolean;
  style?: React.CSSProperties;
}

const ShiftSelector: React.FC<ShiftSelectorProps> = ({
  value = {},
  onChange,
  disabled = false,
  style,
}) => {
  const [selectedPlanGroup, setSelectedPlanGroup] = useState<number | undefined>(value.planGroupId);
  const [selectedShiftTemplate, setSelectedShiftTemplate] = useState<number | undefined>(value.shiftTemplateId);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedTemplateDetail, setSelectedTemplateDetail] = useState<ShiftTemplate | null>(null);

  // 获取计划组
  const { data: planGroups, isLoading: planGroupsLoading } = useQuery(
    'plan-groups',
    () => apiClient.getPlanGroups(true),
    {
      select: (data) => data.groups,
    }
  );

  // 获取班次模板
  const { data: shiftTemplates, isLoading: templatesLoading } = useQuery(
    'shift-templates',
    () => apiClient.getShiftTemplates(true),
    {
      select: (data) => data.templates,
    }
  );

  // 当外部值变化时更新内部状态
  useEffect(() => {
    setSelectedPlanGroup(value.planGroupId);
    setSelectedShiftTemplate(value.shiftTemplateId);
  }, [value.planGroupId, value.shiftTemplateId]);

  // 处理计划组选择
  const handlePlanGroupChange = (planGroupId: number) => {
    setSelectedPlanGroup(planGroupId);
    // 清空班次模板选择
    setSelectedShiftTemplate(undefined);
    
    onChange?.({
      planGroupId,
      shiftTemplateId: undefined,
    });
  };

  // 处理班次模板选择
  const handleShiftTemplateChange = (shiftTemplateId: number) => {
    setSelectedShiftTemplate(shiftTemplateId);
    
    onChange?.({
      planGroupId: selectedPlanGroup,
      shiftTemplateId,
    });
  };

  // 显示班次详情
  const showShiftDetail = (template: ShiftTemplate) => {
    setSelectedTemplateDetail(template);
    setDetailModalVisible(true);
  };

  // 获取班次类型标签颜色
  const getScheduleTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
      '7x24': 'red',
      '7x12': 'orange',
      '5x8': 'green',
      'custom': 'purple',
    };
    return colorMap[type] || 'default';
  };

  // 获取班次类型显示文本
  const getScheduleTypeText = (type: string) => {
    const textMap: Record<string, string> = {
      '7x24': '7×24小时',
      '7x12': '7×12小时',
      '5x8': '5×8小时',
      'custom': '自定义',
    };
    return textMap[type] || type;
  };

  // 格式化工作时间
  const formatWorkTime = (template: ShiftTemplate) => {
    const startTime = `${template.start_hour.toString().padStart(2, '0')}:${template.start_minute.toString().padStart(2, '0')}`;
    const endTime = `${template.end_hour.toString().padStart(2, '0')}:${template.end_minute.toString().padStart(2, '0')}`;
    return `${startTime} - ${endTime}`;
  };

  // 格式化工作日
  const formatWorkDays = (days: number[]) => {
    const dayNames = ['一', '二', '三', '四', '五', '六', '日'];
    return days.map(day => dayNames[day - 1]).join('、');
  };

  // 获取选中的计划组信息
  const selectedPlanGroupInfo = planGroups?.find((g: PlanGroup) => g.id === selectedPlanGroup);
  
  // 获取选中的班次模板信息
  const selectedTemplateInfo = shiftTemplates?.find((t: ShiftTemplate) => t.id === selectedShiftTemplate);

  return (
    <div style={style}>
      <Card 
        title={
          <Space>
            <ClockCircleOutlined />
            <span>班次配置</span>
          </Space>
        }
        size="small"
      >
        <Row gutter={16}>
          <Col span={12}>
            <div style={{ marginBottom: '8px' }}>
              <Space>
                <TeamOutlined />
                <span>计划组</span>
              </Space>
            </div>
            <Select
              placeholder="选择计划组"
              value={selectedPlanGroup}
              onChange={handlePlanGroupChange}
              style={{ width: '100%' }}
              loading={planGroupsLoading}
              disabled={disabled}
            >
              {planGroups?.map((group: PlanGroup) => (
                <Option key={group.id} value={group.id}>
                  <Space>
                    <span>{group.group_name}</span>
                    <Tag>{group.group_code}</Tag>
                    {group.priority === 0 && <Tag color="red">高优先级</Tag>}
                  </Space>
                </Option>
              ))}
            </Select>
          </Col>

          <Col span={12}>
            <div style={{ marginBottom: '8px' }}>
              <Space>
                <CalendarOutlined />
                <span>班次模板</span>
              </Space>
            </div>
            <Select
              placeholder="选择班次模板"
              value={selectedShiftTemplate}
              onChange={handleShiftTemplateChange}
              style={{ width: '100%' }}
              loading={templatesLoading}
              disabled={disabled || !selectedPlanGroup}
            >
              {shiftTemplates?.map((template: ShiftTemplate) => (
                <Option key={template.id} value={template.id}>
                  <Space>
                    <span>{template.template_name}</span>
                    <Tag color={getScheduleTypeColor(template.schedule_type)}>
                      {getScheduleTypeText(template.schedule_type)}
                    </Tag>
                    <Tooltip title="查看详情">
                      <Button
                        type="link"
                        size="small"
                        icon={<InfoCircleOutlined />}
                        onClick={(e) => {
                          e.stopPropagation();
                          showShiftDetail(template);
                        }}
                      />
                    </Tooltip>
                  </Space>
                </Option>
              ))}
            </Select>
          </Col>
        </Row>

        {/* 选择结果显示 */}
        {selectedPlanGroupInfo && (
          <Alert
            message={
              <Space>
                <span>已选择计划组：</span>
                <Tag color="blue">{selectedPlanGroupInfo.group_name}</Tag>
                {selectedTemplateInfo && (
                  <>
                    <span>班次模板：</span>
                    <Tag color={getScheduleTypeColor(selectedTemplateInfo.schedule_type)}>
                      {selectedTemplateInfo.template_name}
                    </Tag>
                    <span>工作时间：{formatWorkTime(selectedTemplateInfo)}</span>
                  </>
                )}
              </Space>
            }
            type="info"
            showIcon
            style={{ marginTop: '16px' }}
          />
        )}

        {/* 班次模式说明 */}
        {selectedTemplateInfo && (
          <div style={{ marginTop: '12px' }}>
            <Row gutter={16}>
              <Col span={8}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                    {selectedTemplateInfo.duration_hours}h
                  </div>
                  <div style={{ color: '#666' }}>班次时长</div>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                    {selectedTemplateInfo.work_days.length}
                  </div>
                  <div style={{ color: '#666' }}>工作天数</div>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#fa8c16' }}>
                    {getScheduleTypeText(selectedTemplateInfo.schedule_type)}
                  </div>
                  <div style={{ color: '#666' }}>班次类型</div>
                </div>
              </Col>
            </Row>
          </div>
        )}
      </Card>

      {/* 班次详情模态框 */}
      <Modal
        title={
          <Space>
            <ClockCircleOutlined />
            <span>班次模板详情</span>
          </Space>
        }
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={600}
      >
        {selectedTemplateDetail && (
          <div>
            <Descriptions column={2} bordered>
              <Descriptions.Item label="模板名称" span={2}>
                <Space>
                  {selectedTemplateDetail.template_name}
                  <Tag color={getScheduleTypeColor(selectedTemplateDetail.schedule_type)}>
                    {getScheduleTypeText(selectedTemplateDetail.schedule_type)}
                  </Tag>
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="工作时间">
                {formatWorkTime(selectedTemplateDetail)}
              </Descriptions.Item>
              <Descriptions.Item label="班次时长">
                {selectedTemplateDetail.duration_hours} 小时
              </Descriptions.Item>
              <Descriptions.Item label="工作日" span={2}>
                {formatWorkDays(selectedTemplateDetail.work_days)}
              </Descriptions.Item>
              {selectedTemplateDetail.description && (
                <Descriptions.Item label="描述" span={2}>
                  {selectedTemplateDetail.description}
                </Descriptions.Item>
              )}
            </Descriptions>

            {/* 班次时间线 */}
            <div style={{ marginTop: '24px' }}>
              <h4>班次时间安排</h4>
              <Timeline>
                <Timeline.Item color="green">
                  <strong>班次开始</strong>
                  <br />
                  {`${selectedTemplateDetail.start_hour.toString().padStart(2, '0')}:${selectedTemplateDetail.start_minute.toString().padStart(2, '0')}`}
                </Timeline.Item>
                
                {selectedTemplateDetail.schedule_type === '7x12' && (
                  <Timeline.Item color="blue">
                    <strong>中间休息</strong>
                    <br />
                    建议安排适当的休息时间
                  </Timeline.Item>
                )}
                
                <Timeline.Item color="red">
                  <strong>班次结束</strong>
                  <br />
                  {`${selectedTemplateDetail.end_hour.toString().padStart(2, '0')}:${selectedTemplateDetail.end_minute.toString().padStart(2, '0')}`}
                  {selectedTemplateDetail.end_hour < selectedTemplateDetail.start_hour && (
                    <Tag color="orange" style={{ marginLeft: '8px' }}>次日</Tag>
                  )}
                </Timeline.Item>
              </Timeline>
            </div>

            {/* 适用场景说明 */}
            <Alert
              message="适用场景"
              description={
                selectedTemplateDetail.schedule_type === '7x24' 
                  ? '适用于需要连续生产的场景，如化工、钢铁等行业的连续生产线。'
                  : selectedTemplateDetail.schedule_type === '7x12'
                  ? '适用于两班制工作模式，白班和夜班轮换，保证设备利用率。'
                  : selectedTemplateDetail.schedule_type === '5x8'
                  ? '适用于标准工作时间，周一至周五的常规生产安排。'
                  : '根据具体需求定制的班次安排。'
              }
              type="info"
              showIcon
              style={{ marginTop: '16px' }}
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ShiftSelector;
