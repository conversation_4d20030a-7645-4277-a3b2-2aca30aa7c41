import React, { useState, useEffect } from 'react';
import { Form, Button, Space, Drawer, Modal } from 'antd';
import { FormInstance } from 'antd/es/form';

interface MobileFormProps {
  form: FormInstance;
  children: React.ReactNode;
  onFinish: (values: any) => void;
  onCancel?: () => void;
  title?: string;
  submitText?: string;
  cancelText?: string;
  loading?: boolean;
  visible?: boolean;
  mode?: 'modal' | 'drawer' | 'inline'; // 显示模式
  width?: number | string;
  height?: number | string;
}

/**
 * 移动端友好的表单组件
 * 根据屏幕尺寸自动选择最佳显示方式
 */
const MobileForm: React.FC<MobileFormProps> = ({
  form,
  children,
  onFinish,
  onCancel,
  title,
  submitText = '确定',
  cancelText = '取消',
  loading = false,
  visible = true,
  mode = 'modal',
  width = '100%',
  height = 'auto',
}) => {
  const [isMobile, setIsMobile] = useState(false);

  // 检测是否为移动设备
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  // 表单提交处理
  const handleFinish = (values: any) => {
    onFinish(values);
  };

  // 渲染表单内容
  const renderFormContent = () => (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleFinish}
      style={{ width: '100%' }}
      scrollToFirstError
    >
      {children}
      
      {/* 表单操作按钮 */}
      <Form.Item style={{ marginBottom: 0, marginTop: '24px' }}>
        <Space 
          style={{ 
            width: '100%', 
            justifyContent: isMobile ? 'stretch' : 'flex-end' 
          }}
          direction={isMobile ? 'vertical' : 'horizontal'}
          size="middle"
        >
          {onCancel && (
            <Button 
              onClick={onCancel}
              block={isMobile}
              size={isMobile ? 'large' : 'middle'}
            >
              {cancelText}
            </Button>
          )}
          <Button 
            type="primary" 
            htmlType="submit"
            loading={loading}
            block={isMobile}
            size={isMobile ? 'large' : 'middle'}
          >
            {submitText}
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );

  // 内联模式（直接渲染表单）
  if (mode === 'inline' || !visible) {
    return mode === 'inline' ? renderFormContent() : null;
  }

  // 移动端使用抽屉，桌面端使用模态框
  if (isMobile || mode === 'drawer') {
    return (
      <Drawer
        title={title}
        placement="bottom"
        onClose={onCancel}
        open={visible}
        height={height === 'auto' ? '90vh' : height}
        styles={{
          body: {
            padding: '20px',
            paddingBottom: '40px',
          }
        }}
        destroyOnClose
        maskClosable={false}
      >
        {renderFormContent()}
      </Drawer>
    );
  }

  // 桌面端模态框
  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={isMobile ? '95vw' : width}
      style={{ 
        top: isMobile ? '5vh' : undefined,
        maxWidth: isMobile ? 'none' : '600px',
      }}
      styles={{
        body: {
          padding: isMobile ? '16px' : '24px',
          maxHeight: isMobile ? '80vh' : '70vh',
          overflowY: 'auto',
        }
      }}
      destroyOnClose
      maskClosable={false}
    >
      {renderFormContent()}
    </Modal>
  );
};

// 移动端表单项组件
interface MobileFormItemProps {
  children: React.ReactNode;
  label?: string;
  required?: boolean;
  help?: string;
  validateStatus?: 'success' | 'warning' | 'error' | 'validating';
  style?: React.CSSProperties;
}

export const MobileFormItem: React.FC<MobileFormItemProps> = ({
  children,
  label,
  required,
  help,
  validateStatus,
  style,
}) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  return (
    <Form.Item
      label={label}
      required={required}
      help={help}
      validateStatus={validateStatus}
      style={{
        marginBottom: isMobile ? '20px' : '16px',
        ...style,
      }}
    >
      {React.cloneElement(children as React.ReactElement, {
        size: isMobile ? 'large' : 'middle',
        style: {
          fontSize: isMobile ? '16px' : '14px',
          minHeight: isMobile ? '48px' : '32px',
          ...(children as React.ReactElement).props?.style,
        },
      })}
    </Form.Item>
  );
};

// 移动端表单分组组件
interface MobileFormGroupProps {
  title: string;
  children: React.ReactNode;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
}

export const MobileFormGroup: React.FC<MobileFormGroupProps> = ({
  title,
  children,
  collapsible = false,
  defaultCollapsed = false,
}) => {
  const [collapsed, setCollapsed] = useState(defaultCollapsed);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  return (
    <div style={{
      marginBottom: isMobile ? '24px' : '20px',
      border: '1px solid #f0f0f0',
      borderRadius: isMobile ? '12px' : '8px',
      overflow: 'hidden',
    }}>
      <div
        style={{
          padding: isMobile ? '16px' : '12px',
          backgroundColor: '#fafafa',
          borderBottom: collapsed ? 'none' : '1px solid #f0f0f0',
          cursor: collapsible ? 'pointer' : 'default',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
        onClick={() => collapsible && setCollapsed(!collapsed)}
      >
        <span style={{
          fontWeight: 'bold',
          fontSize: isMobile ? '16px' : '14px',
        }}>
          {title}
        </span>
        {collapsible && (
          <span style={{ fontSize: '12px', color: '#999' }}>
            {collapsed ? '展开' : '收起'}
          </span>
        )}
      </div>
      {!collapsed && (
        <div style={{
          padding: isMobile ? '20px' : '16px',
        }}>
          {children}
        </div>
      )}
    </div>
  );
};

export default MobileForm;
