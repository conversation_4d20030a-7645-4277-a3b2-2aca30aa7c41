import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

// 路由预加载映射
const routePreloadMap: Record<string, () => Promise<any>> = {
  '/dashboard': () => import('@/pages/Dashboard'),
  '/projects': () => import('@/pages/Projects'),
  '/parts': () => import('@/pages/Parts'),
  '/machines': () => import('@/pages/Machines'),
  '/work-orders': () => import('@/pages/WorkOrders'),
  '/plan-tasks': () => import('@/pages/PlanTasks'),
  '/quality': () => import('@/pages/Quality'),
  '/users': () => import('@/pages/Users'),
  '/routings': () => import('@/pages/Routings'),
  '/production-center': () => import('@/pages/ProductionCenter'),
};

// 预加载相关路由的策略
const getRelatedRoutes = (currentPath: string): string[] => {
  const routeRelations: Record<string, string[]> = {
    '/dashboard': ['/projects', '/plan-tasks'],
    '/projects': ['/project-detail', '/work-orders', '/parts'],
    '/parts': ['/projects', '/routings'],
    '/work-orders': ['/projects', '/plan-tasks'],
    '/plan-tasks': ['/work-orders', '/production-center'],
    '/production-center': ['/plan-tasks', '/quality'],
    '/quality': ['/plan-tasks', '/production-center'],
    '/routings': ['/parts', '/plan-tasks'],
    '/machines': ['/plan-tasks', '/production-center'],
    '/users': ['/role-permissions'],
  };

  return routeRelations[currentPath] || [];
};

// 预加载函数
const preloadRoute = (routePath: string) => {
  const preloader = routePreloadMap[routePath];
  if (preloader) {
    // 使用 requestIdleCallback 在浏览器空闲时预加载
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        preloader().catch(() => {
          // 静默处理预加载错误
        });
      });
    } else {
      // 降级到 setTimeout
      setTimeout(() => {
        preloader().catch(() => {
          // 静默处理预加载错误
        });
      }, 100);
    }
  }
};

// 智能预加载组件
const RoutePreloader: React.FC = () => {
  const location = useLocation();

  useEffect(() => {
    // 预加载相关路由
    const relatedRoutes = getRelatedRoutes(location.pathname);
    relatedRoutes.forEach(route => {
      preloadRoute(route);
    });

    // 预加载常用路由（延迟执行）
    const commonRoutes = ['/dashboard', '/projects', '/plan-tasks'];
    setTimeout(() => {
      commonRoutes.forEach(route => {
        if (route !== location.pathname) {
          preloadRoute(route);
        }
      });
    }, 2000); // 2秒后预加载常用路由

  }, [location.pathname]);

  return null; // 这个组件不渲染任何内容
};

export default RoutePreloader;

// 导出预加载函数供其他组件使用
export { preloadRoute };
