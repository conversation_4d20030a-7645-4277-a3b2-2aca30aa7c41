import React from 'react';
import { Navigate, useLocation, useNavigate } from 'react-router-dom';
import { Result, Button } from 'antd';
import { useAuthStore } from '@/store/auth';
import { usePermissions } from '@/hooks/usePermissions';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: string[];
  requiredSkills?: string[];
  fallbackPath?: string;
}

/**
 * 路由保护组件
 * 检查用户是否有访问当前页面的权限
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRoles = [],
  requiredSkills = [],
  fallbackPath = '/dashboard'
}) => {
  const { user, isAuthenticated } = useAuthStore();
  const { hasPageAccess, loading: permissionsLoading } = usePermissions();
  const location = useLocation();
  const navigate = useNavigate();

  // 未登录用户重定向到登录页
  if (!isAuthenticated || !user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // 检查页面访问权限
  const userRoles = user.roles || [];
  const userSkills = user.skills || [];
  
  // 如果指定了特定角色要求，检查角色权限
  if (requiredRoles.length > 0) {
    const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));
    if (!hasRequiredRole) {
      return (
        <Result
          status="403"
          title="403"
          subTitle="抱歉，您没有权限访问此页面。"
          extra={
            <Button type="primary" onClick={() => window.history.back()}>
              返回上一页
            </Button>
          }
        />
      );
    }
  }

  // 如果指定了特定技能要求，检查技能权限
  if (requiredSkills.length > 0) {
    const hasRequiredSkill = requiredSkills.some(skill => userSkills.includes(skill));
    if (!hasRequiredSkill) {
      return (
        <Result
          status="403"
          title="403"
          subTitle="抱歉，您没有相应的技能权限访问此页面。"
          extra={
            <Button type="primary" onClick={() => window.history.back()}>
              返回上一页
            </Button>
          }
        />
      );
    }
  }

  // 权限加载中，显示加载状态
  if (permissionsLoading) {
    return <div>加载权限信息中...</div>;
  }

  // 检查基于路径的页面权限
  if (!hasPageAccess(location.pathname)) {
    return (
      <Result
        status="403"
        title="403"
        subTitle="抱歉，您没有权限访问此页面。"
        extra={
          <Button type="primary" onClick={() => navigate(fallbackPath)}>
            返回首页
          </Button>
        }
      />
    );
  }

  return <>{children}</>;
};

export default ProtectedRoute;
