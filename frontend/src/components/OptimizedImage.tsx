import React, { useState, useRef, useEffect } from 'react';
import { Skeleton } from 'antd';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number | string;
  height?: number | string;
  className?: string;
  style?: React.CSSProperties;
  placeholder?: React.ReactNode;
  lazy?: boolean;
  webpSupport?: boolean;
  quality?: number;
  sizes?: string;
}

// 检测WebP支持
const checkWebPSupport = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const webP = new Image();
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2);
    };
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
};

// 生成响应式图片URL
const generateResponsiveUrl = (src: string, width?: number, quality = 80, webp = false): string => {
  // 这里可以集成图片CDN服务，如阿里云OSS、腾讯云COS等
  // 示例：如果使用阿里云OSS
  if (src.includes('aliyuncs.com')) {
    const params = [];
    if (width) params.push(`w_${width}`);
    if (quality !== 80) params.push(`q_${quality}`);
    if (webp) params.push('f_webp');
    
    if (params.length > 0) {
      return `${src}?x-oss-process=image/resize,${params.join(',')}`;
    }
  }
  
  return src;
};

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className,
  style,
  placeholder,
  lazy = true,
  webpSupport = true,
  quality = 80,
  sizes,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(!lazy);
  const [supportsWebP, setSupportsWebP] = useState(false);
  const [error, setError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 检测WebP支持
  useEffect(() => {
    if (webpSupport) {
      checkWebPSupport().then(setSupportsWebP);
    }
  }, [webpSupport]);

  // 懒加载逻辑
  useEffect(() => {
    if (!lazy || isInView) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: '50px', // 提前50px开始加载
        threshold: 0.1,
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [lazy, isInView]);

  // 处理图片加载
  const handleLoad = () => {
    setIsLoaded(true);
    setError(false);
  };

  const handleError = () => {
    setError(true);
    setIsLoaded(false);
  };

  // 生成图片源
  const getImageSrc = () => {
    if (!isInView) return '';
    
    const numericWidth = typeof width === 'number' ? width : undefined;
    return generateResponsiveUrl(src, numericWidth, quality, supportsWebP);
  };

  // 生成srcSet用于响应式图片
  const generateSrcSet = () => {
    if (!isInView || typeof width !== 'number') return undefined;
    
    const densities = [1, 1.5, 2];
    return densities
      .map(density => {
        const scaledWidth = Math.round(width * density);
        const url = generateResponsiveUrl(src, scaledWidth, quality, supportsWebP);
        return `${url} ${density}x`;
      })
      .join(', ');
  };

  const defaultPlaceholder = (
    <Skeleton.Image
      style={{
        width: width || '100%',
        height: height || 'auto',
      }}
    />
  );

  const containerStyle: React.CSSProperties = {
    position: 'relative',
    display: 'inline-block',
    width: width || 'auto',
    height: height || 'auto',
    ...style,
  };

  const imageStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    transition: 'opacity 0.3s ease-in-out',
    opacity: isLoaded ? 1 : 0,
  };

  return (
    <div ref={containerRef} className={className} style={containerStyle}>
      {/* 占位符 */}
      {(!isLoaded || error) && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#f5f5f5',
          }}
        >
          {error ? (
            <div style={{ color: '#999', fontSize: '12px' }}>
              图片加载失败
            </div>
          ) : (
            placeholder || defaultPlaceholder
          )}
        </div>
      )}
      
      {/* 实际图片 */}
      {isInView && (
        <img
          ref={imgRef}
          src={getImageSrc()}
          srcSet={generateSrcSet()}
          sizes={sizes}
          alt={alt}
          style={imageStyle}
          onLoad={handleLoad}
          onError={handleError}
          loading={lazy ? 'lazy' : 'eager'}
          decoding="async"
        />
      )}
    </div>
  );
};

export default OptimizedImage;
