import React, { useState, useCallback } from 'react';
import { Card, Button, Space, Modal, Form, Input, InputNumber, Select, Tag, Tooltip } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, ArrowRightOutlined, DragOutlined } from '@ant-design/icons';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  horizontalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import type { RoutingStep } from '@/types/api';

const { TextArea } = Input;

interface ProcessFlowChartProps {
  steps: RoutingStep[];
  onStepAdd?: (step: Omit<RoutingStep, 'id'>) => void;
  onStepEdit?: (stepId: number, step: Partial<RoutingStep>) => void;
  onStepDelete?: (stepId: number) => void;
  onStepReorder?: (steps: RoutingStep[]) => void;
  machines?: Array<{ id: number; machine_name: string; skill_group_name?: string }>;
  skillGroups?: Array<{ id: number; group_name: string }>;
  editable?: boolean;
}

interface StepFormData {
  step_number: number;
  process_name: string;
  work_instructions?: string;
  standard_hours?: number;
}

// 可拖拽的工序卡片组件
interface SortableStepCardProps {
  step: RoutingStep;
  index: number;
  isLast: boolean;
  editable: boolean;
  onEdit: (step: RoutingStep) => void;
  onDelete: (stepId: number) => void;
}

const SortableStepCard: React.FC<SortableStepCardProps> = ({
  step,
  index,
  isLast,
  editable,
  onEdit,
  onDelete,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: step.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <div ref={setNodeRef} style={style}>
        <Card
          size="small"
          style={{
            width: 280,
            margin: '8px',
            border: '2px solid #1890ff',
            borderRadius: '8px',
            cursor: editable ? 'grab' : 'default',
          }}
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                {editable && (
                  <DragOutlined
                    {...attributes}
                    {...listeners}
                    style={{ cursor: 'grab', color: '#1890ff' }}
                  />
                )}
                <span>工序 {step.step_number}</span>
              </div>
              {editable && (
                <Space size="small">
                  <Button
                    type="text"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => onEdit(step)}
                  />
                  <Button
                    type="text"
                    size="small"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => onDelete(step.id)}
                  />
                </Space>
              )}
            </div>
          }
        >
          <div style={{ minHeight: '120px' }}>
            <div style={{ marginBottom: '8px' }}>
              <Tag color="blue">{step.process_name}</Tag>
            </div>

            {step.work_instructions && (
              <div style={{ marginBottom: '8px', fontSize: '12px', color: '#666' }}>
                <strong>工作指导:</strong>
                <div style={{
                  maxHeight: '40px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}>
                  <Tooltip title={step.work_instructions}>
                    {step.work_instructions}
                  </Tooltip>
                </div>
              </div>
            )}

            {step.standard_hours && (
              <div style={{ fontSize: '12px', color: '#666' }}>
                <strong>标准工时:</strong> {step.standard_hours}h
              </div>
            )}
          </div>
        </Card>
      </div>

      {!isLast && (
        <ArrowRightOutlined style={{ fontSize: '20px', color: '#1890ff', margin: '0 8px' }} />
      )}
    </div>
  );
};

const ProcessFlowChart: React.FC<ProcessFlowChartProps> = ({
  steps,
  onStepAdd,
  onStepEdit,
  onStepDelete,
  onStepReorder,
  machines = [],
  skillGroups = [],
  editable = true,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingStep, setEditingStep] = useState<RoutingStep | null>(null);
  const [form] = Form.useForm();

  // 拖拽传感器配置
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // 需要拖拽8px才激活，避免误触
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 按步骤号排序
  const sortedSteps = [...steps].sort((a, b) => a.step_number - b.step_number);

  // 处理拖拽结束
  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = sortedSteps.findIndex(step => step.id === active.id);
      const newIndex = sortedSteps.findIndex(step => step.id === over?.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        const newSteps = arrayMove(sortedSteps, oldIndex, newIndex);

        // 重新分配步骤号
        const reorderedSteps = newSteps.map((step, index) => ({
          ...step,
          step_number: index + 1,
        }));

        // 调用回调函数通知父组件
        if (onStepReorder) {
          onStepReorder(reorderedSteps);
        }
      }
    }
  }, [sortedSteps, onStepReorder]);

  const handleAddStep = () => {
    setEditingStep(null);
    setIsModalVisible(true);
    form.resetFields();
    // 自动设置下一个步骤号
    const nextStepNumber = steps.length > 0 ? Math.max(...steps.map(s => s.step_number)) + 1 : 1;
    form.setFieldsValue({ step_number: nextStepNumber });
  };

  const handleEditStep = (step: RoutingStep) => {
    setEditingStep(step);
    setIsModalVisible(true);
    form.setFieldsValue({
      step_number: step.step_number,
      process_name: step.process_name,
      work_instructions: step.work_instructions,
      standard_hours: step.standard_hours,
    });
  };

  const handleDeleteStep = (stepId: number) => {
    if (onStepDelete) {
      onStepDelete(stepId);
    }
  };

  const handleSubmit = async () => {
    try {
      const values: StepFormData = await form.validateFields();
      
      if (editingStep) {
        // 编辑现有步骤
        if (onStepEdit) {
          onStepEdit(editingStep.id, values);
        }
      } else {
        // 添加新步骤
        if (onStepAdd) {
          onStepAdd(values);
        }
      }
      
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  // 获取工艺名称选项（从技能组）
  const getProcessNameOptions = () => {
    const options: Array<{ label: string; value: string; group: string }> = [];

    // 从技能组获取
    skillGroups.forEach(skillGroup => {
      options.push({
        label: skillGroup.group_name,
        value: skillGroup.group_name,
        group: '技能组',
      });
    });

    return options;
  };

  const processOptions = getProcessNameOptions();



  return (
    <div>
      <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h3>工艺流程图</h3>
        {editable && (
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddStep}
          >
            添加工序
          </Button>
        )}
      </div>

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <div style={{
          display: 'flex',
          flexWrap: 'wrap',
          alignItems: 'center',
          padding: '16px',
          backgroundColor: '#fafafa',
          borderRadius: '8px',
          minHeight: '200px',
        }}>
          {sortedSteps.length > 0 ? (
            <SortableContext
              items={sortedSteps.map(step => step.id)}
              strategy={horizontalListSortingStrategy}
            >
              {sortedSteps.map((step, index) => (
                <SortableStepCard
                  key={step.id}
                  step={step}
                  index={index}
                  isLast={index === sortedSteps.length - 1}
                  editable={editable}
                  onEdit={handleEditStep}
                  onDelete={handleDeleteStep}
                />
              ))}
            </SortableContext>
          ) : (
            <div style={{
              width: '100%',
              textAlign: 'center',
              color: '#999',
              padding: '40px 0'
            }}>
              暂无工艺步骤，点击"添加工序"开始创建
            </div>
          )}
        </div>
      </DndContext>

      {/* 添加/编辑工序模态框 */}
      <Modal
        title={editingStep ? '编辑工序' : '添加工序'}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingStep(null);
          form.resetFields();
        }}
        okText="确定"
        cancelText="取消"
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="step_number"
            label="工序编号"
            rules={[
              { required: true, message: '请输入工序编号' },
              { type: 'number', min: 1, message: '工序编号必须大于0' },
            ]}
          >
            <InputNumber
              min={1}
              style={{ width: '100%' }}
              placeholder="输入工序编号"
            />
          </Form.Item>

          <Form.Item
            name="process_name"
            label="工艺名称"
            rules={[{ required: true, message: '请选择或输入工艺名称' }]}
          >
            <Select
              placeholder="选择技能组或手动输入工艺名称"
              showSearch
              allowClear
              optionFilterProp="children"
            >
              <Select.OptGroup label="技能组">
                {processOptions
                  .filter(opt => opt.group === '技能组')
                  .map(opt => (
                    <Select.Option key={`skill-${opt.value}`} value={opt.value}>
                      {opt.label}
                    </Select.Option>
                  ))
                }
              </Select.OptGroup>
            </Select>
          </Form.Item>

          <Form.Item
            name="work_instructions"
            label="工作指导"
          >
            <TextArea
              rows={4}
              placeholder="输入工作指导书内容"
            />
          </Form.Item>

          <Form.Item
            name="standard_hours"
            label="时间预算（小时）"
          >
            <InputNumber
              min={0}
              step={0.1}
              style={{ width: '100%' }}
              placeholder="输入标准工时"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProcessFlowChart;
