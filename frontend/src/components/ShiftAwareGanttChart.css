/* 班次感知甘特图样式 */
.shift-aware-gantt-chart {
  width: 100%;
  position: relative;
}

/* 甘特图容器 */
.gantt-container {
  position: relative;
  background: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

/* 班次背景区域 */
.shift-background {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1;
  border-radius: 4px;
  transition: opacity 0.3s ease;
}

.shift-background:hover {
  opacity: 0.2 !important;
}

/* 班次边界线 */
.shift-boundary-line {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 2px;
  z-index: 10;
  border-left: 2px dashed;
  pointer-events: none;
}

.shift-boundary-line.shift-start {
  border-color: #52c41a;
}

.shift-boundary-line.shift-end {
  border-color: #ff4d4f;
}

.shift-boundary-line.break-time {
  border-color: #fa8c16;
  border-style: dotted;
}

/* 班次边界标记 */
.shift-boundary-marker {
  position: absolute;
  top: -4px;
  left: -4px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.shift-boundary-marker:hover {
  transform: scale(1.2);
}

/* 7x24模式样式 */
.shift-background.schedule-7x24 {
  background: linear-gradient(90deg, 
    rgba(255, 77, 79, 0.1) 0%, 
    rgba(255, 77, 79, 0.05) 50%, 
    rgba(255, 77, 79, 0.1) 100%);
}

/* 7x12模式样式 */
.shift-background.schedule-7x12 {
  background: linear-gradient(90deg, 
    rgba(250, 140, 22, 0.1) 0%, 
    rgba(250, 140, 22, 0.05) 50%, 
    rgba(250, 140, 22, 0.1) 100%);
}

/* 5x8模式样式 */
.shift-background.schedule-5x8 {
  background: linear-gradient(90deg, 
    rgba(82, 196, 26, 0.1) 0%, 
    rgba(82, 196, 26, 0.05) 50%, 
    rgba(82, 196, 26, 0.1) 100%);
}

/* 自定义模式样式 */
.shift-background.schedule-custom {
  background: linear-gradient(90deg, 
    rgba(114, 46, 209, 0.1) 0%, 
    rgba(114, 46, 209, 0.05) 50%, 
    rgba(114, 46, 209, 0.1) 100%);
}

/* 班次冲突高亮 */
.task-conflict-error {
  border: 2px solid #ff4d4f !important;
  box-shadow: 0 0 8px rgba(255, 77, 79, 0.3);
  animation: conflict-pulse 2s infinite;
}

.task-conflict-warning {
  border: 2px solid #fa8c16 !important;
  box-shadow: 0 0 6px rgba(250, 140, 22, 0.3);
}

.task-conflict-info {
  border: 2px solid #1890ff !important;
  box-shadow: 0 0 4px rgba(24, 144, 255, 0.3);
}

@keyframes conflict-pulse {
  0%, 100% {
    box-shadow: 0 0 8px rgba(255, 77, 79, 0.3);
  }
  50% {
    box-shadow: 0 0 12px rgba(255, 77, 79, 0.6);
  }
}

/* 班次时间标签 */
.shift-time-label {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  white-space: nowrap;
  z-index: 20;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.shift-boundary-line:hover .shift-time-label {
  opacity: 1;
}

/* 班次图例 */
.shift-legend {
  display: flex;
  gap: 16px;
  padding: 8px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
}

.shift-legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.shift-legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

/* 班次切换动画 */
.shift-transition {
  transition: all 0.3s ease-in-out;
}

/* 工作日/非工作日区分 */
.non-working-day {
  background: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 10px,
    rgba(0, 0, 0, 0.05) 10px,
    rgba(0, 0, 0, 0.05) 20px
  );
}

/* 班次重叠区域 */
.shift-overlap {
  background: linear-gradient(
    90deg,
    rgba(255, 193, 7, 0.2) 0%,
    rgba(255, 193, 7, 0.1) 50%,
    rgba(255, 193, 7, 0.2) 100%
  );
  border: 1px dashed #ffc107;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .shift-boundary-marker {
    width: 6px;
    height: 6px;
    left: -3px;
    top: -3px;
  }
  
  .shift-time-label {
    font-size: 10px;
    padding: 1px 4px;
  }
  
  .shift-legend {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .shift-legend-item {
    font-size: 11px;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .gantt-container {
    background: #1f1f1f;
    border-color: #434343;
  }
  
  .shift-time-label {
    background: rgba(255, 255, 255, 0.9);
    color: #000;
  }
  
  .shift-legend {
    border-top-color: #434343;
  }
}

/* 打印样式 */
@media print {
  .shift-boundary-line {
    border-style: solid;
  }
  
  .shift-background {
    opacity: 0.3 !important;
  }
  
  .shift-time-label {
    opacity: 1;
    position: static;
    transform: none;
    background: transparent;
    color: #000;
    display: inline-block;
    margin: 0 4px;
  }
}

/* 可访问性增强 */
.shift-boundary-line:focus-visible {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

.shift-boundary-marker:focus-visible {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .shift-boundary-line {
    border-width: 3px;
  }
  
  .shift-boundary-marker {
    border-width: 3px;
  }
  
  .task-conflict-error,
  .task-conflict-warning,
  .task-conflict-info {
    border-width: 3px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .shift-boundary-marker,
  .shift-transition,
  .conflict-pulse {
    transition: none;
    animation: none;
  }
}
