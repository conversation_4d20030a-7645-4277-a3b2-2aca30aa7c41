import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Tag, Toolt<PERSON>, Spin } from 'antd';
import { ProjectOutlined, LinkOutlined } from '@ant-design/icons';
import { useQuery } from 'react-query';
import { useNavigate } from 'react-router-dom';
import { apiClient } from '@/lib/api';
import type { Project } from '@/types/api';

interface PartProjectsListProps {
  partId: number;
  partNumber: string;
}

const PartProjectsList: React.FC<PartProjectsListProps> = ({ partId, partNumber }) => {
  const navigate = useNavigate();

  const { data: projects = [], isLoading } = useQuery(
    ['part-projects', partId],
    () => apiClient.getPartProjects(partId),
    {
      enabled: !!partId,
    }
  );

  if (isLoading) {
    return <Spin size="small" />;
  }

  if (projects.length === 0) {
    return (
      <Tag color="default">
        <ProjectOutlined /> 未使用
      </Tag>
    );
  }

  if (projects.length === 1) {
    const project = projects[0];
    return (
      <Button
        type="link"
        size="small"
        icon={<ProjectOutlined />}
        onClick={() => navigate(`/projects/${project.id}`)}
        style={{ padding: 0 }}
      >
        {project.project_name}
      </Button>
    );
  }

  // 多个项目的情况
  return (
    <Space direction="vertical" size="small" style={{ width: '100%' }}>
      {projects.slice(0, 2).map((project) => (
        <Button
          key={project.id}
          type="link"
          size="small"
          icon={<ProjectOutlined />}
          onClick={() => navigate(`/projects/${project.id}`)}
          style={{ padding: 0, height: 'auto', textAlign: 'left' }}
        >
          {project.project_name}
        </Button>
      ))}
      {projects.length > 2 && (
        <Tooltip
          title={
            <div>
              <div style={{ marginBottom: 8, fontWeight: 'bold' }}>
                零件 {partNumber} 所属的所有项目：
              </div>
              {projects.map((project) => (
                <div key={project.id} style={{ marginBottom: 4 }}>
                  <Button
                    type="link"
                    size="small"
                    icon={<LinkOutlined />}
                    onClick={() => navigate(`/projects/${project.id}`)}
                    style={{ color: '#fff', padding: 0 }}
                  >
                    {project.project_name}
                  </Button>
                  {project.customer_name && (
                    <span style={{ color: '#ccc', marginLeft: 8 }}>
                      ({project.customer_name})
                    </span>
                  )}
                </div>
              ))}
            </div>
          }
          overlayStyle={{ maxWidth: 300 }}
        >
          <Tag color="blue" style={{ cursor: 'pointer' }}>
            +{projects.length - 2} 个项目
          </Tag>
        </Tooltip>
      )}
    </Space>
  );
};

export default PartProjectsList;
