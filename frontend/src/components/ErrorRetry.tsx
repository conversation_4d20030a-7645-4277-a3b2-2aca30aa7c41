import React, { useState } from 'react';
import { Button, Result, Spin, Alert } from 'antd';
import { ReloadOutlined, ExclamationCircleOutlined } from '@ant-design/icons';

interface ErrorRetryProps {
  error: Error | null;
  onRetry: () => void;
  loading?: boolean;
  maxRetries?: number;
  retryDelay?: number;
  showDetails?: boolean;
  title?: string;
  description?: string;
}

const ErrorRetry: React.FC<ErrorRetryProps> = ({
  error,
  onRetry,
  loading = false,
  maxRetries = 3,
  retryDelay = 1000,
  showDetails = false,
  title = '操作失败',
  description = '请稍后重试，或联系系统管理员',
}) => {
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);

  const handleRetry = async () => {
    if (retryCount >= maxRetries) {
      return;
    }

    setIsRetrying(true);
    setRetryCount(prev => prev + 1);

    // 延迟重试
    if (retryDelay > 0) {
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }

    try {
      await onRetry();
    } catch (err) {
      console.error('Retry failed:', err);
    } finally {
      setIsRetrying(false);
    }
  };

  const getErrorMessage = (error: Error): string => {
    // 根据错误类型返回用户友好的消息
    if (error.message.includes('Network Error')) {
      return '网络连接失败，请检查网络连接';
    }
    if (error.message.includes('timeout')) {
      return '请求超时，请稍后重试';
    }
    if (error.message.includes('401')) {
      return '登录已过期，请重新登录';
    }
    if (error.message.includes('403')) {
      return '权限不足，请联系管理员';
    }
    if (error.message.includes('404')) {
      return '请求的资源不存在';
    }
    if (error.message.includes('500')) {
      return '服务器内部错误，请稍后重试';
    }
    return error.message || '未知错误';
  };

  const getRetryButtonText = (): string => {
    if (isRetrying) {
      return '重试中...';
    }
    if (retryCount >= maxRetries) {
      return '已达到最大重试次数';
    }
    if (retryCount === 0) {
      return '重试';
    }
    return `重试 (${retryCount}/${maxRetries})`;
  };

  const canRetry = retryCount < maxRetries && !isRetrying && !loading;

  if (!error) {
    return null;
  }

  return (
    <div style={{ padding: '20px', textAlign: 'center' }}>
      <Result
        status="error"
        title={title}
        subTitle={description}
        extra={[
          <Button
            key="retry"
            type="primary"
            icon={<ReloadOutlined />}
            onClick={handleRetry}
            disabled={!canRetry}
            loading={isRetrying || loading}
          >
            {getRetryButtonText()}
          </Button>,
        ]}
      >
        <div style={{ marginTop: '16px' }}>
          <Alert
            message={getErrorMessage(error)}
            type="error"
            showIcon
            icon={<ExclamationCircleOutlined />}
          />
          
          {showDetails && (
            <Alert
              message="错误详情"
              description={
                <div style={{ textAlign: 'left', fontSize: '12px', color: '#666' }}>
                  <div><strong>错误类型:</strong> {error.name}</div>
                  <div><strong>错误消息:</strong> {error.message}</div>
                  {error.stack && (
                    <div>
                      <strong>错误堆栈:</strong>
                      <pre style={{ fontSize: '10px', marginTop: '4px' }}>
                        {error.stack}
                      </pre>
                    </div>
                  )}
                </div>
              }
              type="warning"
              style={{ marginTop: '16px', textAlign: 'left' }}
            />
          )}
          
          {retryCount > 0 && (
            <Alert
              message={`已重试 ${retryCount} 次`}
              type="info"
              style={{ marginTop: '8px' }}
            />
          )}
        </div>
      </Result>
    </div>
  );
};

export default ErrorRetry;

// 错误重试Hook
export const useErrorRetry = (
  asyncFunction: () => Promise<any>,
  maxRetries: number = 3,
  retryDelay: number = 1000
) => {
  const [error, setError] = useState<Error | null>(null);
  const [loading, setLoading] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  const execute = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await asyncFunction();
      setRetryCount(0); // 成功后重置重试计数
      return result;
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const retry = async () => {
    if (retryCount >= maxRetries) {
      throw new Error('已达到最大重试次数');
    }

    setRetryCount(prev => prev + 1);
    
    if (retryDelay > 0) {
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }

    return execute();
  };

  return {
    execute,
    retry,
    error,
    loading,
    retryCount,
    canRetry: retryCount < maxRetries,
  };
};
