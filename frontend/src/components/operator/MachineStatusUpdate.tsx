import React, { useState } from 'react';
import { Card, Space, Button, Select, Input, Row, Col, Badge, Descriptions, App } from 'antd';
import { SettingOutlined, ReloadOutlined } from '@ant-design/icons';
import { useAuthStore } from '@/store/auth';


const { Option } = Select;
const { TextArea } = Input;

/**
 * 设备状态更新组件
 * 基于操作员技能显示可操作的设备
 */
const MachineStatusUpdate: React.FC = () => {
  const { message } = App.useApp();
  const { user } = useAuthStore();
  const [selectedMachine, setSelectedMachine] = useState<string>('');
  const [newStatus, setNewStatus] = useState<string>('');
  const [notes, setNotes] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  // 模拟用户可操作的设备（基于技能匹配）
  const availableMachines = [
    { 
      id: 'CNC-001', 
      name: 'CNC加工中心-001', 
      currentStatus: 'running',
      skill: 'CNC Machining',
      location: '车间A-01',
      operator: '张三'
    },
    { 
      id: 'CNC-002', 
      name: 'CNC加工中心-002', 
      currentStatus: 'idle',
      skill: 'CNC Machining',
      location: '车间A-02',
      operator: null
    },
    { 
      id: 'MILL-003', 
      name: '立式铣床-003', 
      currentStatus: 'maintenance',
      skill: 'Milling',
      location: '车间B-01',
      operator: null
    },
    { 
      id: 'TURN-004', 
      name: '数控车床-004', 
      currentStatus: 'running',
      skill: 'Turning',
      location: '车间B-02',
      operator: '李四'
    },
  ];

  const statusOptions = [
    { value: 'running', label: '运行中', color: '#52c41a' },
    { value: 'idle', label: '空闲', color: '#faad14' },
    { value: 'maintenance', label: '维护中', color: '#ff4d4f' },
    { value: 'error', label: '故障', color: '#ff4d4f' },
    { value: 'setup', label: '调试中', color: '#1890ff' },
  ];

  const getStatusColor = (status: string) => {
    const statusMap: { [key: string]: any } = {
      running: 'processing',
      idle: 'default',
      maintenance: 'warning',
      error: 'error',
      setup: 'processing'
    };
    return statusMap[status] || 'default';
  };

  const getStatusText = (status: string) => {
    const statusMap: { [key: string]: string } = {
      running: '运行中',
      idle: '空闲',
      maintenance: '维护中',
      error: '故障',
      setup: '调试中'
    };
    return statusMap[status] || status;
  };

  const selectedMachineInfo = availableMachines.find(m => m.id === selectedMachine);

  const handleStatusUpdate = async () => {
    if (!selectedMachine || !newStatus) {
      message.warning('请选择设备和新状态');
      return;
    }

    setIsLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      message.success(`设备 ${selectedMachineInfo?.name} 状态已更新为：${getStatusText(newStatus)}`);
      
      // 重置表单
      setSelectedMachine('');
      setNewStatus('');
      setNotes('');
    } catch (error) {
      message.error('设备状态更新失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 根据用户技能过滤设备
  const userSkills = user?.skills || [];
  const filteredMachines = availableMachines.filter(machine => 
    userSkills.includes(machine.skill)
  );

  return (
    <div>
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <Card title="设备状态更新" className="operator-machine-card">
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              
              {/* 设备选择 */}
              <div>
                <label style={{ fontWeight: 'bold', fontSize: 16, marginBottom: 8, display: 'block' }}>
                  选择设备
                </label>
                <Select
                  placeholder="选择要更新状态的设备"
                  value={selectedMachine}
                  onChange={setSelectedMachine}
                  style={{ width: '100%' }}
                  size="large"
                >
                  {filteredMachines.map(machine => (
                    <Option key={machine.id} value={machine.id}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>{machine.name}</span>
                        <Badge 
                          status={getStatusColor(machine.currentStatus)} 
                          text={getStatusText(machine.currentStatus)}
                        />
                      </div>
                    </Option>
                  ))}
                </Select>
                {filteredMachines.length === 0 && (
                  <div style={{ fontSize: 12, color: '#ff4d4f', marginTop: 4 }}>
                    您没有可操作的设备权限
                  </div>
                )}
              </div>
              
              {/* 状态选择 */}
              <div>
                <label style={{ fontWeight: 'bold', fontSize: 16, marginBottom: 8, display: 'block' }}>
                  新状态
                </label>
                <Select
                  placeholder="选择新的设备状态"
                  value={newStatus}
                  onChange={setNewStatus}
                  style={{ width: '100%' }}
                  size="large"
                  disabled={!selectedMachine}
                >
                  {statusOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <div 
                          style={{ 
                            width: 8, 
                            height: 8, 
                            borderRadius: '50%', 
                            backgroundColor: option.color,
                            marginRight: 8 
                          }} 
                        />
                        {option.label}
                      </div>
                    </Option>
                  ))}
                </Select>
              </div>
              
              {/* 备注 */}
              <div>
                <label style={{ fontWeight: 'bold', fontSize: 16, marginBottom: 8, display: 'block' }}>
                  状态变更备注（可选）
                </label>
                <TextArea
                  placeholder="请输入状态变更的原因或备注信息"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  rows={3}
                  disabled={!selectedMachine}
                />
              </div>
              
              {/* 更新按钮 */}
              <Button 
                type="primary" 
                size="large" 
                block
                onClick={handleStatusUpdate}
                disabled={!selectedMachine || !newStatus}
                loading={isLoading}
                icon={<ReloadOutlined />}
              >
                更新设备状态
              </Button>
              
            </Space>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          {selectedMachineInfo ? (
            <Card title="设备详情" className="machine-info-card">
              <Descriptions column={1} size="small">
                <Descriptions.Item label="设备编号">
                  <strong>{selectedMachineInfo.id}</strong>
                </Descriptions.Item>
                <Descriptions.Item label="设备名称">
                  {selectedMachineInfo.name}
                </Descriptions.Item>
                <Descriptions.Item label="当前状态">
                  <Badge 
                    status={getStatusColor(selectedMachineInfo.currentStatus)} 
                    text={getStatusText(selectedMachineInfo.currentStatus)}
                  />
                </Descriptions.Item>
                <Descriptions.Item label="所需技能">
                  {selectedMachineInfo.skill}
                </Descriptions.Item>
                <Descriptions.Item label="位置">
                  {selectedMachineInfo.location}
                </Descriptions.Item>
                <Descriptions.Item label="当前操作员">
                  {selectedMachineInfo.operator || '无'}
                </Descriptions.Item>
              </Descriptions>

              {newStatus && newStatus !== selectedMachineInfo.currentStatus && (
                <div style={{ 
                  marginTop: 16, 
                  padding: 12, 
                  backgroundColor: '#f6ffed', 
                  border: '1px solid #b7eb8f',
                  borderRadius: 6 
                }}>
                  <strong>状态变更预览：</strong>
                  <br />
                  <span style={{ color: '#666' }}>
                    {getStatusText(selectedMachineInfo.currentStatus)}
                  </span>
                  <span style={{ margin: '0 8px' }}>→</span>
                  <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
                    {getStatusText(newStatus)}
                  </span>
                </div>
              )}
            </Card>
          ) : (
            <Card title="设备详情" className="machine-info-placeholder">
              <div style={{ 
                textAlign: 'center', 
                padding: '40px 0',
                color: '#999'
              }}>
                <SettingOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                <p>请先选择要更新状态的设备</p>
              </div>
            </Card>
          )}
        </Col>
      </Row>

      {/* 我的设备概览 */}
      <Row style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card title="我的设备概览">
            <Row gutter={[16, 16]}>
              {filteredMachines.map(machine => (
                <Col xs={24} sm={12} lg={6} key={machine.id}>
                  <Card 
                    size="small" 
                    title={machine.name}
                    extra={
                      <Badge 
                        status={getStatusColor(machine.currentStatus)} 
                        text={getStatusText(machine.currentStatus)}
                      />
                    }
                  >
                    <p><strong>位置：</strong>{machine.location}</p>
                    <p><strong>技能：</strong>{machine.skill}</p>
                    <p><strong>操作员：</strong>{machine.operator || '无'}</p>
                  </Card>
                </Col>
              ))}
            </Row>
            {filteredMachines.length === 0 && (
              <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                您当前没有可操作的设备
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default MachineStatusUpdate;
