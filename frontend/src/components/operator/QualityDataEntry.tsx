import React, { useState } from 'react';
import { Card, Space, Button, InputNumber, Radio, App, Row, Col, Descriptions, Alert } from 'antd';
import { SafetyCertificateOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import OperatorInput from '@/components/OperatorInput';

interface QualityMeasurement {
  id: string;
  name: string;
  standardValue: number;
  tolerance: number;
  unit: string;
  measuredValue?: number;
}

/**
 * 质量数据录入组件
 * 支持扫码和手动输入序列号，录入质量检测数据
 */
const QualityDataEntry: React.FC = () => {
  const { message } = App.useApp();
  const [serialNumber, setSerialNumber] = useState('');
  const [inspectionResult, setInspectionResult] = useState<'pass' | 'fail' | ''>('');
  const [remarks, setRemarks] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [qualityParameters, setQualityParameters] = useState<QualityMeasurement[]>([]);
  const [partInfo, setPartInfo] = useState<any>(null);

  // 模拟质量检测参数
  const mockQualityParameters: QualityMeasurement[] = [
    { id: '1', name: '外径', standardValue: 50.0, tolerance: 0.05, unit: 'mm' },
    { id: '2', name: '长度', standardValue: 200.0, tolerance: 0.1, unit: 'mm' },
    { id: '3', name: '表面粗糙度', standardValue: 1.6, tolerance: 0.2, unit: 'μm' },
    { id: '4', name: '圆度', standardValue: 0.01, tolerance: 0.005, unit: 'mm' },
  ];

  const handleLookupPart = async () => {
    if (!serialNumber) {
      message.warning('请输入产品序列号');
      return;
    }

    setIsLoading(true);
    try {
      // 模拟API调用查询零件信息
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟返回的零件信息
      const mockPartInfo = {
        serialNumber,
        partName: '齿轮轴',
        partNumber: 'P123456',
        workOrder: 'WO20241206001',
        batchNumber: 'B20241206001',
        operator: '张三',
        processStep: '精加工',
        machineId: 'CNC-001'
      };
      
      setPartInfo(mockPartInfo);
      setQualityParameters(mockQualityParameters.map(param => ({ ...param, measuredValue: undefined })));
      message.success('零件信息加载成功');
    } catch (error) {
      message.error('查询零件信息失败');
    } finally {
      setIsLoading(false);
    }
  };

  const updateQualityData = (paramId: string, value: number | null) => {
    setQualityParameters(prev => 
      prev.map(param => 
        param.id === paramId 
          ? { ...param, measuredValue: value || undefined }
          : param
      )
    );
  };

  const checkQualityResult = () => {
    const allMeasured = qualityParameters.every(param => param.measuredValue !== undefined);
    if (!allMeasured) return '';

    const allPass = qualityParameters.every(param => {
      const deviation = Math.abs((param.measuredValue || 0) - param.standardValue);
      return deviation <= param.tolerance;
    });

    return allPass ? 'pass' : 'fail';
  };

  const autoResult = checkQualityResult();
  if (autoResult && autoResult !== inspectionResult) {
    setInspectionResult(autoResult);
  }

  const handleQualitySubmit = async () => {
    if (!serialNumber || !inspectionResult) {
      message.warning('请完成所有必填项');
      return;
    }

    const allMeasured = qualityParameters.every(param => param.measuredValue !== undefined);
    if (!allMeasured) {
      message.warning('请完成所有质量检测项的测量');
      return;
    }

    setIsLoading(true);
    try {
      // 模拟API调用提交质量数据
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      message.success(`质量数据提交成功！检验结果：${inspectionResult === 'pass' ? '合格' : '不合格'}`);
      
      // 重置表单
      setSerialNumber('');
      setInspectionResult('');
      setRemarks('');
      setQualityParameters([]);
      setPartInfo(null);
    } catch (error) {
      message.error('质量数据提交失败');
    } finally {
      setIsLoading(false);
    }
  };

  const isFormValid = () => {
    const allMeasured = qualityParameters.every(param => param.measuredValue !== undefined);
    return serialNumber && inspectionResult && allMeasured;
  };

  return (
    <div>
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <Card title="质量数据录入" className="operator-quality-card">
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              
              {/* 序列号输入 */}
              <OperatorInput
                label="产品序列号"
                value={serialNumber}
                onChange={setSerialNumber}
                inputType="serialNumber"
                placeholder="扫描或输入产品序列号"
              />
              
              <Button 
                type="default" 
                onClick={handleLookupPart}
                loading={isLoading}
                disabled={!serialNumber}
                style={{ width: '100%' }}
                size="large"
              >
                查询零件信息
              </Button>

              {/* 质量检测项 */}
              {qualityParameters.length > 0 && (
                <div className="quality-measurements">
                  <h4 style={{ marginBottom: 16 }}>质量检测项</h4>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    {qualityParameters.map((param) => {
                      const deviation = param.measuredValue !== undefined 
                        ? Math.abs(param.measuredValue - param.standardValue)
                        : 0;
                      const isWithinTolerance = deviation <= param.tolerance;
                      
                      return (
                        <div key={param.id} className="measurement-item" style={{
                          padding: '12px',
                          border: '1px solid #d9d9d9',
                          borderRadius: '6px',
                          backgroundColor: param.measuredValue !== undefined 
                            ? (isWithinTolerance ? '#f6ffed' : '#fff2f0')
                            : '#fafafa'
                        }}>
                          <div style={{ marginBottom: 8 }}>
                            <strong>{param.name}</strong>
                            <span style={{ color: '#666', marginLeft: 8 }}>
                              标准值: {param.standardValue} ± {param.tolerance} {param.unit}
                            </span>
                          </div>
                          <InputNumber
                            placeholder={`请输入${param.name}测量值`}
                            value={param.measuredValue}
                            onChange={(value) => updateQualityData(param.id, value)}
                            style={{ width: '100%' }}
                            size="large"
                            step={0.01}
                            precision={2}
                            addonAfter={param.unit}
                          />
                          {param.measuredValue !== undefined && (
                            <div style={{ 
                              marginTop: 4, 
                              fontSize: 12,
                              color: isWithinTolerance ? '#52c41a' : '#ff4d4f'
                            }}>
                              偏差: {deviation.toFixed(3)} {param.unit} 
                              {isWithinTolerance ? ' ✓ 合格' : ' ✗ 超差'}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </Space>
                </div>
              )}
              
              {/* 检验结果 */}
              {qualityParameters.length > 0 && (
                <div className="inspection-result">
                  <label style={{ fontWeight: 'bold', fontSize: 16, marginBottom: 8, display: 'block' }}>
                    检验结果
                  </label>
                  <Radio.Group 
                    value={inspectionResult} 
                    onChange={(e) => setInspectionResult(e.target.value)}
                    size="large"
                    style={{ width: '100%' }}
                  >
                    <Radio.Button 
                      value="pass" 
                      style={{ 
                        width: '50%', 
                        textAlign: 'center',
                        color: inspectionResult === 'pass' ? '#52c41a' : undefined
                      }}
                    >
                      <CheckCircleOutlined /> 合格
                    </Radio.Button>
                    <Radio.Button 
                      value="fail" 
                      style={{ 
                        width: '50%', 
                        textAlign: 'center',
                        color: inspectionResult === 'fail' ? '#ff4d4f' : undefined
                      }}
                    >
                      <CloseCircleOutlined /> 不合格
                    </Radio.Button>
                  </Radio.Group>
                </div>
              )}
              
              {/* 提交按钮 */}
              <Button 
                type="primary" 
                size="large" 
                block
                onClick={handleQualitySubmit}
                disabled={!isFormValid()}
                loading={isLoading}
                icon={<SafetyCertificateOutlined />}
              >
                提交质量数据
              </Button>
              
            </Space>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          {partInfo ? (
            <Card title="零件信息" className="part-info-card">
              <Alert
                message="零件信息已加载"
                description="请完成质量检测并录入数据"
                type="success"
                showIcon
                style={{ marginBottom: 16 }}
              />
              
              <Descriptions column={1} size="small">
                <Descriptions.Item label="序列号">
                  <strong>{partInfo.serialNumber}</strong>
                </Descriptions.Item>
                <Descriptions.Item label="零件名称">
                  {partInfo.partName}
                </Descriptions.Item>
                <Descriptions.Item label="零件号">
                  {partInfo.partNumber}
                </Descriptions.Item>
                <Descriptions.Item label="工单号">
                  {partInfo.workOrder}
                </Descriptions.Item>
                <Descriptions.Item label="批次号">
                  {partInfo.batchNumber}
                </Descriptions.Item>
                <Descriptions.Item label="操作员">
                  {partInfo.operator}
                </Descriptions.Item>
                <Descriptions.Item label="工艺步骤">
                  {partInfo.processStep}
                </Descriptions.Item>
                <Descriptions.Item label="加工设备">
                  {partInfo.machineId}
                </Descriptions.Item>
              </Descriptions>

              {inspectionResult && (
                <div style={{ 
                  marginTop: 16, 
                  padding: 12, 
                  backgroundColor: inspectionResult === 'pass' ? '#f6ffed' : '#fff2f0',
                  border: `1px solid ${inspectionResult === 'pass' ? '#b7eb8f' : '#ffccc7'}`,
                  borderRadius: 6 
                }}>
                  <strong>检验结果预览：</strong>
                  <br />
                  <span style={{ 
                    color: inspectionResult === 'pass' ? '#52c41a' : '#ff4d4f',
                    fontWeight: 'bold',
                    fontSize: 16
                  }}>
                    {inspectionResult === 'pass' ? '✓ 合格' : '✗ 不合格'}
                  </span>
                </div>
              )}
            </Card>
          ) : (
            <Card title="零件信息" className="part-info-placeholder">
              <div style={{ 
                textAlign: 'center', 
                padding: '40px 0',
                color: '#999'
              }}>
                <SafetyCertificateOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                <p>请先输入产品序列号并查询零件信息</p>
              </div>
            </Card>
          )}
        </Col>
      </Row>
    </div>
  );
};

export default QualityDataEntry;
