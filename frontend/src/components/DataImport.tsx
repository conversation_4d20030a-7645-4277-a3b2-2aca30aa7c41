import React, { useState } from 'react';
import {
  Modal,
  Upload,
  Button,
  Steps,
  Table,
  Alert,
  Typography,
  Space,
  Tag,
  message,
  Card,
} from 'antd';
import {
  UploadOutlined,
  DownloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
} from '@ant-design/icons';
import { useMutation } from 'react-query';
import type { UploadFile } from 'antd/es/upload/interface';
import { apiClient } from '@/lib/api';

const { Step } = Steps;
const { Title, Text } = Typography;
const { Dragger } = Upload;

interface DataImportProps {
  visible: boolean;
  onCancel: () => void;
  moduleType: string;
  moduleName: string;
  onSuccess?: () => void;
}

interface ImportJob {
  id: number;
  status: string;
  total_rows?: number;
  processed_rows: number;
  success_rows: number;
  error_rows: number;
}

interface ImportPreview {
  headers: string[];
  sample_data: Record<string, string>[];
  total_rows: number;
  validation_errors: ValidationError[];
}

interface ValidationError {
  row_number: number;
  column_name?: string;
  error_type: string;
  error_message: string;
  row_data: Record<string, string>;
}

interface ImportResult {
  import_job_id: number;
  status: string;
  total_rows: number;
  processed_rows: number;
  success_rows: number;
  error_rows: number;
  errors: ImportError[];
  duration_ms?: number;
}

interface ImportError {
  id: number;
  row_number: number;
  column_name?: string;
  error_type: string;
  error_message: string;
  row_data?: Record<string, string>;
}

const DataImport: React.FC<DataImportProps> = ({
  visible,
  onCancel,
  moduleType,
  moduleName,
  onSuccess,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [importJobId, setImportJobId] = useState<number | null>(null);
  const [previewData, setPreviewData] = useState<ImportPreview | null>(null);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);

  // 上传文件
  const uploadMutation = useMutation(
    async (file: File) => {
      return await apiClient.uploadImportFile(file, moduleType);
    },
    {
      onSuccess: (data) => {
        setImportJobId(data.import_job_id);
        setCurrentStep(1);
        message.success('文件上传成功');
      },
      onError: (error: Error) => {
        message.error(error.message);
      },
    }
  );

  // 预览数据
  const previewMutation = useMutation(
    async (jobId: number) => {
      return await apiClient.previewImportData(jobId);
    },
    {
      onSuccess: (data) => {
        setPreviewData(data);
        setCurrentStep(2);
      },
      onError: (error: Error) => {
        message.error(error.message);
      },
    }
  );

  // 执行导入
  const executeMutation = useMutation(
    async (jobId: number) => {
      return await apiClient.executeImport(jobId, moduleType, {
        skip_duplicates: true,
        batch_size: 100,
      });
    },
    {
      onSuccess: (data) => {
        setImportResult(data);
        setCurrentStep(3);
        if (data.error_rows === 0) {
          message.success(`导入成功！共导入 ${data.success_rows} 条记录`);
          onSuccess?.();
        } else {
          message.warning(`导入完成，成功 ${data.success_rows} 条，失败 ${data.error_rows} 条`);
        }
      },
      onError: (error: Error) => {
        message.error(error.message);
      },
    }
  );

  // 下载模板
  const downloadTemplate = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/import/template?module_type=${moduleType}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('下载模板失败');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${moduleType}_template.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      message.success('模板下载成功');
    } catch (error) {
      message.error('下载模板失败');
    }
  };

  const handleUpload = (file: File) => {
    uploadMutation.mutate(file);
    return false; // 阻止默认上传行为
  };

  const handlePreview = () => {
    if (importJobId) {
      previewMutation.mutate(importJobId);
    }
  };

  const handleExecute = () => {
    if (importJobId) {
      executeMutation.mutate(importJobId);
    }
  };

  const handleCancel = () => {
    setCurrentStep(0);
    setFileList([]);
    setImportJobId(null);
    setPreviewData(null);
    setImportResult(null);
    onCancel();
  };

  const renderUploadStep = () => (
    <div>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ textAlign: 'center', marginBottom: 16 }}>
          <Button
            type="link"
            icon={<DownloadOutlined />}
            onClick={downloadTemplate}
          >
            下载{moduleName}导入模板
          </Button>
        </div>
        
        <Dragger
          accept=".csv,.xlsx"
          beforeUpload={handleUpload}
          fileList={fileList}
          onChange={({ fileList }) => setFileList(fileList)}
          maxCount={1}
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持CSV和Excel格式，文件大小不超过10MB
          </p>
        </Dragger>

        <Alert
          message="导入说明"
          description={
            <ul style={{ margin: 0, paddingLeft: 20 }}>
              <li>请先下载模板文件，按照模板格式填写数据</li>
              <li>第一行必须是列标题，不要修改标题名称</li>
              <li>必填字段不能为空</li>
              <li>重复数据将被跳过</li>
            </ul>
          }
          type="info"
          showIcon
        />
      </Space>
    </div>
  );

  const renderPreviewStep = () => (
    <div>
      {previewData && (
        <Space direction="vertical" style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Title level={5}>数据预览（共 {previewData.total_rows} 行）</Title>
            <Button
              type="primary"
              onClick={handleExecute}
              disabled={previewData.validation_errors.length > 0}
              loading={executeMutation.isLoading}
            >
              开始导入
            </Button>
          </div>

          {previewData.validation_errors.length > 0 && (
            <Alert
              message={`发现 ${previewData.validation_errors.length} 个验证错误`}
              description="请修复错误后重新上传文件"
              type="error"
              showIcon
            />
          )}

          <Table
            dataSource={previewData.sample_data}
            columns={previewData.headers.map(header => ({
              title: header,
              dataIndex: header,
              key: header,
              ellipsis: true,
            }))}
            pagination={false}
            size="small"
            scroll={{ x: true }}
          />
        </Space>
      )}
    </div>
  );

  const renderResultStep = () => (
    <div>
      {importResult && (
        <Space direction="vertical" style={{ width: '100%' }}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              {importResult.error_rows === 0 ? (
                <CheckCircleOutlined style={{ fontSize: 48, color: '#52c41a' }} />
              ) : (
                <ExclamationCircleOutlined style={{ fontSize: 48, color: '#faad14' }} />
              )}
              
              <Title level={4} style={{ marginTop: 16 }}>
                {importResult.error_rows === 0 ? '导入成功' : '导入完成'}
              </Title>
              
              <Space size="large">
                <div>
                  <Text type="secondary">总计</Text>
                  <br />
                  <Text strong style={{ fontSize: 18 }}>{importResult.total_rows}</Text>
                </div>
                <div>
                  <Text type="success">成功</Text>
                  <br />
                  <Text strong style={{ fontSize: 18, color: '#52c41a' }}>
                    {importResult.success_rows}
                  </Text>
                </div>
                <div>
                  <Text type="danger">失败</Text>
                  <br />
                  <Text strong style={{ fontSize: 18, color: '#ff4d4f' }}>
                    {importResult.error_rows}
                  </Text>
                </div>
              </Space>

              {importResult.duration_ms && (
                <div style={{ marginTop: 16 }}>
                  <Text type="secondary">
                    耗时: {(importResult.duration_ms / 1000).toFixed(2)} 秒
                  </Text>
                </div>
              )}
            </div>
          </Card>

          {importResult.errors.length > 0 && (
            <div>
              <Title level={5}>错误详情</Title>
              <Table
                dataSource={importResult.errors}
                columns={[
                  {
                    title: '行号',
                    dataIndex: 'row_number',
                    key: 'row_number',
                    width: 80,
                  },
                  {
                    title: '列名',
                    dataIndex: 'column_name',
                    key: 'column_name',
                    width: 120,
                  },
                  {
                    title: '错误类型',
                    dataIndex: 'error_type',
                    key: 'error_type',
                    width: 100,
                    render: (type: string) => (
                      <Tag color={type === 'validation' ? 'red' : 'orange'}>
                        {type}
                      </Tag>
                    ),
                  },
                  {
                    title: '错误信息',
                    dataIndex: 'error_message',
                    key: 'error_message',
                    ellipsis: true,
                  },
                ]}
                pagination={{ pageSize: 10 }}
                size="small"
              />
            </div>
          )}
        </Space>
      )}
    </div>
  );

  return (
    <Modal
      title={`导入${moduleName}数据`}
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={800}
      destroyOnHidden
    >
      <Steps current={currentStep} style={{ marginBottom: 24 }}>
        <Step title="上传文件" />
        <Step title="预览数据" />
        <Step title="执行导入" />
        <Step title="导入结果" />
      </Steps>

      {currentStep === 0 && renderUploadStep()}
      {currentStep === 1 && (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <LoadingOutlined style={{ fontSize: 24 }} />
          <div style={{ marginTop: 16 }}>正在解析文件...</div>
          <Button
            type="primary"
            onClick={handlePreview}
            loading={previewMutation.isLoading}
            style={{ marginTop: 16 }}
          >
            预览数据
          </Button>
        </div>
      )}
      {currentStep === 2 && renderPreviewStep()}
      {currentStep === 3 && renderResultStep()}
    </Modal>
  );
};

export default DataImport;
