import React, { useState, useMemo, useCallback } from 'react';
import { Select, Card, Row, Col, Typography, Tabs, Input, Space, Empty } from 'antd';
import { ProjectOutlined, FileTextOutlined } from '@ant-design/icons';
import { useQuery } from 'react-query';
import { apiClient } from '@/lib/api';
import type { WorkOrder } from '@/types/api';

const { Text, Title } = Typography;
const { Search } = Input;

interface WorkOrderSelectorProps {
  value?: number;
  onChange?: (workOrderId: number, workOrder: WorkOrder) => void;
  placeholder?: string;
  size?: 'small' | 'middle' | 'large';
  style?: React.CSSProperties;
  disabled?: boolean;
  allowClear?: boolean;
  showWorkOrderInfo?: boolean;
  mode?: 'all' | 'project-based';
}

const WorkOrderSelector: React.FC<WorkOrderSelectorProps> = ({
  value,
  onChange,
  placeholder = "搜索并选择工单",
  size = 'middle',
  style,
  disabled = false,
  allowClear = true,
  showWorkOrderInfo = false,
  mode = 'all',
}) => {
  const [searchText, setSearchText] = useState('');
  const [selectedProject, setSelectedProject] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState<string>(mode === 'project-based' ? 'by-project' : 'all-orders');
  const [selectedWorkOrder, setSelectedWorkOrder] = useState<WorkOrder | null>(null);

  // 获取所有项目
  const { data: projects = [] } = useQuery(
    'projects',
    () => apiClient.getProjects(),
    { enabled: mode === 'project-based' || activeTab === 'by-project' }
  );

  // 获取所有工单
  const { data: allWorkOrders = [], isLoading: isLoadingAllOrders } = useQuery(
    'work-orders',
    () => apiClient.getWorkOrders(),
    { enabled: activeTab === 'all-orders' }
  );

  // 按项目过滤工单
  const projectWorkOrders = useMemo(() => {
    if (!selectedProject || !allWorkOrders.length) return [];
    return allWorkOrders.filter(wo => wo.project_id === selectedProject);
  }, [selectedProject, allWorkOrders]);

  // 处理工单数据
  const processedWorkOrders = useMemo(() => {
    if (activeTab === 'all-orders') {
      return allWorkOrders;
    } else if (activeTab === 'by-project') {
      return projectWorkOrders;
    }
    return [];
  }, [activeTab, allWorkOrders, projectWorkOrders]);

  // 过滤工单
  const filteredWorkOrders = useMemo(() => {
    if (!searchText) return processedWorkOrders;
    
    const searchLower = searchText.toLowerCase();
    return processedWorkOrders.filter(wo =>
      wo.id.toString().includes(searchLower) ||
      (wo.project_name && wo.project_name.toLowerCase().includes(searchLower)) ||
      (wo.part_number && wo.part_number.toLowerCase().includes(searchLower)) ||
      (wo.part_name && wo.part_name.toLowerCase().includes(searchLower))
    );
  }, [processedWorkOrders, searchText]);

  // 处理工单选择
  const handleWorkOrderSelect = useCallback((workOrderId: number) => {
    const workOrder = filteredWorkOrders.find(wo => wo.id === workOrderId);
    if (workOrder && onChange) {
      setSelectedWorkOrder(workOrder);
      onChange(workOrderId, workOrder);
    }
  }, [filteredWorkOrders, onChange]);

  // 处理搜索
  const handleSearch = useCallback((value: string) => {
    setSearchText(value);
  }, []);

  // 处理项目选择
  const handleProjectSelect = useCallback((projectId: number) => {
    setSelectedProject(projectId);
    setSearchText(''); // 清空搜索
  }, []);

  const renderWorkOrderOption = (workOrder: WorkOrder) => (
    <Select.Option key={workOrder.id} value={workOrder.id}>
      <div>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>
            <strong>工单#{workOrder.id}</strong>
            <span style={{ marginLeft: 8, color: '#666' }}>
              {workOrder.project_name}
            </span>
          </span>
          <span style={{ color: '#1890ff', fontSize: '12px' }}>
            数量: {workOrder.quantity}
          </span>
        </div>
        <div style={{ fontSize: '12px', color: '#999', marginTop: 2 }}>
          {workOrder.part_number || '未知零件'}
          {workOrder.part_name && ` - ${workOrder.part_name}`}
        </div>
      </div>
    </Select.Option>
  );

  const renderAllOrdersTab = () => (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Search
          placeholder="搜索工单号、项目名称或零件"
          allowClear
          onSearch={handleSearch}
          onChange={(e) => handleSearch(e.target.value)}
          style={{ width: '100%' }}
        />
      </div>
      
      <Select
        value={value}
        placeholder={placeholder}
        style={{ width: '100%', ...style }}
        size={size}
        disabled={disabled}
        allowClear={allowClear}
        showSearch
        loading={isLoadingAllOrders}
        onChange={handleWorkOrderSelect}
        optionFilterProp="children"
        filterOption={false} // 使用自定义过滤
      >
        {filteredWorkOrders.map(renderWorkOrderOption)}
      </Select>
    </div>
  );

  const renderProjectBasedTab = () => (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Select
            placeholder="先选择项目"
            style={{ width: '100%' }}
            value={selectedProject}
            onChange={handleProjectSelect}
            allowClear
          >
            {projects.map(project => (
              <Select.Option key={project.id} value={project.id}>
                <div>
                  <strong>{project.project_name}</strong>
                  {project.customer_name && (
                    <span style={{ color: '#999', marginLeft: 8 }}>
                      ({project.customer_name})
                    </span>
                  )}
                </div>
              </Select.Option>
            ))}
          </Select>
          
          {selectedProject && (
            <Search
              placeholder="在项目工单中搜索"
              allowClear
              onSearch={handleSearch}
              onChange={(e) => handleSearch(e.target.value)}
              style={{ width: '100%' }}
            />
          )}
        </Space>
      </div>

      {selectedProject ? (
        <Select
          value={value}
          placeholder="从项目工单中选择"
          style={{ width: '100%', ...style }}
          size={size}
          disabled={disabled}
          allowClear={allowClear}
          onChange={handleWorkOrderSelect}
          notFoundContent={
            <Empty description="该项目暂无工单" />
          }
        >
          {filteredWorkOrders.map(renderWorkOrderOption)}
        </Select>
      ) : (
        <div style={{ 
          border: '1px dashed #d9d9d9', 
          borderRadius: 6, 
          padding: 40, 
          textAlign: 'center',
          color: '#999'
        }}>
          <ProjectOutlined style={{ fontSize: 24, marginBottom: 8 }} />
          <div>请先选择项目</div>
        </div>
      )}
    </div>
  );

  // 简单模式：只显示选择器
  if (mode === 'all') {
    return renderAllOrdersTab();
  }

  // 完整模式：显示标签页
  return (
    <div>
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'all-orders',
            label: (
              <span>
                <FileTextOutlined />
                所有工单
              </span>
            ),
            children: renderAllOrdersTab(),
          },
          {
            key: 'by-project',
            label: (
              <span>
                <ProjectOutlined />
                按项目选择
              </span>
            ),
            children: renderProjectBasedTab(),
          },
        ]}
      />

      {/* 显示选中工单的详细信息 */}
      {showWorkOrderInfo && selectedWorkOrder && (
        <Card size="small" style={{ marginTop: 16 }}>
          <Title level={5} style={{ margin: 0, marginBottom: 8 }}>选中工单信息</Title>
          <Row gutter={16}>
            <Col span={6}>
              <Text strong>工单号:</Text>
              <div>#{selectedWorkOrder.id}</div>
            </Col>
            <Col span={6}>
              <Text strong>项目:</Text>
              <div>{selectedWorkOrder.project_name}</div>
            </Col>
            <Col span={6}>
              <Text strong>零件:</Text>
              <div>{selectedWorkOrder.part_number}</div>
            </Col>
            <Col span={6}>
              <Text strong>数量:</Text>
              <div>{selectedWorkOrder.quantity}</div>
            </Col>
          </Row>
          {selectedWorkOrder.part_name && (
            <div style={{ marginTop: 8 }}>
              <Text strong>零件名称:</Text>
              <div>{selectedWorkOrder.part_name}</div>
            </div>
          )}
        </Card>
      )}
    </div>
  );
};

export default WorkOrderSelector;
