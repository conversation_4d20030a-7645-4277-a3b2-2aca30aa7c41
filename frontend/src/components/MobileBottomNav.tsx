import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Badge } from 'antd';
import {
  DashboardOutlined,
  PlayCircleOutlined,
  SafetyCertificateOutlined,
  SettingOutlined,
  MoreOutlined,
} from '@ant-design/icons';
import { useAuthStore } from '@/store/auth';
import { hasPageAccess } from '@/utils/permissions';

interface NavItem {
  key: string;
  path: string;
  icon: React.ReactNode;
  label: string;
  badge?: number;
  roles?: string[];
}

/**
 * 移动端底部导航栏
 * 提供快速访问主要功能的导航方式
 */
const MobileBottomNav: React.FC = () => {
  const [isMobile, setIsMobile] = useState(false);
  const [visible, setVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuthStore();

  // 检测移动设备
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  // 滚动隐藏/显示导航栏
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        // 向下滚动，隐藏导航栏
        setVisible(false);
      } else {
        // 向上滚动，显示导航栏
        setVisible(true);
      }
      
      setLastScrollY(currentScrollY);
    };

    if (isMobile) {
      window.addEventListener('scroll', handleScroll, { passive: true });
      return () => window.removeEventListener('scroll', handleScroll);
    }
  }, [lastScrollY, isMobile]);

  // 导航项配置
  const navItems: NavItem[] = [
    {
      key: 'dashboard',
      path: '/dashboard',
      icon: <DashboardOutlined />,
      label: '首页',
    },
    {
      key: 'production',
      path: '/production-center',
      icon: <PlayCircleOutlined />,
      label: '生产',
      roles: ['operator', 'planner', 'admin'],
    },
    {
      key: 'quality',
      path: '/quality',
      icon: <SafetyCertificateOutlined />,
      label: '质量',
      roles: ['quality_inspector', 'admin'],
    },
    {
      key: 'settings',
      path: '/system-config',
      icon: <SettingOutlined />,
      label: '设置',
      roles: ['admin'],
    },
    {
      key: 'more',
      path: '/more',
      icon: <MoreOutlined />,
      label: '更多',
    },
  ];

  // 过滤用户有权限访问的导航项
  const accessibleNavItems = navItems.filter(item => {
    if (!item.roles) return true;
    if (!user?.roles) return false;
    return item.roles.some(role => user.roles.includes(role));
  });

  // 处理导航点击
  const handleNavClick = (item: NavItem) => {
    if (item.key === 'more') {
      // 显示更多菜单
      // 这里可以打开一个包含所有页面的菜单
      return;
    }
    
    navigate(item.path);
  };

  // 检查是否为当前页面
  const isActive = (path: string) => {
    if (path === '/dashboard') {
      return location.pathname === '/' || location.pathname === '/dashboard';
    }
    return location.pathname.startsWith(path);
  };

  // 如果不是移动设备，不显示底部导航
  if (!isMobile) {
    return null;
  }

  return (
    <div
      style={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        height: '60px',
        backgroundColor: '#fff',
        borderTop: '1px solid #f0f0f0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-around',
        zIndex: 1000,
        transform: visible ? 'translateY(0)' : 'translateY(100%)',
        transition: 'transform 0.3s ease-in-out',
        boxShadow: '0 -2px 8px rgba(0, 0, 0, 0.1)',
        paddingBottom: 'env(safe-area-inset-bottom)', // 适配iPhone底部安全区域
      }}
    >
      {accessibleNavItems.map(item => {
        const active = isActive(item.path);
        
        return (
          <div
            key={item.key}
            style={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '4px 8px',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              borderRadius: '8px',
              margin: '0 4px',
              backgroundColor: active ? '#f0f8ff' : 'transparent',
              minHeight: '48px',
              position: 'relative',
            }}
            onClick={() => handleNavClick(item)}
          >
            {/* 图标 */}
            <div
              style={{
                fontSize: '20px',
                color: active ? '#1890ff' : '#666',
                marginBottom: '2px',
                transition: 'color 0.2s ease',
              }}
            >
              {item.badge ? (
                <Badge count={item.badge} size="small">
                  {item.icon}
                </Badge>
              ) : (
                item.icon
              )}
            </div>
            
            {/* 标签 */}
            <div
              style={{
                fontSize: '10px',
                color: active ? '#1890ff' : '#666',
                fontWeight: active ? '600' : '400',
                transition: 'color 0.2s ease',
                textAlign: 'center',
                lineHeight: 1,
              }}
            >
              {item.label}
            </div>
            
            {/* 活跃指示器 */}
            {active && (
              <div
                style={{
                  position: 'absolute',
                  top: '2px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '4px',
                  height: '4px',
                  backgroundColor: '#1890ff',
                  borderRadius: '50%',
                }}
              />
            )}
          </div>
        );
      })}
    </div>
  );
};

// 移动端手势导航组件
interface MobileGestureNavProps {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
}

export const MobileGestureNav: React.FC<MobileGestureNavProps> = ({
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
}) => {
  const [isMobile, setIsMobile] = useState(false);
  const [touchStart, setTouchStart] = useState<{ x: number; y: number } | null>(null);
  const [touchEnd, setTouchEnd] = useState<{ x: number; y: number } | null>(null);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  const minSwipeDistance = 50;

  const onTouchStart = (e: TouchEvent) => {
    setTouchEnd(null);
    setTouchStart({
      x: e.targetTouches[0].clientX,
      y: e.targetTouches[0].clientY,
    });
  };

  const onTouchMove = (e: TouchEvent) => {
    setTouchEnd({
      x: e.targetTouches[0].clientX,
      y: e.targetTouches[0].clientY,
    });
  };

  const onTouchEndHandler = () => {
    if (!touchStart || !touchEnd) return;
    
    const distanceX = touchStart.x - touchEnd.x;
    const distanceY = touchStart.y - touchEnd.y;
    const isLeftSwipe = distanceX > minSwipeDistance;
    const isRightSwipe = distanceX < -minSwipeDistance;
    const isUpSwipe = distanceY > minSwipeDistance;
    const isDownSwipe = distanceY < -minSwipeDistance;

    if (Math.abs(distanceX) > Math.abs(distanceY)) {
      // 水平滑动
      if (isLeftSwipe) {
        onSwipeLeft?.();
      } else if (isRightSwipe) {
        onSwipeRight?.();
      }
    } else {
      // 垂直滑动
      if (isUpSwipe) {
        onSwipeUp?.();
      } else if (isDownSwipe) {
        onSwipeDown?.();
      }
    }
  };

  useEffect(() => {
    if (isMobile) {
      document.addEventListener('touchstart', onTouchStart);
      document.addEventListener('touchmove', onTouchMove);
      document.addEventListener('touchend', onTouchEndHandler);

      return () => {
        document.removeEventListener('touchstart', onTouchStart);
        document.removeEventListener('touchmove', onTouchMove);
        document.removeEventListener('touchend', onTouchEndHandler);
      };
    }
  }, [isMobile, touchStart, touchEnd]);

  return null; // 这是一个无UI的组件
};

export default MobileBottomNav;
