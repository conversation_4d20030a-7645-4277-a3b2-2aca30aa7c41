import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Button,
  Row,
  Col,
  Card,
  Typography,
  Tag,
  Space,
  Divider,
  message,
  Descriptions,
  Spin,
  Table,
  Tabs,
} from 'antd';
import {
  EditOutlined,
  SaveOutlined,
  CloseOutlined,
  CalendarOutlined,
  ToolOutlined,
  UserOutlined,
  ProjectOutlined,
  TableOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import dayjs from '@/utils/dayjs';
import type { PlanTaskWithDetails, UpdatePlanTaskRequest, Machine, SkillGroup } from '@/types/api';
import { apiClient } from '@/lib/api';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

interface TaskDetailModalProps {
  visible: boolean;
  taskId: number | null;
  onCancel: () => void;
  onUpdate?: (taskId: number, data: UpdatePlanTaskRequest) => void;
}

const TaskDetailModal: React.FC<TaskDetailModalProps> = ({
  visible,
  taskId,
  onCancel,
  onUpdate,
}) => {
  const [form] = Form.useForm();
  const [isEditing, setIsEditing] = useState(false);
  const queryClient = useQueryClient();

  // 获取任务详情
  const { data: task, isLoading: taskLoading } = useQuery(
    ['planTask', taskId],
    () => taskId ? apiClient.getPlanTaskById(taskId) : null,
    {
      enabled: !!taskId && visible,
    }
  );

  // 获取技能组列表
  const { data: skillGroups } = useQuery(
    'skillGroups',
    () => apiClient.getSkillGroups(),
    {
      enabled: visible,
    }
  );

  // 获取设备列表
  const { data: machines } = useQuery(
    'machines',
    () => apiClient.getMachines(),
    {
      enabled: visible,
    }
  );

  // 更新任务
  const updateMutation = useMutation(
    ({ id, data }: { id: number; data: UpdatePlanTaskRequest }) =>
      apiClient.updatePlanTask(id, data),
    {
      onSuccess: () => {
        message.success('任务更新成功');
        setIsEditing(false);
        queryClient.invalidateQueries(['planTask', taskId]);
        queryClient.invalidateQueries('planTasks');
        if (onUpdate && taskId) {
          onUpdate(taskId, form.getFieldsValue());
        }
      },
      onError: (error: any) => {
        message.error(error.response?.data?.message || '更新失败');
      },
    }
  );

  // 当任务数据加载完成时，设置表单值
  useEffect(() => {
    if (task && visible) {
      form.setFieldsValue({
        skill_group_id: task.skill_group_id,
        machine_id: task.machine_id,
        planned_start: task.planned_start ? dayjs(task.planned_start) : null,
        planned_end: task.planned_end ? dayjs(task.planned_end) : null,
        status: task.status,
        work_instructions: task.work_instructions,
      });
    }
  }, [task, visible, form]);

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      const updateData: UpdatePlanTaskRequest = {
        skill_group_id: values.skill_group_id,
        machine_id: values.machine_id || null,
        planned_start: values.planned_start?.toISOString(),
        planned_end: values.planned_end?.toISOString(),
        status: values.status,
      };

      if (taskId) {
        updateMutation.mutate({ id: taskId, data: updateData });
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    if (task) {
      form.setFieldsValue({
        skill_group_id: task.skill_group_id,
        machine_id: task.machine_id,
        planned_start: task.planned_start ? dayjs(task.planned_start) : null,
        planned_end: task.planned_end ? dayjs(task.planned_end) : null,
        status: task.status,
        work_instructions: task.work_instructions,
      });
    }
    onCancel();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'default';
      case 'in_progress': return 'processing';
      case 'completed': return 'success';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'planned': return '已计划';
      case 'scheduled': return '已调度';
      case 'in_progress': return '进行中';
      case 'completed': return '已完成';
      case 'cancelled': return '已取消';
      case 'on_hold': return '暂停';
      default: return status;
    }
  };

  // 过滤当前技能组的设备
  const availableMachines = machines?.filter(
    machine => machine.skill_group_id === form.getFieldValue('skill_group_id')
  ) || [];

  // 模拟工序详情数据表格
  const processColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 60,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: '工序名称',
      dataIndex: 'process_name',
      key: 'process_name',
      width: 120,
    },
    {
      title: '工序代码',
      dataIndex: 'process_code',
      key: 'process_code',
      width: 100,
    },
    {
      title: '设备编号',
      dataIndex: 'machine_code',
      key: 'machine_code',
      width: 100,
    },
    {
      title: '计划数量',
      dataIndex: 'planned_quantity',
      key: 'planned_quantity',
      width: 80,
      align: 'right' as const,
    },
    {
      title: '完成数量',
      dataIndex: 'completed_quantity',
      key: 'completed_quantity',
      width: 80,
      align: 'right' as const,
    },
    {
      title: '合格数量',
      dataIndex: 'qualified_quantity',
      key: 'qualified_quantity',
      width: 80,
      align: 'right' as const,
    },
    {
      title: '不合格数量',
      dataIndex: 'defective_quantity',
      key: 'defective_quantity',
      width: 90,
      align: 'right' as const,
    },
    {
      title: '开始时间',
      dataIndex: 'start_time',
      key: 'start_time',
      width: 140,
    },
    {
      title: '结束时间',
      dataIndex: 'end_time',
      key: 'end_time',
      width: 140,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={status === 'completed' ? 'green' : status === 'in_progress' ? 'blue' : 'default'}>
          {status === 'completed' ? '已完成' : status === 'in_progress' ? '进行中' : '待开始'}
        </Tag>
      ),
    },
  ];

  // 模拟工序详情数据
  const processData = task ? [
    {
      key: '1',
      process_name: '切割',
      process_code: 'P001',
      machine_code: 'M001',
      planned_quantity: 100,
      completed_quantity: 100,
      qualified_quantity: 98,
      defective_quantity: 2,
      start_time: '2024-01-15 08:00:00',
      end_time: '2024-01-15 12:00:00',
      status: 'completed',
    },
    {
      key: '2',
      process_name: '钻孔',
      process_code: 'P002',
      machine_code: 'M002',
      planned_quantity: 98,
      completed_quantity: 65,
      qualified_quantity: 63,
      defective_quantity: 2,
      start_time: '2024-01-15 13:00:00',
      end_time: '-',
      status: 'in_progress',
    },
    {
      key: '3',
      process_name: '组装',
      process_code: 'P003',
      machine_code: 'M003',
      planned_quantity: 63,
      completed_quantity: 0,
      qualified_quantity: 0,
      defective_quantity: 0,
      start_time: '-',
      end_time: '-',
      status: 'pending',
    },
    {
      key: '4',
      process_name: '检验',
      process_code: 'P004',
      machine_code: 'M004',
      planned_quantity: 63,
      completed_quantity: 0,
      qualified_quantity: 0,
      defective_quantity: 0,
      start_time: '-',
      end_time: '-',
      status: 'pending',
    },
    {
      key: '5',
      process_name: '包装',
      process_code: 'P005',
      machine_code: 'M005',
      planned_quantity: 63,
      completed_quantity: 0,
      qualified_quantity: 0,
      defective_quantity: 0,
      start_time: '-',
      end_time: '-',
      status: 'pending',
    },
  ] : [];

  return (
    <Modal
      title={
        <Space>
          <ToolOutlined />
          <span>任务详情</span>
          {task && (
            <Tag color={getStatusColor(task.status)}>
              {getStatusText(task.status)}
            </Tag>
          )}
        </Space>
      }
      open={visible}
      onCancel={handleCancel}
      width="95vw"
      style={{ top: 10 }}
      styles={{
        body: {
          height: 'calc(95vh - 110px)',
          overflow: 'auto',
          padding: '16px',
          backgroundColor: '#f5f5f5'
        }
      }}
      footer={
        <Space>
          {isEditing ? (
            <>
              <Button onClick={() => setIsEditing(false)}>
                取消编辑
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                loading={updateMutation.isLoading}
                onClick={handleSave}
              >
                保存
              </Button>
            </>
          ) : (
            <>
              <Button onClick={handleCancel}>
                关闭
              </Button>
              <Button
                type="primary"
                icon={<EditOutlined />}
                onClick={() => setIsEditing(true)}
              >
                编辑
              </Button>
            </>
          )}
        </Space>
      }
    >
      {taskLoading ? (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      ) : task ? (
        <Tabs defaultActiveKey="1" type="card" size="small">
          <TabPane
            tab={
              <Space>
                <InfoCircleOutlined />
                基本信息
              </Space>
            }
            key="1"
          >
            <Row gutter={[16, 16]}>
              {/* 基本信息 */}
              <Col span={8}>
                <Card
                  title={
                    <Space>
                      <ProjectOutlined />
                      <span>基本信息</span>
                    </Space>
                  }
                  size="small"
                  style={{ height: '100%' }}
                >
                  <Descriptions column={1} size="small">
                    <Descriptions.Item label="任务ID">{task.id}</Descriptions.Item>
                    <Descriptions.Item label="工序名称">{task.process_name}</Descriptions.Item>
                    <Descriptions.Item label="工序步骤">第 {task.step_number} 步</Descriptions.Item>
                    <Descriptions.Item label="标准工时">
                      {task.standard_hours ? `${task.standard_hours} 小时` : '未设置'}
                    </Descriptions.Item>
                    <Descriptions.Item label="工作指导书">
                      {task.work_instructions || '无'}
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
              </Col>

              {/* 项目信息 */}
              <Col span={8}>
                <Card
                  title={
                    <Space>
                      <ProjectOutlined />
                      <span>项目信息</span>
                    </Space>
                  }
                  size="small"
                  style={{ height: '100%' }}
                >
                  <Descriptions column={1} size="small">
                    <Descriptions.Item label="项目名称">{task.project_name}</Descriptions.Item>
                    <Descriptions.Item label="客户名称">{task.customer_name || '无'}</Descriptions.Item>
                    <Descriptions.Item label="零件编号">{task.part_number}</Descriptions.Item>
                    <Descriptions.Item label="零件名称">{task.part_name || '无'}</Descriptions.Item>
                    <Descriptions.Item label="版本">{task.version}</Descriptions.Item>
                  </Descriptions>
                </Card>
              </Col>

              {/* 工单信息 */}
              <Col span={8}>
                <Card
                  title={
                    <Space>
                      <CalendarOutlined />
                      <span>工单信息</span>
                    </Space>
                  }
                  size="small"
                  style={{ height: '100%' }}
                >
                  <Descriptions column={1} size="small">
                    <Descriptions.Item label="工单ID">{task.work_order_id}</Descriptions.Item>
                    <Descriptions.Item label="生产数量">{task.work_order_quantity}</Descriptions.Item>
                    <Descriptions.Item label="工单状态">
                      <Tag color={getStatusColor(task.work_order_status)}>
                        {getStatusText(task.work_order_status)}
                      </Tag>
                    </Descriptions.Item>
                    <Descriptions.Item label="交期">
                      {task.work_order_due_date || '未设置'}
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
              </Col>

              {/* 编辑表单 */}
              <Col span={24}>
                <Card
                  title={
                    <Space>
                      <UserOutlined />
                      <span>分配信息</span>
                      {isEditing && <Tag color="blue">编辑模式</Tag>}
                    </Space>
                  }
                  size="small"
                >
                  <Form
                    form={form}
                    layout="inline"
                    disabled={!isEditing}
                  >
                    <Form.Item
                      name="skill_group_id"
                      label="技能组"
                      rules={[{ required: true, message: '请选择技能组' }]}
                    >
                      <Select placeholder="请选择技能组" style={{ width: 150 }}>
                        {skillGroups?.map(group => (
                          <Option key={group.id} value={group.id}>
                            {group.group_name}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>

                    <Form.Item
                      name="machine_id"
                      label="设备"
                    >
                      <Select placeholder="请选择设备（可选）" allowClear style={{ width: 150 }}>
                        {availableMachines.map(machine => (
                          <Option key={machine.id} value={machine.id}>
                            {machine.machine_name}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>

                    <Form.Item
                      name="planned_start"
                      label="计划开始时间"
                      rules={[{ required: true, message: '请选择开始时间' }]}
                    >
                      <DatePicker
                        showTime
                        format="YYYY-MM-DD HH:mm"
                        style={{ width: 180 }}
                        onChange={(startTime) => {
                          if (startTime && task?.standard_hours) {
                            // 根据标准工时自动计算结束时间
                            const endTime = startTime.add(task.standard_hours, 'hour');
                            form.setFieldValue('planned_end', endTime);
                          }
                        }}
                      />
                    </Form.Item>

                    <Form.Item
                      name="planned_end"
                      label={
                        <Space>
                          <span>计划结束时间</span>
                          {task?.standard_hours && (
                            <Tag color="blue">
                              标准工时: {task.standard_hours}h
                            </Tag>
                          )}
                        </Space>
                      }
                    >
                      <DatePicker
                        showTime
                        format="YYYY-MM-DD HH:mm"
                        style={{ width: 180 }}
                        disabled={true}
                        placeholder="根据标准工时自动计算"
                        title="结束时间根据开始时间和标准工时自动计算"
                      />
                    </Form.Item>

                    <Form.Item
                      name="status"
                      label="任务状态"
                      rules={[{ required: true, message: '请选择状态' }]}
                    >
                      <Select style={{ width: 120 }}>
                        <Option value="planned">已计划</Option>
                        <Option value="scheduled">已调度</Option>
                        <Option value="in_progress">进行中</Option>
                        <Option value="completed">已完成</Option>
                        <Option value="cancelled">已取消</Option>
                        <Option value="on_hold">暂停</Option>
                      </Select>
                    </Form.Item>
                  </Form>
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane
            tab={
              <Space>
                <TableOutlined />
                工序详情
              </Space>
            }
            key="2"
          >
            <Card
              title="工序执行详情"
              size="small"
              style={{ backgroundColor: 'white' }}
            >
              <Table
                columns={processColumns}
                dataSource={processData}
                pagination={false}
                scroll={{ x: 1200, y: 450 }}
                size="small"
                bordered
                style={{
                  fontSize: '12px',
                }}
                summary={(pageData) => {
                  let totalPlanned = 0;
                  let totalCompleted = 0;
                  let totalQualified = 0;
                  let totalDefective = 0;

                  pageData.forEach(({ planned_quantity, completed_quantity, qualified_quantity, defective_quantity }) => {
                    totalPlanned += planned_quantity;
                    totalCompleted += completed_quantity;
                    totalQualified += qualified_quantity;
                    totalDefective += defective_quantity;
                  });

                  return (
                    <Table.Summary.Row style={{ backgroundColor: '#fafafa' }}>
                      <Table.Summary.Cell index={0} colSpan={4}>
                        <Text strong>合计</Text>
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={1}>
                        <Text strong>{totalPlanned}</Text>
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={2}>
                        <Text strong>{totalCompleted}</Text>
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={3}>
                        <Text strong>{totalQualified}</Text>
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={4}>
                        <Text strong>{totalDefective}</Text>
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={5} colSpan={3}>
                        <Text type="secondary">完成率: {totalPlanned > 0 ? ((totalCompleted / totalPlanned) * 100).toFixed(1) : 0}%</Text>
                      </Table.Summary.Cell>
                    </Table.Summary.Row>
                  );
                }}
              />
            </Card>
          </TabPane>
        </Tabs>
      ) : (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Text type="secondary">未找到任务信息</Text>
        </div>
      )}
    </Modal>
  );
};

export default TaskDetailModal;
