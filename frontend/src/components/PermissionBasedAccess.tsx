import React from 'react';
import { usePermissions } from '@/hooks/usePermissions';
import { useAuthStore } from '@/store/auth';

interface PermissionBasedAccessProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
  requiredAnyPermission?: string[];
  requiredRoles?: string[];
  fallback?: React.ReactNode;
}

/**
 * 基于权限的访问控制组件
 * 支持权限代码和角色的混合检查
 */
const PermissionBasedAccess: React.FC<PermissionBasedAccessProps> = ({
  children,
  requiredPermissions = [],
  requiredAnyPermission = [],
  requiredRoles = [],
  fallback = null,
}) => {
  const { user } = useAuthStore();
  const { hasPermission, hasAnyPermission } = usePermissions();

  // 如果用户未登录，不显示内容
  if (!user) {
    return <>{fallback}</>;
  }

  // 检查角色权限（向后兼容）
  const hasRequiredRole = requiredRoles.length === 0 || 
    requiredRoles.some(role => user.roles?.includes(role));

  // 检查所有必需权限
  const hasAllPermissions = requiredPermissions.length === 0 || 
    requiredPermissions.every(permission => hasPermission(permission));

  // 检查任一权限
  const hasAnyRequiredPermission = requiredAnyPermission.length === 0 || 
    hasAnyPermission(requiredAnyPermission);

  // 管理员拥有所有权限
  const isAdmin = user.roles?.includes('admin');

  // 权限检查
  const hasAccess = isAdmin || (hasRequiredRole && hasAllPermissions && hasAnyRequiredPermission);

  return hasAccess ? <>{children}</> : <>{fallback}</>;
};

export default PermissionBasedAccess;
