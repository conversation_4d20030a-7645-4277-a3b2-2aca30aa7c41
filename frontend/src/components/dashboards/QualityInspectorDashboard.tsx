import React from 'react';
import { Row, Col, Card, Statistic, Typography, Table, Tag, Progress, Alert } from 'antd';
import {
  SafetyCertificateOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  BarChartOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { useI18n } from '@/hooks/useI18n';

const { Title, Text } = Typography;

/**
 * 质量检验员专用仪表板
 * 专注于质量数据、检验任务和质量趋势
 */
const QualityInspectorDashboard: React.FC = () => {
  const { dashboard } = useI18n();
  // 模拟质量数据
  const qualityData = {
    todayStats: {
      inspectedItems: 156,
      passedItems: 152,
      failedItems: 4,
      passRate: 97.4,
      pendingInspections: 23
    },
    qualityTrend: [
      { date: '12-01', passRate: 98.2, inspected: 145 },
      { date: '12-02', passRate: 97.8, inspected: 162 },
      { date: '12-03', passRate: 98.5, inspected: 138 },
      { date: '12-04', passRate: 96.9, inspected: 171 },
      { date: '12-05', passRate: 97.4, inspected: 156 },
    ],
    defectTypes: [
      { name: '尺寸偏差', value: 45, color: '#ff4d4f' },
      { name: '表面缺陷', value: 25, color: '#faad14' },
      { name: '材料问题', value: 20, color: '#1890ff' },
      { name: '其他', value: 10, color: '#52c41a' },
    ],
    pendingInspections: [
      {
        key: '1',
        workOrder: 'WO20241206001',
        partName: '齿轮轴',
        quantity: 50,
        priority: 'high',
        submittedTime: '13:45',
        operator: '张三'
      },
      {
        key: '2',
        workOrder: 'WO20241206002',
        partName: '轴承座',
        quantity: 30,
        priority: 'medium',
        submittedTime: '14:20',
        operator: '李四'
      },
      {
        key: '3',
        workOrder: 'WO20241206003',
        partName: '连接器',
        quantity: 100,
        priority: 'low',
        submittedTime: '14:35',
        operator: '王五'
      },
    ]
  };

  const inspectionColumns = [
    {
      title: '工单号',
      dataIndex: 'workOrder',
      key: 'workOrder',
      render: (text: string) => <Text strong>{text}</Text>
    },
    {
      title: '零件名称',
      dataIndex: 'partName',
      key: 'partName',
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (quantity: number) => `${quantity} 件`
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: string) => {
        const color = priority === 'high' ? 'red' : priority === 'medium' ? 'orange' : 'green';
        const text = priority === 'high' ? '高' : priority === 'medium' ? '中' : '低';
        return <Tag color={color}>{text}</Tag>;
      }
    },
    {
      title: '提交时间',
      dataIndex: 'submittedTime',
      key: 'submittedTime',
    },
    {
      title: '操作员',
      dataIndex: 'operator',
      key: 'operator',
    },
  ];

  return (
    <div>
      <div className="page-header" style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          {dashboard.qualityDashboard()}
        </Title>
        <Text type="secondary">
          {dashboard.inspectionOverview()}
        </Text>
      </div>

      {/* 质量警报 */}
      {qualityData.todayStats.passRate < 98 && (
        <Alert
          message={dashboard.qualityAlert()}
          description={dashboard.qualityAlertMessage(qualityData.todayStats.passRate)}
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 今日统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="今日检验"
              value={qualityData.todayStats.inspectedItems}
              suffix="件"
              prefix={<FileTextOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="合格数量"
              value={qualityData.todayStats.passedItems}
              suffix="件"
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="不合格数量"
              value={qualityData.todayStats.failedItems}
              suffix="件"
              prefix={<CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="待检验"
              value={qualityData.todayStats.pendingInspections}
              suffix="批次"
              prefix={<ExclamationCircleOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 合格率指标 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card title="今日合格率">
            <Row align="middle">
              <Col span={6}>
                <Progress
                  type="circle"
                  percent={qualityData.todayStats.passRate}
                  size={120}
                  strokeColor={qualityData.todayStats.passRate >= 98 ? '#52c41a' : '#faad14'}
                />
              </Col>
              <Col span={18}>
                <div style={{ paddingLeft: 24 }}>
                  <Text style={{ fontSize: 16 }}>
                    合格率：<Text strong style={{ fontSize: 24, color: qualityData.todayStats.passRate >= 98 ? '#52c41a' : '#faad14' }}>
                      {qualityData.todayStats.passRate}%
                    </Text>
                  </Text>
                  <br />
                  <Text type="secondary">
                    目标：98% | 
                    {qualityData.todayStats.passRate >= 98 ? ' ✅ 达标' : ' ⚠️ 未达标'}
                  </Text>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={16}>
          <Card title="质量趋势（近5天）">
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={qualityData.qualityTrend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis domain={[95, 100]} />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="passRate" 
                  stroke="#1890ff" 
                  strokeWidth={2}
                  name="合格率(%)"
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="缺陷类型分布">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={qualityData.defectTypes}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {qualityData.defectTypes.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* 待检验任务 */}
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card 
            title="待检验任务"
            extra={<Text type="secondary">{qualityData.pendingInspections.length} 个待处理</Text>}
          >
            <Table
              columns={inspectionColumns}
              dataSource={qualityData.pendingInspections}
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default QualityInspectorDashboard;
