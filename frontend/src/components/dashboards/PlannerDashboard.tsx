import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, Typography, Table, Tag, Progress, Timeline, Spin, Button, Modal, Form, DatePicker, Select, Space, App } from 'antd';
import {
  CalendarOutlined,
  ClockCircleOutlined,
  Bar<PERSON><PERSON>Outlined,
  SettingOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ScheduleOutlined,
} from '@ant-design/icons';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import { useI18n } from '@/hooks/useI18n';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;

/**
 * 生产计划员专用仪表板
 * 专注于生产计划、设备利用率和交期管理
 */

const PlannerDashboard: React.FC = () => {
  const { dashboard, equipment } = useI18n();
  const { message } = App.useApp();
  const queryClient = useQueryClient();
  const [form] = Form.useForm();

  // 重新调度相关状态
  const [isRescheduleModalVisible, setIsRescheduleModalVisible] = useState(false);
  const [selectedTask, setSelectedTask] = useState<any>(null);
  const [isFormReady, setIsFormReady] = useState(false);

  // 获取本周任务统计数据
  const { data: weeklyTaskStats, isLoading: weeklyStatsLoading } = useQuery(
    'weekly-task-stats',
    () => apiClient.getWeeklyTaskStats(),
    { refetchInterval: 60000 }
  );

  // 获取计划员仪表盘数据
  const { data: plannerData, isLoading: plannerDataLoading } = useQuery(
    'planner-dashboard',
    () => apiClient.getPlannerDashboard(),
    { refetchInterval: 30000 }
  );

  // 获取技能组数据（用于重新调度）
  const { data: skillGroups } = useQuery(
    'skill-groups',
    () => apiClient.getSkillGroups()
  );

  // 获取设备数据（用于重新调度）
  const { data: machines } = useQuery(
    'machines',
    () => apiClient.getMachines()
  );

  // 重新调度任务的mutation
  const updateTaskMutation = useMutation(
    ({ id, data }: { id: number; data: any }) => apiClient.updatePlanTask(id, data),
    {
      onSuccess: () => {
        message.success('任务重新调度成功');
        queryClient.invalidateQueries('planner-dashboard');
        setIsRescheduleModalVisible(false);
        setSelectedTask(null);
        form.resetFields();
      },
      onError: (error: any) => {
        message.error(`重新调度失败: ${error.response?.data?.message || error.message || '未知错误'}`);
      },
    }
  );

  // 处理重新调度
  const handleReschedule = (task: any) => {
    if (!task.planTaskId) {
      message.warning('该任务尚未创建计划任务，无法重新调度');
      return;
    }
    setSelectedTask(task);
    setIsRescheduleModalVisible(true);
  };

  // 监控Form准备状态
  useEffect(() => {
    setIsFormReady(isRescheduleModalVisible);
  }, [isRescheduleModalVisible]);

  // 当重新调度Modal打开且选中任务时，设置表单值
  useEffect(() => {
    if (isRescheduleModalVisible && selectedTask) {
      const startTime = selectedTask.plannedStart ? dayjs(selectedTask.plannedStart) : null;
      const standardHours = 1; // 默认1小时，可以从任务数据中获取
      const endTime = startTime ? startTime.add(standardHours, 'hour') : null;

      // 预设技能组和设备信息（保留工艺属性）
      form.setFieldsValue({
        skill_group_id: selectedTask.skillGroupId, // 保留原有技能组
        machine_id: selectedTask.machineId, // 保留原有设备
        planned_start: startTime,
        planned_end: endTime,
      });
    }
  }, [isRescheduleModalVisible, selectedTask, form]);

  const handleRescheduleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (selectedTask) {
        const startTime = values.planned_start;

        if (!startTime) {
          message.error('请选择计划开始时间');
          return;
        }

        // 根据标准工时自动计算结束时间
        const standardHours = 1; // 默认1小时
        const endTime = startTime.add(standardHours, 'hour');

        // 验证计算出的时间是否合理
        const diffMinutes = endTime.diff(startTime, 'minute');
        if (diffMinutes < 30) {
          message.warning(`标准工时过短（${standardHours}小时），建议检查工艺设置`);
        }

        updateTaskMutation.mutate({
          id: selectedTask.planTaskId,
          data: {
            skill_group_id: values.skill_group_id,
            machine_id: values.machine_id,
            planned_start: startTime.toISOString(),
            planned_end: endTime.toISOString(),
          }
        });
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 使用真实数据或默认值
  const planningData = {
    todayStats: plannerData?.today_stats || {
      totalPlans: 0,
      onSchedule: 0,
      delayed: 0,
      completed: 0,
      equipmentUtilization: 0
    },
    // 使用真实的本周任务统计数据
    weeklyProgress: weeklyTaskStats?.daily_stats?.map(stat => ({
      day: stat.day_name,
      planned: stat.planned_tasks,
      completed: stat.completed_tasks,
      utilization: stat.completion_rate, // 使用完成率作为利用率的近似值
    })) || [],
    equipmentStatus: plannerData?.equipment_status?.map((status: any, index: number) => ({
      key: status.skill_group_name.toLowerCase().replace(/\s+/g, '_'),
      name: status.skill_group_name,
      total: status.total,
      running: status.running,
      idle: status.idle,
      maintenance: status.maintenance,
      utilization: status.utilization
    })) || [],
    urgentTasks: plannerData?.urgent_tasks?.map((task: any, index: number) => ({
      key: index.toString(),
      workOrder: task.work_order_number,
      partName: task.part_name,
      dueDate: task.due_date ? new Date(task.due_date).toLocaleString('zh-CN') : 'N/A',
      progress: task.progress,
      status: task.progress >= 80 ? 'on_track' : task.progress >= 50 ? 'at_risk' : 'delayed',
      assignedTo: task.assigned_to || '未分配',
      planTaskId: task.plan_task_id,
      plannedStart: task.planned_start,
      plannedEnd: task.planned_end,
      workOrderId: task.work_order_id,
      skillGroupId: task.skill_group_id,
      skillGroupName: task.skill_group_name,
      machineId: task.machine_id,
      machineName: task.machine_name
    })) || [],
    upcomingMilestones: plannerData?.today_milestones || []
  };

  const taskColumns = [
    {
      title: '工单号',
      dataIndex: 'workOrder',
      key: 'workOrder',
      render: (text: string) => <Text strong>{text}</Text>
    },
    {
      title: '零件名称',
      dataIndex: 'partName',
      key: 'partName',
    },
    {
      title: '交期',
      dataIndex: 'dueDate',
      key: 'dueDate',
      render: (date: string) => <Text>{date}</Text>
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number) => (
        <Progress 
          percent={progress} 
          size="small" 
          strokeColor={progress >= 80 ? '#52c41a' : progress >= 50 ? '#faad14' : '#ff4d4f'}
        />
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          on_track: { color: 'green', text: '正常' },
          at_risk: { color: 'orange', text: '风险' },
          delayed: { color: 'red', text: '延期' }
        };
        const config = statusMap[status as keyof typeof statusMap];
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '负责人',
      dataIndex: 'assignedTo',
      key: 'assignedTo',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <Button
          type="link"
          icon={<ScheduleOutlined />}
          onClick={() => handleReschedule(record)}
          disabled={!record.planTaskId}
          size="small"
        >
          重新调度
        </Button>
      ),
    },
  ];

  const equipmentColumns = [
    {
      title: '设备类型',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => <Text strong>{text}</Text>
    },
    {
      title: '总数',
      dataIndex: 'total',
      key: 'total',
    },
    {
      title: '运行中',
      dataIndex: 'running',
      key: 'running',
      render: (count: number) => <Text style={{ color: '#52c41a' }}>{count}</Text>
    },
    {
      title: '空闲',
      dataIndex: 'idle',
      key: 'idle',
      render: (count: number) => <Text style={{ color: '#faad14' }}>{count}</Text>
    },
    {
      title: '维护中',
      dataIndex: 'maintenance',
      key: 'maintenance',
      render: (count: number) => <Text style={{ color: '#ff4d4f' }}>{count}</Text>
    },
    {
      title: '利用率',
      dataIndex: 'utilization',
      key: 'utilization',
      render: (rate: number) => (
        <Progress 
          percent={rate} 
          size="small" 
          strokeColor={rate >= 85 ? '#52c41a' : rate >= 70 ? '#faad14' : '#ff4d4f'}
        />
      )
    },
  ];

  if (plannerDataLoading) {
    return (
      <div style={{ padding: '50px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text>加载计划员仪表盘数据中...</Text>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="page-header" style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          {dashboard.plannerDashboard()}
        </Title>
        <Text type="secondary">
          {dashboard.planOverview()}
        </Text>
      </div>

      {/* 今日统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总计划数"
              value={planningData.todayStats.total_plans}
              suffix="个"
              prefix={<CalendarOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="按时进行"
              value={planningData.todayStats.on_schedule}
              suffix="个"
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="延期任务"
              value={planningData.todayStats.delayed}
              suffix="个"
              prefix={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="设备利用率"
              value={planningData.todayStats.equipment_utilization?.toFixed(1) || 0}
              suffix="%"
              prefix={<SettingOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={16}>
          <Card
            title={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>本周计划执行情况</span>
                {weeklyTaskStats && (
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    总完成率: {weeklyTaskStats.overall_completion_rate.toFixed(1)}%
                  </Text>
                )}
              </div>
            }
            loading={weeklyStatsLoading}
          >
            {planningData.weeklyProgress.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={planningData.weeklyProgress}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="day" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="planned" fill="#91d5ff" name="计划任务" />
                  <Bar dataKey="completed" fill="#1890ff" name="完成任务" />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div style={{
                height: 300,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#999'
              }}>
                {weeklyStatsLoading ? '数据加载中...' : '暂无数据'}
              </div>
            )}
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="今日重要节点">
            <Timeline
              items={planningData.upcomingMilestones.map((milestone: any) => ({
                color: milestone.status === 'critical' ? 'red' : 
                       milestone.status === 'scheduled' ? 'blue' : 'green',
                children: (
                  <div>
                    <Text strong>{milestone.time}</Text>
                    <br />
                    <Text>{milestone.event}</Text>
                  </div>
                )
              }))}
            />
          </Card>
        </Col>
      </Row>

      {/* 紧急任务和设备状态 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={14}>
          <Card
            title={dashboard.urgentTasks()}
            extra={<Text type="secondary">{planningData.urgentTasks.length} 个紧急任务</Text>}
          >
            <Table
              columns={taskColumns}
              dataSource={planningData.urgentTasks}
              rowKey="key"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
        <Col xs={24} lg={10}>
          <Card title={dashboard.equipmentUtilization()}>
            <Table
              columns={equipmentColumns}
              dataSource={planningData.equipmentStatus}
              rowKey="key"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>

      {/* 专业重新调度Modal */}
      <Modal
        title="任务重新调度"
        open={isRescheduleModalVisible}
        onOk={handleRescheduleSubmit}
        onCancel={() => {
          setIsRescheduleModalVisible(false);
          setSelectedTask(null);
          form.resetFields();
        }}
        confirmLoading={updateTaskMutation.isLoading}
        okText="确定"
        cancelText="取消"
        width={600}
      >
        {selectedTask && (
          <div style={{ marginBottom: 16, padding: '12px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
            <Row gutter={16}>
              <Col span={12}>
                <p><strong>工单号：</strong>{selectedTask.workOrder}</p>
                <p><strong>零件名称：</strong>{selectedTask.partName}</p>
                <p><strong>交期：</strong>{selectedTask.dueDate}</p>
              </Col>
              <Col span={12}>
                <p><strong>当前技能组：</strong>
                  <Tag color="blue">{selectedTask.skillGroupName || '未分配'}</Tag>
                </p>
                <p><strong>当前设备：</strong>
                  <Tag color="cyan">{selectedTask.machineName || '未指定'}</Tag>
                </p>
                <p><strong>当前状态：</strong>
                  <Tag color={selectedTask.status === 'on_track' ? 'green' : selectedTask.status === 'at_risk' ? 'orange' : 'red'}>
                    {selectedTask.status === 'on_track' ? '正常' : selectedTask.status === 'at_risk' ? '风险' : '延期'}
                  </Tag>
                </p>
              </Col>
            </Row>
          </div>
        )}

        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="skill_group_id"
            label={
              <Space>
                <span>技能组</span>
                <Tag color="orange">工艺属性</Tag>
              </Space>
            }
            rules={[{ required: true, message: '请选择技能组' }]}
            tooltip="技能组是工艺的核心属性，建议保持原有设置"
          >
            <Select
              placeholder="请选择技能组（已预设工艺要求）"
              style={{ width: '100%' }}
              onChange={(value) => {
                // 当技能组改变时，清空设备选择
                form.setFieldValue('machine_id', undefined);
              }}
            >
              {skillGroups?.map(group => (
                <Option key={group.id} value={group.id}>
                  {group.group_name}
                  {selectedTask?.skillGroupId === group.id && (
                    <Tag color="blue" style={{ marginLeft: 8 }}>当前</Tag>
                  )}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="machine_id"
            label="指定设备"
            tooltip="可选择具体设备，不选择则分配给技能组"
          >
            <Select
              placeholder="请选择设备（可选，已预设当前设备）"
              allowClear
              style={{ width: '100%' }}
            >
              {machines?.filter(machine => {
                if (!isFormReady) return false;
                try {
                  return machine.skill_group_id === form.getFieldValue('skill_group_id');
                } catch {
                  return false;
                }
              }).map(machine => (
                <Option key={machine.id} value={machine.id}>
                  {machine.machine_name}
                  {selectedTask?.machineId === machine.id && (
                    <Tag color="cyan" style={{ marginLeft: 8 }}>当前</Tag>
                  )}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="planned_start"
            label="计划开始时间"
            rules={[{ required: true, message: '请选择计划开始时间' }]}
          >
            <DatePicker
              showTime={{
                minuteStep: 30,
                hideDisabledOptions: true,
                format: 'HH:mm',
              }}
              style={{ width: '100%' }}
              format="YYYY-MM-DD HH:mm"
              placeholder="选择计划开始时间"
              onChange={(startTime) => {
                if (startTime) {
                  // 根据标准工时自动计算结束时间
                  const standardHours = 1; // 默认1小时
                  const endTime = startTime.add(standardHours, 'hour');
                  form.setFieldValue('planned_end', endTime);
                }
              }}
            />
          </Form.Item>

          <Form.Item
            name="planned_end"
            label={
              <Space>
                <span>计划结束时间</span>
                <Tag color="blue">标准工时: 1h</Tag>
              </Space>
            }
          >
            <DatePicker
              showTime={{
                minuteStep: 30,
                hideDisabledOptions: true,
                format: 'HH:mm',
              }}
              style={{ width: '100%' }}
              format="YYYY-MM-DD HH:mm"
              placeholder="根据标准工时自动计算"
              disabled={true}
              title="结束时间根据开始时间和标准工时自动计算"
            />
          </Form.Item>

          {/* 显示调度信息 */}
          <div style={{
            marginBottom: '16px',
            padding: '8px 12px',
            backgroundColor: '#fff7e6',
            border: '1px solid #ffd591',
            borderRadius: '4px',
            fontSize: '12px'
          }}>
            <Space direction="vertical" size="small">
              <Text type="secondary">
                🔧 <strong>技能组已预设</strong>：基于工艺要求，建议保持原有技能组设置
              </Text>
              <Text type="secondary">
                <ClockCircleOutlined /> 标准工时: 1 小时（默认值）
              </Text>
              <Text type="secondary">
                ⚡ 结束时间将根据开始时间自动计算
              </Text>
              <Text type="secondary">
                📋 调度完成后将更新任务的执行计划
              </Text>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default PlannerDashboard;
