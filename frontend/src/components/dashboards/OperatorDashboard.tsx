import React, { useState } from 'react';
import { Row, Col, Card, Statistic, Typography, Badge, Descriptions, Button, Space, Progress, Modal, Form, Select, App, Popconfirm } from 'antd';
import {
  PlayCircleOutlined,
  CheckCircleOutlined,
  SettingOutlined,
  ClockCircleOutlined,
  ToolOutlined,
  SafetyCertificateOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  StarOutlined,
  StarFilled,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import { useAuthStore } from '@/store/auth';
import { useI18n } from '@/hooks/useI18n';
import { getSkillDisplayText } from '@/utils/textUtils';
import type { CreateUserMachineBindingRequest, UpdateUserMachineBindingRequest, TaskExecutionRequest } from '@/types/api';

const { Title, Text } = Typography;

/**
 * 操作员专用仪表板
 * 专注于当前任务、设备状态和个人绩效
 */
const OperatorDashboard: React.FC = () => {
  const { message } = App.useApp();
  const { user } = useAuthStore();
  const queryClient = useQueryClient();
  const { dashboard, skills } = useI18n();
  const [isBindingModalVisible, setIsBindingModalVisible] = useState(false);
  const [isTaskStartModalVisible, setIsTaskStartModalVisible] = useState(false);
  const [selectedTask, setSelectedTask] = useState<any>(null);
  const [form] = Form.useForm();

  // 获取用户设备绑定
  const { data: machineBindings, isLoading: bindingsLoading } = useQuery(
    'user-machine-bindings',
    () => apiClient.getUserMachineBindings(),
    {
      enabled: !!user?.id
    }
  );

  // 获取操作员仪表板数据
  const { data: operatorData, isLoading } = useQuery(
    ['operator-dashboard', user?.id],
    () => apiClient.getOperatorDashboard(user?.id || 0),
    {
      refetchInterval: 10000, // 10秒刷新一次
      enabled: !!user?.id
    }
  );

  // 创建设备绑定
  const createBindingMutation = useMutation(
    (data: CreateUserMachineBindingRequest) => apiClient.createMachineBinding(data),
    {
      onSuccess: () => {
        message.success('设备绑定成功');
        queryClient.invalidateQueries('user-machine-bindings');
        setIsBindingModalVisible(false);
        form.resetFields();
      },
      onError: (error: any) => {
        message.error(`绑定失败: ${error.response?.data?.message || error.message}`);
      },
    }
  );

  // 更新设备绑定
  const updateBindingMutation = useMutation(
    ({ bindingId, data }: { bindingId: number; data: UpdateUserMachineBindingRequest }) =>
      apiClient.updateMachineBinding(bindingId, data),
    {
      onSuccess: () => {
        message.success('设备绑定更新成功');
        queryClient.invalidateQueries('user-machine-bindings');
      },
      onError: (error: any) => {
        message.error(`更新失败: ${error.response?.data?.message || error.message}`);
      },
    }
  );

  // 删除设备绑定
  const deleteBindingMutation = useMutation(
    (bindingId: number) => apiClient.deleteMachineBinding(bindingId),
    {
      onSuccess: () => {
        message.success('设备绑定删除成功');
        queryClient.invalidateQueries('user-machine-bindings');
      },
      onError: (error: any) => {
        message.error(`删除失败: ${error.response?.data?.message || error.message}`);
      },
    }
  );

  // 开始任务
  const startTaskMutation = useMutation(
    (data: TaskExecutionRequest) => apiClient.startTask(data),
    {
      onSuccess: () => {
        message.success('任务已开始');
        queryClient.invalidateQueries(['operator-dashboard', user?.id]);
        setIsTaskStartModalVisible(false);
        setSelectedTask(null);
      },
      onError: (error: any) => {
        message.error(`开始任务失败: ${error.response?.data?.message || error.message}`);
      },
    }
  );

  // 从API获取的真实数据
  const dashboardData = operatorData || {
    current_task: null,
    daily_stats: {
      completed_tasks: 0,
      quality_rate: 100.0,
      efficiency: 100.0,
      working_hours: 0.0
    },
    upcoming_tasks: [],
    my_machines: []
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'processing';
      case 'idle': return 'default';
      case 'maintenance': return 'warning';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return '#ff4d4f';
      case 'medium': return '#faad14';
      case 'low': return '#52c41a';
      default: return '#1890ff';
    }
  };

  // 处理任务点击
  const handleTaskClick = (task: any) => {
    setSelectedTask(task);
    setIsTaskStartModalVisible(true);
  };

  // 处理开始任务
  const handleStartTask = () => {
    if (!selectedTask) return;

    startTaskMutation.mutate({
      plan_task_id: selectedTask.id,
      notes: `从仪表盘开始任务: ${selectedTask.work_order_number}`
    });
  };

  return (
    <div>


      <div className="page-header" style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          {dashboard.operatorDashboard()}
        </Title>
        <Text type="secondary">
          {dashboard.welcomeMessage(user?.full_name || user?.username || '')} |
          {dashboard.skillGroups()}：{user?.skills?.map(skill =>
            getSkillDisplayText(skill)
          ).join(', ') || dashboard.unassigned()} |
          {dashboard.dailyWorkHours(dashboardData.daily_stats.working_hours.toFixed(1))}
        </Text>
      </div>

      {/* 当前任务卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card 
            title={
              <Space>
                <PlayCircleOutlined style={{ color: '#1890ff' }} />
                当前任务
              </Space>
            }
            extra={
              <Badge 
                color={getPriorityColor(dashboardData.current_task?.priority || 'low')}
                text={dashboardData.current_task?.priority === 'high' ? '高优先级' : '普通优先级'}
              />
            }
            className="current-task-card"
          >
            <Row gutter={16}>
              <Col span={16}>
                <Descriptions column={2} size="small">
                  <Descriptions.Item label="工单号">
                    <Text strong>{dashboardData.current_task?.work_order_number || '暂无'}</Text>
                  </Descriptions.Item>
                  <Descriptions.Item label="零件">
                    {dashboardData.current_task?.part_name || '暂无'} - {dashboardData.current_task?.part_number || '暂无'}
                  </Descriptions.Item>
                  <Descriptions.Item label="进度">
                    {dashboardData.current_task?.progress || 0}/{dashboardData.current_task?.total || 0} 完成
                  </Descriptions.Item>
                  <Descriptions.Item label="预计完成">
                    <Text type="success">{dashboardData.current_task?.estimated_completion || '待定'}</Text>
                  </Descriptions.Item>
                  <Descriptions.Item label="项目">
                    {dashboardData.current_task?.project_name || '暂无'}
                  </Descriptions.Item>
                  <Descriptions.Item label="工序">
                    {dashboardData.current_task?.process_name || '暂无'}
                  </Descriptions.Item>
                </Descriptions>
              </Col>
              <Col span={8}>
                <div style={{ textAlign: 'center' }}>
                  <Progress 
                    type="circle" 
                    percent={dashboardData.current_task ? Math.round((dashboardData.current_task.progress / dashboardData.current_task.total) * 100) : 0}
                    size={80}
                  />
                  <div style={{ marginTop: 8 }}>
                    <Button type="primary" size="large">
                      继续任务
                    </Button>
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 统计数据 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title={dashboard.completedTasksToday()}
              value={dashboardData.daily_stats.completed_tasks}
              suffix="个"
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title={dashboard.qualityRate()}
              value={dashboardData.daily_stats.quality_rate.toFixed(1)}
              suffix="%"
              prefix={<SafetyCertificateOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title={dashboard.workEfficiency()}
              value={dashboardData.daily_stats.efficiency.toFixed(1)}
              suffix="%"
              prefix={<ToolOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title={dashboard.workHours()}
              value={dashboardData.daily_stats.working_hours.toFixed(1)}
              suffix="小时"
              prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 我的设备和待办任务 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <SettingOutlined />
                {dashboard.myMachines()}
              </Space>
            }
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                size="small"
                onClick={() => setIsBindingModalVisible(true)}
              >
                {dashboard.bindMachine()}
              </Button>
            }
          >
            {bindingsLoading ? (
              <div style={{ textAlign: 'center', padding: '20px' }}>加载中...</div>
            ) : machineBindings?.bindings.length ? (
              <Space direction="vertical" style={{ width: '100%' }}>
                {machineBindings.bindings.map(binding => (
                  <div key={binding.id} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '8px 0',
                    borderBottom: '1px solid #f0f0f0'
                  }}>
                    <div style={{ flex: 1 }}>
                      <Space>
                        <Text strong>{binding.machine_name}</Text>
                        {binding.is_primary && (
                          <StarFilled style={{ color: '#faad14' }} title="主要设备" />
                        )}
                      </Space>
                      <br />
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {binding.skill_group_name}
                      </Text>
                    </div>
                    <Space>
                      <Badge
                        status={getStatusColor(binding.machine_status)}
                        text={binding.machine_status === 'available' ? '可用' :
                              binding.machine_status === 'running' ? '运行中' :
                              binding.machine_status === 'maintenance' ? '维护中' : '故障'}
                      />
                      <Space size="small">
                        <Button
                          type="text"
                          size="small"
                          icon={binding.is_primary ? <StarFilled /> : <StarOutlined />}
                          onClick={() => updateBindingMutation.mutate({
                            bindingId: binding.id,
                            data: { is_primary: !binding.is_primary }
                          })}
                          title={binding.is_primary ? "取消主要设备" : "设为主要设备"}
                        />
                        <Popconfirm
                          title="确定要解除绑定吗？"
                          onConfirm={() => deleteBindingMutation.mutate(binding.id)}
                        >
                          <Button
                            type="text"
                            size="small"
                            icon={<DeleteOutlined />}
                            danger
                          />
                        </Popconfirm>
                      </Space>
                    </Space>
                  </div>
                ))}
              </Space>
            ) : (
              <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
                <SettingOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
                <div>还没有绑定设备</div>
                <div style={{ fontSize: '12px' }}>点击"绑定设备"开始绑定您常用的设备</div>
              </div>
            )}
          </Card>
        </Col>
        
        <Col xs={24} lg={12}>
          <Card 
            title={
              <Space>
                <ClockCircleOutlined />
                待办任务
              </Space>
            }
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              {dashboardData.upcoming_tasks.length > 0 ? dashboardData.upcoming_tasks.map((task: any) => (
                <div
                  key={task.id}
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '12px',
                    borderBottom: '1px solid #f0f0f0',
                    cursor: 'pointer',
                    borderRadius: '4px',
                    transition: 'all 0.2s',
                  }}
                  className="task-item"
                  onClick={() => handleTaskClick(task)}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f5f5f5';
                    e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                >
                  <div>
                    <Text strong>{task.work_order_number}</Text>
                    <br />
                    <Text type="secondary">{task.part_name} - {task.part_number}</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '11px' }}>
                      {task.project_name} | {task.process_name}
                    </Text>
                  </div>
                  <div style={{ textAlign: 'right' }}>
                    <Badge
                      color={getPriorityColor(task.priority)}
                      text={task.priority === 'high' ? '高' : task.priority === 'medium' ? '中' : '低'}
                    />
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {task.estimated_start || '待定'}
                    </Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '10px', color: '#1890ff' }}>
                      点击开始任务
                    </Text>
                  </div>
                </div>
              )) : (
                <div style={{ textAlign: 'center', padding: '20px 0' }}>
                  <Text type="secondary">暂无待办任务</Text>
                </div>
              )}
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 设备绑定模态框 */}
      <Modal
        title="绑定设备"
        open={isBindingModalVisible}
        onCancel={() => {
          setIsBindingModalVisible(false);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        confirmLoading={createBindingMutation.isLoading}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={(values) => createBindingMutation.mutate(values)}
        >
          <Form.Item
            name="machine_id"
            label="选择设备"
            rules={[{ required: true, message: '请选择要绑定的设备' }]}
          >
            <Select
              placeholder="请选择设备"
              loading={bindingsLoading}
              showSearch
              filterOption={(input, option) =>
                String(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
            >
              {machineBindings?.available_machines.map(machine => (
                <Select.Option
                  key={machine.id}
                  value={machine.id}
                  label={`${machine.machine_name} - ${machine.skill_group_name}`}
                >
                  <div>
                    <div style={{ fontWeight: 'bold' }}>{machine.machine_name}</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      {machine.skill_group_name} -
                      <Badge
                        status={getStatusColor(machine.status)}
                        text={machine.status === 'available' ? '可用' :
                              machine.status === 'running' ? '运行中' :
                              machine.status === 'maintenance' ? '维护中' : '故障'}
                      />
                    </div>
                  </div>
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="is_primary"
            valuePropName="checked"
          >
            <Space>
              <StarOutlined />
              设为主要设备
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 开始任务模态框 */}
      <Modal
        title="开始任务"
        open={isTaskStartModalVisible}
        onCancel={() => {
          setIsTaskStartModalVisible(false);
          setSelectedTask(null);
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setIsTaskStartModalVisible(false);
              setSelectedTask(null);
            }}
          >
            取消
          </Button>,
          <Button
            key="start"
            type="primary"
            icon={<PlayCircleOutlined />}
            loading={startTaskMutation.isLoading}
            onClick={handleStartTask}
          >
            开始任务
          </Button>,
        ]}
      >
        {selectedTask && (
          <div>
            <Descriptions column={1} size="small" bordered>
              <Descriptions.Item label="工单号">
                <Text strong>{selectedTask.work_order_number}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="零件">
                {selectedTask.part_name} - {selectedTask.part_number}
              </Descriptions.Item>
              <Descriptions.Item label="项目">
                {selectedTask.project_name}
              </Descriptions.Item>
              <Descriptions.Item label="工序">
                {selectedTask.process_name}
              </Descriptions.Item>
              <Descriptions.Item label="优先级">
                <Badge
                  color={getPriorityColor(selectedTask.priority)}
                  text={selectedTask.priority === 'high' ? '高优先级' : selectedTask.priority === 'medium' ? '中优先级' : '低优先级'}
                />
              </Descriptions.Item>
              <Descriptions.Item label="计划开始时间">
                {selectedTask.estimated_start}
              </Descriptions.Item>
            </Descriptions>

            <div style={{ marginTop: 16, padding: 12, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 4 }}>
              <Text type="secondary">
                <PlayCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
                点击"开始任务"将立即开始执行此任务，任务状态将变更为"进行中"。
              </Text>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default OperatorDashboard;
