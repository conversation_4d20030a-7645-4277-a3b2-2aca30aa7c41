import React, { useState, useEffect, useRef } from 'react';
import { Input, Button, Space, Select, DatePicker, InputNumber, Switch, Slider } from 'antd';
import { EyeInvisibleOutlined, EyeTwoTone, SearchOutlined, ClearOutlined } from '@ant-design/icons';

interface MobileInputProps {
  type?: 'text' | 'password' | 'search' | 'number' | 'email' | 'tel' | 'url';
  value?: any;
  onChange?: (value: any) => void;
  placeholder?: string;
  disabled?: boolean;
  size?: 'small' | 'middle' | 'large';
  autoFocus?: boolean;
  clearable?: boolean;
  maxLength?: number;
  showCount?: boolean;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  addonBefore?: React.ReactNode;
  addonAfter?: React.ReactNode;
  style?: React.CSSProperties;
  className?: string;
}

/**
 * 移动端优化的输入组件
 * 自动适配移动设备的输入体验
 */
const MobileInput: React.FC<MobileInputProps> = ({
  type = 'text',
  value,
  onChange,
  placeholder,
  disabled = false,
  size = 'large',
  autoFocus = false,
  clearable = true,
  maxLength,
  showCount = false,
  prefix,
  suffix,
  addonBefore,
  addonAfter,
  style,
  className,
}) => {
  const [isMobile, setIsMobile] = useState(false);
  const [focused, setFocused] = useState(false);
  const inputRef = useRef<any>(null);

  // 检测移动设备
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  // 移动端自动聚焦处理
  useEffect(() => {
    if (autoFocus && isMobile && inputRef.current) {
      // 延迟聚焦，避免键盘弹出导致的布局问题
      setTimeout(() => {
        inputRef.current.focus();
      }, 300);
    }
  }, [autoFocus, isMobile]);

  // 获取移动端优化的样式
  const getMobileStyle = (): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      fontSize: isMobile ? '16px' : '14px', // 防止iOS缩放
      minHeight: isMobile ? '48px' : '32px',
      borderRadius: isMobile ? '8px' : '6px',
      transition: 'all 0.3s ease',
      ...style,
    };

    if (isMobile) {
      baseStyle.boxShadow = focused 
        ? '0 0 0 2px rgba(24, 144, 255, 0.2)' 
        : 'none';
      baseStyle.borderColor = focused ? '#1890ff' : '#d9d9d9';
    }

    return baseStyle;
  };

  // 处理清除按钮
  const handleClear = () => {
    onChange?.('');
    inputRef.current?.focus();
  };

  // 渲染清除按钮
  const renderClearButton = () => {
    if (!clearable || !value || disabled) return null;

    return (
      <Button
        type="text"
        size="small"
        icon={<ClearOutlined />}
        onClick={handleClear}
        style={{
          padding: '0 4px',
          height: 'auto',
          lineHeight: 1,
        }}
      />
    );
  };

  // 根据类型渲染不同的输入组件
  const renderInput = () => {
    const commonProps = {
      ref: inputRef,
      value,
      onChange: (e: any) => onChange?.(e.target?.value ?? e),
      placeholder,
      disabled,
      size: isMobile ? 'large' : size,
      style: getMobileStyle(),
      className,
      onFocus: () => setFocused(true),
      onBlur: () => setFocused(false),
      maxLength,
      showCount: showCount && isMobile,
    };

    switch (type) {
      case 'password':
        return (
          <Input.Password
            {...commonProps}
            iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            suffix={
              <Space size="small">
                {renderClearButton()}
                {suffix}
              </Space>
            }
          />
        );

      case 'search':
        return (
          <Input.Search
            {...commonProps}
            enterButton={isMobile ? <SearchOutlined /> : true}
            suffix={
              <Space size="small">
                {renderClearButton()}
                {suffix}
              </Space>
            }
          />
        );

      case 'number':
        return (
          <InputNumber
            {...commonProps}
            style={{
              ...getMobileStyle(),
              width: '100%',
            }}
            controls={!isMobile}
            keyboard={isMobile}
          />
        );

      case 'email':
        return (
          <Input
            {...commonProps}
            type="email"
            autoComplete="email"
            inputMode="email"
            prefix={prefix}
            suffix={
              <Space size="small">
                {renderClearButton()}
                {suffix}
              </Space>
            }
            addonBefore={addonBefore}
            addonAfter={addonAfter}
          />
        );

      case 'tel':
        return (
          <Input
            {...commonProps}
            type="tel"
            autoComplete="tel"
            inputMode="tel"
            prefix={prefix}
            suffix={
              <Space size="small">
                {renderClearButton()}
                {suffix}
              </Space>
            }
            addonBefore={addonBefore}
            addonAfter={addonAfter}
          />
        );

      case 'url':
        return (
          <Input
            {...commonProps}
            type="url"
            autoComplete="url"
            inputMode="url"
            prefix={prefix}
            suffix={
              <Space size="small">
                {renderClearButton()}
                {suffix}
              </Space>
            }
            addonBefore={addonBefore}
            addonAfter={addonAfter}
          />
        );

      default:
        return (
          <Input
            {...commonProps}
            prefix={prefix}
            suffix={
              <Space size="small">
                {renderClearButton()}
                {suffix}
              </Space>
            }
            addonBefore={addonBefore}
            addonAfter={addonAfter}
          />
        );
    }
  };

  return renderInput();
};

// 移动端优化的选择器组件
interface MobileSelectProps {
  value?: any;
  onChange?: (value: any) => void;
  options?: Array<{ label: string; value: any; disabled?: boolean }>;
  placeholder?: string;
  disabled?: boolean;
  mode?: 'multiple' | 'tags';
  allowClear?: boolean;
  showSearch?: boolean;
  filterOption?: boolean | ((input: string, option: any) => boolean);
  style?: React.CSSProperties;
  className?: string;
}

export const MobileSelect: React.FC<MobileSelectProps> = ({
  value,
  onChange,
  options = [],
  placeholder,
  disabled = false,
  mode,
  allowClear = true,
  showSearch = true,
  filterOption = true,
  style,
  className,
}) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  return (
    <Select
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      disabled={disabled}
      mode={mode}
      allowClear={allowClear}
      showSearch={showSearch}
      filterOption={filterOption}
      size={isMobile ? 'large' : 'middle'}
      style={{
        fontSize: isMobile ? '16px' : '14px',
        minHeight: isMobile ? '48px' : '32px',
        ...style,
      }}
      className={className}
      dropdownStyle={{
        fontSize: isMobile ? '16px' : '14px',
      }}
      listHeight={isMobile ? 200 : 256}
      virtual={options.length > 100}
    >
      {options.map(option => (
        <Select.Option 
          key={option.value} 
          value={option.value}
          disabled={option.disabled}
        >
          {option.label}
        </Select.Option>
      ))}
    </Select>
  );
};

export default MobileInput;
