import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Tag,
  Alert,
  Select,
  Typography,
  Divider,
  List,
  Card,
  Tabs
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  ToolOutlined,
  SafetyOutlined,
  ScheduleOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import { useMessage } from '@/hooks/useMessage';
import { getSkillGroupDisplayName, getSystemSkillGroupTag, isSystemSkillGroup } from '@/utils/roleUtils';
import type { SkillGroup, CreateSkillGroupRequest, SkillGroupDependencyInfo } from '@/types/api';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface SkillGroupManagementProps {
  onSkillGroupChange?: () => void;
}

const SkillGroupManagement: React.FC<SkillGroupManagementProps> = ({ onSkillGroupChange }) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingSkillGroup, setEditingSkillGroup] = useState<SkillGroup | null>(null);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deletingSkillGroup, setDeletingSkillGroup] = useState<SkillGroup | null>(null);
  const [dependencyInfo, setDependencyInfo] = useState<SkillGroupDependencyInfo | null>(null);
  const [replacementSkillGroupId, setReplacementSkillGroupId] = useState<number | undefined>();
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const { success, error } = useMessage();

  const { data: skillGroups = [], isLoading } = useQuery(
    'skill-groups',
    () => apiClient.getSkillGroups()
  );

  const createMutation = useMutation(
    (data: CreateSkillGroupRequest) => apiClient.createSkillGroup(data),
    {
      onSuccess: () => {
        success('技能组创建成功');
        queryClient.invalidateQueries('skill-groups');
        setIsModalVisible(false);
        form.resetFields();
        onSkillGroupChange?.();
      },
      onError: () => {
        error('技能组创建失败');
      },
    }
  );

  const updateMutation = useMutation(
    ({ id, data }: { id: number; data: Partial<CreateSkillGroupRequest> }) =>
      apiClient.updateSkillGroup(id, data),
    {
      onSuccess: () => {
        success('技能组更新成功');
        queryClient.invalidateQueries('skill-groups');
        setIsModalVisible(false);
        form.resetFields();
        onSkillGroupChange?.();
      },
      onError: () => {
        error('技能组更新失败');
      },
    }
  );

  const deleteMutation = useMutation(
    ({ id, replacementId }: { id: number; replacementId?: number }) =>
      apiClient.deleteSkillGroup(id, replacementId),
    {
      onSuccess: () => {
        success('技能组删除成功');
        queryClient.invalidateQueries('skill-groups');
        setDeleteModalVisible(false);
        setDeletingSkillGroup(null);
        setDependencyInfo(null);
        setReplacementSkillGroupId(undefined);
        onSkillGroupChange?.();
      },
      onError: () => {
        error('技能组删除失败');
      },
    }
  );

  const handleCreate = () => {
    setEditingSkillGroup(null);
    setIsModalVisible(true);
    form.resetFields();
  };

  const handleEdit = (skillGroup: SkillGroup) => {
    setEditingSkillGroup(skillGroup);
    setIsModalVisible(true);
    form.setFieldsValue({
      group_name: skillGroup.group_name,
    });
  };

  const handleDelete = async (skillGroup: SkillGroup) => {
    try {
      const deps = await apiClient.checkSkillGroupDependencies(skillGroup.id);
      setDeletingSkillGroup(skillGroup);
      setDependencyInfo(deps);
      setDeleteModalVisible(true);
    } catch (err) {
      error('检查技能组依赖失败');
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (editingSkillGroup) {
        updateMutation.mutate({ id: editingSkillGroup.id, data: values });
      } else {
        createMutation.mutate(values);
      }
    } catch (err) {
      console.error('Validation failed:', err);
    }
  };

  const handleConfirmDelete = () => {
    if (!deletingSkillGroup) return;
    
    if (dependencyInfo && !dependencyInfo.can_delete && !replacementSkillGroupId) {
      error('请选择替换技能组');
      return;
    }

    deleteMutation.mutate({
      id: deletingSkillGroup.id,
      replacementId: replacementSkillGroupId,
    });
  };

  // 获取技能组标签组件
  const getSkillGroupTag = (skillGroup: SkillGroup) => {
    const tagInfo = getSystemSkillGroupTag(skillGroup);
    return (
      <Tag color={tagInfo.color} icon={<SafetyOutlined />}>
        {tagInfo.text}
      </Tag>
    );
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '技能组名称',
      dataIndex: 'group_name',
      key: 'group_name',
      render: (text: string, record: SkillGroup) => <strong>{getSkillGroupDisplayName(record)}</strong>,
    },
    {
      title: '类型',
      dataIndex: 'group_name',
      key: 'type',
      render: (groupName: string, record: SkillGroup) => getSkillGroupTag(record),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_: any, record: SkillGroup) => {
        const systemSkillGroup = isSystemSkillGroup(record);

        return (
          <Space>
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              disabled={systemSkillGroup}
            >
              编辑
            </Button>
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record)}
              disabled={systemSkillGroup}
            >
              删除
            </Button>
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <div style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleCreate}
        >
          新建技能组
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={skillGroups}
        rowKey="id"
        loading={isLoading}
        pagination={false}
      />

      {/* 创建/编辑模态框 */}
      <Modal
        title={editingSkillGroup ? '编辑技能组' : '新建技能组'}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingSkillGroup(null);
          form.resetFields();
        }}
        confirmLoading={createMutation.isLoading || updateMutation.isLoading}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="group_name"
            label="技能组名称"
            rules={[
              { required: true, message: '请输入技能组名称' },
              { min: 2, message: '技能组名称至少2个字符' },
            ]}
          >
            <Input placeholder="请输入技能组名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea
              rows={3}
              placeholder="请输入技能组描述（可选）"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 安全删除模态框 */}
      <Modal
        title={
          <Space>
            <ExclamationCircleOutlined style={{ color: '#faad14' }} />
            删除技能组确认
          </Space>
        }
        open={deleteModalVisible}
        onOk={handleConfirmDelete}
        onCancel={() => {
          setDeleteModalVisible(false);
          setDeletingSkillGroup(null);
          setDependencyInfo(null);
          setReplacementSkillGroupId(undefined);
        }}
        confirmLoading={deleteMutation.isLoading}
        width={700}
        okText="确认删除"
        cancelText="取消"
        okButtonProps={{ danger: true }}
      >
        {dependencyInfo && (
          <div>
            <Alert
              message="警告"
              description={`删除技能组 "${getSkillGroupDisplayName(dependencyInfo.group_name || '')}" 将影响 ${dependencyInfo.user_count || 0} 个用户、${dependencyInfo.machine_count || 0} 台设备、${dependencyInfo.plan_task_count || 0} 个生产计划`}
              type="warning"
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Tabs
              defaultActiveKey="users"
              items={[
                ...(dependencyInfo.affected_users.length > 0 ? [{
                  key: 'users',
                  label: `用户 (${dependencyInfo.user_count})`,
                  children: (
                    <List
                      size="small"
                      dataSource={dependencyInfo.affected_users}
                      renderItem={(user) => (
                        <List.Item>
                          <Space>
                            <UserOutlined />
                            <Text>{user.username}</Text>
                            {user.full_name && <Text type="secondary">({user.full_name})</Text>}
                          </Space>
                        </List.Item>
                      )}
                    />
                  ),
                }] : []),
                ...(dependencyInfo.affected_machines.length > 0 ? [{
                  key: 'machines',
                  label: `设备 (${dependencyInfo.machine_count})`,
                  children: (
                    <List
                      size="small"
                      dataSource={dependencyInfo.affected_machines}
                      renderItem={(machine) => (
                        <List.Item>
                          <Space>
                            <ToolOutlined />
                            <Text>{machine.machine_name}</Text>
                          </Space>
                        </List.Item>
                      )}
                    />
                  ),
                }] : []),
                ...(dependencyInfo.affected_plan_tasks.length > 0 ? [{
                  key: 'plans',
                  label: `生产计划 (${dependencyInfo.plan_task_count})`,
                  children: (
                    <List
                      size="small"
                      dataSource={dependencyInfo.affected_plan_tasks}
                      renderItem={(task) => (
                        <List.Item>
                          <Space>
                            <ScheduleOutlined />
                            <Text>工单 #{task.work_order_id} - 任务 #{task.id}</Text>
                          </Space>
                        </List.Item>
                      )}
                    />
                  ),
                }] : []),
              ]}
            />

            {!dependencyInfo.can_delete && (
              <>
                <Divider />
                <Form.Item
                  label="选择替换技能组"
                  required
                >
                  <Select
                    placeholder="请选择替换技能组"
                    value={replacementSkillGroupId}
                    onChange={setReplacementSkillGroupId}
                    options={skillGroups
                      .filter(sg => sg.id !== dependencyInfo.skill_group_id)
                      .map(sg => ({
                        value: sg.id,
                        label: getSkillGroupDisplayName(sg),
                      }))}
                  />
                </Form.Item>
                <Alert
                  message="必须选择替换技能组"
                  description="由于该技能组被用户、设备或生产计划使用，删除前必须指定一个替换技能组来保证系统的连续性。"
                  type="info"
                  showIcon
                />
              </>
            )}
          </div>
        )}
      </Modal>
    </>
  );
};

export default SkillGroupManagement;
