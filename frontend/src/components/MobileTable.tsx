import React, { useState, useEffect } from 'react';
import { Card, List, Button, Space, Tag, Pagination, Empty, Drawer } from 'antd';
import { EyeOutlined, EditOutlined, DeleteOutlined, MoreOutlined } from '@ant-design/icons';

interface MobileTableColumn {
  title: string;
  dataIndex?: string;
  key: string;
  render?: (value: any, record: any) => React.ReactNode;
  width?: number;
  ellipsis?: boolean;
}

interface MobileTableAction {
  key: string;
  label: string;
  icon?: React.ReactNode;
  onClick: (record: any) => void;
  danger?: boolean;
  disabled?: (record: any) => boolean;
}

interface MobileTableProps {
  columns: MobileTableColumn[];
  dataSource: any[];
  rowKey: string;
  loading?: boolean;
  pagination?: {
    total: number;
    pageSize: number;
    current?: number;
    onChange?: (page: number, pageSize: number) => void;
  };
  actions?: MobileTableAction[];
  onRowClick?: (record: any) => void;
  primaryFields?: string[]; // 主要显示字段
  secondaryFields?: string[]; // 次要显示字段
}

/**
 * 移动端友好的表格组件
 * 将传统表格转换为卡片列表形式，适合移动设备显示
 */
const MobileTable: React.FC<MobileTableProps> = ({
  columns,
  dataSource,
  rowKey,
  loading = false,
  pagination,
  actions = [],
  onRowClick,
  primaryFields = [],
  secondaryFields = [],
}) => {
  const [isMobile, setIsMobile] = useState(false);
  const [actionDrawerVisible, setActionDrawerVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any>(null);

  // 检测是否为移动设备
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  // 获取列的渲染函数
  const getColumnRender = (dataIndex: string) => {
    const column = columns.find(col => col.dataIndex === dataIndex);
    return column?.render;
  };

  // 渲染字段值
  const renderFieldValue = (record: any, dataIndex: string) => {
    const value = record[dataIndex];
    const render = getColumnRender(dataIndex);
    
    if (render) {
      return render(value, record);
    }
    
    return value;
  };

  // 获取字段标题
  const getFieldTitle = (dataIndex: string) => {
    const column = columns.find(col => col.dataIndex === dataIndex);
    return column?.title || dataIndex;
  };

  // 渲染主要字段
  const renderPrimaryFields = (record: any) => {
    const fields = primaryFields.length > 0 ? primaryFields : columns.slice(0, 2).map(col => col.dataIndex).filter(Boolean);

    return (
      <div style={{ marginBottom: '8px' }}>
        {fields.map((field, index) => (
          <div key={field} style={{
            fontSize: index === 0 ? '16px' : '14px',
            fontWeight: index === 0 ? 'bold' : 'normal',
            color: index === 0 ? '#262626' : '#595959',
            marginBottom: index === 0 ? '4px' : '0',
          }}>
            {renderFieldValue(record, field!)}
          </div>
        ))}
      </div>
    );
  };

  // 渲染次要字段
  const renderSecondaryFields = (record: any) => {
    const fields = secondaryFields.length > 0
      ? secondaryFields
      : columns.slice(2, 5).map(col => col.dataIndex).filter(Boolean);

    if (fields.length === 0) return null;

    return (
      <div style={{ marginBottom: '8px' }}>
        {fields.map(field => {
          const value = renderFieldValue(record, field!);
          if (!value) return null;

          return (
            <div key={field} style={{
              fontSize: '12px',
              color: '#8c8c8c',
              marginBottom: '2px',
              display: 'flex',
              justifyContent: 'space-between',
            }}>
              <span>{getFieldTitle(field!)}:</span>
              <span>{value}</span>
            </div>
          );
        })}
      </div>
    );
  };

  // 渲染操作按钮
  const renderActions = (record: any) => {
    if (actions.length === 0) return null;

    if (actions.length <= 2) {
      // 少量操作直接显示
      return (
        <Space size="small">
          {actions.map(action => (
            <Button
              key={action.key}
              type="text"
              size="small"
              icon={action.icon}
              danger={action.danger}
              disabled={action.disabled?.(record)}
              onClick={(e) => {
                e.stopPropagation();
                action.onClick(record);
              }}
            >
              {action.label}
            </Button>
          ))}
        </Space>
      );
    }

    // 多个操作使用更多按钮
    return (
      <Button
        type="text"
        size="small"
        icon={<MoreOutlined />}
        onClick={(e) => {
          e.stopPropagation();
          setSelectedRecord(record);
          setActionDrawerVisible(true);
        }}
      >
        更多
      </Button>
    );
  };

  // 渲染移动端卡片
  const renderMobileCard = (record: any) => (
    <Card
      size="small"
      style={{ 
        marginBottom: '12px',
        borderRadius: '12px',
        cursor: onRowClick ? 'pointer' : 'default',
      }}
      bodyStyle={{ padding: '16px' }}
      onClick={() => onRowClick?.(record)}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <div style={{ flex: 1, marginRight: '12px' }}>
          {renderPrimaryFields(record)}
          {renderSecondaryFields(record)}
        </div>
        <div style={{ flexShrink: 0 }}>
          {renderActions(record)}
        </div>
      </div>
    </Card>
  );

  if (!isMobile) {
    // 桌面端返回null，让父组件使用原始Table
    return null;
  }

  return (
    <>
      <div style={{ marginBottom: '16px' }}>
        {dataSource.length === 0 ? (
          <Empty description="暂无数据" />
        ) : (
          dataSource.map(record => (
            <div key={record[rowKey]}>
              {renderMobileCard(record)}
            </div>
          ))
        )}
      </div>

      {pagination && dataSource.length > 0 && (
        <div style={{ textAlign: 'center', marginTop: '16px' }}>
          <Pagination
            current={pagination.current || 1}
            total={pagination.total}
            pageSize={pagination.pageSize}
            onChange={pagination.onChange}
            showSizeChanger={false}
            showQuickJumper={false}
            showTotal={(total, range) => `${range[0]}-${range[1]} / ${total}`}
            size="small"
          />
        </div>
      )}

      {/* 操作抽屉 */}
      <Drawer
        title="操作"
        placement="bottom"
        onClose={() => setActionDrawerVisible(false)}
        open={actionDrawerVisible}
        height="auto"
        styles={{ body: { padding: '16px' } }}
      >
        <Space direction="vertical" style={{ width: '100%' }} size="middle">
          {actions.map(action => (
            <Button
              key={action.key}
              type={action.danger ? 'primary' : 'default'}
              danger={action.danger}
              block
              icon={action.icon}
              disabled={selectedRecord && action.disabled?.(selectedRecord)}
              onClick={() => {
                if (selectedRecord) {
                  action.onClick(selectedRecord);
                }
                setActionDrawerVisible(false);
              }}
            >
              {action.label}
            </Button>
          ))}
        </Space>
      </Drawer>
    </>
  );
};

export default MobileTable;
