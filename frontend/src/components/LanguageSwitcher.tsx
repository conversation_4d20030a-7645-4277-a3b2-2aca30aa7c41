/**
 * 语言切换组件
 * 用于测试和演示国际化功能
 */

import React from 'react';
import { Select, Space } from 'antd';
import { GlobalOutlined } from '@ant-design/icons';
import { useI18n } from '@/hooks/useI18n';

const { Option } = Select;

const LanguageSwitcher: React.FC = () => {
  const { changeLanguage, currentLanguage } = useI18n();

  const handleLanguageChange = (language: string) => {
    changeLanguage(language);
  };

  return (
    <Space>
      <GlobalOutlined />
      <Select
        value={currentLanguage}
        onChange={handleLanguageChange}
        style={{ width: 120 }}
        size="small"
      >
        <Option value="zh-CN">中文</Option>
        <Option value="en-US">English</Option>
      </Select>
    </Space>
  );
};

export default LanguageSwitcher;
