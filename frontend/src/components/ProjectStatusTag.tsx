import React from 'react';
import { Tag } from 'antd';
import { 
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  PauseCircleOutlined,
} from '@ant-design/icons';
import { PROJECT_STATUS_OPTIONS, ProjectStatus } from '@/types/api';

interface ProjectStatusTagProps {
  status: string;
  showIcon?: boolean;
  size?: 'small' | 'default';
}

const ProjectStatusTag: React.FC<ProjectStatusTagProps> = ({ 
  status, 
  showIcon = true, 
  size = 'default' 
}) => {
  const statusConfig = PROJECT_STATUS_OPTIONS.find(option => option.value === status);
  
  if (!statusConfig) {
    return <Tag>{status}</Tag>;
  }

  const iconMap = {
    normal: <CheckCircleOutlined />,
    priority: <ExclamationCircleOutlined />,
    paused: <PauseCircleOutlined />,
  };

  const icon = showIcon ? iconMap[status as keyof typeof iconMap] : undefined;

  return (
    <Tag 
      color={statusConfig.color} 
      icon={icon}
      style={{ 
        fontSize: size === 'small' ? '12px' : '14px',
        padding: size === 'small' ? '2px 6px' : '4px 8px',
      }}
    >
      {statusConfig.label}
    </Tag>
  );
};

export default ProjectStatusTag;
