import React, { useState, useMemo, useEffect } from 'react';
import { Layout as AntLayout, Menu, Button, Dropdown, Avatar, Space, Typography, Drawer } from 'antd';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
  DashboardOutlined,
  ProjectOutlined,
  ToolOutlined,
  SettingOutlined,
  FileTextOutlined,
  CalendarOutlined,
  PlayCircleOutlined,
  SafetyCertificateOutlined,
  TeamOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  LogoutOutlined,
  UserOutlined,
  ApartmentOutlined,
  HomeOutlined,
  CloseOutlined,
  ClockCircleOutlined,
  // ApiOutlined,
  // DatabaseOutlined,
  // EyeOutlined, // 已移除旧的执行跟踪页面
} from '@ant-design/icons';
import { useAuthStore } from '@/store/auth';
import { usePermissions } from '@/hooks/usePermissions';
import MobileBottomNav from './MobileBottomNav';
import LanguageSwitcher from './LanguageSwitcher';

const { Header, Sider, Content } = AntLayout;
const { Text } = Typography;

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuthStore();
  const { hasPageAccess, getAccessiblePages, loading: permissionsLoading } = usePermissions();

  // 检测屏幕尺寸
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  // 移动端路由变化时关闭菜单
  useEffect(() => {
    if (isMobile) {
      setMobileMenuVisible(false);
    }
  }, [location.pathname, isMobile]);

  // 所有可能的菜单项配置
  const allMenuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: <Link to="/dashboard">仪表板</Link>,
      title: '仪表板'
    },
    {
      key: '/projects',
      icon: <ProjectOutlined />,
      label: <Link to="/projects">项目管理</Link>,
      title: '项目管理'
    },
    {
      key: '/parts',
      icon: <ToolOutlined />,
      label: <Link to="/parts">零件管理</Link>,
      title: '零件管理'
    },
    {
      key: '/machines',
      icon: <SettingOutlined />,
      label: <Link to="/machines">设备管理</Link>,
      title: '设备管理'
    },
    {
      key: '/work-orders',
      icon: <FileTextOutlined />,
      label: <Link to="/work-orders">工单管理</Link>,
      title: '工单管理'
    },
    {
      key: '/plan-tasks',
      icon: <CalendarOutlined />,
      label: <Link to="/plan-tasks">生产计划</Link>,
      title: '生产计划'
    },
    {
      key: '/production-center',
      icon: <PlayCircleOutlined />,
      label: <Link to="/production-center">生产执行中心</Link>,
      title: '生产执行中心'
    },
    // 旧的执行页面已被生产执行中心替代
    // {
    //   key: '/execution',
    //   icon: <EyeOutlined />,
    //   label: <Link to="/execution">执行跟踪(旧)</Link>,
    //   title: '执行跟踪(旧)'
    // },
    // {
    //   key: '/operator-execution',
    //   icon: <ToolOutlined />,
    //   label: <Link to="/operator-execution">操作员执行(旧)</Link>,
    //   title: '操作员执行(旧)'
    // },
    {
      key: '/quality',
      icon: <SafetyCertificateOutlined />,
      label: <Link to="/quality">质量管理</Link>,
      title: '质量管理'
    },
    {
      key: '/bom',
      icon: <ApartmentOutlined />,
      label: <Link to="/bom">BOM管理</Link>,
      title: 'BOM管理'
    },
    {
      key: '/routings',
      icon: <SettingOutlined />,
      label: <Link to="/routings">工艺管理</Link>,
      title: '工艺管理'
    },
    {
      key: '/users',
      icon: <TeamOutlined />,
      label: <Link to="/users">用户管理</Link>,
      title: '用户管理'
    },
    {
      key: '/role-permissions',
      icon: <SettingOutlined />,
      label: <Link to="/role-permissions">角色权限</Link>,
      title: '角色权限管理'
    },
    {
      key: '/system-config',
      icon: <SettingOutlined />,
      label: <Link to="/system-config">系统配置</Link>,
      title: '系统配置'
    },
    {
      key: '/auto-work-order-config',
      icon: <SettingOutlined />,
      label: <Link to="/auto-work-order-config">自动工单配置</Link>,
      title: '自动工单配置'
    },
    {
      key: '/shift-management',
      icon: <ClockCircleOutlined />,
      label: <Link to="/shift-management">班次管理</Link>,
      title: '班次管理'
    },
    // Debug/Test menu items - commented out for production
    // {
    //   key: '/api-test',
    //   icon: <ApiOutlined />,
    //   label: <Link to="/api-test">API测试</Link>,
    //   title: 'API测试'
    // },
    // {
    //   key: '/database',
    //   icon: <DatabaseOutlined />,
    //   label: <Link to="/database">数据库查看</Link>,
    //   title: '数据库查看'
    // },
  ];

  // 根据用户权限动态生成菜单项
  const menuItems = useMemo(() => {
    if (!user || permissionsLoading) {
      return [];
    }

    // 管理员可以访问所有菜单
    if (user.roles?.includes('admin')) {
      return allMenuItems;
    }

    // 其他用户根据权限过滤菜单
    return allMenuItems.filter(item => hasPageAccess(item.key));
  }, [user, hasPageAccess, permissionsLoading]);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => navigate('/profile'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  // 渲染移动端菜单内容
  const renderMobileMenu = () => (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 移动端菜单头部 */}
      <div style={{
        padding: '16px',
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <Avatar
            style={{ backgroundColor: '#1890ff' }}
            icon={<UserOutlined />}
          />
          <div>
            <div style={{ fontWeight: 'bold', fontSize: '16px' }}>
              {user?.full_name || user?.username}
            </div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {user?.roles?.join(', ')}
            </div>
          </div>
        </div>
        <Button
          type="text"
          icon={<CloseOutlined />}
          onClick={() => setMobileMenuVisible(false)}
          style={{ fontSize: '18px' }}
        />
      </div>

      {/* 移动端菜单列表 */}
      <div style={{ flex: 1, overflow: 'auto' }}>
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          style={{ border: 'none' }}
        />
      </div>

      {/* 移动端菜单底部 */}
      <div style={{
        padding: '16px',
        borderTop: '1px solid #f0f0f0',
      }}>
        <Button
          type="primary"
          danger
          block
          icon={<LogoutOutlined />}
          onClick={handleLogout}
        >
          退出登录
        </Button>
      </div>
    </div>
  );

  if (isMobile) {
    // 移动端布局
    return (
      <AntLayout style={{ minHeight: '100vh' }}>
        {/* 移动端顶部导航栏 */}
        <Header style={{
          padding: '0 16px',
          background: '#fff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1000,
          height: '56px',
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <Button
              type="text"
              icon={<MenuFoldOutlined />}
              onClick={() => setMobileMenuVisible(true)}
              style={{ fontSize: '18px', padding: '4px 8px' }}
            />
            <div style={{ fontWeight: 'bold', fontSize: '18px', color: '#1890ff' }}>
              MES
            </div>
          </div>

          <Space>
            <LanguageSwitcher />
            <Button
              type="text"
              icon={<HomeOutlined />}
              onClick={() => navigate('/dashboard')}
              style={{ fontSize: '18px', padding: '4px 8px' }}
            />
          </Space>
        </Header>

        {/* 移动端侧边菜单抽屉 */}
        <Drawer
          title={null}
          placement="left"
          onClose={() => setMobileMenuVisible(false)}
          open={mobileMenuVisible}
          styles={{ body: { padding: 0 } }}
          width={280}
          closable={false}
        >
          {renderMobileMenu()}
        </Drawer>

        {/* 移动端内容区域 */}
        <Content style={{
          marginTop: '56px',
          padding: '16px',
          paddingBottom: '76px', // 为底部导航栏留出空间
          minHeight: 'calc(100vh - 56px)',
        }}>
          {children}
        </Content>

        {/* 移动端底部导航栏 */}
        <MobileBottomNav />
      </AntLayout>
    );
  }

  // 桌面端布局（原有布局）
  return (
    <AntLayout>
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        style={{
          overflow: 'auto',
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
        }}
      >
        <div style={{
          height: 32,
          margin: 16,
          background: 'rgba(255, 255, 255, 0.3)',
          borderRadius: 6,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontWeight: 'bold',
        }}>
          {collapsed ? 'MES' : 'MES 系统'}
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
        />
      </Sider>
      <AntLayout style={{ marginLeft: collapsed ? 80 : 200 }}>
        <Header style={{
          padding: '0 16px',
          background: '#fff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          boxShadow: '0 1px 4px rgba(0,21,41,.08)',
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{
              fontSize: '16px',
              width: 64,
              height: 64,
            }}
          />

          <Space>
            <LanguageSwitcher />
            <Text>欢迎，{user?.full_name || user?.username}</Text>
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
            >
              <Avatar
                style={{ backgroundColor: '#1890ff', cursor: 'pointer' }}
                icon={<UserOutlined />}
              />
            </Dropdown>
          </Space>
        </Header>
        <Content style={{
          margin: '16px',
          overflow: 'initial',
        }}>
          {children}
        </Content>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;
