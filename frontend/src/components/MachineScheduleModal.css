/* 拖拽相关样式 */
.schedule-task-draggable {
  cursor: move;
  user-select: none;
  transition: all 0.2s ease;
}

.schedule-task-draggable:hover {
  transform: scale(1.02);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.schedule-task-dragging {
  opacity: 0.7;
  border: 2px solid #1890ff !important;
  transform: rotate(2deg);
}

.schedule-slot-drop-target {
  transition: all 0.2s ease;
}

.schedule-slot-drop-target:hover {
  background-color: #e6f7ff !important;
  border-color: #1890ff !important;
}

.schedule-slot-drop-active {
  background-color: #e6f7ff !important;
  border-color: #1890ff !important;
  border-style: solid !important;
}

/* 表格样式优化 */
.schedule-table .ant-table-tbody > tr > td {
  padding: 2px !important;
  vertical-align: middle;
}

.schedule-table .ant-table-thead > tr > th {
  text-align: center;
  font-weight: bold;
  background-color: #fafafa;
}

/* 任务卡片样式 */
.task-card {
  border-radius: 4px;
  padding: 4px;
  font-size: 11px;
  text-align: center;
  min-height: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.task-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: rgba(255, 255, 255, 0.3);
}

.task-card-draggable {
  cursor: move;
}

.task-card-draggable:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 空闲时间槽样式 */
.empty-slot {
  border-radius: 4px;
  padding: 4px;
  background-color: #f0f0f0;
  color: #999;
  font-size: 11px;
  text-align: center;
  border: 1px dashed #d9d9d9;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.empty-slot-drop-target {
  background-color: #e6f7ff !important;
  border-color: #1890ff !important;
  border-style: solid !important;
  color: #1890ff !important;
}

/* 拖拽指示器 */
.drag-indicator {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 8px;
  height: 8px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
}

/* 时间列样式 */
.time-column {
  font-weight: bold;
  font-size: 12px;
  color: #666;
  text-align: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .task-card {
    font-size: 10px;
    padding: 2px;
    min-height: 35px;
  }
  
  .empty-slot {
    font-size: 10px;
    padding: 2px;
    min-height: 35px;
  }
}

/* 拖拽动画 */
@keyframes dragPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.schedule-task-dragging {
  animation: dragPulse 0.5s ease-in-out infinite;
}

/* 放置区域高亮 */
.drop-zone-highlight {
  background: linear-gradient(45deg, #e6f7ff, #bae7ff) !important;
  border: 2px solid #1890ff !important;
  box-shadow: inset 0 0 10px rgba(24, 144, 255, 0.2);
}

/* 多选相关样式 */
.task-selected {
  border: 2px solid #52c41a !important;
  box-shadow: 0 0 8px rgba(82, 196, 26, 0.6) !important;
  transform: scale(1.02);
}

.task-selected::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid #52c41a;
  border-radius: 6px;
  pointer-events: none;
  animation: selectedPulse 2s ease-in-out infinite;
}

@keyframes selectedPulse {
  0% { opacity: 0.8; }
  50% { opacity: 0.4; }
  100% { opacity: 0.8; }
}

/* 多选模式下的任务卡片 */
.multi-select-mode .task-card {
  cursor: pointer;
  transition: all 0.2s ease;
}

.multi-select-mode .task-card:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 选择指示器 */
.selection-indicator {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 12px;
  height: 12px;
  background-color: #52c41a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  color: white;
  font-weight: bold;
  z-index: 10;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 批量操作提示 */
.batch-operation-hint {
  background: linear-gradient(45deg, #f6ffed, #d9f7be);
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  padding: 8px 12px;
  margin: 8px 0;
  font-size: 12px;
  color: #389e0d;
}

/* 冲突警告样式 */
.conflict-warning {
  background: linear-gradient(45deg, #fff2e8, #ffd8bf);
  border: 1px solid #ffbb96;
  border-radius: 4px;
  padding: 8px 12px;
  margin: 8px 0;
  font-size: 12px;
  color: #d4380d;
}

/* 时间冲突高亮 */
.time-conflict {
  border: 2px solid #ff4d4f !important;
  background: rgba(255, 77, 79, 0.1) !important;
  animation: conflictShake 0.5s ease-in-out;
}

@keyframes conflictShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

/* 智能填充任务样式 */
.smart-fill-task {
  transition: all 0.2s ease-in-out;
  background: linear-gradient(135deg, var(--task-color, #1890ff) 0%, var(--task-color-dark, #0050b3) 100%);
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.smart-fill-task:hover {
  transform: scale(1.02);
  box-shadow:
    0 4px 8px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  z-index: 5;
}

.smart-fill-task.schedule-task-dragging {
  transform: scale(1.05) rotate(2deg);
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.2),
    0 0 0 2px #1890ff;
  z-index: 10;
}

.smart-fill-task.task-selected {
  box-shadow:
    0 0 0 2px #52c41a,
    0 4px 12px rgba(82, 196, 26, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 任务填充高度变化动画 */
.smart-fill-task {
  animation: taskFillIn 0.3s ease-out;
}

@keyframes taskFillIn {
  from {
    opacity: 0;
    transform: scaleY(0.5);
  }
  to {
    opacity: 0.9;
    transform: scaleY(1);
  }
}

/* 任务颜色主题 */
.smart-fill-task[data-task-type="milling"] {
  background: linear-gradient(135deg, #1890ff 0%, #0050b3 100%);
}

.smart-fill-task[data-task-type="turning"] {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
}

.smart-fill-task[data-task-type="drilling"] {
  background: linear-gradient(135deg, #fa8c16 0%, #d46b08 100%);
}

.smart-fill-task[data-task-type="grinding"] {
  background: linear-gradient(135deg, #eb2f96 0%, #c41d7f 100%);
}

.smart-fill-task[data-task-type="assembly"] {
  background: linear-gradient(135deg, #722ed1 0%, #531dab 100%);
}

.smart-fill-task[data-task-type="inspection"] {
  background: linear-gradient(135deg, #13c2c2 0%, #08979c 100%);
}

/* 任务时间指示器 */
.task-time-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 0 0 4px 4px;
}

.task-time-indicator::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 0 0 4px 4px;
  animation: progressFill 2s ease-in-out infinite;
}

@keyframes progressFill {
  0%, 100% { width: 20%; }
  50% { width: 80%; }
}

/* 任务内容自适应 */
.task-content-adaptive {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
  padding: 2px;
}

.task-content-adaptive.compact {
  justify-content: center;
  padding: 1px;
}

.task-content-adaptive.expanded {
  justify-content: flex-start;
  padding: 4px 2px;
}

/* 任务持续时间标签 */
.task-duration-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 8px;
  padding: 1px 4px;
  border-radius: 8px;
  font-weight: bold;
  z-index: 10;
  white-space: nowrap;
}

/* 任务状态指示器 */
.task-status-indicator {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  z-index: 5;
}

.task-status-indicator.status-pending {
  background: #faad14;
}

.task-status-indicator.status-running {
  background: #52c41a;
  animation: statusPulse 1.5s ease-in-out infinite;
}

.task-status-indicator.status-completed {
  background: #1890ff;
}

.task-status-indicator.status-paused {
  background: #fa8c16;
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.2); }
}

/* 响应式调整 - 智能填充 */
@media (max-width: 768px) {
  .smart-fill-task {
    font-size: 10px;
    padding: 1px 2px;
  }

  .task-duration-badge {
    font-size: 7px;
    padding: 1px 2px;
  }

  .task-status-indicator {
    width: 4px;
    height: 4px;
  }
}

/* 响应式调整 - 多选模式 */
@media (max-width: 768px) {
  .selection-indicator {
    width: 10px;
    height: 10px;
    font-size: 6px;
  }

  .batch-operation-hint,
  .conflict-warning {
    font-size: 11px;
    padding: 6px 8px;
  }
}
