/* 甘特图拖拽样式 */
.gantt-chart-container {
  position: relative;
  overflow: hidden;
}

.gantt-task-bar {
  position: relative;
  transition: all 0.2s ease;
}

.gantt-task-bar:hover .gantt-resize-handle {
  opacity: 0.7 !important;
}

.gantt-task-bar.dragging {
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: scale(1.02);
}

.gantt-resize-handle {
  position: absolute;
  top: 0;
  width: 4px;
  height: 100%;
  background-color: #1890ff;
  cursor: col-resize;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 10;
}

.gantt-resize-handle.left {
  left: -2px;
  border-radius: 2px 0 0 2px;
}

.gantt-resize-handle.right {
  right: -2px;
  border-radius: 0 2px 2px 0;
}

.gantt-resize-handle:hover {
  opacity: 1 !important;
}

.gantt-task-bar.dragging .gantt-resize-handle {
  opacity: 1;
}

/* 拖拽时的全局样式 */
body.gantt-dragging {
  user-select: none;
  cursor: grabbing !important;
}

body.gantt-resizing {
  user-select: none;
  cursor: col-resize !important;
}

/* 甘特图网格线 */
.gantt-grid-line {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: #f0f0f0;
  pointer-events: none;
}

/* 今日线 */
.gantt-today-line {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #ff4d4f;
  pointer-events: none;
  z-index: 5;
}

/* 拖拽预览 */
.gantt-drag-preview {
  position: absolute;
  background-color: rgba(24, 144, 255, 0.3);
  border: 2px dashed #1890ff;
  border-radius: 4px;
  pointer-events: none;
  z-index: 999;
}

/* 任务条悬停效果 */
.gantt-task-bar:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 禁用状态 */
.gantt-task-bar.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.gantt-task-bar.disabled:hover {
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .gantt-resize-handle {
    width: 6px;
  }
  
  .gantt-resize-handle.left {
    left: -3px;
  }
  
  .gantt-resize-handle.right {
    right: -3px;
  }
}
