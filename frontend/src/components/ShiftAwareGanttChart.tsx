import React, { useState, useEffect, useMemo } from 'react';
import {
  Card,
  Select,
  Row,
  Col,
  Switch,
  Tag,
  Space,
  Alert,
  Tooltip,
  Button,
  Modal,
  Timeline,
  Descriptions,
  message,
} from 'antd';
import {
  ClockCircleOutlined,
  InfoCircleOutlined,
  CalendarOutlined,
  TeamOutlined,
  SettingOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
} from '@ant-design/icons';
import { useQuery } from 'react-query';
import { apiClient } from '@/lib/api';
import dayjs from 'dayjs';
import './ShiftAwareGanttChart.css';

const { Option } = Select;

interface ShiftTemplate {
  id: number;
  template_name: string;
  schedule_type: string;
  start_hour: number;
  start_minute: number;
  end_hour: number;
  end_minute: number;
  duration_hours: number;
  work_days: number[];
  description?: string;
}

interface PlanGroup {
  id: number;
  group_name: string;
  group_code: string;
  priority: number;
}

interface GanttTask {
  id: number;
  name: string;
  start: string;
  end: string;
  skill_group_id: number;
  machine_id?: number;
  work_order_id: number;
  part_number: string;
  process_name: string;
  status: string;
  progress: number;
  machine_name?: string;
  plan_group_id?: number;
  shift_constraints?: any;
}

interface ShiftAwareGanttChartProps {
  tasks: GanttTask[];
  onTaskUpdate?: (taskId: number, updates: any) => void;
  onTaskMove?: (taskId: number, newStart: string, newEnd: string) => void;
  dateRange: [dayjs.Dayjs, dayjs.Dayjs];
  viewMode: 'day' | 'week' | 'month';
}

const ShiftAwareGanttChart: React.FC<ShiftAwareGanttChartProps> = ({
  tasks,
  onTaskUpdate,
  onTaskMove,
  dateRange,
  viewMode = 'day',
}) => {
  const [selectedPlanGroup, setSelectedPlanGroup] = useState<number | null>(null);
  const [showShiftBoundaries, setShowShiftBoundaries] = useState(true);
  const [showShiftConflicts, setShowShiftConflicts] = useState(true);
  const [selectedShiftTemplate, setSelectedShiftTemplate] = useState<number | null>(null);
  const [shiftDetailModalVisible, setShiftDetailModalVisible] = useState(false);
  const [selectedShiftDetail, setSelectedShiftDetail] = useState<ShiftTemplate | null>(null);

  // 获取计划组
  const { data: planGroups } = useQuery(
    'plan-groups',
    () => apiClient.getPlanGroups(true),
    {
      select: (data) => data.groups,
    }
  );

  // 获取班次模板
  const { data: shiftTemplates } = useQuery(
    'shift-templates',
    () => apiClient.getShiftTemplates(true),
    {
      select: (data) => data.templates,
    }
  );

  // 获取选中计划组的班次配置
  const { data: shiftConfigs } = useQuery(
    ['shift-configs', selectedPlanGroup],
    () => selectedPlanGroup
      ? Promise.resolve({ configs: [] }) // TODO: 实现获取计划组班次配置的API
      : Promise.resolve({ configs: [] }),
    {
      enabled: !!selectedPlanGroup,
      select: (data) => data.configs,
    }
  );

  // 计算班次边界线
  const shiftBoundaries = useMemo(() => {
    if (!showShiftBoundaries || !selectedShiftTemplate || !shiftTemplates) {
      return [];
    }

    const template = shiftTemplates.find((t: ShiftTemplate) => t.id === selectedShiftTemplate);
    if (!template) return [];

    const boundaries = [];
    const [startDate, endDate] = dateRange;
    let currentDate = startDate.clone();

    while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'day')) {
      const weekday = currentDate.day() === 0 ? 7 : currentDate.day(); // 转换为1-7格式
      
      if (template.work_days.includes(weekday)) {
        // 班次开始边界
        const shiftStart = currentDate
          .hour(template.start_hour)
          .minute(template.start_minute)
          .second(0);
        
        // 班次结束边界
        let shiftEnd = currentDate
          .hour(template.end_hour)
          .minute(template.end_minute)
          .second(0);
        
        // 处理跨天班次
        if (template.end_hour < template.start_hour) {
          shiftEnd = shiftEnd.add(1, 'day');
        }

        boundaries.push({
          type: 'shift_start',
          time: shiftStart,
          template: template,
          label: `${template.template_name} 开始`,
        });

        boundaries.push({
          type: 'shift_end',
          time: shiftEnd,
          template: template,
          label: `${template.template_name} 结束`,
        });

        // 7x12模式添加中间休息时间标记
        if (template.schedule_type === '7x12') {
          const breakTime = shiftStart.add(6, 'hour'); // 假设6小时后休息
          boundaries.push({
            type: 'break_time',
            time: breakTime,
            template: template,
            label: '休息时间',
          });
        }
      }

      currentDate = currentDate.add(1, 'day');
    }

    return boundaries;
  }, [showShiftBoundaries, selectedShiftTemplate, shiftTemplates, dateRange]);

  // 检测班次冲突
  const shiftConflicts = useMemo(() => {
    if (!showShiftConflicts || !selectedShiftTemplate || !shiftTemplates) {
      return [];
    }

    const template = shiftTemplates.find((t: ShiftTemplate) => t.id === selectedShiftTemplate);
    if (!template) return [];

    const conflicts = [];

    for (const task of tasks) {
      const taskStart = dayjs(task.start);
      const taskEnd = dayjs(task.end);
      
      // 检查任务是否在工作日
      const weekday = taskStart.day() === 0 ? 7 : taskStart.day();
      if (!template.work_days.includes(weekday)) {
        conflicts.push({
          taskId: task.id,
          type: 'non_working_day',
          message: `任务 ${task.name} 安排在非工作日`,
          severity: 'error',
        });
        continue;
      }

      // 检查任务时间是否在班次时间内
      const shiftStart = taskStart
        .hour(template.start_hour)
        .minute(template.start_minute)
        .second(0);
      
      let shiftEnd = taskStart
        .hour(template.end_hour)
        .minute(template.end_minute)
        .second(0);
      
      if (template.end_hour < template.start_hour) {
        shiftEnd = shiftEnd.add(1, 'day');
      }

      if (taskStart.isBefore(shiftStart) || taskEnd.isAfter(shiftEnd)) {
        conflicts.push({
          taskId: task.id,
          type: 'time_overflow',
          message: `任务 ${task.name} 超出班次工作时间`,
          severity: 'warning',
        });
      }

      // 检查任务是否跨班次
      if (template.schedule_type === '7x12' && taskEnd.diff(taskStart, 'hour') > 12) {
        conflicts.push({
          taskId: task.id,
          type: 'cross_shift',
          message: `任务 ${task.name} 跨越多个班次`,
          severity: 'info',
        });
      }
    }

    return conflicts;
  }, [showShiftConflicts, selectedShiftTemplate, shiftTemplates, tasks]);

  // 获取班次类型颜色
  const getScheduleTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
      '7x24': '#ff4d4f',
      '7x12': '#fa8c16',
      '5x8': '#52c41a',
      'custom': '#722ed1',
    };
    return colorMap[type] || '#d9d9d9';
  };

  // 渲染班次边界线
  const renderShiftBoundaries = () => {
    if (!showShiftBoundaries) return null;

    return shiftBoundaries.map((boundary, index) => {
      const position = calculateTimePosition(boundary.time);
      const color = boundary.type === 'shift_start' ? '#52c41a' : 
                   boundary.type === 'shift_end' ? '#ff4d4f' : '#fa8c16';
      
      return (
        <div
          key={index}
          className="shift-boundary-line"
          style={{
            left: `${position}%`,
            borderColor: color,
          }}
        >
          <Tooltip title={boundary.label}>
            <div 
              className="shift-boundary-marker"
              style={{ backgroundColor: color }}
            />
          </Tooltip>
        </div>
      );
    });
  };

  // 渲染班次背景区域
  const renderShiftBackgrounds = () => {
    if (!showShiftBoundaries || !selectedShiftTemplate || !shiftTemplates) {
      return null;
    }

    const template = shiftTemplates.find((t: ShiftTemplate) => t.id === selectedShiftTemplate);
    if (!template) return null;

    const backgrounds = [];
    const [startDate, endDate] = dateRange;
    let currentDate = startDate.clone();

    while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'day')) {
      const weekday = currentDate.day() === 0 ? 7 : currentDate.day();
      
      if (template.work_days.includes(weekday)) {
        const shiftStart = currentDate
          .hour(template.start_hour)
          .minute(template.start_minute);
        
        let shiftEnd = currentDate
          .hour(template.end_hour)
          .minute(template.end_minute);
        
        if (template.end_hour < template.start_hour) {
          shiftEnd = shiftEnd.add(1, 'day');
        }

        const startPosition = calculateTimePosition(shiftStart);
        const endPosition = calculateTimePosition(shiftEnd);
        const width = endPosition - startPosition;

        backgrounds.push(
          <div
            key={`shift-bg-${currentDate.format('YYYY-MM-DD')}`}
            className="shift-background"
            style={{
              left: `${startPosition}%`,
              width: `${width}%`,
              backgroundColor: getScheduleTypeColor(template.schedule_type),
              opacity: 0.1,
            }}
          />
        );
      }

      currentDate = currentDate.add(1, 'day');
    }

    return backgrounds;
  };

  // 计算时间位置百分比
  const calculateTimePosition = (time: dayjs.Dayjs) => {
    const [startDate, endDate] = dateRange;
    const totalDuration = endDate.diff(startDate, 'minute');
    const timeDuration = time.diff(startDate, 'minute');
    return Math.max(0, Math.min(100, (timeDuration / totalDuration) * 100));
  };

  // 渲染冲突警告
  const renderConflictWarnings = () => {
    if (!showShiftConflicts || shiftConflicts.length === 0) {
      return null;
    }

    const errorCount = shiftConflicts.filter(c => c.severity === 'error').length;
    const warningCount = shiftConflicts.filter(c => c.severity === 'warning').length;
    const infoCount = shiftConflicts.filter(c => c.severity === 'info').length;

    return (
      <Alert
        message="班次冲突检测"
        description={
          <Space direction="vertical" size="small">
            {errorCount > 0 && (
              <Tag color="red">{errorCount} 个严重冲突</Tag>
            )}
            {warningCount > 0 && (
              <Tag color="orange">{warningCount} 个警告</Tag>
            )}
            {infoCount > 0 && (
              <Tag color="blue">{infoCount} 个提示</Tag>
            )}
            <div>
              {shiftConflicts.slice(0, 3).map((conflict, index) => (
                <div key={index} style={{ fontSize: '12px', color: '#666' }}>
                  • {conflict.message}
                </div>
              ))}
              {shiftConflicts.length > 3 && (
                <div style={{ fontSize: '12px', color: '#666' }}>
                  ... 还有 {shiftConflicts.length - 3} 个冲突
                </div>
              )}
            </div>
          </Space>
        }
        type={errorCount > 0 ? 'error' : warningCount > 0 ? 'warning' : 'info'}
        showIcon
        style={{ marginBottom: '16px' }}
      />
    );
  };

  return (
    <div className="shift-aware-gantt-chart">
      {/* 班次控制面板 */}
      <Card size="small" style={{ marginBottom: '16px' }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Space>
              <TeamOutlined />
              <span>计划组:</span>
            </Space>
            <Select
              placeholder="选择计划组"
              value={selectedPlanGroup}
              onChange={setSelectedPlanGroup}
              style={{ width: '100%', marginTop: '4px' }}
              allowClear
            >
              {planGroups?.map((group: PlanGroup) => (
                <Option key={group.id} value={group.id}>
                  <Space>
                    <span>{group.group_name}</span>
                    <Tag>{group.group_code}</Tag>
                  </Space>
                </Option>
              ))}
            </Select>
          </Col>

          <Col span={6}>
            <Space>
              <ClockCircleOutlined />
              <span>班次模板:</span>
            </Space>
            <Select
              placeholder="选择班次模板"
              value={selectedShiftTemplate}
              onChange={setSelectedShiftTemplate}
              style={{ width: '100%', marginTop: '4px' }}
              allowClear
              disabled={!selectedPlanGroup}
            >
              {shiftTemplates?.map((template: ShiftTemplate) => (
                <Option key={template.id} value={template.id}>
                  <Space>
                    <span>{template.template_name}</span>
                    <Tag
                      color={getScheduleTypeColor(template.schedule_type)}
                    >
                      {template.schedule_type}
                    </Tag>
                  </Space>
                </Option>
              ))}
            </Select>
          </Col>

          <Col span={6}>
            <Space direction="vertical" size="small">
              <Space>
                <Switch
                  checked={showShiftBoundaries}
                  onChange={setShowShiftBoundaries}
                  size="small"
                />
                <span>显示班次边界</span>
              </Space>
              <Space>
                <Switch
                  checked={showShiftConflicts}
                  onChange={setShowShiftConflicts}
                  size="small"
                />
                <span>冲突检测</span>
              </Space>
            </Space>
          </Col>

          <Col span={6}>
            <Space>
              <Button
                type="primary"
                icon={<SettingOutlined />}
                size="small"
                onClick={() => {
                  // TODO: 打开班次配置对话框
                  message.info('班次配置功能开发中');
                }}
              >
                班次配置
              </Button>
              {selectedShiftTemplate && (
                <Button
                  icon={<InfoCircleOutlined />}
                  size="small"
                  onClick={() => {
                    const template = shiftTemplates?.find((t: ShiftTemplate) => t.id === selectedShiftTemplate);
                    if (template) {
                      setSelectedShiftDetail(template);
                      setShiftDetailModalVisible(true);
                    }
                  }}
                >
                  详情
                </Button>
              )}
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 冲突警告 */}
      {renderConflictWarnings()}

      {/* 甘特图容器 */}
      <div className="gantt-container" style={{ position: 'relative', height: '400px', overflow: 'auto' }}>
        {/* 班次背景 */}
        {renderShiftBackgrounds()}
        
        {/* 班次边界线 */}
        {renderShiftBoundaries()}
        
        {/* 这里应该集成实际的甘特图组件 */}
        <div className="gantt-placeholder">
          <Alert
            message="甘特图集成"
            description="这里将集成实际的甘特图组件，显示任务和班次信息"
            type="info"
            showIcon
          />
        </div>
      </div>

      {/* 班次详情模态框 */}
      <Modal
        title={
          <Space>
            <ClockCircleOutlined />
            <span>班次详情</span>
          </Space>
        }
        open={shiftDetailModalVisible}
        onCancel={() => setShiftDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setShiftDetailModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={600}
      >
        {selectedShiftDetail && (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="模板名称" span={2}>
              <Space>
                {selectedShiftDetail.template_name}
                <Tag color={getScheduleTypeColor(selectedShiftDetail.schedule_type)}>
                  {selectedShiftDetail.schedule_type}
                </Tag>
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="工作时间">
              {`${selectedShiftDetail.start_hour.toString().padStart(2, '0')}:${selectedShiftDetail.start_minute.toString().padStart(2, '0')} - ${selectedShiftDetail.end_hour.toString().padStart(2, '0')}:${selectedShiftDetail.end_minute.toString().padStart(2, '0')}`}
            </Descriptions.Item>
            <Descriptions.Item label="班次时长">
              {selectedShiftDetail.duration_hours} 小时
            </Descriptions.Item>
            <Descriptions.Item label="工作日" span={2}>
              {selectedShiftDetail.work_days.map(day => ['一', '二', '三', '四', '五', '六', '日'][day - 1]).join('、')}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default ShiftAwareGanttChart;
