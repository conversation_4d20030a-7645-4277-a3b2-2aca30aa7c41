import React from 'react';
import { Card, Space, Button, Typography, Statistic, Row, Col, Tooltip, Alert } from 'antd';
import { ClockCircleOutlined, BulbOutlined, CalculatorOutlined, WarningOutlined } from '@ant-design/icons';
import dayjs from '@/utils/dayjs';

const { Text, Title } = Typography;

interface TimeEstimateHelperProps {
  routingStep: {
    id: number;
    step_number: number;
    process_name: string;
    standard_hours?: number;
    work_instructions?: string;
  };
  onApplyTime: (startTime: dayjs.Dayjs, endTime: dayjs.Dayjs) => void;
  currentStartTime?: dayjs.Dayjs;
  currentEndTime?: dayjs.Dayjs;
  visible?: boolean;
}

const TimeEstimateHelper: React.FC<TimeEstimateHelperProps> = ({
  routingStep,
  onApplyTime,
  currentStartTime,
  currentEndTime,
  visible = true
}) => {
  if (!visible || !routingStep.standard_hours) {
    return null;
  }

  const standardHours = routingStep.standard_hours;

  // 计算当前时间偏差
  const calculateDeviation = () => {
    if (!currentStartTime || !currentEndTime) return null;
    
    const actualHours = currentEndTime.diff(currentStartTime, 'hour', true);
    const deviationPercent = ((actualHours - standardHours) / standardHours) * 100;
    
    return {
      actualHours,
      deviationPercent,
      isSignificant: Math.abs(deviationPercent) > 20
    };
  };

  const deviation = calculateDeviation();

  // 预设时间选项
  const timeOptions = [
    {
      label: '立即开始',
      startTime: dayjs().add(30, 'minute').startOf('hour'),
      description: '从下个整点开始'
    },
    {
      label: '2小时后',
      startTime: dayjs().add(2, 'hour').startOf('hour'),
      description: '预留准备时间'
    },
    {
      label: '明天早上',
      startTime: dayjs().add(1, 'day').hour(8).minute(0),
      description: '明天8:00开始'
    },
    {
      label: '下周一',
      startTime: dayjs().add(1, 'week').startOf('week').hour(8).minute(0),
      description: '下周一8:00开始'
    }
  ];

  const handleApplyOption = (startTime: dayjs.Dayjs) => {
    const endTime = startTime.add(standardHours, 'hour');
    onApplyTime(startTime, endTime);
  };

  return (
    <Card 
      size="small" 
      title={
        <Space>
          <CalculatorOutlined style={{ color: '#1890ff' }} />
          <span>时间估算助手</span>
        </Space>
      }
      style={{ marginBottom: 16, backgroundColor: '#f8f9fa' }}
    >
      {/* 工艺信息 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={12}>
          <Statistic
            title="工艺步骤"
            value={`步骤${routingStep.step_number}`}
            suffix={routingStep.process_name}
            valueStyle={{ fontSize: '14px' }}
          />
        </Col>
        <Col span={12}>
          <Statistic
            title="标准工时"
            value={standardHours}
            suffix="小时"
            prefix={<ClockCircleOutlined />}
            valueStyle={{ fontSize: '14px', color: '#1890ff' }}
          />
        </Col>
      </Row>

      {/* 当前时间偏差显示 */}
      {deviation && (
        <Alert
          message={
            <Space>
              <span>当前计划时间：{deviation.actualHours.toFixed(1)}小时</span>
              <span style={{ 
                color: deviation.isSignificant ? '#ff4d4f' : '#52c41a',
                fontWeight: 'bold'
              }}>
                ({deviation.deviationPercent > 0 ? '+' : ''}{deviation.deviationPercent.toFixed(0)}%)
              </span>
            </Space>
          }
          type={deviation.isSignificant ? 'warning' : 'info'}
          showIcon
          icon={deviation.isSignificant ? <WarningOutlined /> : <ClockCircleOutlined />}
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 快速时间选项 */}
      <div style={{ marginBottom: 12 }}>
        <Text strong style={{ fontSize: '12px', color: '#666' }}>
          <BulbOutlined /> 快速设置（基于标准工时 {standardHours}小时）
        </Text>
      </div>
      
      <Space wrap>
        {timeOptions.map((option, index) => (
          <Tooltip key={index} title={option.description}>
            <Button 
              size="small"
              onClick={() => handleApplyOption(option.startTime)}
              style={{ fontSize: '12px' }}
            >
              {option.label}
            </Button>
          </Tooltip>
        ))}
      </Space>

      {/* 工艺说明 */}
      {routingStep.work_instructions && (
        <div style={{ marginTop: 12, padding: 8, backgroundColor: '#fff', borderRadius: 4, border: '1px solid #e8e8e8' }}>
          <Text style={{ fontSize: '12px', color: '#666' }}>
            <strong>作业指导：</strong>{routingStep.work_instructions}
          </Text>
        </div>
      )}
    </Card>
  );
};

export default TimeEstimateHelper;
