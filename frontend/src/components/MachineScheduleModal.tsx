import React, { useState, useEffect } from 'react';
import {
  Modal,
  Table,
  Tag,
  Space,
  Typography,
  Card,
  Row,
  Col,
  Tabs,
  Button,
  Select,
  DatePicker,
  Spin,
  message,
} from 'antd';
import {
  ClockCircleOutlined,
  ToolOutlined,
  CalendarOutlined,
  Bar<PERSON>hartOutlined,
  TableOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import dayjs from '@/utils/dayjs';
import type { PlanTaskWithDetails, Machine, UpdatePlanTaskRequest } from '@/types/api';
import { apiClient } from '@/lib/api';
import './MachineScheduleModal.css';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

interface MachineScheduleModalProps {
  visible: boolean;
  machineId: number | null;
  machineName?: string;
  skillGroupId?: number | null;
  skillGroupName?: string;
  onCancel: () => void;
}

interface TimeSlot {
  hour: number;
  day: number;
  task?: PlanTaskWithDetails;
  available: boolean;
}

const MachineScheduleModal: React.FC<MachineScheduleModalProps> = ({
  visible,
  machineId,
  machineName,
  skillGroupId,
  skillGroupName,
  onCancel,
}) => {
  const [scheduleMode, setScheduleMode] = useState<'7x24' | '7x12'>('7x24');
  const [dateRangeMode, setDateRangeMode] = useState<'day' | 'week'>('day');
  const [selectedDate, setSelectedDate] = useState(dayjs());
  const [selectedDateRange, setSelectedDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().startOf('day'),
    dayjs().add(6, 'day').endOf('day')
  ]);
  const [draggedTask, setDraggedTask] = useState<PlanTaskWithDetails | null>(null);
  const [dropTarget, setDropTarget] = useState<{ hour: number; day: number } | null>(null);
  const [selectedTasks, setSelectedTasks] = useState<Set<number>>(new Set());
  const [isMultiSelectMode, setIsMultiSelectMode] = useState(false);
  const queryClient = useQueryClient();

  // 判断当前是设备模式还是技能组模式
  const isSkillGroupMode = !machineId && skillGroupId;
  const displayName = isSkillGroupMode ? skillGroupName : machineName;
  const modalTitle = isSkillGroupMode ? `技能组调度 - ${displayName}` : `设备调度 - ${displayName}`;

  // 获取设备信息
  const { data: machine, isLoading: machineLoading } = useQuery(
    ['machine', machineId],
    () => machineId ? apiClient.getMachineById(machineId) : null,
    {
      enabled: !!machineId && visible,
    }
  );

  // 获取任务数据（设备任务或技能组任务）
  const { data: tasks, isLoading: tasksLoading } = useQuery(
    [isSkillGroupMode ? 'skillGroupTasks' : 'machineTasks', machineId || skillGroupId,
     dateRangeMode === 'day' ? selectedDateRange.map(d => d.format('YYYY-MM-DD')).join('-') : selectedDate.format('YYYY-MM-DD')],
    () => {
      let startDate: string, endDate: string;

      if (dateRangeMode === 'day') {
        startDate = selectedDateRange[0].toISOString();
        endDate = selectedDateRange[1].toISOString();
      } else {
        startDate = selectedDate.startOf('week').toISOString();
        endDate = selectedDate.endOf('week').toISOString();
      }

      if (isSkillGroupMode && skillGroupId) {
        // 获取技能组的计划任务
        return apiClient.getPlanTasks({
          // skill_group_id: skillGroupId,
          start_date: startDate,
          end_date: endDate,
        });
      } else if (machineId) {
        // 获取设备任务
        return apiClient.getMachineTasksByDateRange(machineId, startDate, endDate);
      }
      return [];
    },
    {
      enabled: (!!machineId || !!skillGroupId) && visible,
    }
  );

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!visible) return;

      switch (event.key) {
        case 'Escape':
          if (isMultiSelectMode) {
            clearSelection();
            event.preventDefault();
          }
          break;
        case 'a':
        case 'A':
          if (event.ctrlKey || event.metaKey) {
            // Ctrl+A 全选
            if (tasks && tasks.length > 0) {
              const allTaskIds = tasks.map(task => task.id);
              setSelectedTasks(new Set(allTaskIds));
              setIsMultiSelectMode(true);
              event.preventDefault();
            }
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [visible, isMultiSelectMode, tasks]);

  // 更新任务时间的mutation
  const updateTaskMutation = useMutation(
    ({ taskId, updateData }: { taskId: number; updateData: UpdatePlanTaskRequest }) =>
      apiClient.updatePlanTask(taskId, updateData),
    {
      onSuccess: () => {
        message.success({ content: '任务时间更新成功', key: 'dragUpdate' });
        // 根据模式失效不同的查询
        if (isSkillGroupMode) {
          queryClient.invalidateQueries(['skillGroupTasks', skillGroupId]);
        } else {
          queryClient.invalidateQueries(['machineTasks', machineId]);
        }
        queryClient.invalidateQueries('plan-tasks');
      },
      onError: (error: any) => {
        message.error({
          content: `更新失败: ${error.response?.data?.message || error.message}`,
          key: 'dragUpdate'
        });
      },
    }
  );

  // 时间重叠检测函数
  const checkTimeOverlap = (
    newStartTime: dayjs.Dayjs,
    newEndTime: dayjs.Dayjs,
    excludeTaskId?: number
  ): { hasOverlap: boolean; conflictTasks: PlanTaskWithDetails[] } => {
    const conflictTasks: PlanTaskWithDetails[] = [];

    if (!tasks) return { hasOverlap: false, conflictTasks: [] };

    for (const task of tasks) {
      // 排除当前拖拽的任务和指定排除的任务
      if (task.id === excludeTaskId || task.id === draggedTask?.id) continue;

      // 在技能组模式下，只检查未分配设备的任务
      if (isSkillGroupMode && task.machine_id) continue;

      // 在设备模式下，只检查分配给当前设备的任务
      if (!isSkillGroupMode && task.machine_id !== machineId) continue;

      const taskStart = dayjs(task.planned_start);
      const taskEnd = dayjs(task.planned_end);

      // 检查时间重叠：新任务开始时间在现有任务时间范围内，或新任务结束时间在现有任务时间范围内，或新任务完全包含现有任务
      const hasOverlap = (
        (newStartTime.isBefore(taskEnd) && newStartTime.isAfter(taskStart)) ||
        (newEndTime.isBefore(taskEnd) && newEndTime.isAfter(taskStart)) ||
        (newStartTime.isBefore(taskStart) && newEndTime.isAfter(taskEnd)) ||
        (newStartTime.isSame(taskStart)) ||
        (newEndTime.isSame(taskEnd))
      );

      if (hasOverlap) {
        conflictTasks.push(task);
      }
    }

    return { hasOverlap: conflictTasks.length > 0, conflictTasks };
  };

  // 多选处理函数
  const handleTaskSelect = (taskId: number, event: React.MouseEvent) => {
    if (event.ctrlKey || event.metaKey) {
      // Ctrl/Cmd + 点击：切换选择状态
      const newSelected = new Set(selectedTasks);
      if (newSelected.has(taskId)) {
        newSelected.delete(taskId);
      } else {
        newSelected.add(taskId);
      }
      setSelectedTasks(newSelected);
      setIsMultiSelectMode(newSelected.size > 0);
    } else if (event.shiftKey && selectedTasks.size > 0) {
      // Shift + 点击：范围选择（暂时简化实现）
      const newSelected = new Set(selectedTasks);
      newSelected.add(taskId);
      setSelectedTasks(newSelected);
      setIsMultiSelectMode(true);
    } else {
      // 普通点击：清除其他选择，只选择当前任务
      setSelectedTasks(new Set([taskId]));
      setIsMultiSelectMode(true);
    }
  };

  // 清除选择
  const clearSelection = () => {
    setSelectedTasks(new Set());
    setIsMultiSelectMode(false);
  };

  // 批量移动选中的任务
  const handleBatchMove = (targetHour: number, targetDay: number) => {
    if (selectedTasks.size === 0) return;

    const selectedTaskList = tasks?.filter(task => selectedTasks.has(task.id)) || [];
    if (selectedTaskList.length === 0) return;

    // 计算时间偏移量（以第一个任务为基准）
    const firstTask = selectedTaskList[0];
    const originalStart = dayjs(firstTask.planned_start);

    let newStartTime: dayjs.Dayjs;
    if (dateRangeMode === 'day') {
      // 在日期范围模式下，使用选择的日期范围
      const startDate = selectedDateRange[0];
      newStartTime = startDate
        .add(targetDay, 'day')
        .hour(targetHour)
        .minute(0)
        .second(0);
    } else {
      // 在周模式下，使用当前周的开始
      newStartTime = selectedDate
        .startOf('week')
        .add(1, 'day') // 从周一开始
        .add(targetDay, 'day')
        .hour(targetHour)
        .minute(0)
        .second(0);
    }

    const timeOffset = newStartTime.diff(originalStart, 'minute');

    // 检查所有任务移动后是否有冲突
    const conflicts: { task: PlanTaskWithDetails; conflictTasks: PlanTaskWithDetails[] }[] = [];

    for (const task of selectedTaskList) {
      const taskStart = dayjs(task.planned_start);
      const taskEnd = dayjs(task.planned_end);
      const newTaskStart = taskStart.add(timeOffset, 'minute');
      const newTaskEnd = taskEnd.add(timeOffset, 'minute');

      const { hasOverlap, conflictTasks } = checkTimeOverlap(newTaskStart, newTaskEnd, task.id);
      if (hasOverlap) {
        conflicts.push({ task, conflictTasks });
      }
    }

    if (conflicts.length > 0) {
      const conflictMessages = conflicts.map(({ task, conflictTasks }) =>
        `任务 ${task.part_number} 与 ${conflictTasks.map(t => t.part_number).join(', ')} 时间冲突`
      ).join('\n');

      message.error({
        content: `批量移动失败，存在时间冲突：\n${conflictMessages}`,
        duration: 5,
      });
      return;
    }

    // 批量更新任务
    message.loading({ content: `正在批量移动 ${selectedTaskList.length} 个任务...`, key: 'batchMove' });

    Promise.all(
      selectedTaskList.map(task => {
        const taskStart = dayjs(task.planned_start);
        const taskEnd = dayjs(task.planned_end);
        const newTaskStart = taskStart.add(timeOffset, 'minute');
        const newTaskEnd = taskEnd.add(timeOffset, 'minute');

        const updateData: UpdatePlanTaskRequest = {
          planned_start: newTaskStart.toISOString(),
          planned_end: newTaskEnd.toISOString(),
        };

        return apiClient.updatePlanTask(task.id, updateData);
      })
    ).then(() => {
      message.success({ content: `成功移动 ${selectedTaskList.length} 个任务`, key: 'batchMove' });
      // 刷新数据
      if (isSkillGroupMode) {
        queryClient.invalidateQueries(['skillGroupTasks', skillGroupId]);
      } else {
        queryClient.invalidateQueries(['machineTasks', machineId]);
      }
      queryClient.invalidateQueries('plan-tasks');
      clearSelection();
    }).catch((error) => {
      message.error({
        content: `批量移动失败: ${error.response?.data?.message || error.message}`,
        key: 'batchMove'
      });
    });
  };

  // 拖拽处理函数
  const handleDragStart = (e: React.DragEvent, task: PlanTaskWithDetails) => {
    setDraggedTask(task);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', task.id.toString());

    // 添加拖拽图像
    const dragImage = e.currentTarget.cloneNode(true) as HTMLElement;
    dragImage.style.transform = 'rotate(5deg)';
    dragImage.style.opacity = '0.8';
    document.body.appendChild(dragImage);
    e.dataTransfer.setDragImage(dragImage, 50, 20);
    setTimeout(() => document.body.removeChild(dragImage), 0);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDragEnter = (e: React.DragEvent, hour: number, day: number) => {
    e.preventDefault();
    if (draggedTask) {
      setDropTarget({ hour, day });
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    // 只有当鼠标真正离开元素时才清除目标
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX;
    const y = e.clientY;

    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      setDropTarget(null);
    }
  };

  const handleDrop = (e: React.DragEvent, targetHour: number, targetDay: number) => {
    e.preventDefault();
    setDropTarget(null);

    // 检查是否是多选模式的批量移动
    if (isMultiSelectMode && selectedTasks.size > 1) {
      handleBatchMove(targetHour, targetDay);
      return;
    }

    if (!draggedTask) return;

    // 检查是否拖拽到相同位置
    const originalStart = dayjs(draggedTask.planned_start);
    const originalHour = originalStart.hour();
    const originalDay = originalStart.day() === 0 ? 6 : originalStart.day() - 1; // 转换为周一开始的索引

    if (originalHour === targetHour && originalDay === targetDay) {
      setDraggedTask(null);
      return;
    }

    // 计算新的开始时间
    let newStartTime: dayjs.Dayjs;

    if (dateRangeMode === 'day') {
      // 在日期范围模式下，使用选择的日期范围
      const startDate = selectedDateRange[0];
      newStartTime = startDate
        .add(targetDay, 'day')
        .hour(targetHour)
        .minute(0)
        .second(0);
    } else {
      // 在周模式下，使用当前周的开始
      newStartTime = selectedDate
        .startOf('week')
        .add(1, 'day') // 从周一开始
        .add(targetDay, 'day')
        .hour(targetHour)
        .minute(0)
        .second(0);
    }

    // 计算任务持续时间
    const originalEnd = dayjs(draggedTask.planned_end);
    const duration = originalEnd.diff(originalStart, 'minute');

    // 计算新的结束时间
    const newEndTime = newStartTime.add(duration, 'minute');

    // 检查时间重叠
    const { hasOverlap, conflictTasks } = checkTimeOverlap(newStartTime, newEndTime, draggedTask.id);

    if (hasOverlap) {
      const conflictNames = conflictTasks.map(task => task.part_number).join(', ');
      message.error({
        content: `时间冲突！任务 ${draggedTask.part_number} 与以下任务时间重叠：${conflictNames}`,
        duration: 5,
      });
      setDraggedTask(null);
      return;
    }

    // 显示确认信息
    const formatTime = (time: dayjs.Dayjs) => time.format('MM-DD HH:mm');
    message.loading({
      content: `正在将任务从 ${formatTime(originalStart)} 移动到 ${formatTime(newStartTime)}...`,
      key: 'dragUpdate',
    });

    // 更新任务时间
    const updateData: UpdatePlanTaskRequest = {
      planned_start: newStartTime.toISOString(),
      planned_end: newEndTime.toISOString(),
    };

    updateTaskMutation.mutate({
      taskId: draggedTask.id,
      updateData,
    });

    setDraggedTask(null);
  };

  const handleDragEnd = () => {
    setDraggedTask(null);
    setDropTarget(null);
  };

  // 任务颜色生成器
  const generateTaskColor = (task: PlanTaskWithDetails) => {
    // 基于工序名称生成颜色
    const processName = task.process_name?.toLowerCase() || '';

    // 工序类型映射
    const processColorMap: { [key: string]: string } = {
      // 铣削相关
      '铣': '#1890ff',
      'milling': '#1890ff',
      '铣削': '#1890ff',
      '精铣': '#0050b3',
      '粗铣': '#40a9ff',

      // 车削相关
      '车': '#52c41a',
      'turning': '#52c41a',
      '车削': '#52c41a',
      '精车': '#389e0d',
      '粗车': '#73d13d',

      // 钻孔相关
      '钻': '#fa8c16',
      'drilling': '#fa8c16',
      '钻孔': '#fa8c16',
      '扩孔': '#d46b08',
      '铰孔': '#ffa940',

      // 磨削相关
      '磨': '#eb2f96',
      'grinding': '#eb2f96',
      '磨削': '#eb2f96',
      '精磨': '#c41d7f',
      '粗磨': '#f759ab',

      // 装配相关
      '装配': '#722ed1',
      'assembly': '#722ed1',
      '组装': '#531dab',
      '安装': '#9254de',

      // 检验相关
      '检验': '#13c2c2',
      'inspection': '#13c2c2',
      '检测': '#08979c',
      '质检': '#36cfc9',

      // 其他工序
      '热处理': '#faad14',
      '表面处理': '#f5222d',
      '清洗': '#2f54eb',
      '包装': '#a0d911',
    };

    // 查找匹配的工序类型
    for (const [keyword, color] of Object.entries(processColorMap)) {
      if (processName.includes(keyword)) {
        return color;
      }
    }

    // 如果没有匹配的工序类型，基于任务ID生成颜色
    const fallbackColors = [
      '#1890ff', '#52c41a', '#fa8c16', '#eb2f96',
      '#722ed1', '#13c2c2', '#faad14', '#f5222d',
      '#2f54eb', '#a0d911', '#fa541c', '#c41d7f'
    ];

    return fallbackColors[task.id % fallbackColors.length];
  };

  // 计算任务在时间槽中的填充信息
  const calculateTaskFill = (task: PlanTaskWithDetails, slotDateTime: dayjs.Dayjs) => {
    const taskStart = dayjs(task.planned_start);
    const taskEnd = dayjs(task.planned_end);
    const slotStart = slotDateTime.startOf('hour');
    const slotEnd = slotDateTime.endOf('hour');

    // 计算任务在当前时间槽中的开始和结束位置（百分比）
    const taskStartInSlot = taskStart.isAfter(slotStart) ? taskStart : slotStart;
    const taskEndInSlot = taskEnd.isBefore(slotEnd) ? taskEnd : slotEnd;

    // 计算填充的开始位置和高度百分比
    const startPercent = (taskStartInSlot.diff(slotStart, 'minute') / 60) * 100;
    const endPercent = (taskEndInSlot.diff(slotStart, 'minute') / 60) * 100;
    const fillHeight = endPercent - startPercent;

    // 判断任务在这个时间槽中的位置
    const isTaskStart = taskStart.isSame(slotStart, 'hour') || taskStart.isAfter(slotStart);
    const isTaskEnd = taskEnd.isSame(slotEnd, 'hour') || taskEnd.isBefore(slotEnd);
    const isTaskMiddle = !isTaskStart && !isTaskEnd;

    return {
      startPercent: Math.max(0, Math.min(100, startPercent)),
      fillHeight: Math.max(0, Math.min(100, fillHeight)),
      isTaskStart,
      isTaskEnd,
      isTaskMiddle,
      taskDuration: taskEnd.diff(taskStart, 'hour', true), // 任务总持续时间（小时）
    };
  };

  // 生成时间表格数据
  const generateScheduleData = () => {
    let days: string[];
    let dateRange: dayjs.Dayjs[];

    if (dateRangeMode === 'day') {
      // 按天模式：生成选定日期范围内的天数
      const startDate = selectedDateRange[0];
      const endDate = selectedDateRange[1];
      const dayCount = endDate.diff(startDate, 'day') + 1;

      days = [];
      dateRange = [];
      for (let i = 0; i < dayCount; i++) {
        const currentDate = startDate.add(i, 'day');
        days.push(currentDate.format('MM-DD'));
        dateRange.push(currentDate);
      }
    } else {
      // 按周模式：使用固定的7天
      days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
      dateRange = [];
      for (let i = 0; i < 7; i++) {
        dateRange.push(selectedDate.startOf('week').add(i, 'day'));
      }
    }

    const hours = scheduleMode === '7x24' ? 24 : 12;
    const startHour = scheduleMode === '7x24' ? 0 : 8; // 7x12模式从8点开始

    const scheduleData = [];

    for (let hour = 0; hour < hours; hour++) {
      const actualHour = startHour + hour;
      const row: any = {
        key: `hour-${actualHour}`,
        time: `${actualHour.toString().padStart(2, '0')}:00`,
      };

      days.forEach((day, dayIndex) => {
        const currentDate = dateRange[dayIndex];
        const slotDateTime = currentDate.hour(actualHour);

        // 查找该时间段的任务
        let task;
        if (isSkillGroupMode) {
          // 技能组模式：查找未分配设备的任务
          task = tasks?.find(task => {
            const taskStart = dayjs(task.planned_start);
            const taskEnd = dayjs(task.planned_end);
            // 检查任务是否与当前时间槽有重叠
            return (
              taskStart.isBefore(slotDateTime.endOf('hour')) &&
              taskEnd.isAfter(slotDateTime.startOf('hour')) &&
              !task.machine_id
            );
          });
        } else {
          // 设备模式：查找分配给该设备的任务
          task = tasks?.find(task => {
            const taskStart = dayjs(task.planned_start);
            const taskEnd = dayjs(task.planned_end);
            // 检查任务是否与当前时间槽有重叠
            return (
              taskStart.isBefore(slotDateTime.endOf('hour')) &&
              taskEnd.isAfter(slotDateTime.startOf('hour'))
            );
          });
        }

        if (task) {
          const fillInfo = calculateTaskFill(task, slotDateTime);
          row[`day${dayIndex}`] = {
            task,
            status: task.status,
            available: false,
            fillInfo,
            taskColor: generateTaskColor(task),
          };
        } else {
          row[`day${dayIndex}`] = {
            task: null,
            status: 'available',
            available: true,
            fillInfo: null,
            taskColor: null,
          };
        }
      });

      scheduleData.push(row);
    }

    return scheduleData;
  };

  // 动态生成天数标题
  const getDayTitles = () => {
    if (dateRangeMode === 'day') {
      const startDate = selectedDateRange[0];
      const endDate = selectedDateRange[1];
      const dayCount = endDate.diff(startDate, 'day') + 1;

      const titles = [];
      for (let i = 0; i < dayCount; i++) {
        const currentDate = startDate.add(i, 'day');
        titles.push(currentDate.format('MM-DD'));
      }
      return titles;
    } else {
      return ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
      width: 80,
      fixed: 'left' as const,
      render: (time: string) => (
        <Text strong style={{ fontSize: '12px' }}>{time}</Text>
      ),
    },
    ...getDayTitles().map((day, index) => ({
      title: day,
      dataIndex: `day${index}`,
      key: `day${index}`,
      width: 120,
      render: (slot: any, record: any) => {
        const hour = parseInt(record.time.split(':')[0]);
        const isDropTarget = dropTarget?.hour === hour && dropTarget?.day === index;

        if (slot.task && slot.fillInfo) {
          const isDragging = draggedTask?.id === slot.task.id;
          const isSelected = selectedTasks.has(slot.task.id);
          const { fillHeight, startPercent, isTaskStart, isTaskEnd, isTaskMiddle, taskDuration } = slot.fillInfo;

          // 根据任务在时间槽中的位置决定显示内容
          const shouldShowContent = isTaskStart || (isTaskMiddle && fillHeight > 50);
          const shouldShowDuration = isTaskStart && taskDuration > 0;

          return (
            <div
              style={{
                position: 'relative',
                height: '100%',
                minHeight: '60px',
              }}
            >
              {/* 智能填充的任务条 */}
              <div
                draggable
                onDragStart={(e) => handleDragStart(e, slot.task)}
                onDragEnd={handleDragEnd}
                onClick={(e) => handleTaskSelect(slot.task.id, e)}
                className={`task-card task-card-draggable smart-fill-task ${isDragging ? 'schedule-task-dragging' : ''} ${isSelected ? 'task-selected' : ''}`}
                style={{
                  position: 'absolute',
                  top: `${startPercent}%`,
                  left: '2px',
                  right: '2px',
                  height: `${fillHeight}%`,
                  minHeight: fillHeight < 20 ? '20px' : 'auto',
                  backgroundColor: slot.taskColor,
                  color: 'white',
                  border: isDragging ? '2px solid #1890ff' : isSelected ? '2px solid #52c41a' : '1px solid rgba(255,255,255,0.3)',
                  borderRadius: '4px',
                  opacity: isDragging ? 0.7 : 0.9,
                  boxShadow: isSelected ? '0 0 8px rgba(82, 196, 26, 0.6)' : '0 2px 4px rgba(0,0,0,0.1)',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: shouldShowContent ? 'flex-start' : 'center',
                  alignItems: 'center',
                  padding: '2px 4px',
                  fontSize: '11px',
                  overflow: 'hidden',
                  cursor: 'move',
                  transition: 'all 0.2s ease',
                }}
                title={`${slot.task.project_name} - ${slot.task.process_name}\n持续时间: ${taskDuration.toFixed(1)}小时\n拖拽可调整时间\nCtrl+点击多选\n当前时间: ${dayjs(slot.task.planned_start).format('MM-DD HH:mm')} - ${dayjs(slot.task.planned_end).format('MM-DD HH:mm')}`}
              >
                {shouldShowContent && (
                  <>
                    <div style={{
                      fontWeight: 'bold',
                      fontSize: '10px',
                      textAlign: 'center',
                      lineHeight: '1.2',
                      marginBottom: '1px',
                      textOverflow: 'ellipsis',
                      overflow: 'hidden',
                      whiteSpace: 'nowrap',
                      width: '100%',
                    }}>
                      {slot.task.part_number}
                    </div>
                    <div style={{
                      fontSize: '9px',
                      textAlign: 'center',
                      lineHeight: '1.1',
                      textOverflow: 'ellipsis',
                      overflow: 'hidden',
                      whiteSpace: 'nowrap',
                      width: '100%',
                      opacity: 0.9,
                    }}>
                      {slot.task.process_name}
                    </div>
                    {shouldShowDuration && (
                      <div style={{
                        fontSize: '8px',
                        textAlign: 'center',
                        marginTop: '1px',
                        opacity: 0.8,
                        fontWeight: 'normal',
                      }}>
                        {taskDuration.toFixed(1)}h
                      </div>
                    )}
                  </>
                )}

                {!shouldShowContent && (
                  <div style={{
                    fontSize: '8px',
                    textAlign: 'center',
                    opacity: 0.8,
                  }}>
                    ●
                  </div>
                )}

                {/* 拖拽指示器 */}
                <div className="drag-indicator" style={{
                  position: 'absolute',
                  bottom: '1px',
                  right: '2px',
                  width: '8px',
                  height: '8px',
                  opacity: 0.6,
                }} />

                {/* 选择指示器 */}
                {isSelected && (
                  <div
                    style={{
                      position: 'absolute',
                      top: '1px',
                      left: '1px',
                      width: '10px',
                      height: '10px',
                      backgroundColor: '#52c41a',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '6px',
                      color: 'white',
                      fontWeight: 'bold',
                      zIndex: 10,
                    }}
                  >
                    ✓
                  </div>
                )}
              </div>
            </div>
          );
        } else {
          return (
            <div
              onDragOver={handleDragOver}
              onDragEnter={(e) => handleDragEnter(e, hour, index)}
              onDragLeave={handleDragLeave}
              onDrop={(e) => handleDrop(e, hour, index)}
              onClick={(e) => {
                // 点击空闲区域时，如果是多选模式，可以批量移动
                if (isMultiSelectMode && selectedTasks.size > 0 && (e.ctrlKey || e.metaKey)) {
                  handleBatchMove(hour, index);
                }
              }}
              className={`empty-slot ${isDropTarget ? 'drop-zone-highlight' : ''}`}
              style={{
                backgroundColor: isDropTarget ? '#e6f7ff' : '#f0f0f0',
                borderColor: isDropTarget ? '#1890ff' : '#d9d9d9',
                borderStyle: isDropTarget ? 'solid' : 'dashed',
                color: isDropTarget ? '#1890ff' : '#999',
                cursor: isMultiSelectMode && selectedTasks.size > 0 ? 'pointer' : 'default',
              }}
              title={
                isMultiSelectMode && selectedTasks.size > 0
                  ? `Ctrl+点击可将 ${selectedTasks.size} 个选中任务移动到这里`
                  : isDropTarget
                    ? '放置到这里'
                    : '空闲时间槽'
              }
            >
              {isDropTarget
                ? (isMultiSelectMode && selectedTasks.size > 1 ? `批量放置(${selectedTasks.size})` : '放置到这里')
                : '空闲'
              }
            </div>
          );
        }
      },
    })),
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planned': return '#d9d9d9';
      case 'scheduled': return '#faad14';
      case 'in_progress': return '#1890ff';
      case 'completed': return '#52c41a';
      case 'cancelled': return '#f5222d';
      case 'on_hold': return '#722ed1';
      default: return '#d9d9d9';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'planned': return '已计划';
      case 'scheduled': return '已调度';
      case 'in_progress': return '进行中';
      case 'completed': return '已完成';
      case 'cancelled': return '已取消';
      case 'on_hold': return '暂停';
      default: return status;
    }
  };

  // 任务列表列定义
  const taskColumns = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '项目',
      dataIndex: 'project_name',
      key: 'project_name',
      width: 120,
    },
    {
      title: '零件编号',
      dataIndex: 'part_number',
      key: 'part_number',
      width: 120,
    },
    {
      title: '工序',
      dataIndex: 'process_name',
      key: 'process_name',
      width: 120,
    },
    {
      title: '计划开始',
      dataIndex: 'planned_start',
      key: 'planned_start',
      width: 140,
      render: (date: string) => dayjs(date).format('MM-DD HH:mm'),
    },
    {
      title: '计划结束',
      dataIndex: 'planned_end',
      key: 'planned_end',
      width: 140,
      render: (date: string) => dayjs(date).format('MM-DD HH:mm'),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    // 在技能组模式下显示设备分配信息
    ...(isSkillGroupMode ? [{
      title: '分配设备',
      dataIndex: 'machine_id',
      key: 'machine_id',
      width: 100,
      render: (machineId: number | null, record: PlanTaskWithDetails) => (
        machineId ? (
          <Tag color="blue">{record.machine_name || `设备${machineId}`}</Tag>
        ) : (
          <Tag color="orange">未分配</Tag>
        )
      ),
    }] : []),
  ];

  return (
    <Modal
      title={
        <Space>
          {isSkillGroupMode ? <CalendarOutlined /> : <ToolOutlined />}
          <span>{isSkillGroupMode ? '技能组任务调度' : '设备任务调度'}</span>
          {displayName && <Tag color={isSkillGroupMode ? "green" : "blue"}>{displayName}</Tag>}
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      width="95vw"
      style={{ top: 10 }}
      styles={{
        body: {
          height: 'calc(95vh - 110px)',
          overflow: 'auto',
          padding: '16px',
          backgroundColor: '#f5f5f5'
        }
      }}
      footer={
        <Button onClick={onCancel}>
          关闭
        </Button>
      }
    >
      {machineLoading || tasksLoading ? (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      ) : (
        <div>
          {/* 控制面板 */}
          <Card size="small" style={{ marginBottom: '16px' }}>
            <Row gutter={16} align="middle" style={{ marginBottom: '12px' }}>
              <Col>
                <Space>
                  <Text strong>调度模式:</Text>
                  <Select
                    value={scheduleMode}
                    onChange={setScheduleMode}
                    style={{ width: 120 }}
                  >
                    <Option value="7x24">7×24小时</Option>
                    <Option value="7x12">7×12小时</Option>
                  </Select>
                </Space>
              </Col>
              <Col>
                <Space>
                  <Text strong>时间模式:</Text>
                  <Select
                    value={dateRangeMode}
                    onChange={(mode: 'day' | 'week') => {
                      setDateRangeMode(mode);
                      if (mode === 'day') {
                        // 切换到按天模式时，设置默认7天范围
                        const start = dayjs().startOf('day');
                        const end = start.add(6, 'day').endOf('day');
                        setSelectedDateRange([start, end]);
                      }
                    }}
                    style={{ width: 100 }}
                  >
                    <Option value="day">按天</Option>
                    <Option value="week">按周</Option>
                  </Select>
                </Space>
              </Col>
            </Row>

            <Row gutter={16} align="middle">
              {dateRangeMode === 'day' ? (
                <Col>
                  <Space>
                    <Text strong>选择日期范围:</Text>
                    <DatePicker.RangePicker
                      value={selectedDateRange}
                      onChange={(dates) => {
                        if (dates && dates[0] && dates[1]) {
                          setSelectedDateRange([dates[0], dates[1]]);
                        }
                      }}
                      format="YYYY-MM-DD"
                      style={{ width: 240 }}
                    />
                  </Space>
                </Col>
              ) : (
                <Col>
                  <Space>
                    <Text strong>选择周:</Text>
                    <DatePicker
                      value={selectedDate}
                      onChange={(date) => date && setSelectedDate(date)}
                      picker="week"
                      format="YYYY年第WW周"
                    />
                  </Space>
                </Col>
              )}
              <Col>
                <Text type="secondary">
                  {dateRangeMode === 'day'
                    ? `${selectedDateRange[0].format('YYYY-MM-DD')} 至 ${selectedDateRange[1].format('YYYY-MM-DD')}`
                    : `${selectedDate.startOf('week').format('YYYY-MM-DD')} 至 ${selectedDate.endOf('week').format('YYYY-MM-DD')}`
                  }
                </Text>
              </Col>
              {/* 多选控制 */}
              {isMultiSelectMode && (
                <Col>
                  <Space>
                    <Text strong style={{ color: '#52c41a' }}>
                      已选择 {selectedTasks.size} 个任务
                    </Text>
                    <Button
                      size="small"
                      onClick={clearSelection}
                      type="default"
                    >
                      清除选择
                    </Button>
                  </Space>
                </Col>
              )}
              {/* 颜色图例 */}
              <Col>
                <Space size="small">
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    颜色图例:
                  </Text>
                  <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                    {[
                      { color: '#1890ff', label: '铣削' },
                      { color: '#52c41a', label: '车削' },
                      { color: '#fa8c16', label: '钻孔' },
                      { color: '#eb2f96', label: '磨削' },
                      { color: '#722ed1', label: '装配' },
                      { color: '#13c2c2', label: '检验' },
                    ].map(({ color, label }) => (
                      <div key={label} style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
                        <div
                          style={{
                            width: '8px',
                            height: '8px',
                            backgroundColor: color,
                            borderRadius: '2px',
                          }}
                        />
                        <Text style={{ fontSize: '10px', color: '#666' }}>{label}</Text>
                      </div>
                    ))}
                  </div>
                </Space>
              </Col>
            </Row>
            {/* 任务统计信息 */}
            <Row style={{ marginTop: '8px' }}>
              <Col span={12}>
                {!isMultiSelectMode && (
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    💡 提示：Ctrl+点击可多选任务，拖拽到空闲区域可批量移动
                  </Text>
                )}
              </Col>
              <Col span={12} style={{ textAlign: 'right' }}>
                <Space size="large">
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    总任务: <Text strong>{tasks?.length || 0}</Text>
                  </Text>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    本周工时: <Text strong>
                      {tasks?.reduce((total, task) => {
                        const start = dayjs(task.planned_start);
                        const end = dayjs(task.planned_end);
                        const weekStart = selectedDate.startOf('week');
                        const weekEnd = selectedDate.endOf('week');

                        // 只计算本周内的工时
                        if (start.isAfter(weekEnd) || end.isBefore(weekStart)) return total;

                        const taskStart = start.isBefore(weekStart) ? weekStart : start;
                        const taskEnd = end.isAfter(weekEnd) ? weekEnd : end;

                        return total + taskEnd.diff(taskStart, 'hour', true);
                      }, 0)?.toFixed(1) || '0'}h
                    </Text>
                  </Text>
                  {isSkillGroupMode && (
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      未分配: <Text strong style={{ color: '#fa8c16' }}>
                        {tasks?.filter(task => !task.machine_id).length || 0}
                      </Text>
                    </Text>
                  )}
                </Space>
              </Col>
            </Row>
          </Card>

          <Tabs defaultActiveKey="1" type="card">
            <TabPane
              tab={
                <Space>
                  <BarChartOutlined />
                  时间表视图
                </Space>
              }
              key="1"
            >
              <Card size="small">
                <Table
                  columns={columns}
                  dataSource={generateScheduleData()}
                  pagination={false}
                  scroll={{ x: 1000, y: 500 }}
                  size="small"
                  bordered
                  className="schedule-table"
                />
              </Card>
            </TabPane>
            
            <TabPane
              tab={
                <Space>
                  <TableOutlined />
                  任务列表
                </Space>
              }
              key="2"
            >
              <Card size="small">
                <Table
                  columns={taskColumns}
                  dataSource={tasks || []}
                  pagination={{ pageSize: 20 }}
                  size="small"
                  bordered
                />
              </Card>
            </TabPane>
          </Tabs>
        </div>
      )}
    </Modal>
  );
};

export default MachineScheduleModal;
