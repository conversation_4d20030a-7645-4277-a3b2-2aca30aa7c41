import React, { useMemo, useState, useCallback, useRef, useEffect } from 'react';
import { Card, Typography, Row, Col, Tag, Tooltip, message, Space, Button, Select, DatePicker } from 'antd';
import { format, parseISO, differenceInDays, addDays, startOfDay, startOfWeek, endOfWeek, addWeeks, subWeeks, getWeek, getYear } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import dayjs from '@/utils/dayjs';

import type { PlanTaskWithDetails } from '@/types/api';
import { apiClient } from '@/lib/api';
import { useTouchGestures } from '@/hooks/useTouchGestures';
import TaskDetailModal from './TaskDetailModal';
import MachineScheduleModal from './MachineScheduleModal';
import './GanttChart.css';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

interface GanttChartProps {
  tasks: PlanTaskWithDetails[];
  title?: string;
  height?: number;
  editable?: boolean;
  onTaskUpdate?: (taskId: number, newStart: Date, newEnd: Date) => void;
}



interface GanttTask {
  id: number;
  name: string;
  startDate: Date;
  endDate: Date;
  status: string;
  progress: number;
  dependencies?: number[];
  assignee?: string;
}

interface DragState {
  isDragging: boolean;
  dragType: 'move' | 'resize-start' | 'resize-end' | null;
  taskId: number | null;
  startX: number;
  startDate: Date | null;
  endDate: Date | null;
  originalStartDate: Date | null;
  originalEndDate: Date | null;
}

const GanttChart: React.FC<GanttChartProps> = ({
  tasks,
  title = "生产计划甘特图",
  height = 400,
  editable = false,
  onTaskUpdate
}) => {
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    dragType: null,
    taskId: null,
    startX: 0,
    startDate: null,
    endDate: null,
    originalStartDate: null,
    originalEndDate: null,
  });

  const [isMobile, setIsMobile] = useState(false);
  const [selectedTaskId, setSelectedTaskId] = useState<number | null>(null);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [selectedMachineId, setSelectedMachineId] = useState<number | null>(null);
  const [selectedMachineName, setSelectedMachineName] = useState<string>('');
  const [selectedSkillGroupId, setSelectedSkillGroupId] = useState<number | null>(null);
  const [selectedSkillGroupName, setSelectedSkillGroupName] = useState<string>('');
  const [machineScheduleVisible, setMachineScheduleVisible] = useState(false);

  // 日期选择相关状态
  const [timeMode, setTimeMode] = useState<'week' | 'custom'>('week');
  const [customDateRange, setCustomDateRange] = useState<[Date, Date] | null>(null);
  const [currentWeekOffset, setCurrentWeekOffset] = useState(0); // 相对于当前周的偏移

  const ganttRef = useRef<HTMLDivElement>(null);

  // 检测移动设备
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);



  // 处理任务更新
  const handleTaskUpdate = useCallback(async (taskId: number, newStart: Date, newEnd: Date) => {
    if (!editable) return;

    try {
      if (onTaskUpdate) {
        onTaskUpdate(taskId, newStart, newEnd);
      } else {
        // 默认使用API更新
        await apiClient.updatePlanTask(taskId, {
          planned_start: newStart.toISOString(),
          planned_end: newEnd.toISOString(),
        });
        message.success('任务时间更新成功');
      }
    } catch (error) {
      console.error('更新任务失败:', error);
      message.error('更新任务失败');
    }
  }, [editable, onTaskUpdate]);

  const ganttTasks: GanttTask[] = useMemo(() => {
    console.log('🔍 甘特图数据转换 - 原始任务数量:', tasks?.length || 0);
    console.log('🔍 甘特图数据转换 - 原始任务数据:', tasks);

    if (!tasks || tasks.length === 0) {
      console.log('⚠️ 甘特图数据转换 - 没有任务数据');
      return [];
    }

    const processedTasks = tasks.map((task, index) => {
      console.log(`🔍 处理任务 ${index + 1}:`, task);

      try {
        const ganttTask = {
          id: task.id,
          name: `${task.process_name} - ${task.part_number}`,
          startDate: parseISO(task.planned_start),
          endDate: parseISO(task.planned_end),
          status: task.status,
          progress: 0, // 可以根据状态计算进度
          assignee: task.skill_group_name,
        };

        console.log(`✅ 任务 ${index + 1} 转换成功:`, ganttTask);
        console.log(`📅 开始时间有效: ${!isNaN(ganttTask.startDate.getTime())}`);
        console.log(`📅 结束时间有效: ${!isNaN(ganttTask.endDate.getTime())}`);

        return ganttTask;
      } catch (error) {
        console.error(`❌ 任务 ${index + 1} 转换失败:`, error);
        return null;
      }
    }).filter(task => task !== null);

    console.log('✅ 甘特图数据转换完成 - 有效任务数量:', processedTasks.length);
    return processedTasks;
  }, [tasks]);

  // 从任务数据中提取技能组信息（只显示技能组，不显示具体设备）
  const skillGroupsData = useMemo(() => {
    const skillGroupMap = new Map<number, {
      id: number;
      name: string;
      tasks: typeof ganttTasks;
      machines: Array<{id: number; name: string; tasks: typeof ganttTasks}>;
    }>();

    ganttTasks.forEach(task => {
      const originalTask = tasks?.find(t => t.id === task.id);
      if (!originalTask) return;

      const skillGroupId = originalTask.skill_group_id;
      const skillGroupName = originalTask.skill_group_name || `技能组${skillGroupId}`;

      // 确保技能组存在
      if (!skillGroupMap.has(skillGroupId)) {
        skillGroupMap.set(skillGroupId, {
          id: skillGroupId,
          name: skillGroupName,
          tasks: [],
          machines: []
        });
      }

      const skillGroup = skillGroupMap.get(skillGroupId)!;
      skillGroup.tasks.push(task);

      // 如果有设备信息，添加到技能组的设备列表中
      if (originalTask.machine_id) {
        const machineExists = skillGroup.machines.find(m => m.id === originalTask.machine_id);
        if (!machineExists) {
          skillGroup.machines.push({
            id: originalTask.machine_id,
            name: originalTask.machine_name || `设备${originalTask.machine_id}`,
            tasks: []
          });
        }
        // 将任务添加到对应设备
        const machine = skillGroup.machines.find(m => m.id === originalTask.machine_id)!;
        machine.tasks.push(task);
      }
    });

    return Array.from(skillGroupMap.values()).sort((a, b) => a.name.localeCompare(b.name));
  }, [ganttTasks, tasks]);

  // 选中的技能组
  const [selectedSkillGroups, setSelectedSkillGroups] = useState<Set<number>>(new Set());



  // 过滤后的技能组和任务
  const filteredSkillGroups = useMemo(() => {
    if (selectedSkillGroups.size === 0) {
      return skillGroupsData; // 没有选择任何过滤条件，显示所有技能组
    }

    return skillGroupsData.filter(skillGroup =>
      selectedSkillGroups.has(skillGroup.id)
    );
  }, [skillGroupsData, selectedSkillGroups]);

  const filteredTasks = useMemo(() => {
    if (selectedSkillGroups.size === 0) {
      return ganttTasks; // 没有选择任何过滤条件，显示所有任务
    }

    return filteredSkillGroups.flatMap(skillGroup => skillGroup.tasks);
  }, [filteredSkillGroups, ganttTasks, selectedSkillGroups]);



  // 全选/取消全选技能组
  const handleSelectAllSkillGroups = useCallback((checked: boolean) => {
    if (checked) {
      const allSkillGroupIds = skillGroupsData.map(skillGroup => skillGroup.id);
      setSelectedSkillGroups(new Set(allSkillGroupIds));
    } else {
      setSelectedSkillGroups(new Set());
    }
  }, [skillGroupsData]);

  // 切换单个技能组的选择状态
  const handleSkillGroupToggle = useCallback((skillGroupId: number) => {
    setSelectedSkillGroups(prev => {
      const newSet = new Set(prev);
      if (newSet.has(skillGroupId)) {
        newSet.delete(skillGroupId);
      } else {
        newSet.add(skillGroupId);
      }
      return newSet;
    });
  }, []);



  // 获取当前显示的日期范围
  const getDisplayDateRange = useCallback((): [Date, Date] => {
    if (timeMode === 'custom' && customDateRange) {
      return customDateRange;
    }

    // 周模式：当前周 + 偏移
    const now = new Date();
    const baseWeekStart = startOfWeek(now, { weekStartsOn: 1 });
    const targetWeekStart = addWeeks(baseWeekStart, currentWeekOffset);
    const targetWeekEnd = endOfWeek(targetWeekStart, { weekStartsOn: 1 });

    return [targetWeekStart, targetWeekEnd];
  }, [timeMode, customDateRange, currentWeekOffset]);

  // 触摸手势处理
  const { bindGestures } = useTouchGestures({
    onPanStart: (point) => {
      if (!editable || !isMobile) return;

      // 查找触摸点下的任务
      const element = document.elementFromPoint(point.x, point.y);
      const taskElement = element?.closest('[data-task-id]') as HTMLElement;

      if (taskElement) {
        const taskId = parseInt(taskElement.dataset.taskId || '0');
        const task = ganttTasks.find(t => t.id === taskId);

        if (task) {
          setDragState({
            isDragging: true,
            dragType: 'move',
            taskId,
            startX: point.x,
            startDate: task.startDate,
            endDate: task.endDate,
            originalStartDate: task.startDate,
            originalEndDate: task.endDate,
          });
        }
      }
    },

    onPan: (delta) => {
      if (!dragState.isDragging || !dragState.taskId || !dragState.originalStartDate || !dragState.originalEndDate) return;

      const dayWidth = 40;
      const daysDelta = Math.round(delta.x / dayWidth);

      let newStartDate = dragState.originalStartDate;
      let newEndDate = dragState.originalEndDate;

      if (dragState.dragType === 'move') {
        newStartDate = addDays(dragState.originalStartDate, daysDelta);
        newEndDate = addDays(dragState.originalEndDate, daysDelta);
      }

      setDragState(prev => ({
        ...prev,
        startDate: newStartDate,
        endDate: newEndDate,
      }));
    },

    onPanEnd: () => {
      if (!dragState.isDragging || !dragState.taskId || !dragState.startDate || !dragState.endDate) {
        setDragState({
          isDragging: false,
          dragType: null,
          taskId: null,
          startX: 0,
          startDate: null,
          endDate: null,
          originalStartDate: null,
          originalEndDate: null,
        });
        return;
      }

      const hasChanged =
        dragState.startDate.getTime() !== dragState.originalStartDate?.getTime() ||
        dragState.endDate.getTime() !== dragState.originalEndDate?.getTime();

      if (hasChanged) {
        handleTaskUpdate(dragState.taskId, dragState.startDate, dragState.endDate);
      }

      setDragState({
        isDragging: false,
        dragType: null,
        taskId: null,
        startX: 0,
        startDate: null,
        endDate: null,
        originalStartDate: null,
        originalEndDate: null,
      });
    },

    onTap: (point) => {
      // 处理任务点击
      const element = document.elementFromPoint(point.x, point.y);
      const taskElement = element?.closest('[data-task-id]') as HTMLElement;

      if (taskElement) {
        const taskId = parseInt(taskElement.dataset.taskId || '0');
        const task = ganttTasks.find(t => t.id === taskId);

        if (task) {
          setSelectedTaskId(taskId);
          setIsDetailModalVisible(true);
        }
      }
    },

    preventDefaultTouch: true,
  });

  // 绑定触摸手势到甘特图容器
  useEffect(() => {
    if (ganttRef.current && isMobile) {
      return bindGestures(ganttRef.current);
    }
  }, [bindGestures, isMobile]);

  // 处理任务点击事件
  const handleTaskClick = useCallback((e: React.MouseEvent, taskId: number) => {
    // 如果正在拖拽，不处理点击
    if (dragState.isDragging) return;

    e.preventDefault();
    e.stopPropagation();

    setSelectedTaskId(taskId);
    setIsDetailModalVisible(true);
  }, [dragState.isDragging]);

  // 鼠标事件处理（桌面端）
  const handleMouseDown = useCallback((e: React.MouseEvent, taskId: number, dragType: 'move' | 'resize-start' | 'resize-end') => {
    if (!editable) return;

    e.preventDefault();
    e.stopPropagation();

    const task = ganttTasks.find(t => t.id === taskId);
    if (!task) return;

    setDragState({
      isDragging: true,
      dragType,
      taskId,
      startX: e.clientX,
      startDate: task.startDate,
      endDate: task.endDate,
      originalStartDate: task.startDate,
      originalEndDate: task.endDate,
    });
  }, [editable, ganttTasks]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!dragState.isDragging || !dragState.taskId || !dragState.originalStartDate || !dragState.originalEndDate) return;

    const deltaX = e.clientX - dragState.startX;
    const dayWidth = 40;
    const daysDelta = Math.round(deltaX / dayWidth);

    let newStartDate = dragState.originalStartDate;
    let newEndDate = dragState.originalEndDate;

    switch (dragState.dragType) {
      case 'move':
        newStartDate = addDays(dragState.originalStartDate, daysDelta);
        newEndDate = addDays(dragState.originalEndDate, daysDelta);
        break;
      case 'resize-start':
        newStartDate = addDays(dragState.originalStartDate, daysDelta);
        // 确保开始时间不晚于结束时间
        if (newStartDate >= dragState.originalEndDate) {
          newStartDate = addDays(dragState.originalEndDate, -1);
        }
        break;
      case 'resize-end':
        newEndDate = addDays(dragState.originalEndDate, daysDelta);
        // 确保结束时间不早于开始时间
        if (newEndDate <= dragState.originalStartDate) {
          newEndDate = addDays(dragState.originalStartDate, 1);
        }
        break;
    }

    setDragState(prev => ({
      ...prev,
      startDate: newStartDate,
      endDate: newEndDate,
    }));
  }, [dragState]);

  const handleMouseUp = useCallback(() => {
    if (!dragState.isDragging || !dragState.taskId || !dragState.startDate || !dragState.endDate) {
      setDragState({
        isDragging: false,
        dragType: null,
        taskId: null,
        startX: 0,
        startDate: null,
        endDate: null,
        originalStartDate: null,
        originalEndDate: null,
      });
      return;
    }

    // 检查是否有实际变化
    const hasChanged =
      dragState.startDate.getTime() !== dragState.originalStartDate?.getTime() ||
      dragState.endDate.getTime() !== dragState.originalEndDate?.getTime();

    if (hasChanged) {
      handleTaskUpdate(dragState.taskId, dragState.startDate, dragState.endDate);
    }

    setDragState({
      isDragging: false,
      dragType: null,
      taskId: null,
      startX: 0,
      startDate: null,
      endDate: null,
      originalStartDate: null,
      originalEndDate: null,
    });
  }, [dragState, handleTaskUpdate]);

  // 添加全局鼠标事件监听
  React.useEffect(() => {
    if (dragState.isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = dragState.dragType === 'move' ? 'grabbing' : 'col-resize';
      document.body.style.userSelect = 'none';

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }
  }, [dragState.isDragging, handleMouseMove, handleMouseUp, dragState.dragType]);



  const { startDate, endDate, totalDays } = useMemo(() => {
    // 获取用户选择的日期范围
    const [displayStart, displayEnd] = getDisplayDateRange();

    if (filteredTasks.length === 0) {
      return {
        startDate: startOfDay(displayStart),
        endDate: startOfDay(displayEnd),
        totalDays: differenceInDays(displayEnd, displayStart) + 1,
      };
    }

    // 获取任务的实际日期范围
    const taskDates = filteredTasks.flatMap(task => [task.startDate, task.endDate]);
    const taskMinDate = startOfDay(new Date(Math.min(...taskDates.map(d => d.getTime()))));
    const taskMaxDate = startOfDay(new Date(Math.max(...taskDates.map(d => d.getTime()))));

    // 使用显示范围和任务范围的并集，确保用户选择的范围始终可见
    const finalStartDate = new Date(Math.min(displayStart.getTime(), taskMinDate.getTime()));
    const finalEndDate = new Date(Math.max(displayEnd.getTime(), taskMaxDate.getTime()));

    return {
      startDate: startOfDay(finalStartDate),
      endDate: startOfDay(finalEndDate),
      totalDays: differenceInDays(finalEndDate, finalStartDate) + 1,
    };
  }, [filteredTasks, getDisplayDateRange]);



  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'planned':
        return '#d9d9d9';
      case 'scheduled':
        return '#faad14';
      case 'in_progress':
        return '#1890ff';
      case 'completed':
        return '#52c41a';
      case 'cancelled':
        return '#ff4d4f';
      case 'on_hold':
        return '#722ed1';
      default:
        return '#d9d9d9';
    }
  };

  const getStatusText = (status: string) => {
    switch (status.toLowerCase()) {
      case 'planned':
        return '已计划';
      case 'scheduled':
        return '已调度';
      case 'in_progress':
        return '进行中';
      case 'completed':
        return '已完成';
      case 'cancelled':
        return '已取消';
      case 'on_hold':
        return '暂停';
      default:
        return '未知';
    }
  };

  const generateDateHeaders = () => {
    const headers = [];
    for (let i = 0; i < totalDays; i++) {
      const date = addDays(startDate, i);
      headers.push(
        <div
          key={i}
          className="gantt-date-header"
          style={{
            width: '40px',
            minWidth: '40px',
            padding: '8px 4px',
            borderRight: '1px solid #f0f0f0',
            textAlign: 'center',
            fontSize: '12px',
            backgroundColor: '#fafafa',
          }}
        >
          <div>{format(date, 'MM/dd', { locale: zhCN })}</div>
          <div style={{ fontSize: '10px', color: '#999' }}>
            {format(date, 'EEE', { locale: zhCN })}
          </div>
        </div>
      );
    }
    return headers;
  };

  // 生成资源行（技能组和设备）
  const generateResourceRows = () => {
    const rows: React.ReactNode[] = [];

    filteredSkillGroups.forEach(skillGroup => {
      // 技能组行
      rows.push(generateSkillGroupRow(skillGroup));

      // 该技能组下的设备行
      skillGroup.machines.forEach(machine => {
        if (machine.tasks.length > 0) {
          rows.push(generateMachineRow(machine, skillGroup));
        }
      });
    });

    return rows;
  };

  // 生成技能组行
  const generateSkillGroupRow = (skillGroup: {id: number; name: string; tasks: typeof ganttTasks}) => {
    return (
      <div
        key={`skillgroup-${skillGroup.id}`}
        style={{
          display: 'flex',
          borderBottom: '1px solid #f0f0f0',
          backgroundColor: '#fafafa',
          minHeight: isMobile ? '40px' : '32px',
        }}
      >
        <div
          style={{
            width: '200px',
            minWidth: '200px',
            padding: '8px 12px',
            borderRight: '1px solid #f0f0f0',
            display: 'flex',
            alignItems: 'center',
            cursor: 'pointer',
            fontWeight: 600,
          }}
          onClick={() => {
            // 点击跳转到技能组任务调度
            setSelectedSkillGroupId(skillGroup.id);
            setSelectedSkillGroupName(skillGroup.name);
            setSelectedMachineId(null);
            setSelectedMachineName('');
            setMachineScheduleVisible(true);
          }}
        >
          <Space>
            <span>👥</span>
            <span>{skillGroup.name}</span>
            <Tag color="green" style={{ fontSize: '11px' }}>
              {skillGroup.tasks.length}个任务
            </Tag>
          </Space>
        </div>
        <div style={{ display: 'flex', flex: 1, position: 'relative' }}>
          {skillGroup.tasks.map(task => generateTaskBarInRow(task))}
        </div>
      </div>
    );
  };

  // 生成设备行
  const generateMachineRow = (machine: {id: number; name: string; tasks: typeof ganttTasks}, skillGroup: {id: number; name: string}) => {
    return (
      <div
        key={`machine-${machine.id}`}
        style={{
          display: 'flex',
          borderBottom: '1px solid #f0f0f0',
          minHeight: isMobile ? '40px' : '32px',
        }}
      >
        <div
          style={{
            width: '200px',
            minWidth: '200px',
            padding: '8px 12px 8px 24px', // 左侧缩进表示层级
            borderRight: '1px solid #f0f0f0',
            display: 'flex',
            alignItems: 'center',
            cursor: 'pointer',
          }}
          onClick={() => {
            // 点击跳转到设备调度
            setSelectedMachineId(machine.id);
            setSelectedMachineName(machine.name);
            setSelectedSkillGroupId(skillGroup.id);
            setSelectedSkillGroupName(skillGroup.name);
            setMachineScheduleVisible(true);
          }}
        >
          <Space>
            <span>🔧</span>
            <span>{machine.name}</span>
            <Tag color="blue" style={{ fontSize: '11px' }}>
              {machine.tasks.length}个任务
            </Tag>
          </Space>
        </div>
        <div style={{ display: 'flex', flex: 1, position: 'relative' }}>
          {machine.tasks.map(task => generateTaskBarInRow(task))}
        </div>
      </div>
    );
  };

  // 在行内生成任务条
  const generateTaskBarInRow = (task: GanttTask) => {
    // 如果任务正在被拖拽，使用拖拽状态中的时间
    const currentTask = dragState.taskId === task.id && dragState.startDate && dragState.endDate
      ? { ...task, startDate: dragState.startDate, endDate: dragState.endDate }
      : task;

    const taskStart = Math.max(0, differenceInDays(currentTask.startDate, startDate));
    const taskDuration = differenceInDays(currentTask.endDate, currentTask.startDate) + 1;
    const taskWidth = Math.max(1, taskDuration);

    const isDragging = dragState.isDragging && dragState.taskId === task.id;

    return (
      <React.Fragment key={task.id}>
        {/* Empty cells before task */}
        {Array.from({ length: taskStart }, (_, i) => (
          <div
            key={`empty-${task.id}-${i}`}
            style={{
              width: '40px',
              minWidth: '40px',
              height: '100%',
              borderRight: '1px solid #f0f0f0',
            }}
          />
        ))}

        {/* Task bar */}
        <Tooltip
          title={
            <div>
              <div><strong>{task.name}</strong></div>
              <div>开始: {format(currentTask.startDate, 'yyyy-MM-dd')}</div>
              <div>结束: {format(currentTask.endDate, 'yyyy-MM-dd')}</div>
              <div>状态: {getStatusText(task.status)}</div>
              {task.progress > 0 && <div>进度: {task.progress}%</div>}
              {editable && <div style={{ marginTop: '4px', fontSize: '11px', color: '#999' }}>拖拽调整时间</div>}
            </div>
          }
        >
          <div
            data-task-id={task.id}
            style={{
              width: `${taskWidth * 40}px`,
              height: isMobile ? '28px' : '20px',
              backgroundColor: getStatusColor(task.status),
              borderRadius: '3px',
              position: 'relative',
              cursor: editable ? (isDragging ? 'grabbing' : 'grab') : 'pointer',
              border: `1px solid ${isDragging ? '#1890ff' : 'rgba(0,0,0,0.1)'}`,
              opacity: isDragging ? 0.8 : 1,
              transform: isDragging ? 'scale(1.02)' : 'scale(1)',
              transition: isDragging ? 'none' : 'all 0.2s ease',
              boxShadow: isDragging ? '0 2px 8px rgba(0,0,0,0.15)' : 'none',
              margin: '2px 0',
              touchAction: 'none',
            }}
            onMouseDown={editable && !isMobile ? (e) => handleMouseDown(e, task.id, 'move') : undefined}
            onClick={!isMobile ? (e) => handleTaskClick(e, task.id) : undefined}
          >
            {/* Progress bar */}
            {task.progress > 0 && (
              <div
                style={{
                  width: `${task.progress}%`,
                  height: '100%',
                  backgroundColor: task.status === 'completed' ? '#389e0d' : '#096dd9',
                  borderRadius: '2px',
                  transition: 'width 0.3s ease',
                  pointerEvents: 'none',
                }}
              />
            )}

            {/* Task text */}
            <div
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                fontSize: '10px',
                color: '#fff',
                fontWeight: 500,
                textShadow: '0 1px 2px rgba(0,0,0,0.3)',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                maxWidth: '100%',
                padding: '0 2px',
                pointerEvents: 'none',
              }}
            >
              {taskWidth > 1 ? task.name.split(' - ')[0] : ''}
            </div>
          </div>
        </Tooltip>
      </React.Fragment>
    );
  };

  const generateTaskBar = (task: GanttTask) => {
    // 如果任务正在被拖拽，使用拖拽状态中的时间
    const currentTask = dragState.taskId === task.id && dragState.startDate && dragState.endDate
      ? { ...task, startDate: dragState.startDate, endDate: dragState.endDate }
      : task;

    const taskStart = Math.max(0, differenceInDays(currentTask.startDate, startDate));
    const taskDuration = differenceInDays(currentTask.endDate, currentTask.startDate) + 1;
    const taskWidth = Math.max(1, taskDuration);

    const isDragging = dragState.isDragging && dragState.taskId === task.id;

    return (
      <div
        key={task.id}
        className="gantt-task-row"
        style={{
          display: 'flex',
          alignItems: 'center',
          minHeight: '60px',
          borderBottom: '1px solid #f0f0f0',
        }}
      >
        {/* Task Info */}
        <div
          style={{
            width: '200px',
            minWidth: '200px',
            padding: '8px 12px',
            borderRight: '1px solid #f0f0f0',
            backgroundColor: '#fafafa',
          }}
        >
          <div style={{ fontWeight: 500, marginBottom: '4px' }}>
            <span
              style={{
                cursor: 'pointer',
                color: '#1890ff',
                textDecoration: 'underline'
              }}
              onClick={() => {
                const originalTask = tasks.find(t => t.id === task.id);
                if (originalTask?.machine_id) {
                  // 有设备ID，显示设备调度
                  setSelectedMachineId(originalTask.machine_id);
                  setSelectedMachineName(originalTask.machine_name || `设备${originalTask.machine_id}`);
                  setSelectedSkillGroupId(null);
                  setSelectedSkillGroupName('');
                  setMachineScheduleVisible(true);
                } else if (originalTask?.skill_group_id) {
                  // 没有设备ID但有技能组ID，显示技能组调度
                  setSelectedMachineId(null);
                  setSelectedMachineName('');
                  setSelectedSkillGroupId(originalTask.skill_group_id);
                  setSelectedSkillGroupName(originalTask.skill_group_name || `技能组${originalTask.skill_group_id}`);
                  setMachineScheduleVisible(true);
                }
              }}
            >
              {(() => {
                const originalTask = tasks.find(t => t.id === task.id);
                if (originalTask?.machine_id) {
                  return `设备: ${originalTask.machine_name || originalTask.machine_id}`;
                } else if (originalTask?.skill_group_id) {
                  return `技能组: ${originalTask.skill_group_name || originalTask.skill_group_id}`;
                } else {
                  return '未分配';
                }
              })()}
            </span>
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            <Tag color={getStatusColor(task.status)}>
              {getStatusText(task.status)}
            </Tag>
            {task.progress > 0 && (
              <span style={{ marginLeft: '8px' }}>
                进度: {task.progress}%
              </span>
            )}
          </div>
        </div>

        {/* Timeline */}
        <div
          style={{
            flex: 1,
            display: 'flex',
            position: 'relative',
            height: '60px',
            alignItems: 'center',
          }}
        >
          {/* Empty cells before task */}
          {Array.from({ length: taskStart }, (_, i) => (
            <div
              key={`empty-${i}`}
              style={{
                width: '40px',
                minWidth: '40px',
                height: '100%',
                borderRight: '1px solid #f0f0f0',
              }}
            />
          ))}

          {/* Task bar */}
          <Tooltip
            title={
              <div>
                <div><strong>{task.name}</strong></div>
                <div>开始: {format(currentTask.startDate, 'yyyy-MM-dd')}</div>
                <div>结束: {format(currentTask.endDate, 'yyyy-MM-dd')}</div>
                <div>状态: {getStatusText(task.status)}</div>
                {task.progress > 0 && <div>进度: {task.progress}%</div>}
                {editable && <div style={{ marginTop: '4px', fontSize: '11px', color: '#999' }}>拖拽调整时间</div>}
              </div>
            }
          >
            <div
              data-task-id={task.id}
              style={{
                width: `${taskWidth * 40}px`,
                height: isMobile ? '32px' : '24px',
                backgroundColor: getStatusColor(task.status),
                borderRadius: '4px',
                position: 'relative',
                cursor: editable ? (isDragging ? 'grabbing' : 'grab') : 'pointer',
                border: `1px solid ${isDragging ? '#1890ff' : 'rgba(0,0,0,0.1)'}`,
                opacity: isDragging ? 0.8 : 1,
                transform: isDragging ? 'scale(1.02)' : 'scale(1)',
                transition: isDragging ? 'none' : 'all 0.2s ease',
                boxShadow: isDragging ? '0 4px 12px rgba(0,0,0,0.15)' : 'none',
                minHeight: isMobile ? '32px' : '24px',
                touchAction: 'none',
              }}
              onMouseDown={editable && !isMobile ? (e) => handleMouseDown(e, task.id, 'move') : undefined}
              onClick={!isMobile ? (e) => handleTaskClick(e, task.id) : undefined}
            >
              {/* 左侧调整手柄 */}
              {editable && (
                <div
                  style={{
                    position: 'absolute',
                    left: '-2px',
                    top: '0',
                    width: '4px',
                    height: '100%',
                    backgroundColor: '#1890ff',
                    cursor: 'col-resize',
                    opacity: isDragging && dragState.dragType === 'resize-start' ? 1 : 0,
                    transition: 'opacity 0.2s ease',
                    borderRadius: '2px 0 0 2px',
                  }}
                  onMouseDown={(e) => handleMouseDown(e, task.id, 'resize-start')}
                  onMouseEnter={(e) => {
                    if (!isDragging) {
                      (e.target as HTMLElement).style.opacity = '0.7';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isDragging) {
                      (e.target as HTMLElement).style.opacity = '0';
                    }
                  }}
                />
              )}

              {/* 右侧调整手柄 */}
              {editable && (
                <div
                  style={{
                    position: 'absolute',
                    right: '-2px',
                    top: '0',
                    width: '4px',
                    height: '100%',
                    backgroundColor: '#1890ff',
                    cursor: 'col-resize',
                    opacity: isDragging && dragState.dragType === 'resize-end' ? 1 : 0,
                    transition: 'opacity 0.2s ease',
                    borderRadius: '0 2px 2px 0',
                  }}
                  onMouseDown={(e) => handleMouseDown(e, task.id, 'resize-end')}
                  onMouseEnter={(e) => {
                    if (!isDragging) {
                      (e.target as HTMLElement).style.opacity = '0.7';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isDragging) {
                      (e.target as HTMLElement).style.opacity = '0';
                    }
                  }}
                />
              )}

              {/* Progress bar */}
              {task.progress > 0 && (
                <div
                  style={{
                    width: `${task.progress}%`,
                    height: '100%',
                    backgroundColor: task.status === 'completed' ? '#389e0d' : '#096dd9',
                    borderRadius: '3px',
                    transition: 'width 0.3s ease',
                    pointerEvents: 'none', // 防止进度条干扰拖拽
                  }}
                />
              )}

              {/* Task text */}
              <div
                style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  fontSize: '11px',
                  color: '#fff',
                  fontWeight: 500,
                  textShadow: '0 1px 2px rgba(0,0,0,0.3)',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  maxWidth: '100%',
                  padding: '0 4px',
                  pointerEvents: 'none', // 防止文本干扰拖拽
                }}
              >
                {taskWidth > 2 ? task.name : ''}
              </div>
            </div>
          </Tooltip>

          {/* Empty cells after task */}
          {Array.from({ length: Math.max(0, totalDays - taskStart - taskWidth) }, (_, i) => (
            <div
              key={`empty-after-${i}`}
              style={{
                width: '40px',
                minWidth: '40px',
                height: '100%',
                borderRight: '1px solid #f0f0f0',
              }}
            />
          ))}
        </div>
      </div>
    );
  };



  // 渲染技能组选择面板 - 下拉选择形式
  const renderSkillGroupSelector = () => (
    <Card
      title="技能组筛选"
      size="small"
      style={{ marginBottom: '16px' }}
    >
      <Row gutter={16} align="middle">
        <Col span={6}>
          <Text strong>筛选技能组:</Text>
        </Col>
        <Col span={14}>
          <Select
            mode="multiple"
            placeholder="选择要显示的技能组"
            style={{ width: '100%' }}
            value={Array.from(selectedSkillGroups)}
            onChange={(values: number[]) => setSelectedSkillGroups(new Set(values))}
            allowClear
            showSearch
            optionFilterProp="label"
            maxTagCount="responsive"
          >
            {skillGroupsData.map(skillGroup => (
              <Select.Option
                key={skillGroup.id}
                value={skillGroup.id}
                label={`技能组: ${skillGroup.name}`}
              >
                <Space>
                  <span>
                    👥 {skillGroup.name}
                  </span>
                  <Tag color="green" style={{ fontSize: '12px' }}>
                    {skillGroup.tasks.length}个任务
                  </Tag>
                  {skillGroup.machines.length > 0 && (
                    <Tag color="blue" style={{ fontSize: '12px' }}>
                      {skillGroup.machines.length}台设备
                    </Tag>
                  )}
                </Space>
              </Select.Option>
            ))}
          </Select>
        </Col>
        <Col span={4}>
          <Space>
            <Button
              size="small"
              onClick={() => handleSelectAllSkillGroups(true)}
              disabled={selectedSkillGroups.size === skillGroupsData.length}
            >
              全选
            </Button>
            <Button
              size="small"
              onClick={() => handleSelectAllSkillGroups(false)}
              disabled={selectedSkillGroups.size === 0}
            >
              清空
            </Button>
          </Space>
        </Col>
      </Row>

      {/* 显示选中技能组的统计信息 */}
      {selectedSkillGroups.size > 0 && (
        <div style={{ marginTop: 12, padding: '8px 12px', backgroundColor: '#f6ffed', borderRadius: '6px' }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            已选择 {selectedSkillGroups.size} 个技能组，共 {filteredTasks.length} 个任务
          </Text>
          <div style={{ marginTop: 4 }}>
            {Array.from(selectedSkillGroups).slice(0, 3).map(skillGroupId => {
              const skillGroup = skillGroupsData.find(sg => sg.id === skillGroupId);
              return skillGroup ? (
                <Tag key={skillGroupId} color="green" style={{ marginBottom: 2, fontSize: '12px' }}>
                  👥 {skillGroup.name}
                </Tag>
              ) : null;
            })}
            {selectedSkillGroups.size > 3 && (
              <Tag color="default" style={{ fontSize: '12px' }}>
                +{selectedSkillGroups.size - 3} 更多...
              </Tag>
            )}
          </div>
        </div>
      )}
    </Card>
  );

  if (ganttTasks.length === 0) {
    console.log('❌ 甘特图显示空数据页面');
    console.log('📊 原始任务数量:', tasks?.length || 0);
    console.log('📊 处理后任务数量:', ganttTasks.length);

    return (
      <div>
        {skillGroupsData.length > 0 && renderSkillGroupSelector()}
        <Card>
          <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
            <div>暂无计划任务数据</div>
            <div style={{ fontSize: '12px', marginTop: '10px', color: '#ccc' }}>
              原始任务数量: {tasks?.length || 0} | 处理后任务数量: {ganttTasks.length}
            </div>
            <div style={{ fontSize: '12px', marginTop: '5px', color: '#ccc' }}>
              请检查浏览器控制台查看详细调试信息
            </div>
          </div>
        </Card>
      </div>
    );
  }

  if (filteredTasks.length === 0 && selectedSkillGroups.size > 0) {
    return (
      <div>
        {renderSkillGroupSelector()}
        <Card>
          <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
            <div>当前筛选条件下暂无任务</div>
            <div style={{ fontSize: '12px', marginTop: '10px', color: '#ccc' }}>
              已选择 {selectedSkillGroups.size} 个技能组
            </div>
            <div style={{ fontSize: '12px', marginTop: '5px', color: '#ccc' }}>
              请调整筛选条件或清除所有筛选
            </div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div>
      {/* 技能组筛选面板 */}
      {skillGroupsData.length > 0 && renderSkillGroupSelector()}

      <Card>
        <div style={{ marginBottom: '16px' }}>
          <Title level={4} style={{ margin: 0 }}>
            {title}
          </Title>

          {/* 日期选择控件 */}
          <div style={{ marginTop: '12px', marginBottom: '12px' }}>
            <Row gutter={16} align="middle">
              <Col>
                <span style={{ marginRight: '8px' }}>时间模式:</span>
                <Select
                  value={timeMode}
                  onChange={(value) => setTimeMode(value)}
                  style={{ width: 120 }}
                >
                  <Select.Option value="week">按周</Select.Option>
                  <Select.Option value="custom">自定义</Select.Option>
                </Select>
              </Col>

              {timeMode === 'week' && (
                <Col>
                  <Space>
                    <Button
                      size="small"
                      onClick={() => setCurrentWeekOffset(prev => prev - 1)}
                    >
                      上一周
                    </Button>
                    <span style={{ margin: '0 8px' }}>
                      {(() => {
                        const targetWeek = addWeeks(startOfWeek(new Date(), { weekStartsOn: 1 }), currentWeekOffset);
                        return `${getYear(targetWeek)}年第${getWeek(targetWeek)}周`;
                      })()}
                    </span>
                    <Button
                      size="small"
                      onClick={() => setCurrentWeekOffset(prev => prev + 1)}
                    >
                      下一周
                    </Button>
                    <Button
                      size="small"
                      onClick={() => setCurrentWeekOffset(0)}
                    >
                      本周
                    </Button>
                  </Space>
                </Col>
              )}

              {timeMode === 'custom' && (
                <Col>
                  <RangePicker
                    value={customDateRange ? [dayjs(customDateRange[0]), dayjs(customDateRange[1])] : null}
                    onChange={(dates) => {
                      if (dates && dates[0] && dates[1]) {
                        setCustomDateRange([dates[0].toDate(), dates[1].toDate()]);
                      } else {
                        setCustomDateRange(null);
                      }
                    }}
                    format="YYYY-MM-DD"
                  />
                </Col>
              )}
            </Row>
          </div>

          <Text type="secondary">
            时间范围: {format(startDate, 'yyyy-MM-dd')} 至 {format(endDate, 'yyyy-MM-dd')}
            ({totalDays} 天) | 显示任务: {filteredTasks.length}/{ganttTasks.length}
          </Text>
        </div>

      <div
        ref={ganttRef}
        style={{
          border: '1px solid #f0f0f0',
          borderRadius: '6px',
          overflow: 'auto',
          maxHeight: height,
          touchAction: isMobile ? 'pan-x pan-y' : 'auto',
        }}
      >
        {/* Header */}
        <div style={{ display: 'flex', backgroundColor: '#fafafa' }}>
          <div
            style={{
              width: '200px',
              minWidth: '200px',
              padding: '12px',
              borderRight: '1px solid #f0f0f0',
              fontWeight: 600,
            }}
          >
            任务/资源
          </div>
          <div style={{ display: 'flex', flex: 1 }}>
            {generateDateHeaders()}
          </div>
        </div>

        {/* Resource rows */}
        <div>
          {generateResourceRows()}
        </div>
      </div>

      {/* Legend */}
      <Row gutter={16} style={{ marginTop: '16px' }}>
        <Col>
          <Text strong>状态说明：</Text>
        </Col>
        <Col>
          <Tag color="#d9d9d9">待开始</Tag>
        </Col>
        <Col>
          <Tag color="#1890ff">进行中</Tag>
        </Col>
        <Col>
          <Tag color="#52c41a">已完成</Tag>
        </Col>
        <Col>
          <Tag color="#ff4d4f">已取消</Tag>
        </Col>
      </Row>

      {/* 任务详情弹窗 */}
      <TaskDetailModal
        visible={isDetailModalVisible}
        taskId={selectedTaskId}
        onCancel={() => {
          setIsDetailModalVisible(false);
          setSelectedTaskId(null);
        }}
        onUpdate={(taskId, data) => {
          // 如果有自定义更新回调，调用它
          if (onTaskUpdate && data.planned_start && data.planned_end) {
            onTaskUpdate(taskId, new Date(data.planned_start), new Date(data.planned_end));
          }
        }}
      />

      {/* 设备/技能组调度弹窗 */}
      <MachineScheduleModal
        visible={machineScheduleVisible}
        machineId={selectedMachineId}
        machineName={selectedMachineName}
        skillGroupId={selectedSkillGroupId}
        skillGroupName={selectedSkillGroupName}
        onCancel={() => {
          setMachineScheduleVisible(false);
          setSelectedMachineId(null);
          setSelectedMachineName('');
          setSelectedSkillGroupId(null);
          setSelectedSkillGroupName('');
        }}
      />
    </Card>
    </div>
  );
};

export default GanttChart;
