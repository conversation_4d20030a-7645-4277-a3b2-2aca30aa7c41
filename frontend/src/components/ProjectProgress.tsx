import React, { useState } from 'react';
import {
  Card,
  Progress,
  Row,
  Col,
  Statistic,
  Tag,
  Typography,
  Spin,
  Alert,
  Table,
  Tooltip,
  Modal,
  Button,
  Steps,
  Descriptions,
  Badge,
  Space,
  Divider,
} from 'antd';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  MinusCircleOutlined,
  EyeOutlined,
  ToolOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
} from '@ant-design/icons';
import { useQuery } from 'react-query';
import { apiClient } from '@/lib/api';

const { Title, Text } = Typography;
const { Step } = Steps;

interface ProjectProgressProps {
  projectId: number;
}

interface PartDetailModalProps {
  visible: boolean;
  onClose: () => void;
  partId: number | null;
  partInfo: any;
}

const PartDetailModal: React.FC<PartDetailModalProps> = ({ visible, onClose, partId, partInfo }) => {
  // 获取零件的工艺路由信息
  const { data: partRouting, isLoading: routingLoading } = useQuery(
    ['part-routing', partId],
    () => partId ? apiClient.getPartRouting(partId) : Promise.resolve(null),
    { enabled: !!partId && visible }
  );

  // 获取零件的计划任务信息
  const { data: planTasks, isLoading: tasksLoading } = useQuery(
    ['plan-tasks-by-part', partId],
    () => partId ? apiClient.getPlanTasks({ part_id: partId }) : Promise.resolve(null),
    { enabled: !!partId && visible }
  );

  const getProcessStepStatus = (stepId: number) => {
    if (!planTasks) return 'wait';

    const stepTasks = planTasks.filter((task: any) => task.routing_step_id === stepId);
    if (stepTasks.length === 0) return 'wait';

    const completedTasks = stepTasks.filter((task: any) => task.status === 'completed');
    const inProgressTasks = stepTasks.filter((task: any) => task.status === 'in_progress');

    if (completedTasks.length === stepTasks.length) return 'finish';
    if (inProgressTasks.length > 0) return 'process';
    return 'wait';
  };

  const getProcessStepIcon = (status: string) => {
    switch (status) {
      case 'finish':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'process':
        return <PlayCircleOutlined style={{ color: '#1890ff' }} />;
      case 'wait':
        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  return (
    <Modal
      title={
        <Space>
          <ToolOutlined />
          零件工艺进度详情
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>
      ]}
      width={800}
      style={{ top: 20 }}
    >
      {partInfo && (
        <div>
          {/* 零件基本信息 */}
          <Card size="small" style={{ marginBottom: 16 }}>
            <Descriptions title="零件信息" column={2} size="small">
              <Descriptions.Item label="零件编号">{partInfo.part_number}</Descriptions.Item>
              <Descriptions.Item label="零件名称">{partInfo.part_name || '-'}</Descriptions.Item>
              <Descriptions.Item label="BOM数量">{partInfo.bom_quantity}</Descriptions.Item>
              <Descriptions.Item label="完成度">
                <Progress
                  percent={Math.round(partInfo.completion_percentage)}
                  size="small"
                  status={partInfo.completion_percentage === 100 ? 'success' : 'active'}
                />
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* 工单状态概览 */}
          <Card size="small" style={{ marginBottom: 16 }}>
            <Title level={5}>工单状态</Title>
            <Row gutter={16}>
              <Col span={6}>
                <Statistic title="总工单" value={partInfo.total_work_orders} suffix="个" />
              </Col>
              <Col span={6}>
                <Statistic
                  title="已完成"
                  value={partInfo.completed_work_orders}
                  suffix="个"
                  valueStyle={{ color: '#3f8600' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="进行中"
                  value={partInfo.in_progress_work_orders}
                  suffix="个"
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="待处理"
                  value={partInfo.pending_work_orders}
                  suffix="个"
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
            </Row>
          </Card>

          {/* 工艺流程进度 */}
          <Card size="small">
            <Title level={5}>工艺流程进度</Title>
            {routingLoading ? (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <Spin />
                <div style={{ marginTop: 8 }}>
                  <Text type="secondary">正在加载工艺信息...</Text>
                </div>
              </div>
            ) : partRouting?.routing_steps && partRouting.routing_steps.length > 0 ? (
              <Steps
                direction="vertical"
                size="small"
                current={-1}
                style={{ marginTop: 16 }}
              >
                {partRouting.routing_steps.map((step: any, index: number) => {
                  const status = getProcessStepStatus(step.id);
                  return (
                    <Step
                      key={step.id}
                      title={
                        <Space>
                          <Text strong>工序 {step.step_number}</Text>
                          <Text>{step.process_name}</Text>
                        </Space>
                      }
                      description={
                        <div>
                          <Text type="secondary">{step.work_instructions || '无工作指导'}</Text>
                          {step.standard_hours && (
                            <div>
                              <Text type="secondary">标准工时: {step.standard_hours}h</Text>
                            </div>
                          )}
                        </div>
                      }
                      status={status}
                      icon={getProcessStepIcon(status)}
                    />
                  );
                })}
              </Steps>
            ) : (
              <Alert
                message="暂无工艺信息"
                description="该零件尚未配置工艺路由。"
                type="info"
                showIcon
              />
            )}
          </Card>
        </div>
      )}
    </Modal>
  );
};

const ProjectProgress: React.FC<ProjectProgressProps> = ({ projectId }) => {
  const [selectedPart, setSelectedPart] = useState<{ id: number; info: any } | null>(null);
  const { data: completionStatus, isLoading, error } = useQuery(
    ['project-completion-status', projectId],
    () => apiClient.getProjectCompletionStatus(projectId),
    {
      enabled: !!projectId,
      refetchInterval: 30000, // 每30秒刷新一次
    }
  );

  if (isLoading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Text type="secondary">正在加载项目进度...</Text>
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <Alert
          message="加载失败"
          description="无法获取项目完成状态，请稍后重试。"
          type="error"
          showIcon
        />
      </Card>
    );
  }

  if (!completionStatus) {
    return (
      <Card>
        <Alert
          message="暂无数据"
          description="该项目暂无完成状态数据。"
          type="info"
          showIcon
        />
      </Card>
    );
  }

  const { overall_progress, parts_status, work_orders_summary } = completionStatus;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'in_progress':
        return <ClockCircleOutlined style={{ color: '#1890ff' }} />;
      case 'cancelled':
        return <MinusCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in_progress':
        return 'processing';
      case 'cancelled':
        return 'error';
      default:
        return 'warning';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'in_progress':
        return '进行中';
      case 'cancelled':
        return '已取消';
      case 'not_started':
        return '未开始';
      default:
        return '未知';
    }
  };

  const handlePartClick = (record: any) => {
    setSelectedPart({
      id: record.part_id,
      info: record
    });
  };

  const partsColumns = [
    {
      title: '零件编号',
      dataIndex: 'part_number',
      key: 'part_number',
      render: (text: string, record: any) => (
        <Button
          type="link"
          onClick={() => handlePartClick(record)}
          style={{ padding: 0, height: 'auto' }}
        >
          <Text strong style={{ color: '#1890ff' }}>{text}</Text>
        </Button>
      ),
    },
    {
      title: '零件名称',
      dataIndex: 'part_name',
      key: 'part_name',
      render: (text: string) => text || '-',
    },
    {
      title: 'BOM数量',
      dataIndex: 'bom_quantity',
      key: 'bom_quantity',
    },
    {
      title: '工单状态',
      key: 'work_orders',
      render: (_: any, record: any) => (
        <div>
          <div>
            <Text strong>总计: </Text>
            <Text>{record.total_work_orders}</Text>
          </div>
          {record.completed_work_orders > 0 && (
            <Tag color="success">已完成: {record.completed_work_orders}</Tag>
          )}
          {record.in_progress_work_orders > 0 && (
            <Tag color="processing">进行中: {record.in_progress_work_orders}</Tag>
          )}
          {record.pending_work_orders > 0 && (
            <Tag color="warning">待处理: {record.pending_work_orders}</Tag>
          )}
          {record.cancelled_work_orders > 0 && (
            <Tag color="error">已取消: {record.cancelled_work_orders}</Tag>
          )}
        </div>
      ),
    },
    {
      title: '完成度',
      key: 'completion',
      render: (_: any, record: any) => (
        <div>
          <Progress
            percent={Math.round(record.completion_percentage)}
            size="small"
            status={record.completion_percentage === 100 ? 'success' : 'active'}
          />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {Math.round(record.completion_percentage)}%
          </Text>
        </div>
      ),
    },
    {
      title: '工艺状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_: any, record: any) => (
        <Space>
          <Tooltip title="查看工艺详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handlePartClick(record)}
              size="small"
            >
              详情
            </Button>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 整体进度概览 */}
      <Card
        title="项目进度概览"
        style={{ marginBottom: 16 }}
      >
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Statistic
              title="整体完成度"
              value={Math.round(overall_progress.overall_completion_percentage)}
              suffix="%"
              valueStyle={{ color: overall_progress.overall_completion_percentage === 100 ? '#3f8600' : '#1890ff' }}
            />
            <Progress
              percent={Math.round(overall_progress.overall_completion_percentage)}
              status={overall_progress.overall_completion_percentage === 100 ? 'success' : 'active'}
              style={{ marginTop: 8 }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="总零件数"
              value={overall_progress.total_parts}
              suffix="个"
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="已完成零件"
              value={overall_progress.completed_parts}
              suffix="个"
              valueStyle={{ color: '#3f8600' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="进行中零件"
              value={overall_progress.in_progress_parts}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
        </Row>
      </Card>

      {/* 工单状态概览 */}
      <Card
        title="工单状态概览"
        style={{ marginBottom: 16 }}
      >
        <Row gutter={[16, 16]}>
          <Col span={4}>
            <Statistic
              title="总工单"
              value={work_orders_summary.total_work_orders}
              suffix="个"
            />
          </Col>
          <Col span={4}>
            <Statistic
              title="已完成"
              value={work_orders_summary.completed_orders}
              suffix="个"
              valueStyle={{ color: '#3f8600' }}
            />
          </Col>
          <Col span={4}>
            <Statistic
              title="进行中"
              value={work_orders_summary.in_progress_orders}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={4}>
            <Statistic
              title="已计划"
              value={work_orders_summary.planned_orders}
              suffix="个"
              valueStyle={{ color: '#722ed1' }}
            />
          </Col>
          <Col span={4}>
            <Statistic
              title="待处理"
              value={work_orders_summary.pending_orders}
              suffix="个"
              valueStyle={{ color: '#faad14' }}
            />
          </Col>
          <Col span={4}>
            <Statistic
              title="逾期"
              value={work_orders_summary.overdue_orders}
              suffix="个"
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Col>
        </Row>
      </Card>

      {/* 零件详细状态 */}
      <Card
        title={
          <Space>
            <ToolOutlined />
            零件完成状态详情
            <Text type="secondary" style={{ fontSize: '14px', fontWeight: 'normal' }}>
              (点击零件编号或详情按钮查看工艺进度)
            </Text>
          </Space>
        }
      >
        <Table
          columns={partsColumns}
          dataSource={parts_status}
          rowKey="part_id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个零件`,
          }}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* 零件详情模态框 */}
      <PartDetailModal
        visible={!!selectedPart}
        onClose={() => setSelectedPart(null)}
        partId={selectedPart?.id || null}
        partInfo={selectedPart?.info}
      />
    </div>
  );
};

export default ProjectProgress;
