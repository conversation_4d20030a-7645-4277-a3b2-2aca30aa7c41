import React from 'react';
import { Select, Space } from 'antd';
import { PROJECT_STATUS_OPTIONS } from '@/types/api';
import ProjectStatusTag from './ProjectStatusTag';

interface ProjectStatusSelectorProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  allowClear?: boolean;
  disabled?: boolean;
  size?: 'small' | 'middle' | 'large';
  style?: React.CSSProperties;
}

const ProjectStatusSelector: React.FC<ProjectStatusSelectorProps> = ({
  value,
  onChange,
  placeholder = "请选择项目状态",
  allowClear = false,
  disabled = false,
  size = 'middle',
  style,
}) => {
  return (
    <Select
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      allowClear={allowClear}
      disabled={disabled}
      size={size}
      style={style}
    >
      {PROJECT_STATUS_OPTIONS.map(option => (
        <Select.Option key={option.value} value={option.value}>
          <Space>
            <ProjectStatusTag status={option.value} size="small" />
          </Space>
        </Select.Option>
      ))}
    </Select>
  );
};

export default ProjectStatusSelector;
