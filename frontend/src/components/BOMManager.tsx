import React, { useState } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  InputNumber,
  Space,
  Popconfirm,
  Tag,
  Input,
  Row,
  Col,
  Empty,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import { useMessage } from '@/hooks/useMessage';
import PartSelector from './PartSelector';
import QuickPartCreator from './QuickPartCreator';
import type { ProjectBom, CreateProjectBomRequest } from '@/types/api';

const { Search } = Input;

interface BOMManagerProps {
  projectId: number;
  readonly?: boolean;
}

interface BOMItem extends ProjectBom {
  part_name?: string;
  part_number?: string;
  specifications?: string;
  version?: string;
}

const BOMManager: React.FC<BOMManagerProps> = ({ projectId, readonly = false }) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingBom, setEditingBom] = useState<BOMItem | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedPartId, setSelectedPartId] = useState<number | undefined>();
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const { success, error } = useMessage();

  // Fetch parts for reference
  const { data: parts = [] } = useQuery('parts', () => apiClient.getParts());

  // Fetch BOM items for the project
  const { data: bomItems = [], isLoading } = useQuery(
    ['project-bom', projectId],
    () => apiClient.getProjectBom(projectId),
    {
      enabled: !!projectId,
    }
  );

  // Create BOM item mutation
  const createBomMutation = useMutation(
    (data: CreateProjectBomRequest) => apiClient.createProjectBom(projectId, data),
    {
      onSuccess: () => {
        setIsModalVisible(false);
        form.resetFields();
        setSelectedPartId(undefined);
        queryClient.invalidateQueries(['project-bom', projectId]);
        success('BOM项目添加成功');
      },
      onError: () => {
        error('添加BOM项目失败');
      },
    }
  );

  // Update BOM item mutation
  const updateBomMutation = useMutation(
    ({ id, updates }: { id: number; updates: { quantity?: number } }) =>
      apiClient.updateProjectBom(id, updates),
    {
      onSuccess: () => {
        setIsModalVisible(false);
        form.resetFields();
        setEditingBom(null);
        setSelectedPartId(undefined);
        queryClient.invalidateQueries(['project-bom', projectId]);
        success('BOM项目更新成功');
      },
      onError: () => {
        error('更新BOM项目失败');
      },
    }
  );

  // Delete BOM item mutation
  const deleteBomMutation = useMutation(
    (id: number) => apiClient.deleteProjectBom(id),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['project-bom', projectId]);
        success('BOM项目删除成功');
      },
      onError: () => {
        error('删除BOM项目失败');
      },
    }
  );

  const handleAdd = () => {
    setEditingBom(null);
    form.resetFields();
    setSelectedPartId(undefined);
    setIsModalVisible(true);
  };

  const handleEdit = (record: BOMItem) => {
    setEditingBom(record);
    setSelectedPartId(record.part_id);
    form.setFieldsValue({
      part_id: record.part_id,
      quantity: record.quantity,
    });
    setIsModalVisible(true);
  };

  const handleDelete = (id: number) => {
    deleteBomMutation.mutate(id);
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingBom) {
        updateBomMutation.mutate({
          id: editingBom.id,
          updates: { quantity: values.quantity },
        });
      } else {
        createBomMutation.mutate({
          part_id: values.part_id,
          quantity: values.quantity,
        });
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handlePartSelect = (partId: number) => {
    setSelectedPartId(partId);
    form.setFieldsValue({ part_id: partId });
  };

  const columns = [
    {
      title: '零件编号',
      dataIndex: 'part_number',
      key: 'part_number',
      render: (_: any, record: BOMItem) => {
        const part = parts.find(p => p.id === record.part_id);
        return part?.part_number || '-';
      },
    },
    {
      title: '零件名称',
      dataIndex: 'part_name',
      key: 'part_name',
      render: (_: any, record: BOMItem) => {
        const part = parts.find(p => p.id === record.part_id);
        return part?.part_name || '-';
      },
    },
    {
      title: '规格说明',
      dataIndex: 'specifications',
      key: 'specifications',
      render: (_: any, record: BOMItem) => {
        const part = parts.find(p => p.id === record.part_id);
        return part?.specifications || '-';
      },
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (quantity: number) => (
        <Tag color="blue">{quantity}</Tag>
      ),
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      render: (_: any, record: BOMItem) => {
        const part = parts.find(p => p.id === record.part_id);
        return part?.version || '-';
      },
    },
  ];

  if (!readonly) {
    columns.push({
      title: '操作',
      key: 'action',
      render: (_: any, record: BOMItem) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个BOM项目吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    } as any);
  }

  const filteredBomItems = bomItems.filter((item: BOMItem) => {
    if (!searchText) return true;
    const part = parts.find(p => p.id === item.part_id);
    return (
      part?.part_number?.toLowerCase().includes(searchText.toLowerCase()) ||
      part?.part_name?.toLowerCase().includes(searchText.toLowerCase()) ||
      part?.specifications?.toLowerCase().includes(searchText.toLowerCase())
    );
  });

  return (
    <div>
      {/* 操作栏 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col flex="auto">
          <Search
            placeholder="搜索零件编号、名称或规格"
            allowClear
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ maxWidth: 300 }}
          />
        </Col>
        {!readonly && (
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
              >
                添加零件
              </Button>
              <QuickPartCreator
                projectId={projectId}
                autoAddToBom={true}
                onPartCreated={() => {
                  // 刷新BOM数据
                  queryClient.invalidateQueries(['project-bom', projectId]);
                }}
              />
            </Space>
          </Col>
        )}
      </Row>

      {/* BOM表格 */}
      <Table
        columns={columns}
        dataSource={filteredBomItems}
        rowKey="id"
        loading={isLoading}
        locale={{
          emptyText: <Empty description="暂无BOM数据" />,
        }}
        pagination={{
          total: filteredBomItems.length,
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
        }}
      />

      {/* 添加/编辑模态框 */}
      <Modal
        title={editingBom ? '编辑BOM项目' : '添加BOM项目'}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
          setEditingBom(null);
          setSelectedPartId(undefined);
        }}
        confirmLoading={createBomMutation.isLoading || updateBomMutation.isLoading}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            quantity: 1,
          }}
        >
          <Form.Item
            name="part_id"
            label="选择零件"
            rules={[{ required: true, message: '请选择零件' }]}
          >
            <PartSelector
              value={selectedPartId}
              onChange={handlePartSelect}
              placeholder="搜索并选择零件"
              mode="all"
            />
          </Form.Item>

          <Form.Item
            name="quantity"
            label="数量"
            rules={[
              { required: true, message: '请输入数量' },
              { type: 'number', min: 1, message: '数量必须大于0' },
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入数量"
              min={1}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default BOMManager;
