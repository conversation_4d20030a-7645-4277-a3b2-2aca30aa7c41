import React, { useState } from 'react';
import {
  Modal,
  Form,
  Input,
  Button,
  Space,
  Typography,
  Divider,
  Row,
  Col,
} from 'antd';
import {
  PlusOutlined,
  ToolOutlined,
} from '@ant-design/icons';
import { useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import { useMessage } from '@/hooks/useMessage';
import type { CreatePartRequest, Part } from '@/types/api';

const { TextArea } = Input;
const { Text } = Typography;

interface QuickPartCreatorProps {
  onPartCreated?: (part: Part) => void;
  trigger?: React.ReactNode;
  projectId?: number; // 如果提供，创建零件后自动添加到项目BOM
  autoAddToBom?: boolean;
  defaultQuantity?: number;
}

const QuickPartCreator: React.FC<QuickPartCreatorProps> = ({
  onPartCreated,
  trigger,
  projectId,
  autoAddToBom = false,
  defaultQuantity = 1,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const { success, error } = useMessage();

  const createPartMutation = useMutation(
    async (data: { partData: CreatePartRequest; quantity?: number }) => {
      const part = await apiClient.createPart(data.partData);

      // 如果需要自动添加到项目BOM
      if (autoAddToBom && projectId && data.quantity) {
        await apiClient.createProjectBom(projectId, {
          part_id: part.id,
          quantity: data.quantity,
        });
      }

      return part;
    },
    {
      onSuccess: (part, variables) => {
        if (autoAddToBom && projectId && variables.quantity) {
          success('零件创建成功并已添加到项目BOM');
          // 刷新项目BOM数据
          queryClient.invalidateQueries(['project-bom', projectId]);
        } else {
          success('零件创建成功');
        }
        
        // 刷新零件列表
        queryClient.invalidateQueries('parts');
        
        // 关闭模态框并重置表单
        setIsModalVisible(false);
        form.resetFields();
        
        // 回调通知父组件
        if (onPartCreated) {
          onPartCreated(part);
        }
      },
      onError: () => {
        error('零件创建失败');
      },
    }
  );

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      const partData: CreatePartRequest = {
        part_number: values.part_number,
        part_name: values.part_name,
        version: values.version,
        specifications: values.specifications,
      };

      const submitData = {
        partData,
        quantity: autoAddToBom ? values.quantity || defaultQuantity : undefined,
      };

      createPartMutation.mutate(submitData);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const defaultTrigger = (
    <Button
      type="dashed"
      icon={<PlusOutlined />}
      onClick={() => setIsModalVisible(true)}
    >
      快速创建零件
    </Button>
  );

  return (
    <>
      {trigger ? (
        <div onClick={() => setIsModalVisible(true)}>
          {trigger}
        </div>
      ) : (
        defaultTrigger
      )}

      <Modal
        title={
          <Space>
            <ToolOutlined />
            快速创建零件
          </Space>
        }
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        confirmLoading={createPartMutation.isLoading}
        width={600}
        okText="创建零件"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            version: '1.0',
            quantity: defaultQuantity,
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="part_number"
                label="零件编号"
                rules={[
                  { required: true, message: '请输入零件编号' },
                  { max: 255, message: '零件编号不能超过255个字符' },
                ]}
              >
                <Input placeholder="请输入零件编号" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="version"
                label="版本"
                rules={[
                  { required: true, message: '请输入版本' },
                  { max: 50, message: '版本不能超过50个字符' },
                ]}
              >
                <Input placeholder="请输入版本" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="part_name"
            label="零件名称"
            rules={[
              { max: 255, message: '零件名称不能超过255个字符' },
            ]}
          >
            <Input placeholder="请输入零件名称（可选）" />
          </Form.Item>

          <Form.Item
            name="specifications"
            label="规格说明"
          >
            <TextArea
              rows={3}
              placeholder="请输入规格说明（可选）"
              maxLength={1000}
              showCount
            />
          </Form.Item>

          {autoAddToBom && projectId && (
            <>
              <Divider />
              <Text type="secondary">
                创建后将自动添加到当前项目BOM中
              </Text>
              <Form.Item
                name="quantity"
                label="BOM数量"
                rules={[
                  { required: true, message: '请输入数量' },
                  { type: 'number', min: 1, message: '数量必须大于0' },
                ]}
                style={{ marginTop: 16 }}
              >
                <Input
                  type="number"
                  placeholder="请输入数量"
                  min={1}
                />
              </Form.Item>
            </>
          )}
        </Form>
      </Modal>
    </>
  );
};

export default QuickPartCreator;
