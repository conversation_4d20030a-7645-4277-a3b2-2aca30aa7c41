import React, { useState, useEffect } from 'react';
import { Card, Badge, Button, Space, Typography, Descriptions } from 'antd';
import { ReloadOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { TokenManager } from '@/utils/tokenManager';
import { useAuthStore } from '@/store/auth';

const { Text, Title } = Typography;

interface TokenStatusProps {
  showDetails?: boolean;
}

const TokenStatus: React.FC<TokenStatusProps> = ({ showDetails = false }) => {
  const [tokenInfo, setTokenInfo] = useState(TokenManager.getTokenInfo());
  const { refreshToken } = useAuthStore();

  const updateTokenInfo = () => {
    setTokenInfo(TokenManager.getTokenInfo());
  };

  useEffect(() => {
    updateTokenInfo();
    
    // 每30秒更新一次token信息
    const interval = setInterval(updateTokenInfo, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const handleRefreshToken = async () => {
    const success = await refreshToken();
    if (success) {
      updateTokenInfo();
    }
  };

  const formatTime = (milliseconds: number): string => {
    if (milliseconds <= 0) return '已过期';
    
    const minutes = Math.floor(milliseconds / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days}天${hours % 24}小时`;
    if (hours > 0) return `${hours}小时${minutes % 60}分钟`;
    return `${minutes}分钟`;
  };

  const getStatusBadge = () => {
    if (!tokenInfo.hasToken) {
      return <Badge status="default" text="未登录" />;
    }
    
    if (tokenInfo.isExpired) {
      return <Badge status="error" text="已过期" />;
    }
    
    if (tokenInfo.shouldRefresh) {
      return <Badge status="warning" text="即将过期" />;
    }
    
    return <Badge status="success" text="有效" />;
  };

  if (!showDetails) {
    return (
      <Space>
        {getStatusBadge()}
        <Text type="secondary">
          剩余: {formatTime(tokenInfo.remainingTime)}
        </Text>
        <Button 
          size="small" 
          icon={<ReloadOutlined />} 
          onClick={updateTokenInfo}
          type="text"
        />
      </Space>
    );
  }

  return (
    <Card 
      title={
        <Space>
          <InfoCircleOutlined />
          <Title level={5} style={{ margin: 0 }}>Token状态</Title>
        </Space>
      }
      size="small"
      extra={
        <Space>
          <Button 
            size="small" 
            icon={<ReloadOutlined />} 
            onClick={updateTokenInfo}
          >
            刷新状态
          </Button>
          {tokenInfo.hasToken && !tokenInfo.isExpired && (
            <Button 
              size="small" 
              type="primary"
              onClick={handleRefreshToken}
            >
              手动刷新Token
            </Button>
          )}
        </Space>
      }
    >
      <Descriptions column={1} size="small">
        <Descriptions.Item label="状态">
          {getStatusBadge()}
        </Descriptions.Item>
        
        <Descriptions.Item label="剩余时间">
          <Text type={tokenInfo.isExpired ? 'danger' : tokenInfo.shouldRefresh ? 'warning' : 'success'}>
            {formatTime(tokenInfo.remainingTime)}
          </Text>
        </Descriptions.Item>
        
        {tokenInfo.payload && (
          <>
            <Descriptions.Item label="用户名">
              {tokenInfo.payload.username}
            </Descriptions.Item>
            
            <Descriptions.Item label="角色">
              {tokenInfo.payload.roles.join(', ') || '无'}
            </Descriptions.Item>
            
            <Descriptions.Item label="技能组">
              {tokenInfo.payload.skills.join(', ') || '无'}
            </Descriptions.Item>
            
            <Descriptions.Item label="签发时间">
              {new Date(tokenInfo.payload.iat * 1000).toLocaleString()}
            </Descriptions.Item>
            
            <Descriptions.Item label="过期时间">
              {new Date(tokenInfo.payload.exp * 1000).toLocaleString()}
            </Descriptions.Item>
          </>
        )}
      </Descriptions>
      
      {tokenInfo.shouldRefresh && (
        <div style={{ marginTop: 16, padding: 8, backgroundColor: '#fff7e6', borderRadius: 4 }}>
          <Text type="warning">
            <InfoCircleOutlined /> Token即将过期，系统将自动刷新
          </Text>
        </div>
      )}
      
      {tokenInfo.isExpired && (
        <div style={{ marginTop: 16, padding: 8, backgroundColor: '#fff2f0', borderRadius: 4 }}>
          <Text type="danger">
            <InfoCircleOutlined /> Token已过期，请重新登录
          </Text>
        </div>
      )}
    </Card>
  );
};

export default TokenStatus;
