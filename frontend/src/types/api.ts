// API Response Types
export interface ApiResponse<T = any> {
  data: T;
  message: string;
  timestamp: string;
}

export interface ApiError {
  error: string;
  message: string;
  timestamp: string;
}

// Authentication Types
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: User;
}

export interface User {
  id: number;
  username: string;
  full_name?: string;
  is_active: boolean;
  roles: string[];
  skills: string[];
  created_at?: string; // 可选字段，因为登录响应中可能不包含
}

export interface Role {
  id: number;
  role_name: string;
  display_name?: string;
  description?: string;
  is_active?: boolean;
  role_type?: string;
  created_at?: string;
}

export interface SkillGroup {
  id: number;
  group_name: string;
  display_name?: string;
  description?: string;
}

export interface CreateRoleRequest {
  role_name: string;
}

export interface RoleDependencyInfo {
  can_delete: boolean;
  is_system_role: boolean;
  affected_users: UserInfo[];
  warnings: string[];
}

export interface CreateSkillGroupRequest {
  group_name: string;
}

export interface SkillGroupDependencyInfo {
  can_delete: boolean;
  is_system_skill_group: boolean;
  affected_users: UserInfo[];
  affected_machines: MachineInfo[];
  affected_plan_tasks: PlanTaskInfo[];
  warnings: string[];
  // 添加缺少的属性
  group_name?: string;
  user_count?: number;
  machine_count?: number;
  plan_task_count?: number;
  blocking_reason?: string;
  skill_group_id?: number;
}

export interface UserInfo {
  id: number;
  username: string;
  full_name?: string;
}

export interface MachineInfo {
  id: number;
  machine_name: string;
}

export interface PlanTaskInfo {
  id: number;
  task_name: string;
  work_order_id?: number;
}

export interface CreateUserRequest {
  username: string;
  password: string;
  full_name?: string;
  role_ids: number[];
  skill_group_ids: number[];
}

export interface UpdateProfileRequest {
  full_name?: string;
}

export interface ChangePasswordRequest {
  current_password: string;
  new_password: string;
}

export interface WeeklyTaskStats {
  day_name: string;
  date: string;
  planned_tasks: number;
  completed_tasks: number;
  completion_rate: number;
}

export interface WeeklyTaskSummary {
  week_start: string;
  week_end: string;
  daily_stats: WeeklyTaskStats[];
  total_planned: number;
  total_completed: number;
  overall_completion_rate: number;
}

export interface CreateRoleRequest {
  role_name: string;
  description?: string;
}

export interface CreateSkillGroupRequest {
  group_name: string;
  description?: string;
}

export interface RoleDependencyInfo {
  role_id: number;
  role_name: string;
  can_delete: boolean;
  blocking_reason?: string;
  affected_users: {
    id: number;
    username: string;
    full_name?: string;
  }[];
  user_count: number;
}



// Project Management Types
export interface Project {
  id: number;
  project_name: string;
  customer_name?: string;
  status: string;
  created_at: string;
}

export interface CreateProjectRequest {
  project_name: string;
  customer_name?: string;
  status?: string;
}

export interface UpdateProjectRequest {
  project_name?: string;
  customer_name?: string;
  status?: string;
}

export interface ProjectStatusUpdate {
  status: string;
}

export interface ProjectQuery {
  status?: string;
  customer_name?: string;
  limit?: number;
  offset?: number;
}

export interface ProjectSearchResult {
  projects: Project[];
  total_count: number;
  limit: number;
  offset: number;
}

export interface ProjectStatusStats {
  status: string;
  count: number;
  percentage: number;
}

// Project status constants
export const PROJECT_STATUS = {
  NORMAL: 'Normal',
  PRIORITY: 'Priority',
  PAUSED: 'Paused',
} as const;

export type ProjectStatus = typeof PROJECT_STATUS[keyof typeof PROJECT_STATUS];

export const PROJECT_STATUS_OPTIONS = [
  { value: PROJECT_STATUS.NORMAL, label: '正常', color: 'green' },
  { value: PROJECT_STATUS.PRIORITY, label: '优先', color: 'red' },
  { value: PROJECT_STATUS.PAUSED, label: '暂停', color: 'orange' },
];

// Parts Management Types
export interface Part {
  id: number;
  part_number: string;
  part_name?: string;
  version: string;
  specifications?: string;
}

export interface CreatePartRequest {
  part_number: string;
  part_name?: string;
  version: string;
  specifications?: string;
}

// BOM Types
export interface ProjectBom {
  id: number;
  project_id: number;
  part_id: number;
  quantity: number;
  part_number?: string;
  part_name?: string;
  version?: string;
  specifications?: string;
  part?: Part;
  project_name?: string;
}

export interface CreateProjectBomRequest {
  part_id: number;
  quantity: number;
}

export interface ProjectBomWithDetails {
  id: number;
  project_id: number;
  part_id: number;
  part_number: string;
  part_name?: string;
  version: string;
  quantity: number;
  specifications?: string;
}

// Machine Types
export interface Machine {
  id: number;
  machine_name: string;
  skill_group_id: number;
  status: string;
  skill_group_name?: string;
}

export interface CreateMachineRequest {
  machine_name: string;
  skill_group_id: number;
  status?: string;
}

// Work Order Types
export interface WorkOrder {
  id: number;
  project_bom_id: number;
  quantity: number;
  status: string;
  due_date?: string;
  created_at: string;
  project_name?: string;
  part_number?: string;
  part_id?: number;
  part_name?: string;
  project_id?: number;
  customer_name?: string;
  version?: string;
  bom_quantity?: number;
  specifications?: string;
}

export interface CreateWorkOrderRequest {
  project_bom_id: number;
  quantity: number;
  due_date?: string;
  priority?: string;
}

// Plan Task Types
export interface PlanTask {
  id: number;
  work_order_id: number;
  routing_step_id: number;
  skill_group_id: number;
  machine_id?: number;
  planned_start: string;
  planned_end: string;
  status: string;
  work_order?: WorkOrder;
  skill_group_name?: string;
  machine_name?: string;
  project_name?: string;
  part_number?: string;
  process_name?: string;
}

export interface CreatePlanTaskRequest {
  work_order_id: number;
  routing_step_id: number;
  skill_group_id: number;
  machine_id?: number;
  planned_start: string;
  planned_end: string;
}

export interface UpdatePlanTaskRequest {
  skill_group_id?: number;
  machine_id?: number;
  planned_start?: string;
  planned_end?: string;
  status?: string;
}

export interface ReschedulePlanTaskRequest {
  planned_start: string;
  planned_end: string;
}

export interface CreatePlanTasksFromWorkOrderRequest {
  work_order_id: number;
  start_date: string;
  skill_group_assignments?: SkillGroupAssignment[];
  machine_assignments?: MachineAssignment[];
}

export interface BatchCreatePlanTasksRequest {
  creation_type: 'part' | 'project' | 'work_order';
  target_id: number;
  start_date: string;
  skill_group_assignments?: SkillGroupAssignment[];
  machine_assignments?: MachineAssignment[];
  auto_schedule?: boolean;
}

export interface SkillGroupAssignment {
  routing_step_id: number;
  skill_group_id: number;
}

export interface MachineAssignment {
  routing_step_id: number;
  machine_id: number;
}

export interface PlanTaskWithDetails {
  id: number;
  work_order_id: number;
  routing_step_id: number;
  skill_group_id: number;
  machine_id?: number;
  planned_start: string;
  planned_end: string;
  status: string;
  // Work order details
  work_order_quantity: number;
  work_order_status: string;
  work_order_due_date?: string;
  // Project details
  project_id: number;
  project_name: string;
  customer_name?: string;
  // Part details
  part_id: number;
  part_number: string;
  part_name?: string;
  version: string;
  // Routing step details
  step_number: number;
  process_name: string;
  work_instructions?: string;
  standard_hours?: number;
  // Skill group details
  skill_group_name: string;
  // Machine details (optional)
  machine_name?: string;
}

export interface PlanTaskSearchResult {
  plan_tasks: PlanTaskWithDetails[];
  total_count: number;
  limit: number;
  offset: number;
}

export interface GanttChartData {
  tasks: GanttTask[];
  skill_groups: GanttSkillGroup[];
  time_range: GanttTimeRange;
}

export interface GanttTask {
  id: number;
  name: string;
  start: string;
  end: string;
  skill_group_id: number;
  work_order_id: number;
  part_number: string;
  process_name: string;
  status: string;
  progress: number; // 0.0 to 1.0
  machine_id?: number | null;
  machine_name?: string | null;
}

export interface GanttSkillGroup {
  id: number;
  name: string;
  machines: GanttMachine[];
}

export interface GanttMachine {
  id: number;
  name: string;
  status: string;
}

export interface GanttTimeRange {
  start: string;
  end: string;
}

// Execution Types
export interface ExecutionLog {
  id: number;
  plan_task_id: number;
  machine_id?: number;
  user_id: number;
  event_type: string;
  event_time: string;
  notes?: string;
}

export interface TaskExecutionRequest {
  plan_task_id: number;
  machine_id?: number;
  notes?: string;
}

// Quality Types
export interface QualityInspection {
  id: number;
  plan_task_id: number;
  inspector_user_id: number;
  inspection_type: string;
  status: string;
  result: string;
  notes?: string;
  inspection_date: string;
  created_at: string;
}

export interface CreateQualityInspectionRequest {
  plan_task_id: number;
  inspector_user_id: number;
  inspection_type: string;
  notes?: string;
}

// Routing Types
export interface Routing {
  id: number;
  part_id: number;
  step_number: number;
  process_name: string;
  work_instructions?: string;
  standard_hours?: number;
}

export interface RoutingWithPartInfo {
  id: number;
  part_id: number;
  part_number: string;
  part_name?: string;
  version: string;
  step_number: number;
  process_name: string;
  work_instructions?: string;
  standard_hours?: number;
  skill_group_id?: number;
}

export interface CreateRoutingRequest {
  part_id: number;
  step_number: number;
  process_name: string;
  work_instructions?: string;
  standard_hours?: number;
}

export interface UpdateRoutingRequest {
  process_name?: string;
  work_instructions?: string;
  standard_hours?: number;
}

export interface PartRoutingSteps {
  part_id: number;
  part_number: string;
  part_name?: string;
  version: string;
  routing_steps: RoutingStep[];
}

export interface RoutingStep {
  id: number;
  step_number: number;
  process_name: string;
  work_instructions?: string;
  standard_hours?: number;
}

export interface RoutingQuery {
  part_id?: number;
  process_name?: string;
}

export interface ReorderStepsRequest {
  step_orders: StepOrder[];
}

export interface StepOrder {
  routing_id: number;
  new_step_number: number;
}

export interface CopyRoutingRequest {
  target_part_id: number;
}

// Dashboard Types
export interface DashboardOverview {
  machine_status: {
    available_machines: number;
    in_use_machines: number;
    maintenance_machines: number;
    offline_machines: number;
    total_machines: number;
    utilization_rate: number;
  };
  production_summary: {
    on_time_delivery_rate: number;
    overall_efficiency: number;
    tasks_completed_today: number;
    tasks_in_progress: number;
    tasks_pending: number;
    total_work_orders: number;
  };
  quality_metrics: {
    defect_rate: number;
    failed_inspections: number;
    passed_inspections: number;
    quality_rate: number;
    total_inspections: number;
  };
  work_order_status: {
    cancelled_orders: number;
    completed_orders: number;
    in_progress_orders: number;
    overdue_orders: number;
    pending_orders: number;
    planned_orders: number;
  };
  recent_activities: any[];
}

export interface ProductionSummary {
  total_work_orders: number;
  completed_work_orders: number;
  in_progress_work_orders: number;
  pending_work_orders: number;
  total_tasks: number;
  completed_tasks: number;
  efficiency_rate: number;
}

// Query Parameters
export interface PaginationParams {
  limit?: number;
  offset?: number;
}

export interface DateRangeParams {
  start_date?: string;
  end_date?: string;
}

export interface SearchParams extends PaginationParams, DateRangeParams {
  status?: string;
  user_id?: number;
  project_id?: number;
  part_id?: number;
}

// System Configuration Types
export interface SystemConfig {
  id: number;
  config_key: string;
  config_value: string;
  config_type: string;
  description?: string;
  category: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  updated_by?: number;
}

export interface CreateSystemConfigRequest {
  config_key: string;
  config_value: string;
  config_type: string;
  description?: string;
  category: string;
}

export interface UpdateSystemConfigRequest {
  config_value?: string;
  config_type?: string;
  description?: string;
  category?: string;
  is_active?: boolean;
}

export interface SystemConfigQuery {
  category?: string;
  config_key?: string;
  is_active?: boolean;
}

export interface TypedConfigValue {
  key: string;
  value: string | boolean | number | object;
  description?: string;
}

export interface PlanAssignmentModeConfig {
  mode: 'skill_group' | 'machine';
  enabled: boolean;
}

export interface SetPlanAssignmentModeRequest {
  mode: 'skill_group' | 'machine';
}

// User Machine Bindings
export interface UserMachineBinding {
  id: number;
  user_id: number;
  machine_id: number;
  is_primary: boolean;
  created_at: string;
  updated_at: string;
}

export interface UserMachineBindingWithDetails {
  id: number;
  user_id: number;
  machine_id: number;
  machine_name: string;
  skill_group_name: string;
  machine_status: string;
  is_primary: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateUserMachineBindingRequest {
  machine_id: number;
  is_primary?: boolean;
}

export interface UpdateUserMachineBindingRequest {
  is_primary?: boolean;
}

export interface UserMachineBindingsResponse {
  bindings: UserMachineBindingWithDetails[];
  available_machines: AvailableMachine[];
}

export interface AvailableMachine {
  id: number;
  machine_name: string;
  skill_group_name: string;
  status: string;
  is_bound: boolean;
}

// Auto Work Order Configuration
export interface AutoWorkOrderConfig {
  id: number;
  trigger_type: string;
  project_id: number | null;
  is_enabled: boolean;
  default_quantity: number | null;
  default_due_days: number | null;
  auto_create_plan_tasks: boolean;
  created_by: number;
  created_at: string;
  updated_at: string;
}

export interface CreateAutoWorkOrderConfigRequest {
  trigger_type: string;
  project_id?: number;
  is_enabled: boolean;
  default_quantity?: number;
  default_due_days?: number;
  auto_create_plan_tasks: boolean;
}

export interface UpdateAutoWorkOrderConfigRequest {
  is_enabled?: boolean;
  default_quantity?: number;
  default_due_days?: number;
  auto_create_plan_tasks?: boolean;
}

export interface AutoWorkOrderConfigQuery {
  project_id?: number;
  trigger_type?: string;
  is_enabled?: boolean;
  limit?: number;
  offset?: number;
}

export interface AutoWorkOrderConfigResponse {
  configs: AutoWorkOrderConfig[];
  total_count: number;
}

export interface AutoWorkOrderTriggerEvent {
  trigger_type: string;
  project_id?: number;
  part_id?: number;
  bom_id?: number;
  routing_id?: number;
  quantity?: number;
  user_id: number;
}

export interface AutoWorkOrderResult {
  work_order_id: number;
  plan_task_ids: number[];
  trigger_config_id: number;
  created_at: string;
}

// Data Import Types
export interface ImportJob {
  id: number;
  user_id: number;
  module_type: string;
  file_name: string;
  file_path: string;
  status: string;
  total_rows?: number;
  processed_rows: number;
  success_rows: number;
  error_rows: number;
  error_details?: any;
  created_at: string;
  started_at?: string;
  completed_at?: string;
}

export interface ImportError {
  id: number;
  import_job_id: number;
  row_number: number;
  column_name?: string;
  error_type: string;
  error_message: string;
  row_data?: Record<string, string>;
}

export interface CreateImportJobRequest {
  module_type: string;
  file_name: string;
}

export interface ImportPreviewRequest {
  file_id: string;
  module_type: string;
}

export interface ImportExecuteRequest {
  file_id: string;
  module_type: string;
  options?: ImportOptions;
}

export interface ImportOptions {
  skip_duplicates?: boolean;
  update_existing?: boolean;
  batch_size?: number;
}

export interface ImportPreviewResponse {
  headers: string[];
  sample_data: Record<string, string>[];
  total_rows: number;
  validation_errors: ValidationError[];
}

export interface ValidationError {
  row_number: number;
  column_name?: string;
  error_type: string;
  error_message: string;
  row_data: Record<string, string>;
}

export interface ImportResult {
  import_job_id: number;
  status: string;
  total_rows: number;
  processed_rows: number;
  success_rows: number;
  error_rows: number;
  errors: ImportError[];
  duration_ms?: number;
}

export interface ImportStatusResponse {
  import_job: ImportJob;
  errors: ImportError[];
  progress_percentage: number;
}

export interface UploadResponse {
  import_job_id: number;
  message: string;
}


