import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { TokenManager } from '@/utils/tokenManager';
import { handleApiError, createErrorInterceptor } from '@/utils/errorHandler';
import type {
  ApiResponse,
  LoginRequest,
  CreateUserRequest,
  UpdateProfileRequest,
  ChangePasswordRequest,
  WeeklyTaskSummary,
  LoginResponse,
  User,
  Role,
  SkillGroup,
  CreateRoleRequest,
  RoleDependencyInfo,
  CreateSkillGroupRequest,
  SkillGroupDependencyInfo,
  Project,
  CreateProjectRequest,
  UpdateProjectRequest,
  ProjectStatusUpdate,
  ProjectQuery,
  ProjectSearchResult,
  ProjectStatusStats,
  Part,
  CreatePartRequest,
  Machine,
  CreateMachineRequest,
  WorkOrder,
  CreateWorkOrderRequest,
  PlanTask,
  PlanTaskWithDetails,
  PlanTaskSearchResult,
  CreatePlanTaskRequest,
  UpdatePlanTaskRequest,
  ReschedulePlanTaskRequest,
  CreatePlanTasksFromWorkOrderRequest,
  GanttChartData,
  ExecutionLog,
  TaskExecutionRequest,
  QualityInspection,
  CreateQualityInspectionRequest,
  DashboardOverview,
  ProductionSummary,
  SearchParams,
  ProjectBom,
  CreateProjectBomRequest,
  Routing,
  RoutingWithPartInfo,
  CreateRoutingRequest,
  UpdateRoutingRequest,
  PartRoutingSteps,
  RoutingQuery,
  ReorderStepsRequest,
  CopyRoutingRequest,
  SystemConfig,
  CreateSystemConfigRequest,
  UpdateSystemConfigRequest,
  SystemConfigQuery,
  TypedConfigValue,
  PlanAssignmentModeConfig,
  SetPlanAssignmentModeRequest,
  UserMachineBinding,
  UserMachineBindingWithDetails,
  CreateUserMachineBindingRequest,
  UpdateUserMachineBindingRequest,
  UserMachineBindingsResponse,
  AutoWorkOrderConfig,
  AutoWorkOrderConfigQuery,
  AutoWorkOrderConfigResponse,
  CreateAutoWorkOrderConfigRequest,
  UpdateAutoWorkOrderConfigRequest,
  AutoWorkOrderTriggerEvent,
  AutoWorkOrderResult,
  ImportJob,
  ImportPreviewResponse,
  ImportResult,
  ImportStatusResponse,
  UploadResponse,
} from '@/types/api';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    // 获取API基础URL
    const getApiBaseUrl = () => {
      // 优先使用环境变量
      if (import.meta.env.VITE_API_URL) {
        return import.meta.env.VITE_API_URL;
      }

      // 开发模式和生产模式都使用相对路径代理
      // 开发模式：Vite dev server 代理到后端
      // 生产模式：代理服务器代理到后端
      return '/api';
    };

    this.client = axios.create({
      baseURL: getApiBaseUrl(),
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      async (config) => {
        // 尝试自动刷新token（如果需要）
        const token = await TokenManager.autoRefreshToken(async () => {
          const response = await this.client.post<{ token: string; message: string }>('/auth/refresh');
          return response.data.token;
        });

        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        return response;
      },
      createErrorInterceptor({
        showMessage: true,
        showNotification: false,
        redirectOnAuth: true,
      })
    );
  }

  // Authentication APIs
  async login(data: LoginRequest): Promise<LoginResponse> {
    const response = await this.client.post<LoginResponse>('/auth/login', data);
    return response.data;
  }

  async getCurrentUser(): Promise<User> {
    const response = await this.client.get<User>('/auth/me');
    return response.data;
  }

  async refreshToken(): Promise<{ token: string; message: string }> {
    const response = await this.client.post<{ token: string; message: string }>('/auth/refresh');
    return response.data;
  }

  // User Management APIs
  async getUsers(params?: SearchParams): Promise<User[]> {
    const response = await this.client.get<{ users: User[] }>('/users', { params });
    return response.data.users;
  }

  async getUserById(id: number): Promise<User> {
    const response = await this.client.get<{ user: User }>(`/users/${id}`);
    return response.data.user;
  }

  async createUser(data: CreateUserRequest): Promise<User> {
    const response = await this.client.post<{ message: string; user: User }>('/auth/users', data);
    return response.data.user;
  }

  async updateUserStatus(id: number, isActive: boolean): Promise<void> {
    await this.client.post(`/users/${id}/status`, { is_active: isActive });
  }

  async updateUserRoles(id: number, roleIds: number[]): Promise<void> {
    await this.client.post(`/users/${id}/roles`, { role_ids: roleIds });
  }

  async updateUserSkills(id: number, skillGroupIds: number[]): Promise<void> {
    await this.client.post(`/users/${id}/skills`, { skill_group_ids: skillGroupIds });
  }

  async deleteUser(id: number): Promise<void> {
    await this.client.delete(`/users/${id}`);
  }

  // User profile management
  async updateProfile(data: UpdateProfileRequest): Promise<User> {
    const response = await this.client.put<{ message: string; user: User }>('/user/profile', data);
    return response.data.user;
  }

  async changePassword(data: ChangePasswordRequest): Promise<void> {
    await this.client.put('/user/password', data);
  }

  // Dashboard data
  async getWeeklyTaskStats(): Promise<WeeklyTaskSummary> {
    const response = await this.client.get<WeeklyTaskSummary>('/dashboard/weekly-task-stats');
    return response.data;
  }

  // Role and Skill Group APIs
  async getRoles(): Promise<{ roles: Role[] }> {
    const response = await this.client.get<{ roles: Role[] }>('/auth/roles');
    return response.data;
  }

  async getUserPermissions(): Promise<any> {
    const response = await this.client.get('/user/permissions');
    return response.data;
  }

  async getRolePermissions(roleId: number): Promise<any> {
    const response = await this.client.get(`/roles/${roleId}/permissions`);
    return response.data;
  }

  async getSkillGroups(): Promise<SkillGroup[]> {
    const response = await this.client.get<{ skill_groups: SkillGroup[] }>('/auth/skill-groups');
    return response.data.skill_groups;
  }

  // Role Management APIs
  async createRole(data: CreateRoleRequest): Promise<Role> {
    const response = await this.client.post<{ message: string; role: Role }>('/roles', data);
    return response.data.role;
  }

  async updateRole(id: number, data: Partial<CreateRoleRequest>): Promise<Role> {
    const response = await this.client.put<{ message: string; role: Role }>(`/roles/${id}`, data);
    return response.data.role;
  }

  async deleteRole(id: number, replacementRoleId?: number): Promise<void> {
    const body = replacementRoleId ? { replacement_role_id: replacementRoleId } : {};
    await this.client.delete(`/roles/${id}`, { data: body });
  }

  async checkRoleDependencies(id: number): Promise<RoleDependencyInfo> {
    const response = await this.client.get<RoleDependencyInfo>(`/roles/${id}/dependencies`);
    return response.data;
  }

  // Skill Group Management APIs
  async createSkillGroup(data: CreateSkillGroupRequest): Promise<SkillGroup> {
    const response = await this.client.post<{ message: string; skill_group: SkillGroup }>('/skill-groups', data);
    return response.data.skill_group;
  }

  async updateSkillGroup(id: number, data: Partial<CreateSkillGroupRequest>): Promise<SkillGroup> {
    const response = await this.client.put<{ message: string; skill_group: SkillGroup }>(`/skill-groups/${id}`, data);
    return response.data.skill_group;
  }

  async deleteSkillGroup(id: number, replacementSkillGroupId?: number): Promise<void> {
    const body: any = {};
    if (replacementSkillGroupId) {
      body.replacement_skill_group_id = replacementSkillGroupId;
    }
    await this.client.delete(`/skill-groups/${id}`, {
      headers: { 'Content-Type': 'application/json' },
      data: body
    });
  }

  async checkSkillGroupDependencies(id: number): Promise<SkillGroupDependencyInfo> {
    const response = await this.client.get<SkillGroupDependencyInfo>(`/skill-groups/${id}/dependencies`);
    return response.data;
  }

  // Project Management APIs
  async getProjects(params?: SearchParams): Promise<Project[]> {
    const response = await this.client.get<{ projects: Project[] }>('/projects', { params });
    return response.data.projects;
  }

  async searchProjects(query: ProjectQuery): Promise<ProjectSearchResult> {
    const response = await this.client.get<ProjectSearchResult>('/projects', { params: query });
    return response.data;
  }

  async getProjectById(id: number): Promise<Project> {
    const response = await this.client.get<{ project: Project }>(`/projects/${id}`);
    return response.data.project;
  }

  async createProject(data: CreateProjectRequest): Promise<Project> {
    const response = await this.client.post<{ message: string; project: Project }>('/projects', data);
    return response.data.project;
  }

  async updateProject(id: number, data: UpdateProjectRequest): Promise<Project> {
    const response = await this.client.put<{ message: string; project: Project }>(`/projects/${id}`, data);
    return response.data.project;
  }

  async updateProjectStatus(id: number, statusUpdate: ProjectStatusUpdate): Promise<Project> {
    const response = await this.client.post<{ message: string; project: Project }>(`/projects/${id}/status`, statusUpdate);
    return response.data.project;
  }

  async deleteProject(id: number): Promise<void> {
    await this.client.delete(`/projects/${id}`);
  }

  async getProjectCompletionStatus(id: number): Promise<any> {
    const response = await this.client.get(`/projects/${id}/completion-status`);
    return response.data.completion_status;
  }

  async getProjectStatusStats(): Promise<ProjectStatusStats[]> {
    const response = await this.client.get<{ status_stats: ProjectStatusStats[] }>('/projects/status/stats');
    return response.data.status_stats;
  }

  // Parts Management APIs
  async getParts(params?: SearchParams & { project_id?: number }): Promise<Part[]> {
    const response = await this.client.get<{ data: { parts: Part[] } }>('/parts', { params });
    return response.data.data.parts;
  }

  async getPartById(id: number): Promise<Part> {
    const response = await this.client.get<{ part: Part }>(`/parts/${id}`);
    return response.data.part;
  }

  async createPart(data: CreatePartRequest): Promise<Part> {
    const response = await this.client.post<{ message: string; part: Part }>('/parts', data);
    return response.data.part;
  }

  async updatePart(id: number, data: Partial<CreatePartRequest>): Promise<Part> {
    const response = await this.client.put<{ message: string; part: Part }>(`/parts/${id}`, data);
    return response.data.part;
  }

  async deletePart(id: number): Promise<void> {
    await this.client.delete(`/parts/${id}`);
  }

  async getPartProjects(id: number): Promise<Project[]> {
    const response = await this.client.get<{ projects: Project[] }>(`/parts/${id}/projects`);
    return response.data.projects;
  }

  // Machine Management APIs
  async getMachines(params?: SearchParams): Promise<Machine[]> {
    const response = await this.client.get<{ data: { machines: Machine[] } }>('/machines', { params });
    return response.data.data.machines;
  }

  async getMachineById(id: number): Promise<Machine> {
    const response = await this.client.get<{ machine: Machine }>(`/machines/${id}`);
    return response.data.machine;
  }

  // 获取设备在指定时间范围内的任务
  async getMachineTasksByDateRange(machineId: number, startDate: string, endDate: string): Promise<PlanTaskWithDetails[]> {
    const response = await this.client.get<{ data: { tasks: PlanTaskWithDetails[] } }>(
      `/machines/${machineId}/tasks`,
      {
        params: {
          start_date: startDate,
          end_date: endDate,
        }
      }
    );
    return response.data.data.tasks;
  }

  async createMachine(data: CreateMachineRequest): Promise<Machine> {
    const response = await this.client.post<{ message: string; machine: Machine }>('/machines', data);
    return response.data.machine;
  }

  async updateMachine(id: number, data: Partial<CreateMachineRequest>): Promise<Machine> {
    const response = await this.client.put<{ message: string; machine: Machine }>(`/machines/${id}`, data);
    return response.data.machine;
  }

  async deleteMachine(id: number): Promise<void> {
    await this.client.delete(`/machines/${id}`);
  }

  // Work Order APIs
  async getWorkOrders(params?: SearchParams): Promise<WorkOrder[]> {
    const response = await this.client.get<{ data: { work_orders: WorkOrder[] } }>('/work-orders', { params });
    return response.data.data.work_orders;
  }

  async getWorkOrderById(id: number): Promise<WorkOrder> {
    const response = await this.client.get<{ work_order: WorkOrder }>(`/work-orders/${id}`);
    return response.data.work_order;
  }

  async createWorkOrder(data: CreateWorkOrderRequest): Promise<WorkOrder> {
    const response = await this.client.post<{ message: string; work_order: WorkOrder }>('/work-orders', data);
    return response.data.work_order;
  }

  async updateWorkOrder(id: number, data: Partial<CreateWorkOrderRequest>): Promise<WorkOrder> {
    const response = await this.client.put<{ message: string; work_order: WorkOrder }>(`/work-orders/${id}`, data);
    return response.data.work_order;
  }

  async deleteWorkOrder(id: number): Promise<void> {
    await this.client.delete(`/work-orders/${id}`);
  }

  // Plan Task APIs
  async getPlanTasks(params?: SearchParams): Promise<PlanTaskWithDetails[]> {
    const response = await this.client.get<PlanTaskSearchResult>('/plan-tasks', { params });
    return response.data.plan_tasks;
  }

  async getPlanTaskById(id: number): Promise<PlanTaskWithDetails> {
    const response = await this.client.get<{ plan_task: PlanTaskWithDetails }>(`/plan-tasks/${id}`);
    return response.data.plan_task;
  }

  async createPlanTask(data: CreatePlanTaskRequest): Promise<PlanTask> {
    const response = await this.client.post<{ message: string; plan_task: PlanTask }>('/plan-tasks', data);
    return response.data.plan_task;
  }

  async updatePlanTask(id: number, data: UpdatePlanTaskRequest): Promise<PlanTask> {
    const response = await this.client.put<{ message: string; plan_task: PlanTask }>(`/plan-tasks/${id}`, data);
    return response.data.plan_task;
  }

  async deletePlanTask(id: number): Promise<void> {
    await this.client.delete(`/plan-tasks/${id}`);
  }

  async updatePlanTaskStatus(id: number, status: string): Promise<void> {
    await this.client.post(`/plan-tasks/${id}/status`, { status });
  }

  async reschedulePlanTask(id: number, data: ReschedulePlanTaskRequest): Promise<PlanTask> {
    const response = await this.client.post<{ message: string; plan_task: PlanTask }>(`/plan-tasks/${id}/reschedule`, data);
    return response.data.plan_task;
  }

  async createPlanTasksFromWorkOrder(workOrderId: number, data: CreatePlanTasksFromWorkOrderRequest): Promise<PlanTask[]> {
    const response = await this.client.post<{ message: string; plan_tasks: PlanTask[] }>(`/work-orders/${workOrderId}/plan-tasks`, data);
    return response.data.plan_tasks;
  }

  async batchCreatePlanTasks(data: CreatePlanTaskRequest[]): Promise<{ plan_tasks: PlanTask[]; count: number; message: string }> {
    const response = await this.client.post<{ message: string; plan_tasks: PlanTask[]; count: number }>('/plan-tasks/batch-create', data);
    return response.data;
  }

  async getGanttChartData(startDate: string, endDate: string): Promise<GanttChartData> {
    const response = await this.client.get<GanttChartData>('/planning/gantt', {
      params: { start_date: startDate, end_date: endDate }
    });
    return response.data;
  }

  // Execution APIs
  async getExecutionLogs(params?: SearchParams): Promise<ExecutionLog[]> {
    const response = await this.client.get<{ data: { logs: ExecutionLog[] } }>('/execution/logs', { params });
    return response.data.data.logs;
  }

  async startTask(data: TaskExecutionRequest): Promise<void> {
    await this.client.post('/execution/tasks/start', data);
  }

  async completeTask(data: TaskExecutionRequest): Promise<void> {
    await this.client.post('/execution/tasks/complete', data);
  }

  async pauseTask(data: TaskExecutionRequest): Promise<void> {
    await this.client.post('/execution/tasks/pause', data);
  }

  async resumeTask(data: TaskExecutionRequest): Promise<void> {
    await this.client.post('/execution/tasks/resume', data);
  }

  async getMySkillGroupTasks(): Promise<PlanTaskWithDetails[]> {
    const response = await this.client.get<{ tasks: PlanTaskWithDetails[] }>('/execution/my-tasks');
    return response.data.tasks;
  }

  async getShopFloorDashboard(skillGroupId?: number): Promise<any> {
    const params = skillGroupId ? { skill_group_id: skillGroupId } : {};
    const response = await this.client.get('/execution/shop-floor-dashboard', { params });
    return response.data;
  }

  async getOperatorDashboard(userId: number): Promise<any> {
    const response = await this.client.get(`/dashboard/operator/${userId}`);
    return response.data;
  }

  async getPlannerDashboard(): Promise<any> {
    const response = await this.client.get('/dashboard/planner');
    return response.data;
  }

  // Quality APIs
  async getQualityInspections(params?: SearchParams): Promise<QualityInspection[]> {
    const response = await this.client.get<{ data: { inspections: QualityInspection[] } }>('/quality/inspections', { params });
    return response.data.data.inspections;
  }

  async createQualityInspection(data: CreateQualityInspectionRequest): Promise<QualityInspection> {
    const response = await this.client.post<{ message: string; inspection: QualityInspection }>('/quality/inspections', data);
    return response.data.inspection;
  }

  // BOM APIs
  async getProjectBom(projectId: number): Promise<ProjectBom[]> {
    const response = await this.client.get<{ bom_items: ProjectBom[] }>(`/projects/${projectId}/bom`);
    return response.data.bom_items;
  }

  async getAllProjectBoms(): Promise<ProjectBom[]> {
    // Get all projects first, then get their BOMs
    const projects = await this.getProjects();
    const allBoms: ProjectBom[] = [];

    for (const project of projects) {
      try {
        const boms = await this.getProjectBom(project.id);
        // Add project name to each BOM item for display
        const bomsWithProjectName = boms.map(bom => ({
          ...bom,
          project_name: project.project_name
        }));
        allBoms.push(...bomsWithProjectName);
      } catch (error) {
        console.warn(`Failed to fetch BOM for project ${project.id}:`, error);
      }
    }

    return allBoms;
  }

  async createProjectBom(projectId: number, data: CreateProjectBomRequest): Promise<ProjectBom> {
    const response = await this.client.post<{ message: string; bom_item: ProjectBom }>(`/projects/${projectId}/bom`, data);
    return response.data.bom_item;
  }

  async updateProjectBom(bomId: number, data: Partial<CreateProjectBomRequest>): Promise<ProjectBom> {
    const response = await this.client.put<{ message: string; bom_item: ProjectBom }>(`/bom/${bomId}`, data);
    return response.data.bom_item;
  }

  async deleteProjectBom(bomId: number): Promise<void> {
    await this.client.delete(`/bom/${bomId}`);
  }

  // Dashboard APIs
  async getDashboardOverview(): Promise<DashboardOverview> {
    const response = await this.client.get<DashboardOverview>('/dashboard/overview');
    return response.data;
  }

  async getProductionSummary(): Promise<ProductionSummary> {
    const response = await this.client.get<ProductionSummary>('/dashboard/production-summary');
    return response.data;
  }

  // Routing APIs
  async getRoutings(params?: RoutingQuery): Promise<RoutingWithPartInfo[]> {
    const response = await this.client.get<{ routings: RoutingWithPartInfo[] }>('/routings', { params });
    return response.data.routings;
  }

  async getRoutingById(id: number): Promise<RoutingWithPartInfo> {
    const response = await this.client.get<{ routing: RoutingWithPartInfo }>(`/routings/${id}`);
    return response.data.routing;
  }

  async createRouting(data: CreateRoutingRequest): Promise<Routing> {
    const response = await this.client.post<{ message: string; routing: Routing }>('/routings', data);
    return response.data.routing;
  }

  async updateRouting(id: number, data: UpdateRoutingRequest): Promise<Routing> {
    const response = await this.client.put<{ message: string; routing: Routing }>(`/routings/${id}`, data);
    return response.data.routing;
  }

  async deleteRouting(id: number): Promise<void> {
    await this.client.delete(`/routings/${id}`);
  }

  async getPartRouting(partId: number): Promise<PartRoutingSteps> {
    const response = await this.client.get<{ part_routing: PartRoutingSteps }>(`/parts/${partId}/routing`);
    return response.data.part_routing;
  }

  async reorderRoutingSteps(partId: number, data: ReorderStepsRequest): Promise<void> {
    await this.client.post(`/parts/${partId}/routing/reorder`, data);
  }

  async copyRouting(sourcePartId: number, data: CopyRoutingRequest): Promise<void> {
    await this.client.post(`/parts/${sourcePartId}/routing/copy`, data);
  }

  // System Configuration Methods
  async getSystemConfigs(query?: SystemConfigQuery): Promise<{ configs: SystemConfig[]; total: number }> {
    const response = await this.client.get<{ configs: SystemConfig[]; total: number }>('/system/configs', {
      params: query
    });
    return response.data;
  }

  async getConfigsByCategory(): Promise<Record<string, SystemConfig[]>> {
    const response = await this.client.get<Record<string, SystemConfig[]>>('/system/configs/categories');
    return response.data;
  }

  async getConfigByKey(key: string): Promise<TypedConfigValue> {
    const response = await this.client.get<TypedConfigValue>(`/system/configs/${key}`);
    return response.data;
  }

  async createConfig(data: CreateSystemConfigRequest): Promise<SystemConfig> {
    const response = await this.client.post<SystemConfig>('/system/configs', data);
    return response.data;
  }

  async updateConfig(key: string, data: UpdateSystemConfigRequest): Promise<SystemConfig> {
    const response = await this.client.put<SystemConfig>(`/system/configs/${key}`, data);
    return response.data;
  }

  async getPlanAssignmentMode(): Promise<PlanAssignmentModeConfig> {
    const response = await this.client.get<PlanAssignmentModeConfig>('/system/plan-assignment-mode');
    return response.data;
  }

  async setPlanAssignmentMode(data: SetPlanAssignmentModeRequest): Promise<{ message: string; config: SystemConfig }> {
    const response = await this.client.post<{ message: string; config: SystemConfig }>('/system/plan-assignment-mode', data);
    return response.data;
  }

  // User machine bindings
  async getUserMachineBindings(): Promise<UserMachineBindingsResponse> {
    const response = await this.client.get('/user/machine-bindings');
    return response.data;
  }

  async createMachineBinding(data: CreateUserMachineBindingRequest): Promise<UserMachineBinding> {
    const response = await this.client.post('/user/machine-bindings', data);
    return response.data.binding;
  }

  async updateMachineBinding(bindingId: number, data: UpdateUserMachineBindingRequest): Promise<UserMachineBinding> {
    const response = await this.client.put(`/user/machine-bindings/${bindingId}`, data);
    return response.data.binding;
  }

  async deleteMachineBinding(bindingId: number): Promise<void> {
    await this.client.delete(`/user/machine-bindings/${bindingId}`);
  }

  async getUserPrimaryMachine(): Promise<UserMachineBindingWithDetails | null> {
    const response = await this.client.get('/user/primary-machine');
    return response.data.primary_machine;
  }

  // Auto work order configuration
  async getAutoWorkOrderConfigs(query?: AutoWorkOrderConfigQuery): Promise<AutoWorkOrderConfigResponse> {
    const response = await this.client.get('/auto-work-orders/configs', { params: query });
    return response.data;
  }

  async createAutoWorkOrderConfig(data: CreateAutoWorkOrderConfigRequest): Promise<AutoWorkOrderConfig> {
    const response = await this.client.post('/auto-work-orders/configs', data);
    return response.data.config;
  }

  async updateAutoWorkOrderConfig(configId: number, data: UpdateAutoWorkOrderConfigRequest): Promise<AutoWorkOrderConfig> {
    const response = await this.client.put(`/auto-work-orders/configs/${configId}`, data);
    return response.data.config;
  }

  async deleteAutoWorkOrderConfig(configId: number): Promise<void> {
    await this.client.delete(`/auto-work-orders/configs/${configId}`);
  }

  async triggerAutoWorkOrderCreation(event: AutoWorkOrderTriggerEvent): Promise<AutoWorkOrderResult[]> {
    const response = await this.client.post('/auto-work-orders/trigger', event);
    return response.data.results;
  }

  // Data Import Methods
  async uploadImportFile(file: File, moduleType: string): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('module_type', moduleType);

    const response = await this.client.post('/import/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async previewImportData(importJobId: number): Promise<ImportPreviewResponse> {
    const response = await this.client.get(`/import/${importJobId}/preview`);
    return response.data;
  }

  async executeImport(importJobId: number, moduleType: string, options?: any): Promise<ImportResult> {
    const response = await this.client.post(`/import/${importJobId}/execute`, {
      file_id: importJobId.toString(),
      module_type: moduleType,
      options,
    });
    return response.data;
  }

  async getImportStatus(importJobId: number): Promise<ImportStatusResponse> {
    const response = await this.client.get(`/import/${importJobId}/status`);
    return response.data;
  }

  async downloadImportTemplate(moduleType: string): Promise<Blob> {
    const response = await this.client.get(`/import/template?module_type=${moduleType}`, {
      responseType: 'blob',
    });
    return response.data;
  }

  async getImportHistory(params?: { limit?: number; offset?: number; module_type?: string }): Promise<ImportJob[]> {
    const response = await this.client.get('/import/history', { params });
    return response.data;
  }

  // Shift Management APIs
  async getShiftTemplates(activeOnly: boolean = true): Promise<any> {
    const response = await this.client.get('/shifts/templates', {
      params: { active_only: activeOnly }
    });
    return response.data;
  }

  async createShiftTemplate(data: any): Promise<any> {
    const response = await this.client.post('/shifts/templates', data);
    return response.data;
  }

  async getShiftTemplateById(id: number): Promise<any> {
    const response = await this.client.get(`/shifts/templates/${id}`);
    return response.data;
  }

  async updateShiftTemplate(id: number, data: any): Promise<any> {
    const response = await this.client.put(`/shifts/templates/${id}`, data);
    return response.data;
  }

  async deleteShiftTemplate(id: number): Promise<any> {
    const response = await this.client.delete(`/shifts/templates/${id}`);
    return response.data;
  }

  async getPlanGroups(activeOnly: boolean = true): Promise<any> {
    const response = await this.client.get('/shifts/plan-groups', {
      params: { active_only: activeOnly }
    });
    return response.data;
  }

  async createPlanGroup(data: any): Promise<any> {
    const response = await this.client.post('/shifts/plan-groups', data);
    return response.data;
  }

  async createShiftInstance(data: any): Promise<any> {
    const response = await this.client.post('/shifts/instances', data);
    return response.data;
  }

  async checkShiftConflicts(data: any): Promise<any> {
    const response = await this.client.post('/shifts/conflicts/check', data);
    return response.data;
  }
}

export const apiClient = new ApiClient();
