@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
@import './styles/operator.css';

/* Custom styles */
.ant-layout {
  min-height: 100vh;
}

.ant-layout-sider {
  box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
}

.ant-menu-item-selected {
  background-color: #e6f7ff !important;
}

.ant-menu-item-selected a {
  color: #1890ff !important;
}

.page-header {
  background: #fff;
  padding: 16px 24px;
  margin-bottom: 16px;
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

.page-content {
  background: #fff;
  padding: 24px;
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
}

.stat-card.primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
}

.stat-card.success {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
}

.stat-card.warning {
  background: linear-gradient(135deg, #faad14 0%, #d48806 100%);
}

.stat-card.danger {
  background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  flex-direction: column;
}

.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-form {
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 400px;
}

.login-title {
  text-align: center;
  margin-bottom: 32px;
  color: #262626;
  font-size: 24px;
  font-weight: 600;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.active {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-badge.inactive {
  background-color: #fff2e8;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-badge.pending {
  background-color: #f0f0f0;
  color: #595959;
  border: 1px solid #d9d9d9;
}

.status-badge.in-progress {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.status-badge.completed {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-badge.cancelled {
  background-color: #fff1f0;
  color: #ff4d4f;
  border: 1px solid #ffa39e;
}

/* 高亮任务行样式 */
.highlighted-task-row {
  background-color: #fff7e6 !important;
  border: 2px solid #ffa940 !important;
  animation: highlight-pulse 2s ease-in-out;
}

.highlighted-task-row:hover {
  background-color: #fff1e6 !important;
}

@keyframes highlight-pulse {
  0% {
    background-color: #fff7e6;
    box-shadow: 0 0 0 0 rgba(255, 169, 64, 0.7);
  }
  50% {
    background-color: #ffe7ba;
    box-shadow: 0 0 0 10px rgba(255, 169, 64, 0);
  }
  100% {
    background-color: #fff7e6;
    box-shadow: 0 0 0 0 rgba(255, 169, 64, 0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  /* 移动端布局调整 */
  .ant-layout-sider {
    position: fixed !important;
    height: 100vh;
    z-index: 999;
  }

  .ant-layout-content {
    margin-left: 0 !important;
  }

  .page-content {
    margin: 8px;
    padding: 12px;
    border-radius: 8px;
  }

  .page-header {
    margin: 8px;
    padding: 12px 16px;
    border-radius: 8px;
  }

  .login-form {
    margin: 16px;
    padding: 24px;
  }

  /* 移动端表格优化 */
  .ant-table-wrapper {
    overflow-x: auto;
  }

  .ant-table {
    min-width: 600px;
  }

  .ant-table-thead > tr > th {
    padding: 8px 4px;
    font-size: 12px;
  }

  .ant-table-tbody > tr > td {
    padding: 8px 4px;
    font-size: 12px;
  }

  /* 移动端按钮优化 */
  .ant-btn {
    min-height: 40px;
    padding: 8px 16px;
  }

  .ant-btn-sm {
    min-height: 32px;
    padding: 4px 8px;
  }

  .ant-btn-lg {
    min-height: 48px;
    padding: 12px 24px;
  }

  /* 移动端表单优化 */
  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-input,
  .ant-select-selector,
  .ant-picker {
    min-height: 40px;
    font-size: 16px; /* 防止iOS缩放 */
  }

  .ant-input-lg,
  .ant-select-lg .ant-select-selector,
  .ant-picker-lg {
    min-height: 48px;
    font-size: 16px;
  }

  /* 移动端模态框优化 */
  .ant-modal {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }

  .ant-modal-content {
    border-radius: 12px;
  }

  .ant-modal-body {
    padding: 16px;
  }

  /* 移动端卡片优化 */
  .ant-card {
    border-radius: 12px;
    margin-bottom: 12px;
  }

  .ant-card-body {
    padding: 16px;
  }

  .ant-card-head {
    padding: 0 16px;
    min-height: 48px;
  }

  /* 移动端分页优化 */
  .ant-pagination {
    text-align: center;
  }

  .ant-pagination-options {
    display: none;
  }

  .ant-pagination-jump-prev,
  .ant-pagination-jump-next {
    display: none;
  }

  /* 移动端间距调整 */
  .ant-space-item {
    margin-bottom: 8px;
  }

  .ant-row {
    margin-left: -8px;
    margin-right: -8px;
  }

  .ant-col {
    padding-left: 8px;
    padding-right: 8px;
  }

  /* 移动端隐藏桌面端组件 */
  .desktop-only {
    display: none !important;
  }
}

/* 桌面端隐藏移动端组件 */
@media (min-width: 769px) {
  .mobile-only {
    display: none !important;
  }
}
