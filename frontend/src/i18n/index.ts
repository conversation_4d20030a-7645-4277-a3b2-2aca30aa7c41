/**
 * 国际化配置
 */

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// 导入语言资源
import zhCN from './locales/zh-CN.json';
import enUS from './locales/en-US.json';

const resources = {
  'zh-CN': {
    translation: zhCN,
  },
  'en-US': {
    translation: enUS,
  },
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'zh-CN', // 默认语言
    fallbackLng: 'zh-CN', // 回退语言
    
    interpolation: {
      escapeValue: false, // React已经默认转义了
    },
    
    // 调试模式（生产环境应设为false）
    debug: process.env.NODE_ENV === 'development',
    
    // 命名空间
    defaultNS: 'translation',
    
    // 检测用户语言的选项
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
    },
  });

export default i18n;
