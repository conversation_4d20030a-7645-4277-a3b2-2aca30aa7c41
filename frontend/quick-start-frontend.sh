#!/bin/bash

# MES系统前端快速启动脚本
# 自动检测并启动最合适的模式

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${GREEN}=== MES前端快速启动 ===${NC}"

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 设置端口 (避免使用受限端口)
FRONTEND_PORT=${FRONTEND_PORT:-3080}

# 检查是否有构建文件
if [ -d "dist" ] && [ -f "dist/index.html" ]; then
    echo -e "${GREEN}发现生产构建文件，启动生产模式...${NC}"
    echo -e "${BLUE}访问地址: http://localhost:$FRONTEND_PORT${NC}"
    echo -e "${YELLOW}按 Ctrl+C 停止服务${NC}"
    echo ""

    # 使用Python启动静态文件服务器
    if command -v python3 >/dev/null 2>&1; then
        cd dist
        exec python3 -m http.server $FRONTEND_PORT
    elif command -v python >/dev/null 2>&1; then
        cd dist
        exec python -m SimpleHTTPServer $FRONTEND_PORT
    else
        echo -e "${YELLOW}Python未安装，尝试使用npx serve...${NC}"
        exec npx serve dist -l $FRONTEND_PORT
    fi
else
    echo -e "${YELLOW}未发现构建文件，启动开发模式...${NC}"
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}安装依赖...${NC}"
        npm install
    fi
    
    # 设置开发端口
    DEV_PORT=${VITE_PORT:-3000}
    export VITE_PORT=$DEV_PORT

    echo -e "${GREEN}启动Vite开发服务器...${NC}"
    echo -e "${BLUE}访问地址: http://localhost:$DEV_PORT${NC}"
    echo -e "${YELLOW}按 Ctrl+C 停止服务${NC}"
    echo ""

    exec npm run dev
fi
