#!/bin/bash

# MES系统前端启动脚本
# 支持开发模式和生产模式

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
MES系统前端启动脚本

用法: $0 [模式] [选项]

模式:
    dev         开发模式 (默认) - 使用Vite开发服务器
    prod        生产模式 - 使用静态文件服务器
    build       构建生产版本
    preview     预览生产构建

选项:
    -p, --port PORT     设置端口 (开发模式默认: 3000, 生产模式默认: 8080)
    -h, --host HOST     设置主机 (默认: localhost)
    -o, --open          自动打开浏览器
    --help              显示此帮助信息

示例:
    $0                  # 开发模式启动
    $0 dev              # 开发模式启动
    $0 dev -p 3001      # 开发模式，指定端口3001
    $0 prod             # 生产模式启动
    $0 prod -p 8080     # 生产模式，指定端口8080
    $0 build            # 构建生产版本
    $0 preview          # 预览生产构建

EOF
}

# 检查Node.js和npm
check_dependencies() {
    if ! command -v node >/dev/null 2>&1; then
        log_error "Node.js未安装，请先安装Node.js"
        exit 1
    fi
    
    if ! command -v npm >/dev/null 2>&1; then
        log_error "npm未安装，请先安装npm"
        exit 1
    fi
    
    log_info "Node.js版本: $(node --version)"
    log_info "npm版本: $(npm --version)"
}

# 检查依赖包
check_node_modules() {
    if [ ! -d "node_modules" ]; then
        log_warning "node_modules不存在，正在安装依赖..."
        npm install
        log_success "依赖安装完成"
    fi
}

# 开发模式启动
start_dev() {
    local port=$1
    local host=$2
    local open_browser=$3
    
    log_info "启动开发模式..."
    
    check_dependencies
    check_node_modules
    
    # 设置环境变量
    export VITE_PORT=${port:-3000}
    export VITE_HOST=${host:-localhost}

    # 设置API代理目标
    if [ -n "$BACKEND_PORT" ]; then
        export VITE_API_PROXY_TARGET="http://localhost:$BACKEND_PORT"
    elif [ -n "$MES_PORT" ]; then
        export VITE_API_PROXY_TARGET="http://localhost:$MES_PORT"
    fi

    log_info "开发服务器配置:"
    log_info "  - 主机: $VITE_HOST"
    log_info "  - 端口: $VITE_PORT"
    log_info "  - 热重载: 启用"
    log_info "  - 源码映射: 启用"
    if [ -n "$VITE_API_PROXY_TARGET" ]; then
        log_info "  - API代理: $VITE_API_PROXY_TARGET"
    fi
    echo ""
    
    log_success "启动Vite开发服务器..."
    log_info "访问地址: http://$VITE_HOST:$VITE_PORT"
    log_warning "按 Ctrl+C 停止服务"
    echo ""
    
    # 构建npm run dev命令
    local cmd="npm run dev"
    if [ "$open_browser" = "true" ]; then
        cmd="$cmd -- --open"
    fi
    
    # 启动开发服务器
    exec $cmd
}

# 构建生产版本
build_prod() {
    log_info "构建生产版本..."
    
    check_dependencies
    check_node_modules
    
    # 清理旧的构建文件
    if [ -d "dist" ]; then
        log_info "清理旧的构建文件..."
        rm -rf dist
    fi
    
    log_info "开始构建..."

    # 设置API URL环境变量
    if [ -n "$BACKEND_PORT" ]; then
        export VITE_API_URL="http://localhost:$BACKEND_PORT/api"
        log_info "API URL: $VITE_API_URL"
    elif [ -n "$MES_PORT" ]; then
        export VITE_API_URL="http://localhost:$MES_PORT/api"
        log_info "API URL: $VITE_API_URL"
    fi

    npm run build
    
    if [ -d "dist" ]; then
        log_success "构建完成！"
        log_info "构建文件位置: $(pwd)/dist"
        
        # 显示构建文件大小
        if command -v du >/dev/null 2>&1; then
            local size=$(du -sh dist 2>/dev/null | cut -f1)
            log_info "构建文件大小: $size"
        fi
    else
        log_error "构建失败，dist目录不存在"
        exit 1
    fi
}

# 预览生产构建
preview_prod() {
    local port=$1
    local host=$2
    local open_browser=$3
    
    log_info "预览生产构建..."
    
    check_dependencies
    check_node_modules
    
    # 检查构建文件
    if [ ! -d "dist" ]; then
        log_warning "构建文件不存在，正在构建..."
        build_prod
    fi
    
    # 设置环境变量
    export VITE_PREVIEW_PORT=${port:-4173}
    export VITE_PREVIEW_HOST=${host:-localhost}
    
    log_info "预览服务器配置:"
    log_info "  - 主机: $VITE_PREVIEW_HOST"
    log_info "  - 端口: $VITE_PREVIEW_PORT"
    log_info "  - 模式: 生产预览"
    echo ""
    
    log_success "启动预览服务器..."
    log_info "访问地址: http://$VITE_PREVIEW_HOST:$VITE_PREVIEW_PORT"
    log_warning "按 Ctrl+C 停止服务"
    echo ""
    
    # 构建npm run preview命令
    local cmd="npm run preview"
    if [ "$open_browser" = "true" ]; then
        cmd="$cmd -- --open"
    fi
    
    # 启动预览服务器
    exec $cmd
}

# 生产模式启动 (使用静态文件服务器)
start_prod() {
    local port=$1
    local host=$2
    local open_browser=$3
    
    log_info "启动生产模式..."
    
    # 检查构建文件
    if [ ! -d "dist" ]; then
        log_warning "构建文件不存在，正在构建..."
        build_prod
    fi
    
    # 设置默认值
    local serve_port=${port:-8080}
    local serve_host=${host:-localhost}
    
    log_info "静态文件服务器配置:"
    log_info "  - 主机: $serve_host"
    log_info "  - 端口: $serve_port"
    log_info "  - 文档根目录: $(pwd)/dist"
    echo ""
    
    # 尝试使用不同的静态文件服务器
    if command -v python3 >/dev/null 2>&1; then
        log_success "使用Python3 HTTP服务器..."
        log_info "访问地址: http://$serve_host:$serve_port"
        log_warning "按 Ctrl+C 停止服务"
        echo ""
        
        cd dist
        if [ "$open_browser" = "true" ]; then
            # 在后台启动服务器，然后打开浏览器
            python3 -m http.server $serve_port --bind $serve_host &
            sleep 2
            if command -v xdg-open >/dev/null 2>&1; then
                xdg-open "http://$serve_host:$serve_port"
            elif command -v open >/dev/null 2>&1; then
                open "http://$serve_host:$serve_port"
            fi
            wait
        else
            exec python3 -m http.server $serve_port --bind $serve_host
        fi
    elif command -v python >/dev/null 2>&1; then
        log_success "使用Python2 HTTP服务器..."
        log_info "访问地址: http://$serve_host:$serve_port"
        log_warning "按 Ctrl+C 停止服务"
        echo ""
        
        cd dist
        exec python -m SimpleHTTPServer $serve_port
    elif command -v npx >/dev/null 2>&1; then
        log_success "使用serve包..."
        log_info "访问地址: http://$serve_host:$serve_port"
        log_warning "按 Ctrl+C 停止服务"
        echo ""
        
        local cmd="npx serve dist -l $serve_port"
        if [ "$open_browser" = "true" ]; then
            cmd="$cmd --open"
        fi
        exec $cmd
    else
        log_error "未找到可用的静态文件服务器"
        log_info "请安装以下任一工具:"
        log_info "  - Python3: python3 -m http.server"
        log_info "  - Python2: python -m SimpleHTTPServer"
        log_info "  - serve: npm install -g serve"
        exit 1
    fi
}

# 解析命令行参数
MODE="dev"
PORT=""
HOST=""
OPEN_BROWSER=false

while [[ $# -gt 0 ]]; do
    case $1 in
        dev|prod|build|preview)
            MODE=$1
            shift
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -h|--host)
            HOST="$2"
            shift 2
            ;;
        -o|--open)
            OPEN_BROWSER=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 获取脚本目录并切换到前端目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo -e "${GREEN}=== MES系统前端启动脚本 ===${NC}"
echo ""

# 根据模式执行相应操作
case $MODE in
    dev)
        start_dev "$PORT" "$HOST" "$OPEN_BROWSER"
        ;;
    prod)
        start_prod "$PORT" "$HOST" "$OPEN_BROWSER"
        ;;
    build)
        build_prod
        ;;
    preview)
        preview_prod "$PORT" "$HOST" "$OPEN_BROWSER"
        ;;
    *)
        log_error "未知模式: $MODE"
        show_help
        exit 1
        ;;
esac
