import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react({
      // 启用React Fast Refresh
      fastRefresh: true,
      // 优化JSX运行时
      jsxRuntime: 'automatic',
    }),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  // 优化依赖预构建
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'antd',
      '@ant-design/icons',
      'axios',
      'dayjs',
      'react-query',
      'zustand',
    ],
    exclude: ['@vite/client', '@vite/env'],
  },
  // 启用实验性功能
  experimental: {
    renderBuiltUrl(filename, { hostType }) {
      if (hostType === 'js') {
        return { js: `/${filename}` };
      } else {
        return { relative: true };
      }
    },
  },
  server: {
    host: '::', // Allow external access (IPv6 dual-stack)
    port: process.env.VITE_PORT ? parseInt(process.env.VITE_PORT) : 3000,
    allowedHosts: [
      'localhost',
      '127.0.0.1',
      '::1',
      'ql.hjch.cf',
      '.hjch.cf', // 允许所有 hjch.cf 子域名
      'all', // 允许所有主机（开发环境使用）
    ],
    proxy: {
      '/api': {
        target: process.env.VITE_API_PROXY_TARGET || 'http://localhost:9001',
        changeOrigin: true,
        secure: false,
      },
    },
  },
  preview: {
    host: '::', // Allow external access (IPv6 dual-stack)
    port: 3000,
    allowedHosts: [
      'localhost',
      '127.0.0.1',
      '::1',
      'ql.hjch.cf',
      '.hjch.cf', // 允许所有 hjch.cf 子域名
      'all', // 允许所有主机（开发环境使用）
    ],
  },
  build: {
    outDir: 'dist',
    sourcemap: process.env.NODE_ENV !== 'production', // 生产环境不生成sourcemap
    // 优化构建配置
    rollupOptions: {
      output: {
        // 手动分包策略 - 更细粒度的分包
        manualChunks: (id) => {
          // React生态系统
          if (id.includes('react') || id.includes('react-dom') || id.includes('react-router')) {
            return 'react-vendor';
          }
          // Ant Design生态系统
          if (id.includes('antd') || id.includes('@ant-design') || id.includes('rc-')) {
            return 'antd-vendor';
          }
          // 图表和可视化库
          if (id.includes('recharts') || id.includes('d3-') || id.includes('victory')) {
            return 'chart-vendor';
          }
          // 日期处理库
          if (id.includes('dayjs') || id.includes('date-fns')) {
            return 'date-vendor';
          }
          // 工具库
          if (id.includes('axios') || id.includes('lodash') || id.includes('react-query') || id.includes('zustand')) {
            return 'utils-vendor';
          }
          // 表单和验证库
          if (id.includes('react-hook-form') || id.includes('zod') || id.includes('@hookform')) {
            return 'form-vendor';
          }
          // 国际化库
          if (id.includes('i18next') || id.includes('react-i18next')) {
            return 'i18n-vendor';
          }
          // 其他第三方库
          if (id.includes('node_modules')) {
            return 'vendor';
          }
        },
        // 优化文件名
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.');
          const ext = info[info.length - 1];
          if (/\.(png|jpe?g|gif|svg|webp|ico)$/i.test(assetInfo.name)) {
            return `assets/images/[name]-[hash].[ext]`;
          }
          if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name)) {
            return `assets/fonts/[name]-[hash].[ext]`;
          }
          return `assets/[ext]/[name]-[hash].[ext]`;
        },
      },
      // 外部化依赖（可选，用于CDN）
      external: process.env.NODE_ENV === 'production' ? [] : [],
    },
    // 提高chunk大小警告阈值
    chunkSizeWarningLimit: 1000,
    // 压缩配置
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: process.env.NODE_ENV === 'production', // 生产环境移除console
        drop_debugger: true,
        pure_funcs: process.env.NODE_ENV === 'production' ? ['console.log', 'console.info'] : [],
      },
      mangle: {
        safari10: true, // 兼容Safari 10
      },
    },
    // 启用CSS代码分割
    cssCodeSplit: true,
    // 资源内联阈值
    assetsInlineLimit: 4096, // 4KB以下的资源内联为base64
    // 启用gzip压缩报告
    reportCompressedSize: true,
  },
})
