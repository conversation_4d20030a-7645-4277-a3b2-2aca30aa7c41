#!/bin/bash

# MES前端生产模式启动脚本

echo "🚀 启动MES前端生产模式..."
echo ""

# 检查是否存在构建产物
if [ ! -d "dist" ]; then
    echo "📦 未找到构建产物，开始构建..."
    npm run build:prod
    if [ $? -ne 0 ]; then
        echo "❌ 构建失败，请检查错误信息"
        exit 1
    fi
    echo "✅ 构建完成"
    echo ""
fi

# 检查后端服务是否运行
echo "🔍 检查后端服务状态..."
if curl -s http://localhost:9001/api/health > /dev/null 2>&1; then
    echo "✅ 后端服务运行正常"
else
    echo "⚠️  后端服务未运行，请先启动后端服务"
    echo "   在项目根目录运行: cargo run"
    echo ""
fi

echo "🌐 启动前端代理服务器..."
echo ""
echo "📱 前端地址: http://localhost:3080"
echo "🔗 API代理: http://localhost:3080/api -> http://localhost:9001/api"
echo ""
echo "💡 提示："
echo "   - 前端使用相对路径 /api 进行API调用"
echo "   - 代理服务器自动转发到后端 http://localhost:9001"
echo "   - 这样避免了CORS问题和直连后端的问题"
echo ""
echo "按 Ctrl+C 停止服务"
echo ""

# 启动代理服务器
npm run serve:proxy
