#!/bin/bash

# MES系统生产环境启动脚本
# 版本: 1.0
# 作者: MES开发团队

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MES_BINARY="$SCRIPT_DIR/target/release/mes-system"
FRONTEND_DIST="$SCRIPT_DIR/frontend/dist"
PID_FILE="$SCRIPT_DIR/mes-system.pid"
LOG_FILE="$SCRIPT_DIR/logs/mes-system.log"
LOG_DIR="$SCRIPT_DIR/logs"

# 默认配置
DEFAULT_PORT=9001
DEFAULT_HOST="0.0.0.0"
DEFAULT_DATABASE_URL="postgresql://mes_user:mes_password@localhost:5432/mes_db"

# 从环境变量或使用默认值
PORT=${MES_PORT:-$DEFAULT_PORT}
HOST=${MES_HOST:-$DEFAULT_HOST}
DATABASE_URL=${DATABASE_URL:-$DEFAULT_DATABASE_URL}

# 显示帮助信息
show_help() {
    cat << EOF
MES系统启动脚本

用法: $0 [选项] [命令]

命令:
    start       启动MES系统 (默认)
    stop        停止MES系统
    restart     重启MES系统
    status      查看系统状态
    logs        查看日志
    build       重新构建系统

选项:
    -p, --port PORT         设置端口 (默认: $DEFAULT_PORT)
    -h, --host HOST         设置主机 (默认: $DEFAULT_HOST)
    -d, --daemon            后台运行
    -f, --foreground        前台运行 (默认)
    --help                  显示此帮助信息

环境变量:
    MES_PORT               服务端口
    MES_HOST               服务主机
    DATABASE_URL           数据库连接字符串
    RUST_LOG               日志级别 (默认: info)

示例:
    $0 start                    # 前台启动
    $0 start -d                 # 后台启动
    $0 start -p 8080            # 指定端口启动
    $0 stop                     # 停止服务
    $0 restart                  # 重启服务
    $0 status                   # 查看状态

EOF
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查二进制文件
    if [ ! -f "$MES_BINARY" ]; then
        log_error "MES系统二进制文件不存在: $MES_BINARY"
        log_info "请先运行: $0 build"
        exit 1
    fi
    
    # 检查前端文件
    if [ ! -d "$FRONTEND_DIST" ]; then
        log_warning "前端构建文件不存在: $FRONTEND_DIST"
        log_info "将尝试构建前端..."
        build_frontend
    fi
    
    # 检查日志目录
    if [ ! -d "$LOG_DIR" ]; then
        log_info "创建日志目录: $LOG_DIR"
        mkdir -p "$LOG_DIR"
    fi
    
    log_success "依赖检查完成"
}

# 构建前端
build_frontend() {
    log_info "构建前端..."
    cd "$SCRIPT_DIR/frontend"
    
    if command -v npm >/dev/null 2>&1; then
        npm run build
        log_success "前端构建完成"
    else
        log_error "npm未安装，无法构建前端"
        exit 1
    fi
    
    cd "$SCRIPT_DIR"
}

# 构建整个系统
build_system() {
    log_info "开始构建MES系统..."
    
    # 构建后端
    log_info "构建后端 (Release模式)..."
    cargo build --release
    
    # 构建前端
    build_frontend
    
    log_success "系统构建完成"
}

# 检查进程状态
check_status() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            return 0  # 运行中
        else
            rm -f "$PID_FILE"  # 清理无效的PID文件
            return 1  # 未运行
        fi
    else
        return 1  # 未运行
    fi
}

# 启动系统
start_system() {
    local daemon_mode=$1
    
    log_info "启动MES系统..."
    
    # 检查是否已经运行
    if check_status; then
        PID=$(cat "$PID_FILE")
        log_warning "MES系统已经在运行 (PID: $PID)"
        return 0
    fi
    
    # 检查依赖
    check_dependencies
    
    # 设置环境变量
    export RUST_LOG=${RUST_LOG:-info}
    export MES_PORT=$PORT
    export MES_HOST=$HOST
    
    log_info "配置信息:"
    log_info "  - 主机: $HOST"
    log_info "  - 端口: $PORT"
    log_info "  - 数据库: ${DATABASE_URL%@*}@***"
    log_info "  - 日志级别: $RUST_LOG"
    log_info "  - 二进制文件: $MES_BINARY"
    log_info "  - 前端文件: $FRONTEND_DIST"
    
    if [ "$daemon_mode" = "true" ]; then
        # 后台运行
        log_info "以守护进程模式启动..."
        nohup "$MES_BINARY" > "$LOG_FILE" 2>&1 &
        echo $! > "$PID_FILE"
        sleep 2
        
        if check_status; then
            PID=$(cat "$PID_FILE")
            log_success "MES系统已启动 (PID: $PID)"
            log_info "日志文件: $LOG_FILE"
            log_info "访问地址: http://$HOST:$PORT"
        else
            log_error "启动失败，请检查日志: $LOG_FILE"
            exit 1
        fi
    else
        # 前台运行
        log_info "以前台模式启动..."
        log_info "按 Ctrl+C 停止服务"
        echo $$ > "$PID_FILE"
        
        # 设置信号处理
        trap 'log_info "收到停止信号，正在关闭..."; rm -f "$PID_FILE"; exit 0' INT TERM
        
        exec "$MES_BINARY"
    fi
}

# 停止系统
stop_system() {
    log_info "停止MES系统..."
    
    if check_status; then
        PID=$(cat "$PID_FILE")
        log_info "发送停止信号到进程 $PID"
        
        # 优雅停止
        kill -TERM "$PID" 2>/dev/null || true
        
        # 等待进程结束
        local count=0
        while [ $count -lt 30 ] && ps -p "$PID" > /dev/null 2>&1; do
            sleep 1
            count=$((count + 1))
        done
        
        # 如果还在运行，强制杀死
        if ps -p "$PID" > /dev/null 2>&1; then
            log_warning "进程未响应，强制终止..."
            kill -KILL "$PID" 2>/dev/null || true
            sleep 2
        fi
        
        rm -f "$PID_FILE"
        log_success "MES系统已停止"
    else
        log_info "MES系统未运行"
    fi
}

# 显示状态
show_status() {
    if check_status; then
        PID=$(cat "$PID_FILE")
        log_success "MES系统正在运行 (PID: $PID)"
        
        # 显示进程信息
        if command -v ps >/dev/null 2>&1; then
            echo
            echo "进程信息:"
            ps -p "$PID" -o pid,ppid,cmd,etime,pcpu,pmem 2>/dev/null || true
        fi
        
        # 检查端口
        if command -v netstat >/dev/null 2>&1; then
            echo
            echo "端口监听:"
            netstat -tlnp 2>/dev/null | grep ":$PORT " || echo "端口 $PORT 未监听"
        fi
    else
        log_info "MES系统未运行"
    fi
}

# 显示日志
show_logs() {
    if [ -f "$LOG_FILE" ]; then
        log_info "显示日志文件: $LOG_FILE"
        echo "----------------------------------------"
        tail -f "$LOG_FILE"
    else
        log_warning "日志文件不存在: $LOG_FILE"
    fi
}

# 解析命令行参数
DAEMON_MODE=false
COMMAND="start"

while [[ $# -gt 0 ]]; do
    case $1 in
        start|stop|restart|status|logs|build)
            COMMAND=$1
            shift
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -h|--host)
            HOST="$2"
            shift 2
            ;;
        -d|--daemon)
            DAEMON_MODE=true
            shift
            ;;
        -f|--foreground)
            DAEMON_MODE=false
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主逻辑
case $COMMAND in
    start)
        start_system $DAEMON_MODE
        ;;
    stop)
        stop_system
        ;;
    restart)
        stop_system
        sleep 2
        start_system $DAEMON_MODE
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    build)
        build_system
        ;;
    *)
        log_error "未知命令: $COMMAND"
        show_help
        exit 1
        ;;
esac
