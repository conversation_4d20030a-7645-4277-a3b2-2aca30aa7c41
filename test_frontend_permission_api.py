#!/usr/bin/env python3
"""
测试前端权限配置页面使用的API端点
"""

import requests
import json
import sys

# 配置
BASE_URL = "http://localhost:9001/api"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "admin123"

def login():
    """登录获取token"""
    login_data = {
        "username": ADMIN_USERNAME,
        "password": ADMIN_PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        data = response.json()
        return data.get("token")
    else:
        print(f"登录失败: {response.status_code} - {response.text}")
        return None

def test_permissions_api(token):
    """测试权限列表API"""
    print("测试 GET /api/permissions...")
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/permissions", headers=headers)
    
    if response.status_code == 200:
        permissions = response.json()
        print(f"✅ 权限列表API正常，返回 {len(permissions)} 个权限")
        return permissions
    else:
        print(f"❌ 权限列表API失败: {response.status_code} - {response.text}")
        return None

def test_roles_api(token):
    """测试角色列表API"""
    print("测试 GET /api/auth/roles...")
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/auth/roles", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        roles = data.get("roles", [])
        print(f"✅ 角色列表API正常，返回 {len(roles)} 个角色")
        return roles
    else:
        print(f"❌ 角色列表API失败: {response.status_code} - {response.text}")
        return None

def test_role_permissions_api(token, role_id):
    """测试角色权限API"""
    print(f"测试 GET /api/roles/{role_id}/permissions...")
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/roles/{role_id}/permissions", headers=headers)
    
    if response.status_code == 200:
        role_permissions = response.json()
        print(f"✅ 角色权限API正常，角色: {role_permissions['role_name']}")
        print(f"   权限总数: {len(role_permissions['permissions'])}")
        granted_count = len([p for p in role_permissions['permissions'] if p['granted']])
        print(f"   已授权权限: {granted_count}")
        return role_permissions
    else:
        print(f"❌ 角色权限API失败: {response.status_code} - {response.text}")
        return None

def test_update_role_permissions_api(token, role_id, permissions_data):
    """测试更新角色权限API"""
    print(f"测试 PUT /api/roles/{role_id}/permissions...")
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    data = {"permissions": permissions_data}
    response = requests.put(f"{BASE_URL}/roles/{role_id}/permissions", 
                          headers=headers, json=data)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 更新角色权限API正常: {result}")
        return True
    else:
        print(f"❌ 更新角色权限API失败: {response.status_code} - {response.text}")
        return False

def main():
    print("开始测试前端权限配置页面使用的API端点...")
    
    # 1. 登录
    print("\n1. 登录...")
    token = login()
    if not token:
        sys.exit(1)
    print(f"登录成功")
    
    # 2. 测试权限列表API
    print("\n2. 测试权限列表API...")
    permissions = test_permissions_api(token)
    if not permissions:
        sys.exit(1)
    
    # 3. 测试角色列表API
    print("\n3. 测试角色列表API...")
    roles = test_roles_api(token)
    if not roles:
        sys.exit(1)
    
    # 4. 测试角色权限API
    print("\n4. 测试角色权限API...")
    operator_role = None
    for role in roles:
        if role['role_name'] == 'operator':
            operator_role = role
            break
    
    if not operator_role:
        print("未找到operator角色")
        sys.exit(1)
    
    role_permissions = test_role_permissions_api(token, operator_role['id'])
    if not role_permissions:
        sys.exit(1)
    
    # 5. 测试更新角色权限API（模拟前端的调用方式）
    print("\n5. 测试更新角色权限API...")
    
    # 构建前端格式的权限数据
    permissions_update = []
    for perm in role_permissions['permissions']:
        permissions_update.append({
            "permission_id": perm['id'],
            "granted": perm['granted']
        })
    
    success = test_update_role_permissions_api(token, operator_role['id'], permissions_update)
    if not success:
        sys.exit(1)
    
    # 6. 验证数据格式
    print("\n6. 验证API数据格式...")
    print("权限数据示例:")
    if permissions:
        sample_perm = permissions[0]
        print(f"  权限ID: {sample_perm.get('id')}")
        print(f"  权限代码: {sample_perm.get('permission_code')}")
        print(f"  权限名称: {sample_perm.get('permission_name')}")
        print(f"  权限分类: {sample_perm.get('category')}")
    
    print("\n角色数据示例:")
    if roles:
        sample_role = roles[0]
        print(f"  角色ID: {sample_role.get('id')}")
        print(f"  角色名称: {sample_role.get('role_name')}")
        print(f"  显示名称: {sample_role.get('display_name')}")
        print(f"  描述: {sample_role.get('description')}")
    
    print("\n角色权限数据示例:")
    if role_permissions and role_permissions['permissions']:
        sample_role_perm = role_permissions['permissions'][0]
        print(f"  权限ID: {sample_role_perm.get('id')}")
        print(f"  权限代码: {sample_role_perm.get('permission_code')}")
        print(f"  权限名称: {sample_role_perm.get('permission_name')}")
        print(f"  是否授权: {sample_role_perm.get('granted')}")
        print(f"  权限分类: {sample_role_perm.get('category')}")
    
    print("\n✅ 所有API端点测试通过！权限配置功能应该正常工作。")
    print("\n如果前端权限修改不生效，可能的原因：")
    print("1. 前端缓存问题 - 尝试刷新页面或清除浏览器缓存")
    print("2. React Query缓存问题 - 检查queryClient.invalidateQueries是否正确调用")
    print("3. 前端状态更新问题 - 检查权限更新后的状态同步")
    print("4. 浏览器网络问题 - 检查浏览器开发者工具的网络面板")

if __name__ == "__main__":
    main()
