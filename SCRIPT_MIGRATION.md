# 脚本迁移说明

## 🔄 脚本整合完成

我们已经将所有Docker管理脚本整合到一个统一的交互式管理器中。

## 📋 变更说明

### ❌ 已移除的脚本
- `docker-start.sh` → 使用 `./mes start`
- `docker-stop.sh` → 使用 `./mes stop`
- `docker-restart.sh` → 使用 `./mes restart`
- `docker-logs.sh` → 使用 `./mes logs`

### ✅ 新的统一脚本
- `docker-manager.sh` - 完整的交互式管理界面
- `mes` - 简化的命令行接口

## 🚀 新的使用方式

### 交互式界面（推荐）
```bash
./mes
```

### 命令行模式
```bash
./mes start      # 启动服务
./mes stop       # 停止服务
./mes restart    # 重启服务
./mes status     # 查看状态
./mes logs       # 查看日志
./mes deploy     # 完整部署
./mes backup     # 备份数据
```

## 🎯 优势

1. **统一界面** - 所有功能集中在一个脚本中
2. **交互式操作** - 直观的菜单系统
3. **彩色输出** - 清晰的状态指示
4. **错误处理** - 友好的错误提示
5. **帮助系统** - 完整的使用指南

## 📚 详细文档

请参考 `docs/deployment/UNIFIED_DOCKER_MANAGER.md` 获取完整的使用指南。
