#!/bin/bash

# MES系统生产版本构建脚本
echo "🏭 MES系统生产版本构建"
echo "======================"

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_step() {
    echo -e "${GREEN}📋 $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_step "1. 检查环境"

# 检查Rust环境
if ! command -v cargo &> /dev/null; then
    print_error "Cargo未安装，请先安装Rust"
    exit 1
fi

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    print_error "Node.js未安装，请先安装Node.js"
    exit 1
fi

print_step "2. 构建后端"
echo "编译Rust后端..."
if cargo build --release; then
    echo "✅ 后端构建成功"
else
    print_error "后端构建失败"
    exit 1
fi

print_step "3. 构建前端"
echo "进入前端目录..."
cd frontend || {
    print_error "frontend目录不存在"
    exit 1
}

echo "安装依赖..."
if npm install; then
    echo "✅ 依赖安装成功"
else
    print_error "依赖安装失败"
    exit 1
fi

echo "构建前端（跳过类型检查）..."
if npm run build-skip-check; then
    echo "✅ 前端构建成功"
else
    print_error "前端构建失败"
    exit 1
fi

cd ..

print_step "4. 创建发布包"
echo "创建发布目录..."
mkdir -p release
cp target/release/mes-system release/
cp -r frontend/dist release/frontend
cp README.md release/
cp start_frontend.sh release/
cp start_mes_external.sh release/
cp quick_start.sh release/
cp -r migrations release/

print_step "5. 生成发布说明"
cat > release/RELEASE_NOTES.md << EOF
# MES系统发布版本

## 版本信息
- 构建时间: $(date)
- 构建环境: $(uname -a)

## 包含文件
- mes-system: 后端可执行文件
- frontend/: 前端静态文件
- migrations/: 数据库迁移文件
- *.sh: 启动脚本

## 启动说明
1. 确保PostgreSQL数据库运行
2. 设置环境变量（参考README.md）
3. 运行: ./quick_start.sh

## 注意事项
- 测试代码已被注释或移除
- 调试功能已被禁用
- 日志级别设置为info
EOF

echo ""
echo -e "${GREEN}🎉 生产版本构建完成！${NC}"
echo "发布文件位于: ./release/"
echo ""
echo "下一步："
echo "1. 测试发布版本"
echo "2. 部署到生产环境"
echo "3. 配置数据库和环境变量"
