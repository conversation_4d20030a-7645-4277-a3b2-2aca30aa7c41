#!/bin/bash

# MES系统开发模式后台启动脚本
# 支持前端和后端同时后台运行

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_DIR="$SCRIPT_DIR/logs"
BACKEND_LOG="$LOG_DIR/backend.log"
FRONTEND_LOG="$LOG_DIR/frontend.log"
BACKEND_PID_FILE="$LOG_DIR/backend.pid"
FRONTEND_PID_FILE="$LOG_DIR/frontend.pid"

# 默认配置
BACKEND_PORT=${MES_PORT:-9001}
FRONTEND_PORT=${FRONTEND_PORT:-3000}
FRONTEND_HOST=${FRONTEND_HOST:-localhost}

# 显示帮助信息
show_help() {
    cat << EOF
MES系统开发模式后台启动脚本

用法: $0 [命令] [选项]

命令:
    start       启动开发环境 (默认)
    stop        停止开发环境
    restart     重启开发环境
    status      查看运行状态
    logs        查看日志

选项:
    --backend-port PORT     后端端口 (默认: 9001)
    --frontend-port PORT    前端端口 (默认: 3000)
    --frontend-host HOST    前端主机 (默认: localhost)
    --help                  显示此帮助信息

示例:
    $0 start                        # 启动开发环境
    $0 start --backend-port 9002    # 指定后端端口
    $0 stop                         # 停止开发环境
    $0 status                       # 查看状态
    $0 logs                         # 查看日志

EOF
}

# 创建日志目录
create_log_dir() {
    if [ ! -d "$LOG_DIR" ]; then
        mkdir -p "$LOG_DIR"
        log_info "创建日志目录: $LOG_DIR"
    fi
}

# 检查进程状态
check_process_status() {
    local pid_file=$1
    local service_name=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p "$pid" > /dev/null 2>&1; then
            echo "$pid"
            return 0
        else
            rm -f "$pid_file"
            return 1
        fi
    fi
    return 1
}

# 启动后端服务
start_backend() {
    log_info "启动后端开发服务器..."
    
    cd "$SCRIPT_DIR"
    
    # 设置环境变量
    export RUST_LOG=${RUST_LOG:-info}
    export MES_PORT=$BACKEND_PORT
    export MES_HOST="0.0.0.0"
    
    # 检查是否已经在运行
    if backend_pid=$(check_process_status "$BACKEND_PID_FILE" "backend"); then
        log_warning "后端服务已在运行 (PID: $backend_pid)"
        return 0
    fi
    
    # 启动后端 (开发模式)
    log_info "使用cargo run启动后端..."
    nohup cargo run > "$BACKEND_LOG" 2>&1 &
    local pid=$!
    echo $pid > "$BACKEND_PID_FILE"
    
    # 等待启动
    sleep 3
    
    # 检查是否启动成功
    if ps -p "$pid" > /dev/null 2>&1; then
        log_success "后端服务启动成功 (PID: $pid)"
        log_info "后端地址: http://localhost:$BACKEND_PORT"
        log_info "后端日志: $BACKEND_LOG"
    else
        log_error "后端服务启动失败"
        rm -f "$BACKEND_PID_FILE"
        return 1
    fi
}

# 启动前端服务
start_frontend() {
    log_info "启动前端开发服务器..."
    
    cd "$SCRIPT_DIR/frontend"
    
    # 检查是否已经在运行
    if frontend_pid=$(check_process_status "$FRONTEND_PID_FILE" "frontend"); then
        log_warning "前端服务已在运行 (PID: $frontend_pid)"
        return 0
    fi
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        log_info "安装前端依赖..."
        npm install
    fi
    
    # 设置环境变量
    export VITE_PORT=$FRONTEND_PORT
    export VITE_HOST=$FRONTEND_HOST
    export VITE_API_PROXY_TARGET="http://localhost:$BACKEND_PORT"
    
    # 启动前端 (开发模式)
    log_info "使用npm run dev启动前端..."
    nohup npm run dev > "$FRONTEND_LOG" 2>&1 &
    local pid=$!
    echo $pid > "$FRONTEND_PID_FILE"
    
    cd "$SCRIPT_DIR"
    
    # 等待启动
    sleep 5
    
    # 检查是否启动成功
    if ps -p "$pid" > /dev/null 2>&1; then
        log_success "前端服务启动成功 (PID: $pid)"
        log_info "前端地址: http://$FRONTEND_HOST:$FRONTEND_PORT"
        log_info "前端日志: $FRONTEND_LOG"
    else
        log_error "前端服务启动失败"
        rm -f "$FRONTEND_PID_FILE"
        return 1
    fi
}

# 停止服务
stop_service() {
    local pid_file=$1
    local service_name=$2
    local log_file=$3
    
    if pid=$(check_process_status "$pid_file" "$service_name"); then
        log_info "停止${service_name}服务 (PID: $pid)..."
        
        # 优雅停止
        kill -TERM "$pid" 2>/dev/null || true
        
        # 等待进程结束
        local count=0
        while [ $count -lt 15 ] && ps -p "$pid" > /dev/null 2>&1; do
            sleep 1
            count=$((count + 1))
        done
        
        # 如果还在运行，强制杀死
        if ps -p "$pid" > /dev/null 2>&1; then
            log_warning "${service_name}进程未响应，强制终止..."
            kill -KILL "$pid" 2>/dev/null || true
            sleep 1
        fi
        
        rm -f "$pid_file"
        log_success "${service_name}服务已停止"
    else
        log_info "${service_name}服务未运行"
    fi
}

# 启动开发环境
start_dev() {
    log_info "启动MES开发环境..."
    
    create_log_dir
    
    # 启动后端
    start_backend
    
    # 启动前端
    start_frontend
    
    echo ""
    log_success "=== MES开发环境启动完成 ==="
    echo ""
    log_info "服务地址:"
    log_info "  前端: http://$FRONTEND_HOST:$FRONTEND_PORT"
    log_info "  后端: http://localhost:$BACKEND_PORT"
    echo ""
    log_info "日志文件:"
    log_info "  后端: $BACKEND_LOG"
    log_info "  前端: $FRONTEND_LOG"
    echo ""
    log_info "管理命令:"
    log_info "  查看状态: $0 status"
    log_info "  查看日志: $0 logs"
    log_info "  停止服务: $0 stop"
    echo ""
}

# 停止开发环境
stop_dev() {
    log_info "停止MES开发环境..."
    
    stop_service "$FRONTEND_PID_FILE" "前端" "$FRONTEND_LOG"
    stop_service "$BACKEND_PID_FILE" "后端" "$BACKEND_LOG"
    
    log_success "MES开发环境已停止"
}

# 查看状态
show_status() {
    log_info "MES开发环境状态:"
    echo ""
    
    # 检查后端状态
    if backend_pid=$(check_process_status "$BACKEND_PID_FILE" "backend"); then
        log_success "后端服务: 运行中 (PID: $backend_pid)"
        log_info "  地址: http://localhost:$BACKEND_PORT"
        log_info "  日志: $BACKEND_LOG"
    else
        log_warning "后端服务: 未运行"
    fi
    
    echo ""
    
    # 检查前端状态
    if frontend_pid=$(check_process_status "$FRONTEND_PID_FILE" "frontend"); then
        log_success "前端服务: 运行中 (PID: $frontend_pid)"
        log_info "  地址: http://$FRONTEND_HOST:$FRONTEND_PORT"
        log_info "  日志: $FRONTEND_LOG"
    else
        log_warning "前端服务: 未运行"
    fi
    
    echo ""
}

# 查看日志
show_logs() {
    echo -e "${GREEN}=== 后端日志 (最后20行) ===${NC}"
    if [ -f "$BACKEND_LOG" ]; then
        tail -20 "$BACKEND_LOG"
    else
        echo "后端日志文件不存在"
    fi
    
    echo ""
    echo -e "${GREEN}=== 前端日志 (最后20行) ===${NC}"
    if [ -f "$FRONTEND_LOG" ]; then
        tail -20 "$FRONTEND_LOG"
    else
        echo "前端日志文件不存在"
    fi
}

# 解析命令行参数
COMMAND="start"

while [[ $# -gt 0 ]]; do
    case $1 in
        start|stop|restart|status|logs)
            COMMAND=$1
            shift
            ;;
        --backend-port)
            BACKEND_PORT="$2"
            shift 2
            ;;
        --frontend-port)
            FRONTEND_PORT="$2"
            shift 2
            ;;
        --frontend-host)
            FRONTEND_HOST="$2"
            shift 2
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行命令
case $COMMAND in
    start)
        start_dev
        ;;
    stop)
        stop_dev
        ;;
    restart)
        stop_dev
        sleep 2
        start_dev
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    *)
        log_error "未知命令: $COMMAND"
        show_help
        exit 1
        ;;
esac
