<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端认证状态调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button { margin: 5px; padding: 10px 15px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>前端认证状态调试</h1>
    
    <div>
        <button onclick="checkAuthState()">检查认证状态</button>
        <button onclick="testLogin()">测试登录</button>
        <button onclick="testPartsAPI()">测试零件API</button>
        <button onclick="clearAllStorage()">清除所有存储</button>
        <button onclick="forceRefreshToken()">强制刷新Token</button>
    </div>
    
    <div id="results"></div>

    <script>
        const API_BASE = '/api';
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        function parseJWT(token) {
            try {
                const parts = token.split('.');
                if (parts.length !== 3) return null;
                
                const payload = parts[1];
                const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
                return JSON.parse(decoded);
            } catch (e) {
                return null;
            }
        }
        
        function checkAuthState() {
            clearResults();
            addResult('<h3>检查前端认证状态</h3>');
            
            // 检查localStorage
            const authStorage = localStorage.getItem('auth-storage');
            if (authStorage) {
                try {
                    const parsed = JSON.parse(authStorage);
                    addResult(`<h4>Zustand认证存储:</h4><pre>${JSON.stringify(parsed, null, 2)}</pre>`, 'info');
                } catch (e) {
                    addResult(`认证存储解析错误: ${e.message}`, 'error');
                }
            } else {
                addResult('未找到Zustand认证存储', 'warning');
            }
            
            // 检查token
            const token = localStorage.getItem('token');
            if (token) {
                addResult(`<h4>Token存储:</h4><pre>${token.substring(0, 100)}...</pre>`, 'info');
                
                const payload = parseJWT(token);
                if (payload) {
                    addResult(`<h4>Token内容:</h4><pre>${JSON.stringify(payload, null, 2)}</pre>`, 'info');
                    
                    const now = Math.floor(Date.now() / 1000);
                    const isExpired = payload.exp < now;
                    addResult(`Token过期状态: ${isExpired ? '已过期' : '有效'} (过期时间: ${new Date(payload.exp * 1000).toLocaleString()})`, isExpired ? 'error' : 'success');
                } else {
                    addResult('Token解析失败', 'error');
                }
            } else {
                addResult('未找到Token', 'warning');
            }
            
            // 检查sessionStorage
            const sessionToken = sessionStorage.getItem('token');
            if (sessionToken) {
                addResult(`SessionStorage中也有token: ${sessionToken.substring(0, 50)}...`, 'info');
            }
        }
        
        async function testLogin() {
            clearResults();
            addResult('<h3>测试gongyi用户登录</h3>');
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'gongyi',
                        password: 'gongyi'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`<h4>登录成功:</h4><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                    
                    // 存储新token
                    localStorage.setItem('token', data.token);
                    addResult('新Token已存储到localStorage', 'success');
                    
                    // 解析新token
                    const payload = parseJWT(data.token);
                    if (payload) {
                        addResult(`<h4>新Token内容:</h4><pre>${JSON.stringify(payload, null, 2)}</pre>`, 'info');
                    }
                    
                    return data.token;
                } else {
                    const error = await response.text();
                    addResult(`登录失败: ${response.status} - ${error}`, 'error');
                    return null;
                }
            } catch (error) {
                addResult(`登录请求错误: ${error.message}`, 'error');
                return null;
            }
        }
        
        async function testPartsAPI() {
            clearResults();
            addResult('<h3>测试零件API</h3>');
            
            const token = localStorage.getItem('token');
            if (!token) {
                addResult('未找到Token，请先登录', 'error');
                return;
            }
            
            // 1. 测试获取零件列表
            try {
                const listResponse = await fetch(`${API_BASE}/parts`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    }
                });
                
                addResult(`获取零件列表状态: ${listResponse.status}`, listResponse.ok ? 'success' : 'error');
                
                if (listResponse.ok) {
                    const listData = await listResponse.json();
                    addResult(`零件列表: 共 ${listData.data?.total_count || 0} 个零件`, 'success');
                } else {
                    const error = await listResponse.text();
                    addResult(`获取零件列表错误: ${error}`, 'error');
                }
            } catch (error) {
                addResult(`获取零件列表异常: ${error.message}`, 'error');
            }
            
            // 2. 测试创建零件
            const partData = {
                part_number: `DEBUG-TEST-${Date.now()}`,
                part_name: '调试测试零件',
                version: '1.0',
                specifications: '前端调试创建的测试零件'
            };
            
            addResult(`<h4>尝试创建零件:</h4><pre>${JSON.stringify(partData, null, 2)}</pre>`, 'info');
            
            try {
                const createResponse = await fetch(`${API_BASE}/parts`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(partData)
                });
                
                addResult(`创建零件状态: ${createResponse.status}`, createResponse.ok ? 'success' : 'error');
                
                if (createResponse.ok) {
                    const createData = await createResponse.json();
                    addResult(`<h4>零件创建成功:</h4><pre>${JSON.stringify(createData, null, 2)}</pre>`, 'success');
                } else {
                    const error = await createResponse.text();
                    addResult(`<h4>零件创建失败:</h4><pre>${error}</pre>`, 'error');
                    
                    // 如果是403错误，提供详细分析
                    if (createResponse.status === 403) {
                        addResult('<h4>403错误分析:</h4>', 'warning');
                        addResult('1. 检查Token中的角色信息是否正确', 'warning');
                        addResult('2. 检查后端权限验证逻辑', 'warning');
                        addResult('3. 可能需要重新登录获取新的Token', 'warning');
                    }
                }
            } catch (error) {
                addResult(`创建零件异常: ${error.message}`, 'error');
            }
        }
        
        function clearAllStorage() {
            localStorage.clear();
            sessionStorage.clear();
            addResult('所有存储已清除', 'info');
            addResult('请重新登录以获取新的认证状态', 'warning');
        }
        
        async function forceRefreshToken() {
            clearResults();
            addResult('<h3>强制刷新Token</h3>');
            
            const token = localStorage.getItem('token');
            if (!token) {
                addResult('未找到Token，无法刷新', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/auth/refresh`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`Token刷新成功: ${data.message}`, 'success');
                    
                    // 存储新token
                    localStorage.setItem('token', data.token);
                    addResult('新Token已存储', 'success');
                    
                    // 解析新token
                    const payload = parseJWT(data.token);
                    if (payload) {
                        addResult(`<h4>刷新后Token内容:</h4><pre>${JSON.stringify(payload, null, 2)}</pre>`, 'info');
                    }
                } else {
                    const error = await response.text();
                    addResult(`Token刷新失败: ${response.status} - ${error}`, 'error');
                }
            } catch (error) {
                addResult(`Token刷新异常: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动检查
        window.onload = function() {
            checkAuthState();
        };
    </script>
</body>
</html>
