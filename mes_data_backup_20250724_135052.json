{
  "backup_time": "2025-07-24T13:50:52.071632",
  "tables": {
    "projects": {
      "count": 5,
      "data": [
        {
          "id": 5,
          "project_name": "测试项目-正常状态",
          "customer_name": "测试客户",
          "created_at": "2025-07-15T12:16:12.229601+00:00",
          "status": "Normal"
        },
        {
          "id": 6,
          "project_name": "24098",
          "customer_name": null,
          "created_at": "2025-07-18T08:55:53.884624+00:00",
          "status": "Normal"
        },
        {
          "id": 7,
          "project_name": "test01",
          "customer_name": null,
          "created_at": "2025-07-19T08:41:09.966233+00:00",
          "status": "Normal"
        },
        {
          "id": 8,
          "project_name": "23331",
          "customer_name": "gg",
          "created_at": "2025-07-20T12:42:27.517634+00:00",
          "status": "Normal"
        },
        {
          "id": 9,
          "project_name": "test02",
          "customer_name": "test",
          "created_at": "2025-07-24T12:57:37.580465+00:00",
          "status": "Normal"
        }
      ]
    },
    "parts": {
      "count": 13,
      "data": [
        {
          "id": 1,
          "part_number": "11",
          "part_name": "11",
          "version": "1",
          "specifications": "111"
        },
        {
          "id": 2,
          "part_number": "212",
          "part_name": "2#",
          "version": "1",
          "specifications": null
        },
        {
          "id": 4,
          "part_number": "130",
          "part_name": "滑块1",
          "version": "1",
          "specifications": null
        },
        {
          "id": 3,
          "part_number": "131",
          "part_name": "滑块2",
          "version": "1",
          "specifications": null
        },
        {
          "id": 5,
          "part_number": "11",
          "part_name": "1",
          "version": "1.0",
          "specifications": null
        },
        {
          "id": 6,
          "part_number": "12",
          "part_name": "12",
          "version": "1.0",
          "specifications": null
        },
        {
          "id": 7,
          "part_number": "P001",
          "part_name": "示例零件1",
          "version": "1.0",
          "specifications": "示例规格说明"
        },
        {
          "id": 8,
          "part_number": "P002",
          "part_name": "示例零件2",
          "version": "1.0",
          "specifications": "示例规格说明"
        },
        {
          "id": 17,
          "part_number": "31",
          "part_name": "test031",
          "version": "1",
          "specifications": null
        },
        {
          "id": 18,
          "part_number": "PE-TEST-1753362221",
          "part_name": "工艺员测试零件",
          "version": "1.0",
          "specifications": "这是工艺员创建的测试零件"
        },
        {
          "id": 19,
          "part_number": "GONGYI-TEST-1753363553",
          "part_name": "gongyi测试零件",
          "version": "1.0",
          "specifications": "这是gongyi用户创建的测试零件"
        },
        {
          "id": 20,
          "part_number": "FIX-TEST-**********",
          "part_name": "修复测试零件",
          "version": "1.0",
          "specifications": "用于修复前端认证问题的测试零件"
        },
        {
          "id": 21,
          "part_number": "test001",
          "part_name": "test001",
          "version": "1",
          "specifications": null
        }
      ]
    },
    "routings": {
      "count": 18,
      "data": [
        {
          "id": 1,
          "part_id": 1,
          "step_number": 1,
          "process_name": "Milling",
          "work_instructions": null,
          "standard_hours": 2.0
        },
        {
          "id": 2,
          "part_id": 1,
          "step_number": 2,
          "process_name": "CNC Machining",
          "work_instructions": null,
          "standard_hours": 2.0
        },
        {
          "id": 3,
          "part_id": 1,
          "step_number": 3,
          "process_name": "Grinding",
          "work_instructions": "Ghhgfg",
          "standard_hours": 2.0
        },
        {
          "id": 4,
          "part_id": 3,
          "step_number": 1,
          "process_name": "CNC Machining",
          "work_instructions": "维生素",
          "standard_hours": 5.0
        },
        {
          "id": 5,
          "part_id": 3,
          "step_number": 2,
          "process_name": "Milling",
          "work_instructions": "而我却",
          "standard_hours": 5.0
        },
        {
          "id": 6,
          "part_id": 5,
          "step_number": 1,
          "process_name": "CNC Machining",
          "work_instructions": null,
          "standard_hours": 2.0
        },
        {
          "id": 7,
          "part_id": 5,
          "step_number": 2,
          "process_name": "Grinding",
          "work_instructions": null,
          "standard_hours": 2.0
        },
        {
          "id": 8,
          "part_id": 5,
          "step_number": 3,
          "process_name": "Milling",
          "work_instructions": null,
          "standard_hours": 2.0
        },
        {
          "id": 9,
          "part_id": 6,
          "step_number": 1,
          "process_name": "Milling",
          "work_instructions": null,
          "standard_hours": 4.0
        },
        {
          "id": 10,
          "part_id": 6,
          "step_number": 2,
          "process_name": "Grinding",
          "work_instructions": null,
          "standard_hours": 4.0
        },
        {
          "id": 11,
          "part_id": 6,
          "step_number": 3,
          "process_name": "EDM",
          "work_instructions": null,
          "standard_hours": 3.0
        },
        {
          "id": 12,
          "part_id": 6,
          "step_number": 4,
          "process_name": "CNC Machining",
          "work_instructions": null,
          "standard_hours": 3.0
        },
        {
          "id": 13,
          "part_id": 7,
          "step_number": 1,
          "process_name": "CNC Machining",
          "work_instructions": null,
          "standard_hours": 4.0
        },
        {
          "id": 14,
          "part_id": 7,
          "step_number": 2,
          "process_name": "Milling",
          "work_instructions": null,
          "standard_hours": 7.0
        },
        {
          "id": 15,
          "part_id": 7,
          "step_number": 3,
          "process_name": "Grinding",
          "work_instructions": null,
          "standard_hours": 2.0
        },
        {
          "id": 16,
          "part_id": 21,
          "step_number": 1,
          "process_name": "CNC Machining",
          "work_instructions": "cnc",
          "standard_hours": 2.0
        },
        {
          "id": 17,
          "part_id": 21,
          "step_number": 2,
          "process_name": "Milling",
          "work_instructions": "m",
          "standard_hours": 2.0
        },
        {
          "id": 18,
          "part_id": 21,
          "step_number": 3,
          "process_name": "Grinding",
          "work_instructions": "g",
          "standard_hours": 3.0
        }
      ]
    },
    "plan_tasks": {
      "count": 32,
      "data": [
        {
          "id": 12,
          "work_order_id": 9,
          "routing_step_id": 8,
          "skill_group_id": 1,
          "planned_start": "2025-07-22T02:00:00+00:00",
          "planned_end": "2025-07-22T04:00:00+00:00",
          "status": "scheduled",
          "machine_id": null
        },
        {
          "id": 13,
          "work_order_id": 10,
          "routing_step_id": 9,
          "skill_group_id": 1,
          "planned_start": "2025-07-21T04:00:00+00:00",
          "planned_end": "2025-07-21T08:00:00+00:00",
          "status": "planned",
          "machine_id": 1
        },
        {
          "id": 4,
          "work_order_id": 1,
          "routing_step_id": 1,
          "skill_group_id": 1,
          "planned_start": "2025-07-24T16:00:00+00:00",
          "planned_end": "2025-07-24T18:00:00+00:00",
          "status": "planned",
          "machine_id": null
        },
        {
          "id": 5,
          "work_order_id": 1,
          "routing_step_id": 2,
          "skill_group_id": 1,
          "planned_start": "2025-07-24T18:00:00+00:00",
          "planned_end": "2025-07-24T20:00:00+00:00",
          "status": "planned",
          "machine_id": null
        },
        {
          "id": 6,
          "work_order_id": 1,
          "routing_step_id": 3,
          "skill_group_id": 1,
          "planned_start": "2025-07-24T20:00:00+00:00",
          "planned_end": "2025-07-24T22:00:00+00:00",
          "status": "planned",
          "machine_id": null
        },
        {
          "id": 11,
          "work_order_id": 9,
          "routing_step_id": 7,
          "skill_group_id": 1,
          "planned_start": "2025-07-22T02:00:00+00:00",
          "planned_end": "2025-07-22T04:00:00+00:00",
          "status": "planned",
          "machine_id": 1
        },
        {
          "id": 2,
          "work_order_id": 2,
          "routing_step_id": 1,
          "skill_group_id": 4,
          "planned_start": "2025-07-21T01:00:00+00:00",
          "planned_end": "2025-07-21T03:00:00+00:00",
          "status": "planned",
          "machine_id": 4
        },
        {
          "id": 22,
          "work_order_id": 1,
          "routing_step_id": 1,
          "skill_group_id": 2,
          "planned_start": "2025-07-22T00:00:00+00:00",
          "planned_end": "2025-07-22T02:00:00+00:00",
          "status": "planned",
          "machine_id": null
        },
        {
          "id": 23,
          "work_order_id": 1,
          "routing_step_id": 2,
          "skill_group_id": 1,
          "planned_start": "2025-07-22T02:00:00+00:00",
          "planned_end": "2025-07-22T04:00:00+00:00",
          "status": "planned",
          "machine_id": null
        },
        {
          "id": 15,
          "work_order_id": 10,
          "routing_step_id": 11,
          "skill_group_id": 1,
          "planned_start": "2025-07-20T12:00:00+00:00",
          "planned_end": "2025-07-20T15:00:00+00:00",
          "status": "planned",
          "machine_id": null
        },
        {
          "id": 16,
          "work_order_id": 10,
          "routing_step_id": 12,
          "skill_group_id": 1,
          "planned_start": "2025-07-20T15:00:00+00:00",
          "planned_end": "2025-07-20T18:00:00+00:00",
          "status": "planned",
          "machine_id": null
        },
        {
          "id": 17,
          "work_order_id": 11,
          "routing_step_id": 9,
          "skill_group_id": 1,
          "planned_start": "2025-07-20T18:00:00+00:00",
          "planned_end": "2025-07-20T22:00:00+00:00",
          "status": "planned",
          "machine_id": null
        },
        {
          "id": 19,
          "work_order_id": 11,
          "routing_step_id": 11,
          "skill_group_id": 1,
          "planned_start": "2025-07-21T02:00:00+00:00",
          "planned_end": "2025-07-21T05:00:00+00:00",
          "status": "planned",
          "machine_id": null
        },
        {
          "id": 20,
          "work_order_id": 11,
          "routing_step_id": 12,
          "skill_group_id": 1,
          "planned_start": "2025-07-21T05:00:00+00:00",
          "planned_end": "2025-07-21T08:00:00+00:00",
          "status": "planned",
          "machine_id": null
        },
        {
          "id": 24,
          "work_order_id": 1,
          "routing_step_id": 3,
          "skill_group_id": 4,
          "planned_start": "2025-07-22T04:00:00+00:00",
          "planned_end": "2025-07-22T06:00:00+00:00",
          "status": "planned",
          "machine_id": null
        },
        {
          "id": 25,
          "work_order_id": 1,
          "routing_step_id": 1,
          "skill_group_id": 2,
          "planned_start": "2025-07-21T00:00:00+00:00",
          "planned_end": "2025-07-21T02:00:00+00:00",
          "status": "planned",
          "machine_id": null
        },
        {
          "id": 26,
          "work_order_id": 1,
          "routing_step_id": 2,
          "skill_group_id": 1,
          "planned_start": "2025-07-21T02:00:00+00:00",
          "planned_end": "2025-07-21T04:00:00+00:00",
          "status": "planned",
          "machine_id": null
        },
        {
          "id": 27,
          "work_order_id": 1,
          "routing_step_id": 3,
          "skill_group_id": 4,
          "planned_start": "2025-07-21T04:00:00+00:00",
          "planned_end": "2025-07-21T06:00:00+00:00",
          "status": "planned",
          "machine_id": null
        },
        {
          "id": 30,
          "work_order_id": 12,
          "routing_step_id": 15,
          "skill_group_id": 4,
          "planned_start": "2025-07-22T03:00:00+00:00",
          "planned_end": "2025-07-22T05:00:00+00:00",
          "status": "planned",
          "machine_id": null
        },
        {
          "id": 31,
          "work_order_id": 13,
          "routing_step_id": 13,
          "skill_group_id": 1,
          "planned_start": "2025-07-22T05:00:00+00:00",
          "planned_end": "2025-07-22T09:00:00+00:00",
          "status": "planned",
          "machine_id": null
        },
        {
          "id": 32,
          "work_order_id": 13,
          "routing_step_id": 14,
          "skill_group_id": 2,
          "planned_start": "2025-07-22T09:00:00+00:00",
          "planned_end": "2025-07-22T16:00:00+00:00",
          "status": "planned",
          "machine_id": null
        },
        {
          "id": 33,
          "work_order_id": 13,
          "routing_step_id": 15,
          "skill_group_id": 4,
          "planned_start": "2025-07-22T16:00:00+00:00",
          "planned_end": "2025-07-22T18:00:00+00:00",
          "status": "planned",
          "machine_id": null
        },
        {
          "id": 28,
          "work_order_id": 12,
          "routing_step_id": 13,
          "skill_group_id": 1,
          "planned_start": "2025-07-22T16:00:00+00:00",
          "planned_end": "2025-07-22T20:00:00+00:00",
          "status": "planned",
          "machine_id": 1
        },
        {
          "id": 14,
          "work_order_id": 10,
          "routing_step_id": 10,
          "skill_group_id": 4,
          "planned_start": "2025-07-22T02:00:00+00:00",
          "planned_end": "2025-07-22T06:00:00+00:00",
          "status": "planned",
          "machine_id": 3
        },
        {
          "id": 9,
          "work_order_id": 8,
          "routing_step_id": 8,
          "skill_group_id": 2,
          "planned_start": "2025-07-23T01:00:00+00:00",
          "planned_end": "2025-07-23T03:00:00+00:00",
          "status": "planned",
          "machine_id": 2
        },
        {
          "id": 1,
          "work_order_id": 1,
          "routing_step_id": 1,
          "skill_group_id": 2,
          "planned_start": "2025-07-23T04:00:00+00:00",
          "planned_end": "2025-07-23T06:00:00+00:00",
          "status": "completed",
          "machine_id": 2
        },
        {
          "id": 29,
          "work_order_id": 12,
          "routing_step_id": 14,
          "skill_group_id": 2,
          "planned_start": "2025-07-23T02:00:00+00:00",
          "planned_end": "2025-07-23T09:00:00+00:00",
          "status": "planned",
          "machine_id": 2
        },
        {
          "id": 8,
          "work_order_id": 8,
          "routing_step_id": 7,
          "skill_group_id": 4,
          "planned_start": "2025-07-20T18:00:00+00:00",
          "planned_end": "2025-07-20T20:00:00+00:00",
          "status": "planned",
          "machine_id": 3
        },
        {
          "id": 18,
          "work_order_id": 11,
          "routing_step_id": 10,
          "skill_group_id": 4,
          "planned_start": "2025-07-20T22:00:00+00:00",
          "planned_end": "2025-07-21T02:00:00+00:00",
          "status": "planned",
          "machine_id": null
        },
        {
          "id": 7,
          "work_order_id": 8,
          "routing_step_id": 6,
          "skill_group_id": 1,
          "planned_start": "2025-07-20T16:00:00+00:00",
          "planned_end": "2025-07-20T18:00:00+00:00",
          "status": "planned",
          "machine_id": null
        },
        {
          "id": 3,
          "work_order_id": 3,
          "routing_step_id": 1,
          "skill_group_id": 2,
          "planned_start": "2025-07-22T02:00:00+00:00",
          "planned_end": "2025-07-22T04:00:00+00:00",
          "status": "planned",
          "machine_id": 2
        },
        {
          "id": 10,
          "work_order_id": 9,
          "routing_step_id": 6,
          "skill_group_id": 1,
          "planned_start": "2025-07-20T22:00:00+00:00",
          "planned_end": "2025-07-21T00:00:00+00:00",
          "status": "planned",
          "machine_id": null
        }
      ]
    },
    "work_orders": {
      "count": 17,
      "data": [
        {
          "id": 1,
          "project_bom_id": 1,
          "quantity": 1,
          "status": "pending",
          "due_date": 