#!/bin/bash

# MES系统脚本清理工具
# 整理和归档重复的启动脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== MES系统脚本清理工具 ===${NC}"
echo ""

# 创建归档目录
ARCHIVE_DIR="scripts/archived"
mkdir -p "$ARCHIVE_DIR"

echo -e "${YELLOW}正在分析重复脚本...${NC}"

# 定义要保留的核心脚本
KEEP_SCRIPTS=(
    "mes-launcher.sh"           # 新的统一启动器
    "start-mes-system.sh"       # 完整的后端启动脚本
    "quick-start.sh"            # 后端快速启动
    "install-production.sh"     # 生产环境安装
    "frontend/start-frontend.sh"     # 前端完整启动脚本
    "frontend/quick-start-frontend.sh" # 前端快速启动
)

# 定义要归档的重复脚本
ARCHIVE_SCRIPTS=(
    "start.sh"                  # 被mes-launcher.sh替代
    "start_all.sh"              # 被mes-launcher.sh替代
    "start_background.sh"       # 被start-mes-system.sh替代
    "start_frontend.sh"         # 被frontend/start-frontend.sh替代
    "start_mes_external.sh"     # 功能重复
    "quick_start.sh"            # 被quick-start.sh替代
    "start-preview.sh"          # 被前端脚本替代
    "stop-preview.sh"           # 被前端脚本替代
    "stop.sh"                   # 被mes-launcher.sh替代
    "status.sh"                 # 被mes-launcher.sh替代
    "check_status.sh"           # 被mes-launcher.sh替代
    "check_services.sh"         # 被mes-launcher.sh替代
)

# 归档重复脚本
echo -e "${YELLOW}归档重复脚本...${NC}"
for script in "${ARCHIVE_SCRIPTS[@]}"; do
    if [ -f "$script" ]; then
        echo "  归档: $script"
        mv "$script" "$ARCHIVE_DIR/"
    fi
done

# 归档Windows批处理文件到单独目录
WINDOWS_DIR="scripts/windows"
mkdir -p "$WINDOWS_DIR"

echo -e "${YELLOW}整理Windows脚本...${NC}"
WINDOWS_SCRIPTS=(
    "start_all.bat"
    "quick_start.bat"
    "build_production.bat"
    "check-system.bat"
    "docker-install.bat"
    "init-database.bat"
    "install.bat"
    "system-health-check.bat"
)

for script in "${WINDOWS_SCRIPTS[@]}"; do
    if [ -f "$script" ]; then
        echo "  移动: $script -> $WINDOWS_DIR/"
        mv "$script" "$WINDOWS_DIR/"
    fi
done

# 保留核心Windows脚本在根目录
KEEP_WINDOWS=(
    "start-mes-system.bat"
    "frontend/start-frontend.bat"
)

# 整理Docker相关脚本
DOCKER_DIR="scripts/docker"
mkdir -p "$DOCKER_DIR"

echo -e "${YELLOW}整理Docker脚本...${NC}"
DOCKER_SCRIPTS=(
    "docker-manager.sh"
    "docker-install.sh"
    "scripts/docker-deploy.sh"
    "scripts/docker-optimize.sh"
)

for script in "${DOCKER_SCRIPTS[@]}"; do
    if [ -f "$script" ]; then
        echo "  移动: $script -> $DOCKER_DIR/"
        mv "$script" "$DOCKER_DIR/"
    fi
done

# 整理测试脚本
TEST_DIR="scripts/testing"
mkdir -p "$TEST_DIR"

echo -e "${YELLOW}整理测试脚本...${NC}"
TEST_SCRIPTS=(
    "test_api.sh"
    "test_operator_dashboard.sh"
    "test_quality_module.sh"
    "test_quality_update.sh"
    "deploy_and_test_project_status.sh"
)

for script in "${TEST_SCRIPTS[@]}"; do
    if [ -f "$script" ]; then
        echo "  移动: $script -> $TEST_DIR/"
        mv "$script" "$TEST_DIR/"
    fi
done

# 整理数据库相关脚本
DATABASE_DIR="scripts/database"
mkdir -p "$DATABASE_DIR"

echo -e "${YELLOW}整理数据库脚本...${NC}"
DATABASE_SCRIPTS=(
    "init-database.sh"
    "reset_engineer_password.py"
    "reset_password_db.py"
    "setup_auto_work_order_configs.py"
    "cleanup_all_data.py"
    "simple_cleanup.py"
)

for script in "${DATABASE_SCRIPTS[@]}"; do
    if [ -f "$script" ]; then
        echo "  移动: $script -> $DATABASE_DIR/"
        mv "$script" "$DATABASE_DIR/"
    fi
done

# 创建脚本说明文件
cat > scripts/README.md << 'EOF'
# MES系统脚本目录

本目录包含MES系统的各种脚本，按功能分类整理。

## 📁 目录结构

### 核心启动脚本 (根目录)
- `mes-launcher.sh` - **统一启动器** (推荐使用)
- `start-mes-system.sh` - 完整的后端启动脚本
- `quick-start.sh` - 后端快速启动
- `install-production.sh` - 生产环境安装

### 前端脚本 (frontend/)
- `frontend/start-frontend.sh` - 前端完整启动脚本
- `frontend/quick-start-frontend.sh` - 前端快速启动

### 分类脚本目录

#### archived/ - 已归档的重复脚本
包含被新统一启动器替代的旧脚本，保留作为参考。

#### windows/ - Windows批处理脚本
包含所有Windows环境的.bat脚本文件。

#### docker/ - Docker相关脚本
- Docker构建和部署脚本
- Docker管理工具

#### testing/ - 测试脚本
- API测试脚本
- 功能模块测试
- 系统集成测试

#### database/ - 数据库相关脚本
- 数据库初始化
- 数据清理工具
- 密码重置工具

## 🚀 推荐使用方式

### 1. 统一启动器 (最推荐)
```bash
./mes-launcher.sh
```
提供交互式菜单，包含所有常用功能。

### 2. 快速启动
```bash
# 后端
./quick-start.sh

# 前端
./frontend/quick-start-frontend.sh
```

### 3. 完整功能启动
```bash
# 后端
./start-mes-system.sh start

# 前端
./frontend/start-frontend.sh prod
```

## 📋 脚本迁移说明

以下脚本已被整合到统一启动器中：
- `start.sh` → `mes-launcher.sh`
- `start_all.sh` → `mes-launcher.sh`
- `stop.sh` → `mes-launcher.sh`
- `status.sh` → `mes-launcher.sh`

旧脚本已移动到 `archived/` 目录，如需要可以继续使用。
EOF

# 创建清理报告
echo ""
echo -e "${GREEN}=== 脚本清理完成 ===${NC}"
echo ""
echo -e "${BLUE}📁 目录结构:${NC}"
echo "  scripts/"
echo "  ├── archived/     - 已归档的重复脚本"
echo "  ├── windows/      - Windows批处理脚本"
echo "  ├── docker/       - Docker相关脚本"
echo "  ├── testing/      - 测试脚本"
echo "  ├── database/     - 数据库脚本"
echo "  └── README.md     - 脚本说明文档"
echo ""
echo -e "${BLUE}🚀 推荐使用:${NC}"
echo "  ./mes-launcher.sh    - 统一启动器 (交互式)"
echo "  ./quick-start.sh     - 后端快速启动"
echo "  ./frontend/quick-start-frontend.sh - 前端快速启动"
echo ""
echo -e "${YELLOW}📋 清理统计:${NC}"
echo "  归档脚本: ${#ARCHIVE_SCRIPTS[@]} 个"
echo "  Windows脚本: ${#WINDOWS_SCRIPTS[@]} 个"
echo "  Docker脚本: ${#DOCKER_SCRIPTS[@]} 个"
echo "  测试脚本: ${#TEST_SCRIPTS[@]} 个"
echo "  数据库脚本: ${#DATABASE_SCRIPTS[@]} 个"
echo ""
echo -e "${GREEN}✅ 脚本整理完成！现在可以使用 ./mes-launcher.sh 启动系统${NC}"
