# MES系统环境配置 - production
# 自动生成于: Wed Jul 16 02:04:13 PM UTC 2025

# ===========================================
# 数据库配置
# ===========================================
POSTGRES_DB=mes_db
POSTGRES_USER=mes_user
POSTGRES_PASSWORD=KhraXjvK5y40LaT7QKdj3Zd7u
DATABASE_URL=*************************************************************/mes_db

# ===========================================
# Redis配置
# ===========================================
REDIS_PASSWORD=YBI5d9YzyLC7cdorBgv1ZqIFH
REDIS_URL=redis://:YBI5d9YzyLC7cdorBgv1ZqIFH@redis:6379

# ===========================================
# 服务器配置
# ===========================================
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
RUST_LOG=info

# ===========================================
# 安全配置
# ===========================================
JWT_SECRET=fidUpQtBblxhSkV6pEFo0haVEPwNNy6YzOzDl267espVrfMSKT
z6gy3Yerj50vRmECQoi06g
JWT_EXPIRATION=86400

# ===========================================
# 前端配置
# ===========================================
VITE_API_BASE_URL=http://localhost:8080/api
VITE_APP_TITLE=MES制造执行系统

# ===========================================
# 系统配置
# ===========================================
TIMEZONE=Asia/Shanghai
DEFAULT_LANGUAGE=zh-CN
NODE_ENV=production
RUST_ENV=production

# ===========================================
# IPv6配置
# ===========================================
ENABLE_IPV6=true

# ===========================================
# 移动设备配置
# ===========================================
MOBILE_CACHE_DURATION=3600
MOBILE_MAX_IMAGE_SIZE=2MB
