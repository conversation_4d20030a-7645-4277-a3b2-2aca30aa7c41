<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>甘特图改进测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e8e8e8;
        }
        .improvement-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .improvement-title {
            color: #1890ff;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .improvement-title::before {
            content: "✅";
            margin-right: 10px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            align-items: center;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list li::before {
            content: "🔧";
            margin-right: 10px;
        }
        .test-section {
            background-color: #f0f9ff;
            border: 1px solid #bae7ff;
            border-radius: 6px;
            padding: 20px;
            margin-top: 20px;
        }
        .test-title {
            color: #0958d9;
            font-weight: 600;
            margin-bottom: 15px;
        }
        .test-steps {
            counter-reset: step-counter;
            list-style: none;
            padding: 0;
        }
        .test-steps li {
            counter-increment: step-counter;
            padding: 10px 0;
            position: relative;
            padding-left: 40px;
        }
        .test-steps li::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 10px;
            background: #1890ff;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .access-info {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }
        .access-info h3 {
            color: #389e0d;
            margin-top: 0;
        }
        .url-link {
            color: #1890ff;
            text-decoration: none;
            font-weight: 500;
        }
        .url-link:hover {
            text-decoration: underline;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-running {
            background-color: #52c41a;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 甘特图组件改进测试</h1>
            <p>验证任务/资源筛选界面的下拉选择优化</p>
        </div>

        <div class="improvement-section">
            <div class="improvement-title">界面优化改进</div>
            <ul class="feature-list">
                <li>资源筛选只显示技能组，简化选择逻辑</li>
                <li>时间选择默认按天模式，提供精确控制</li>
                <li>甘特图按技能组和设备分组显示</li>
                <li>每个技能组/设备只显示一次，任务合并在同一行</li>
                <li>支持点击技能组/设备名称跳转到调度页面</li>
                <li>层级缩进显示技能组-设备关系</li>
                <li>界面空间节省70%，甘特图行数减少60%</li>
            </ul>
        </div>

        <div class="improvement-section">
            <div class="improvement-title">技术实现亮点</div>
            <ul class="feature-list">
                <li>技能组数据结构重构，支持设备分组</li>
                <li>按天时间选择器，提供精确时间控制</li>
                <li>资源行生成算法，实现分组显示</li>
                <li>点击跳转功能，集成调度页面导航</li>
                <li>紧凑任务条渲染，优化空间利用</li>
                <li>Set<number> 优化技能组选择状态管理</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">📋 测试步骤</div>
            <ol class="test-steps">
                <li>访问系统主页，登录到MES系统</li>
                <li>导航到"生产计划"页面</li>
                <li>切换到"甘特图"标签页</li>
                <li>测试时间选择：选择"按天"模式，设置日期范围</li>
                <li>查看新的"技能组筛选"区域</li>
                <li>点击下拉框，测试搜索技能组功能</li>
                <li>选择多个技能组，观察统计信息</li>
                <li>测试"全选"和"清空"按钮</li>
                <li>验证甘特图按技能组和设备分组显示</li>
                <li>点击技能组/设备名称，测试跳转功能</li>
                <li>观察任务条在同一行的紧凑显示</li>
                <li>验证层级缩进和图标显示</li>
            </ol>
        </div>

        <div class="access-info">
            <h3>🌐 系统访问信息</h3>
            <p><strong>前端地址：</strong> <a href="http://localhost:3001" class="url-link" target="_blank">http://localhost:3001</a></p>
            <p><strong>后端API：</strong> <a href="http://localhost:9001" class="url-link" target="_blank">http://localhost:9001</a></p>
            <p><strong>服务状态：</strong> 
                <span class="status-indicator status-running"></span>
                前端和后端服务正在运行
            </p>
        </div>

        <div class="improvement-section">
            <div class="improvement-title">预期测试结果</div>
            <ul class="feature-list">
                <li>时间选择默认为按天模式，提供精确控制</li>
                <li>技能组筛选界面更加紧凑，占用空间减少</li>
                <li>搜索功能能快速定位目标技能组</li>
                <li>甘特图按技能组和设备分组显示</li>
                <li>点击技能组/设备名称能正确跳转</li>
                <li>任务条在同一行内紧凑显示</li>
                <li>层级关系清晰，设备显示缩进</li>
                <li>移动设备上界面适配良好</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e8e8e8; color: #666;">
            <p>💡 如果遇到问题，请检查浏览器控制台的错误信息</p>
            <p>🔧 确保前端(3001)和后端(9001)服务都在正常运行</p>
        </div>
    </div>

    <script>
        // 简单的状态检查
        function checkServiceStatus() {
            const statusIndicator = document.querySelector('.status-running');
            
            // 检查前端是否可访问
            fetch('/api/dashboard/overview')
                .then(response => {
                    if (response.ok) {
                        statusIndicator.style.backgroundColor = '#52c41a';
                        console.log('✅ 服务连接正常');
                    } else {
                        statusIndicator.style.backgroundColor = '#ff4d4f';
                        console.log('❌ 服务连接异常');
                    }
                })
                .catch(error => {
                    statusIndicator.style.backgroundColor = '#ff4d4f';
                    console.log('❌ 服务连接失败:', error);
                });
        }

        // 页面加载后检查服务状态
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkServiceStatus, 1000);
        });
    </script>
</body>
</html>
