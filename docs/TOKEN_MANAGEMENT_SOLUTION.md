# Token自动过期策略实现方案

## 问题描述

用户反馈：时间太长再打开页面会无限重载，删除数据或者无痕打开再次登录正常，应该是cookie或者缓存的问题。

## 问题分析

经过分析，无限重载的根本原因是：

1. **JWT Token过期但前端未正确处理**：Token过期后，前端仍然尝试使用过期token进行API调用
2. **认证状态循环检查**：App.tsx中的useEffect可能造成循环调用getCurrentUser
3. **缓存状态不一致**：Zustand持久化存储了过期的认证状态
4. **缺乏自动刷新机制**：没有在token即将过期时自动刷新

## 解决方案

### 1. 创建TokenManager工具类

**文件**: `frontend/src/utils/tokenManager.ts`

**功能**:
- JWT token解析和验证
- 过期时间检测
- 自动清理过期token
- token刷新管理
- 防止并发刷新请求

**核心方法**:
```typescript
// 检查token是否过期
static isTokenExpired(token: string): boolean

// 检查是否需要刷新token（5分钟前）
static shouldRefreshToken(token: string): boolean

// 获取有效token（自动清理过期token）
static getValidToken(): string | null

// 自动刷新token
static async autoRefreshToken(refreshFn: () => Promise<string>): Promise<string | null>

// 设置过期监听器
static setupTokenExpiryWatcher(onExpiry: () => void): () => void
```

### 2. 后端添加Token刷新API

**文件**: `src/handlers/auth.rs`

**新增端点**: `POST /api/auth/refresh`

**功能**:
- 验证当前token有效性
- 获取最新的用户角色和技能
- 生成新的JWT token
- 返回新token给前端

### 3. 优化认证状态管理

**文件**: `frontend/src/store/auth.ts`

**改进**:
- 集成TokenManager进行token管理
- 添加token有效性检查方法
- 实现自动初始化认证状态
- 添加token刷新功能

**新增方法**:
```typescript
checkTokenValidity(): boolean
initializeAuth(): void
refreshToken(): Promise<boolean>
```

### 4. 修复App.tsx循环调用

**文件**: `frontend/src/App.tsx`

**改进**:
- 使用空依赖数组避免无限循环
- 集成token过期监听器
- 定期检查token有效性
- 优化初始化逻辑

### 5. 增强API客户端

**文件**: `frontend/src/lib/api.ts`

**改进**:
- 请求拦截器集成自动token刷新
- 响应拦截器优化401处理
- 避免在登录页面重复跳转

### 6. 添加Token状态显示组件

**文件**: `frontend/src/components/TokenStatus.tsx`

**功能**:
- 实时显示token状态
- 显示剩余有效时间
- 手动刷新token功能
- 详细的token信息展示

## 技术特性

### 1. 自动过期检测
- 每分钟检查token有效性
- 页面刷新时自动清理过期token
- API请求前自动验证token

### 2. 智能刷新策略
- token过期前5分钟自动刷新
- 防止并发刷新请求
- 刷新失败时优雅降级

### 3. 用户体验优化
- 无感知的token刷新
- 避免频繁登录
- 清晰的状态提示

### 4. 安全性保障
- 过期token自动清理
- 401错误统一处理
- 防止token泄露

## 使用方法

### 1. 查看Token状态
访问"系统配置"页面，可以看到详细的token状态信息，包括：
- 当前状态（有效/即将过期/已过期）
- 剩余有效时间
- 用户信息
- 手动刷新按钮

### 2. 自动刷新
系统会在以下情况自动刷新token：
- token即将过期（5分钟前）
- 进行API请求时检测到需要刷新
- 用户主动触发刷新

### 3. 过期处理
当token过期时：
- 自动清理本地存储
- 重定向到登录页面
- 显示友好的提示信息

## 配置参数

### JWT过期时间
**环境变量**: `JWT_EXPIRATION_HOURS`
**默认值**: 24小时
**说明**: 可以通过环境变量调整token有效期

### 刷新阈值
**参数**: `REFRESH_THRESHOLD`
**默认值**: 5分钟
**说明**: token过期前多长时间开始自动刷新

### 检查间隔
**参数**: 60秒
**说明**: 定期检查token有效性的间隔时间

## 测试建议

1. **正常使用测试**：登录后正常使用系统，观察token是否自动刷新
2. **长时间测试**：保持页面打开超过token有效期，验证自动过期处理
3. **刷新测试**：在token即将过期时刷新页面，验证状态恢复
4. **网络异常测试**：在网络不稳定时测试token刷新的容错性

## 监控和调试

### 浏览器控制台日志
系统会输出详细的token管理日志：
```
TokenManager: Refreshing token...
Auth store: Token refreshed successfully
App: Token expired, user will be logged out
```

### Token状态组件
在系统配置页面可以实时查看：
- Token有效性状态
- 剩余时间倒计时
- 用户和权限信息
- 手动操作按钮

## 总结

通过实现这套完整的token自动过期策略，我们解决了：

✅ **无限重载问题**：过期token自动清理，避免循环调用  
✅ **用户体验问题**：自动刷新token，减少登录次数  
✅ **安全性问题**：及时清理过期认证信息  
✅ **状态管理问题**：统一的token管理机制  
✅ **调试支持**：详细的状态显示和日志输出  

这套方案确保了系统的稳定性和用户体验，同时提供了良好的可维护性和扩展性。
