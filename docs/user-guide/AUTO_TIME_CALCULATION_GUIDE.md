# 自动时间计算使用指南

## 🎯 功能介绍

新的任务调度功能现在支持自动计算结束时间！您只需要设置开始时间，系统会根据工艺的标准工时自动计算结束时间，让调度更加准确和高效。

## 📋 操作步骤

### 1. 打开任务调度

#### 从任务列表调度
1. 进入"计划任务"页面
2. 找到要调度的任务
3. 点击该任务的"调度"按钮

### 2. 查看工艺信息

调度弹窗会显示工艺信息面板：
```
🔧 工艺信息: Milling
📏 标准工时: 2.0 小时  
⚡ 结束时间将根据开始时间自动计算
```

### 3. 设置调度信息

#### 选择技能组和设备
- **技能组**: 必须选择（红色星号标识）
- **指定设备**: 可选择具体设备

#### 设置开始时间
1. 点击"计划开始时间"字段
2. 选择合适的日期和时间
3. **结束时间会自动计算并显示**

### 4. 确认和保存
1. 检查自动计算的结束时间是否合理
2. 确认技能组和设备选择正确
3. 点击"确定"保存调度设置

## 🎨 界面说明

### 时间字段区别

#### 计划开始时间 ⏰
- **可编辑**: 用户可以选择日期和时间
- **必填字段**: 红色星号标识
- **操作**: 点击选择器设置时间

#### 计划结束时间 🔒
- **自动计算**: 系统自动计算，不可手动编辑
- **灰色显示**: 表示只读状态
- **标准工时标签**: 显示"标准工时: X.X h"
- **提示信息**: 鼠标悬停显示计算说明

### 工艺信息面板
```
┌─────────────────────────────────┐
│ 🔧 工艺信息: Milling            │
│ 📏 标准工时: 2.0 小时           │
│ ⚡ 结束时间将根据开始时间自动计算 │
└─────────────────────────────────┘
```

## ⚡ 自动计算原理

### 计算公式
```
结束时间 = 开始时间 + 标准工时
```

### 计算示例
- **开始时间**: 2025-07-19 08:00
- **标准工时**: 2.0 小时
- **结束时间**: 2025-07-19 10:00（自动计算）

### 实时更新
当您修改开始时间时：
1. 系统立即重新计算结束时间
2. 结束时间字段自动更新
3. 保持标准工时不变

## 💡 使用技巧

### 快速调度
1. **批量调度**: 对于相同工艺的任务，只需设置不同的开始时间
2. **连续安排**: 可以将下一个任务的开始时间设为上一个任务的结束时间
3. **时间对齐**: 建议将开始时间设置为整点或半点

### 时间规划
1. **预留缓冲**: 在任务间预留适当的缓冲时间
2. **考虑换班**: 注意工作班次的时间安排
3. **设备准备**: 考虑设备准备和切换时间

### 质量检查
1. **核对工时**: 确认标准工时是否合理
2. **检查结果**: 验证计算出的结束时间是否符合预期
3. **及时反馈**: 发现工时异常及时反馈给工艺工程师

## ⚠️ 注意事项

### 标准工时
- **数据来源**: 结束时间基于工艺路线中的标准工时计算
- **默认值**: 如果没有设置标准工时，系统使用1小时作为默认值
- **准确性**: 标准工时的准确性直接影响调度的准确性

### 时间验证
- **最小时长**: 如果标准工时少于30分钟，系统会显示警告
- **合理性**: 建议检查过短或过长的标准工时设置
- **工艺核对**: 如有疑问，请联系工艺工程师确认

### 特殊情况
- **紧急调度**: 紧急情况下如需手动调整时间，请联系管理员
- **工艺变更**: 工艺变更后需要重新进行任务调度
- **设备故障**: 设备故障可能影响实际执行时间

## 🔧 常见问题

### Q: 为什么结束时间不能手动修改？
A: 为了确保时间设置的准确性和一致性，结束时间基于标准工时自动计算，避免人为设置错误。

### Q: 标准工时从哪里来？
A: 标准工时来自工艺路线设置，由工艺工程师根据实际加工情况设定。

### Q: 如果标准工时不准确怎么办？
A: 请联系工艺工程师更新工艺路线中的标准工时设置。

### Q: 可以为任务预留额外时间吗？
A: 当前版本严格按照标准工时计算，如需预留时间，请在安排下一个任务时考虑间隔。

### Q: 系统显示工时过短的警告怎么办？
A: 检查工艺设置是否正确，如确实需要很短时间，可以忽略警告继续操作。

## 📊 时间管理建议

### 日常调度
1. **提前规划**: 提前安排任务时间，避免临时调整
2. **合理分布**: 将任务合理分布在不同时间段
3. **避免冲突**: 注意避免任务时间冲突

### 效率优化
1. **连续作业**: 安排相关任务连续执行
2. **设备利用**: 提高设备利用率
3. **人员配置**: 考虑人员班次安排

### 质量保证
1. **标准执行**: 严格按照标准工时执行
2. **实时监控**: 监控实际执行与计划的偏差
3. **持续改进**: 根据实际情况优化标准工时

## 🎯 最佳实践

### 调度原则
1. **工艺优先**: 以工艺要求为准进行时间安排
2. **标准执行**: 严格按照标准工时进行调度
3. **合理缓冲**: 在关键节点预留适当缓冲时间

### 操作建议
1. **仔细核对**: 调度前仔细核对工艺信息
2. **及时保存**: 设置完成后及时保存
3. **定期检查**: 定期检查调度结果的合理性

### 协作配合
1. **与工艺配合**: 与工艺工程师保持沟通
2. **与生产配合**: 与生产现场保持协调
3. **与质量配合**: 确保质量要求得到满足

---

**使用愉快！** 🎉

通过自动时间计算功能，您可以更准确、更高效地进行任务调度管理。
