# 任务拖拽调度使用指南

## 🎯 快速开始

### 1. 打开设备调度界面
1. 进入"计划任务"页面
2. 在甘特图中找到要调度的设备
3. 点击设备名称（如"m1"）打开调度窗口

### 2. 选择时间范围
1. 在调度窗口顶部选择"调度模式"（7×24小时 或 7×12小时）
2. 使用"选择周"日期选择器选择要调度的周
3. 确认显示的日期范围正确

### 3. 拖拽调整任务时间
1. 在时间表视图中找到要调整的任务
2. 鼠标悬停在任务卡片上，光标会变成移动图标
3. 按住鼠标左键开始拖拽
4. 拖拽到目标时间槽（空闲的灰色区域）
5. 释放鼠标完成调整

## 🎨 界面说明

### 任务卡片
- **蓝色卡片**: 进行中的任务
- **绿色卡片**: 已完成的任务
- **橙色卡片**: 待开始的任务
- **红色卡片**: 延期的任务

### 拖拽状态
- **移动光标**: 鼠标悬停时显示，表示可拖拽
- **半透明**: 拖拽过程中任务变为半透明
- **蓝色边框**: 拖拽中的任务显示蓝色边框
- **高亮区域**: 可放置的空闲时间槽会高亮显示

### 时间信息
- **任务详情**: 鼠标悬停显示完整的任务信息
- **时间范围**: 显示任务的计划开始和结束时间
- **持续时间**: 拖拽时自动保持原始持续时间

## ✅ 操作示例

### 示例1: 调整任务到下一天
1. 找到今天的一个任务（比如周一上午的任务）
2. 拖拽到周二的相同时间槽
3. 系统会自动计算新的开始和结束时间
4. 显示"正在将任务从 01-20 09:00 移动到 01-21 09:00..."
5. 完成后显示"任务时间更新成功"

### 示例2: 调整任务到不同时间
1. 选择一个下午的任务
2. 拖拽到上午的空闲时间槽
3. 任务会保持原来的持续时间
4. 自动更新数据库中的时间信息

## ⚠️ 注意事项

### 拖拽限制
- **已占用时间**: 不能拖拽到已有其他任务的时间槽
- **相同位置**: 拖拽到原来位置不会触发更新
- **权限检查**: 需要有任务编辑权限才能拖拽

### 时间约束
- **小时对齐**: 任务开始时间会对齐到整点
- **持续时间**: 拖拽不会改变任务的持续时间
- **跨天处理**: 支持将任务拖拽到不同日期

### 数据同步
- **即时更新**: 拖拽完成后立即保存到数据库
- **界面刷新**: 自动刷新相关的界面数据
- **错误处理**: 如果更新失败会显示错误信息

## 🔧 故障排除

### 拖拽不工作
**问题**: 鼠标悬停时没有显示移动光标
**解决**: 
- 检查是否有任务编辑权限
- 刷新页面重试
- 确保浏览器支持拖拽功能

### 放置失败
**问题**: 拖拽到空闲区域但没有更新
**解决**:
- 确保目标时间槽确实是空闲的
- 检查网络连接是否正常
- 查看是否有错误提示信息

### 时间显示错误
**问题**: 拖拽后时间计算不正确
**解决**:
- 检查系统时区设置
- 确认选择的周范围正确
- 联系管理员检查服务器时间

## 💡 使用技巧

### 高效调度
1. **批量查看**: 使用周视图一次查看整周的任务安排
2. **颜色识别**: 通过任务卡片颜色快速识别任务状态
3. **时间优化**: 将相似任务安排在连续时间段

### 最佳实践
1. **提前规划**: 在任务开始前进行时间调整
2. **避免冲突**: 拖拽前检查目标时间是否有其他安排
3. **及时保存**: 拖拽完成后确认看到成功提示

### 快捷操作
1. **双击查看**: 双击任务卡片查看详细信息
2. **右键菜单**: 右键点击可能有更多操作选项
3. **键盘导航**: 使用Tab键在界面元素间导航

## 📞 获取帮助

如果在使用过程中遇到问题：

1. **查看提示**: 注意界面上的错误提示信息
2. **检查权限**: 确认有相应的操作权限
3. **联系支持**: 联系系统管理员或技术支持

---

**使用愉快！** 🎉
