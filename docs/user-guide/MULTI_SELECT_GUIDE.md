# 多选任务和冲突检测使用指南

## 🎯 功能介绍

新版本的任务调度系统支持多选任务和智能冲突检测，让您可以更高效地管理和调度任务。

## 🖱️ 多选操作

### 基本多选
1. **单选任务**: 直接点击任务卡片
2. **多选任务**: 按住 `Ctrl` 键（Mac用户按 `Cmd` 键）点击多个任务
3. **取消选择**: 再次 `Ctrl+点击` 已选中的任务可取消选择

### 快捷选择
- **全选**: 按 `Ctrl+A` 选择当前视图中的所有任务
- **清除选择**: 按 `ESC` 键或点击"清除选择"按钮

### 视觉提示
- **选中状态**: 任务卡片显示绿色边框和勾选标记 ✓
- **选择计数**: 界面顶部显示"已选择 X 个任务"
- **操作提示**: 底部显示操作提示信息

## 🚀 批量移动

### 操作步骤
1. **选择任务**: 使用 `Ctrl+点击` 选择要移动的多个任务
2. **拖拽移动**: 拖拽任何一个选中的任务到目标时间槽
3. **自动处理**: 系统自动计算所有任务的新时间
4. **冲突检测**: 自动检测是否存在时间冲突
5. **批量更新**: 无冲突时同时更新所有任务

### 时间计算规则
- **相对位置**: 所有任务保持相对时间关系不变
- **时间偏移**: 基于第一个任务的时间偏移量计算
- **持续时间**: 每个任务的持续时间保持不变

### 批量移动示例
```
原始时间安排:
任务A: 09:00-11:00
任务B: 10:00-12:00  
任务C: 11:00-13:00

拖拽任务A到14:00位置后:
任务A: 14:00-16:00 (偏移+5小时)
任务B: 15:00-17:00 (偏移+5小时)
任务C: 16:00-18:00 (偏移+5小时)
```

## ⚠️ 冲突检测

### 检测机制
- **实时检测**: 拖拽过程中实时检测时间冲突
- **智能范围**: 只检测相关设备/技能组的任务
- **精确判断**: 按分钟级别精确检测时间重叠

### 冲突类型
1. **部分重叠**: 新任务时间与现有任务部分重叠
2. **完全包含**: 新任务时间完全包含现有任务
3. **边界冲突**: 任务开始/结束时间完全相同

### 冲突处理
- **阻止操作**: 检测到冲突时阻止移动操作
- **详细提示**: 显示具体冲突的任务信息
- **解决建议**: 提供调整建议和替代方案

### 冲突提示示例
```
❌ 时间冲突！
任务 零件001 与以下任务时间重叠：
- 零件002 (09:00-11:00)
- 零件003 (10:30-12:30)

建议：请选择其他时间段或调整冲突任务的时间
```

## 🎨 界面说明

### 任务状态指示
| 状态 | 外观 | 说明 |
|------|------|------|
| 普通 | 默认颜色边框 | 未选中的任务 |
| 选中 | 🟢 绿色边框 + ✓ | 已选中的任务 |
| 拖拽中 | 🔵 蓝色边框 + 半透明 | 正在拖拽的任务 |
| 冲突 | 🔴 红色边框 + 震动 | 存在时间冲突 |

### 控制面板
- **选择计数**: 显示当前选中的任务数量
- **清除按钮**: 一键清除所有选择
- **操作提示**: 显示当前可用的操作提示

### 空闲区域
- **普通状态**: 灰色虚线框显示"空闲"
- **拖拽目标**: 蓝色实线框显示"放置到这里"
- **批量目标**: 显示"批量放置(X个)"

## 💡 使用技巧

### 高效选择
1. **按类型选择**: 先选择同类型的任务进行批量操作
2. **按时间选择**: 选择相邻时间的任务便于整体调整
3. **分批处理**: 大量任务时分批选择和移动

### 避免冲突
1. **预先规划**: 移动前先查看目标时间段的任务安排
2. **分步调整**: 复杂调整时分步进行，避免大范围冲突
3. **使用空闲时间**: 优先利用空闲时间段进行任务安排

### 操作建议
1. **保存习惯**: 重要调整后及时保存
2. **检查结果**: 批量操作后检查所有任务的新时间
3. **备份计划**: 大幅调整前备份原有计划

## 🔧 故障排除

### 常见问题

#### 1. 无法多选任务
**原因**: 可能没有按住Ctrl键或权限不足
**解决**: 
- 确保按住Ctrl键（Mac用户按Cmd键）
- 检查是否有任务编辑权限
- 刷新页面重试

#### 2. 批量移动失败
**原因**: 可能存在时间冲突或网络问题
**解决**:
- 查看冲突提示信息
- 选择其他时间段重试
- 检查网络连接状态

#### 3. 冲突检测过于严格
**原因**: 系统按分钟级别检测冲突
**解决**:
- 调整任务时间避免边界重叠
- 使用更精确的时间安排
- 联系管理员调整检测规则

#### 4. 选择状态丢失
**原因**: 页面刷新或网络中断
**解决**:
- 重新选择需要的任务
- 避免在操作过程中刷新页面
- 确保网络连接稳定

## 📋 操作清单

### 多选操作清单
- [ ] 按住Ctrl键点击选择多个任务
- [ ] 确认选中的任务显示绿色边框
- [ ] 查看选择计数是否正确
- [ ] 准备进行批量操作

### 批量移动清单
- [ ] 确认选中了正确的任务
- [ ] 检查目标时间段是否空闲
- [ ] 拖拽任务到目标位置
- [ ] 等待冲突检测完成
- [ ] 确认操作成功提示

### 冲突解决清单
- [ ] 仔细阅读冲突提示信息
- [ ] 识别冲突的具体任务
- [ ] 选择合适的解决方案
- [ ] 重新安排任务时间
- [ ] 验证冲突已解决

## 📞 获取帮助

如果在使用多选和冲突检测功能时遇到问题：

1. **查看提示**: 注意界面上的操作提示和错误信息
2. **检查权限**: 确认有相应的任务编辑权限
3. **网络状态**: 确保网络连接稳定
4. **浏览器兼容**: 使用现代浏览器以获得最佳体验
5. **联系支持**: 如问题持续存在，请联系技术支持

---

**使用愉快！** 🎉

通过多选和冲突检测功能，您可以更高效、更准确地进行任务调度管理。
