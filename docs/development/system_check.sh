#!/bin/bash

# MES System Health Check Script
# This script verifies that all system components are working correctly

BASE_URL="http://localhost:8080/api"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ $1${NC}"
}

# Check if server is running
check_server() {
    print_header "Server Health Check"
    
    # Test basic connectivity
    if curl -s --connect-timeout 5 "$BASE_URL/auth/login" > /dev/null 2>&1; then
        print_success "Server is running and accessible"
        return 0
    else
        print_error "Server is not accessible at $BASE_URL"
        print_info "Make sure the server is running with: cargo run"
        return 1
    fi
}

# Check authentication
check_auth() {
    print_header "Authentication Check"
    
    # Test login
    login_response=$(curl -s -w "%{http_code}" -X POST "$BASE_URL/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"username": "admin", "password": "admin123"}')
    
    status_code="${login_response: -3}"
    
    if [ "$status_code" = "200" ]; then
        print_success "Authentication is working"
        
        # Extract token
        body="${login_response%???}"
        token=$(echo "$body" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
        
        if [ -n "$token" ]; then
            print_success "JWT token generation is working"
            echo "$token" > /tmp/mes_token
            return 0
        else
            print_warning "Token extraction failed"
            return 1
        fi
    else
        print_error "Authentication failed (Status: $status_code)"
        return 1
    fi
}

# Check database connectivity
check_database() {
    print_header "Database Connectivity Check"
    
    if [ -f /tmp/mes_token ]; then
        token=$(cat /tmp/mes_token)
        
        # Test a simple database query
        response=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $token" "$BASE_URL/users")
        status_code="${response: -3}"
        
        if [ "$status_code" = "200" ]; then
            print_success "Database connectivity is working"
            
            # Check if we have users
            body="${response%???}"
            user_count=$(echo "$body" | grep -o '"id":[0-9]*' | wc -l)
            print_info "Found $user_count users in database"
            return 0
        else
            print_error "Database query failed (Status: $status_code)"
            return 1
        fi
    else
        print_error "No authentication token available"
        return 1
    fi
}

# Check core endpoints
check_endpoints() {
    print_header "Core Endpoints Check"
    
    if [ -f /tmp/mes_token ]; then
        token=$(cat /tmp/mes_token)
        
        endpoints=(
            "/users:User Management"
            "/projects:Project Management"
            "/parts:Parts Management"
            "/machines:Machine Management"
            "/skill-groups:Skill Groups"
            "/work-orders:Work Orders"
            "/planning/plan-tasks:Production Planning"
            "/execution/logs:Execution Tracking"
            "/dashboard/overview:Dashboard"
            "/quality/inspections:Quality Management"
        )
        
        success_count=0
        total_count=${#endpoints[@]}
        
        for endpoint_info in "${endpoints[@]}"; do
            endpoint=$(echo "$endpoint_info" | cut -d':' -f1)
            name=$(echo "$endpoint_info" | cut -d':' -f2)
            
            response=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $token" "$BASE_URL$endpoint")
            status_code="${response: -3}"
            
            if [ "$status_code" = "200" ]; then
                print_success "$name endpoint is working"
                ((success_count++))
            else
                print_error "$name endpoint failed (Status: $status_code)"
            fi
        done
        
        print_info "Endpoint success rate: $success_count/$total_count"
        
        if [ $success_count -eq $total_count ]; then
            return 0
        else
            return 1
        fi
    else
        print_error "No authentication token available"
        return 1
    fi
}

# Check system resources
check_resources() {
    print_header "System Resources Check"
    
    # Check if PostgreSQL is running
    if pgrep -x "postgres" > /dev/null; then
        print_success "PostgreSQL is running"
    else
        print_warning "PostgreSQL process not found"
    fi
    
    # Check disk space
    disk_usage=$(df -h . | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -lt 90 ]; then
        print_success "Disk space is adequate ($disk_usage% used)"
    else
        print_warning "Disk space is running low ($disk_usage% used)"
    fi
    
    # Check memory usage
    if command -v free > /dev/null; then
        memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
        if [ "$memory_usage" -lt 90 ]; then
            print_success "Memory usage is normal ($memory_usage% used)"
        else
            print_warning "Memory usage is high ($memory_usage% used)"
        fi
    fi
}

# Main execution
main() {
    echo -e "${BLUE}MES System Health Check${NC}"
    echo -e "${BLUE}======================${NC}"
    echo
    
    overall_status=0
    
    # Run all checks
    check_server || overall_status=1
    echo
    
    check_auth || overall_status=1
    echo
    
    check_database || overall_status=1
    echo
    
    check_endpoints || overall_status=1
    echo
    
    check_resources
    echo
    
    # Summary
    print_header "Summary"
    if [ $overall_status -eq 0 ]; then
        print_success "All critical checks passed - System is healthy!"
    else
        print_error "Some checks failed - Please review the issues above"
    fi
    
    # Cleanup
    rm -f /tmp/mes_token
    
    exit $overall_status
}

# Run the main function
main
