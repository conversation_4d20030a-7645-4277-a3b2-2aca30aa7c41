#!/bin/bash

# MES System Code Cleanup Script
# This script helps clean up code warnings and improve code quality

echo "🧹 MES System Code Cleanup"
echo "=========================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_step() {
    echo -e "${BLUE}📋 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "Cargo.toml" ]; then
    print_error "Not in a Rust project directory. Please run from the project root."
    exit 1
fi

print_step "Step 1: Running cargo check to identify issues"
echo "Running: cargo check"
if cargo check > /tmp/cargo_check.log 2>&1; then
    print_success "Cargo check completed"
    warning_count=$(grep -c "warning:" /tmp/cargo_check.log || echo "0")
    echo "Found $warning_count warnings"
else
    print_error "Cargo check failed"
    cat /tmp/cargo_check.log
    exit 1
fi

print_step "Step 2: Running cargo clippy for additional suggestions"
echo "Running: cargo clippy"
if cargo clippy > /tmp/cargo_clippy.log 2>&1; then
    print_success "Cargo clippy completed"
    clippy_warnings=$(grep -c "warning:" /tmp/cargo_clippy.log || echo "0")
    echo "Found $clippy_warnings clippy warnings"
else
    print_warning "Cargo clippy completed with warnings"
fi

print_step "Step 3: Applying automatic fixes"
echo "Running: cargo fix --allow-dirty --allow-staged"
if cargo fix --allow-dirty --allow-staged > /tmp/cargo_fix.log 2>&1; then
    print_success "Cargo fix completed"
else
    print_warning "Cargo fix completed with some issues"
    echo "Check /tmp/cargo_fix.log for details"
fi

print_step "Step 4: Applying clippy fixes"
echo "Running: cargo clippy --fix --allow-dirty --allow-staged"
if cargo clippy --fix --allow-dirty --allow-staged > /tmp/cargo_clippy_fix.log 2>&1; then
    print_success "Cargo clippy fix completed"
else
    print_warning "Cargo clippy fix completed with some issues"
    echo "Check /tmp/cargo_clippy_fix.log for details"
fi

print_step "Step 5: Formatting code"
echo "Running: cargo fmt"
if cargo fmt > /tmp/cargo_fmt.log 2>&1; then
    print_success "Code formatting completed"
else
    print_warning "Code formatting had issues"
    cat /tmp/cargo_fmt.log
fi

print_step "Step 6: Final verification"
echo "Running final cargo check..."
if cargo check > /tmp/final_check.log 2>&1; then
    final_warnings=$(grep -c "warning:" /tmp/final_check.log || echo "0")
    if [ "$final_warnings" -eq 0 ]; then
        print_success "All warnings resolved! 🎉"
    else
        print_warning "Still have $final_warnings warnings remaining"
        echo "These may require manual intervention:"
        grep "warning:" /tmp/final_check.log | head -10
    fi
else
    print_error "Final check failed"
    cat /tmp/final_check.log
    exit 1
fi

print_step "Step 7: Summary"
echo "================================"
echo "Initial warnings: $warning_count"
echo "Final warnings: $final_warnings"
if [ "$final_warnings" -lt "$warning_count" ]; then
    improvement=$((warning_count - final_warnings))
    print_success "Improved by $improvement warnings!"
else
    print_warning "No improvement in warning count"
fi

print_step "Step 8: Recommendations"
echo "🔧 Manual cleanup recommendations:"
echo "1. Review remaining unused imports and variables"
echo "2. Consider removing dead code (unused constants/functions)"
echo "3. Add #[allow(dead_code)] for intentionally unused code"
echo "4. Consider adding unit tests for better code coverage"
echo "5. Review TODO comments and implement missing features"

print_step "Step 9: Next steps"
echo "📋 Suggested next actions:"
echo "1. Run: git diff to review changes"
echo "2. Test the application: cargo run"
echo "3. Run API tests: ./test_api.sh"
echo "4. Commit changes: git add . && git commit -m 'Clean up code warnings'"

echo ""
print_success "Code cleanup completed! 🎉"
echo "Check the log files in /tmp/ for detailed information."

# Cleanup temp files
rm -f /tmp/cargo_*.log /tmp/final_check.log

echo ""
echo "💡 Pro tip: Run this script regularly to maintain code quality!"
