#!/bin/bash

# 更新软件包列表
sudo apt update

# 安装 XFCE 桌面环境
sudo apt install -y xfce4 xfce4-goodies

# 安装 VNC 服务器
sudo apt install -y tigervnc-standalone-server tigervnc-common

# 设置 VNC 密码
vncpasswd

# 创建 VNC 配置文件
cat > ~/.vnc/xstartup <<EOL
#!/bin/sh

unset SESSION_MANAGER
unset DBUS_SESSION_BUS_ADDRESS

[ -x /etc/vnc/xstartup ] && exec /etc/vnc/xstartup
[ -r $HOME/.Xresources ] && xrdb $HOME/.Xresources
xsetroot -solid grey
vncconfig -iconic &
x-terminal-emulator -geometry 80x24+10+10 -ls -title "$VNCDESKTOP Desktop" &
startxfce4 &
EOL

# 使 VNC 配置文件可执行
chmod +x ~/.vnc/xstartup

# 启动 VNC 服务器
vncserver :1

echo "XFCE 桌面环境安装完成！"
echo "请使用 VNC 客户端连接到服务器：vncviewer ************:1"