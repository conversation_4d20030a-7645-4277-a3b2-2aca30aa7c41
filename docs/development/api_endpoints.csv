Category,Method,Endpoint,Description,Access Level,Status
Public,GET,/,Root endpoint,Public,Active
Public,GET,/health,Health check,Public,Active
Authentication,POST,/api/auth/login,User login,Public,Active
Authentication,GET,/api/auth/roles,Get all roles,Public,Active
Authentication,GET,/api/auth/skill-groups,Get skill groups,Public,Active
Authentication,GET,/api/auth/me,Get current user,Protected,Active
Authentication,POST,/api/auth/users,Create user,Admin,Active
User Management,GET,/api/users,Get all users,Protected,Active
User Management,GET,/api/users/{id},Get user by ID,Protected,Active
User Management,POST,/api/users/{id}/status,Update user status,Protected,Active
User Management,POST,/api/users/{id}/roles,Update user roles,Protected,Active
User Management,POST,/api/users/{id}/skills,Update user skills,Protected,Active
User Management,DELETE,/api/users/{id},Delete user,Protected,Active
Machine Management,GET,/api/machines,Get all machines,Protected,Active
Machine Management,POST,/api/machines,Create machine,Protected,Active
Machine Management,GET,/api/machines/{id},Get machine by ID,Protected,Active
Machine Management,PUT,/api/machines/{id},Update machine,Protected,Active
Machine Management,DELETE,/api/machines/{id},Delete machine,Protected,Active
Machine Management,POST,/api/machines/{id}/status,Update machine status,Protected,Active
Parts Management,GET,/api/parts,Get all parts,Protected,Active
Parts Management,POST,/api/parts,Create part,Protected,Active
Parts Management,GET,/api/parts/{id},Get part by ID,Protected,Active
Parts Management,PUT,/api/parts/{id},Update part,Protected,Active
Parts Management,DELETE,/api/parts/{id},Delete part,Protected,Active
Parts Management,GET,/api/parts/{id}/routing,Get part routing,Protected,Disabled
Parts Management,POST,/api/parts/{id}/routing/reorder,Reorder routing steps,Protected,Disabled
Parts Management,POST,/api/parts/{id}/routing/copy,Copy routing,Protected,Disabled
Project Management,GET,/api/projects,Get all projects,Protected,Active
Project Management,POST,/api/projects,Create project,Protected,Active
Project Management,GET,/api/projects/{id},Get project by ID,Protected,Active
Project Management,GET,/api/projects/{id}/full,Get project with BOM,Protected,Active
Project Management,PUT,/api/projects/{id},Update project,Protected,Active
Project Management,DELETE,/api/projects/{id},Delete project,Protected,Active
Project Management,GET,/api/projects/{id}/bom,Get project BOM,Protected,Active
Project Management,POST,/api/projects/{id}/bom,Add BOM item,Protected,Active
Project Management,GET,/api/projects/{id}/work-orders,Get work orders by project,Protected,Active
Project Management,POST,/api/projects/work-orders/create,Create work orders from project,Protected,Active
BOM Management,PUT,/api/bom/{id},Update BOM item,Protected,Active
BOM Management,DELETE,/api/bom/{id},Remove BOM item,Protected,Active
Routing Management,GET,/api/routings,Get all routings,Protected,Disabled
Routing Management,POST,/api/routings,Create routing,Protected,Disabled
Routing Management,GET,/api/routings/{id},Get routing by ID,Protected,Disabled
Routing Management,PUT,/api/routings/{id},Update routing,Protected,Disabled
Routing Management,DELETE,/api/routings/{id},Delete routing,Protected,Disabled
Work Orders,GET,/api/work-orders,Get all work orders,Protected,Active
Work Orders,POST,/api/work-orders,Create work order,Protected,Active
Work Orders,GET,/api/work-orders/{id},Get work order by ID,Protected,Active
Work Orders,PUT,/api/work-orders/{id},Update work order,Protected,Active
Work Orders,DELETE,/api/work-orders/{id},Delete work order,Protected,Active
Work Orders,POST,/api/work-orders/{id}/status,Update work order status,Protected,Active
Work Orders,POST,/api/work-orders/{id}/plan-tasks,Create plan tasks from work order,Protected,Active
Production Planning,GET,/api/plan-tasks,Get all plan tasks,Protected,Active
Production Planning,POST,/api/plan-tasks,Create plan task,Protected,Active
Production Planning,GET,/api/plan-tasks/{id},Get plan task by ID,Protected,Active
Production Planning,PUT,/api/plan-tasks/{id},Update plan task,Protected,Active
Production Planning,DELETE,/api/plan-tasks/{id},Delete plan task,Protected,Active
Production Planning,POST,/api/plan-tasks/{id}/status,Update plan task status,Protected,Active
Production Planning,POST,/api/plan-tasks/{id}/reschedule,Reschedule plan task,Protected,Active
Production Planning,GET,/api/planning/gantt,Get Gantt chart data,Protected,Active
Production Planning,GET,/api/planning/plan-tasks,Get all plan tasks (alt),Protected,Active
Execution Tracking,GET,/api/execution/logs,Get execution logs,Protected,Active
Execution Tracking,POST,/api/execution/logs,Create execution log,Protected,Active
Execution Tracking,POST,/api/execution/tasks/start,Start task execution,Protected,Active
Execution Tracking,POST,/api/execution/tasks/complete,Complete task execution,Protected,Active
Execution Tracking,POST,/api/execution/tasks/pause,Pause task execution,Protected,Active
Execution Tracking,POST,/api/execution/tasks/resume,Resume task execution,Protected,Active
Execution Tracking,POST,/api/execution/barcode/validate,Validate barcode,Protected,Active
Execution Tracking,GET,/api/execution/tasks/active,Get active tasks for user,Protected,Active
Execution Tracking,GET,/api/execution/dashboard,Get shop floor dashboard,Protected,Active
Dashboard,GET,/api/dashboard/overview,Get dashboard overview,Protected,Active
Dashboard,GET,/api/dashboard/production-summary,Get production summary,Protected,Active
Dashboard,GET,/api/dashboard/work-order-status,Get work order status,Protected,Active
Dashboard,GET,/api/dashboard/machine-utilization,Get machine utilization,Protected,Active
Dashboard,GET,/api/dashboard/skill-group-performance,Get skill group performance,Protected,Active
Dashboard,GET,/api/dashboard/recent-activities,Get recent activities,Protected,Active
Dashboard,GET,/api/dashboard/kpi-metrics,Get KPI metrics,Protected,Active
Dashboard,GET,/api/dashboard/trends,Get trend data,Protected,Active
Reporting,POST,/api/reports/production,Generate production report,Protected,Active
Quality Management,GET,/api/quality/inspections,Get all quality inspections,Protected,Active
Quality Management,POST,/api/quality/inspections,Create quality inspection,Protected,Active
Quality Management,GET,/api/quality/inspections/{id},Get quality inspection by ID,Protected,Active
Quality Management,PUT,/api/quality/inspections/{id},Update quality inspection,Protected,Active
Quality Management,POST,/api/quality/checkpoints,Create quality checkpoint,Protected,Active
Quality Management,POST,/api/quality/reports,Generate quality report,Protected,Active
Quality Management,GET,/api/quality/metrics,Get quality metrics,Protected,Active
Quality Management,GET,/api/quality/pending,Get pending inspections,Protected,Active
Audit Logging,GET,/api/audit/logs,Get audit logs,Admin,Active
Audit Logging,GET,/api/audit/trail/{entity_type}/{entity_id},Get audit trail,Protected,Active
Audit Logging,GET,/api/audit/summary,Get audit summary,Admin,Active
Audit Logging,GET,/api/audit/statistics,Get audit statistics,Admin,Active
Audit Logging,POST,/api/audit/reports,Generate audit report,Admin,Active
Audit Logging,GET,/api/audit/export,Export audit logs,Admin,Active
Audit Logging,POST,/api/audit/cleanup,Cleanup old audit logs,Admin,Active
Skill Groups,GET,/api/skill-groups,Get all skill groups,Protected,Active
