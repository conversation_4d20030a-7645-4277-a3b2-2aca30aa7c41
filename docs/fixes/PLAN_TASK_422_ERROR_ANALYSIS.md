# 计划任务422错误分析和解决方案

**分析时间**: 2025-07-19 14:17:00  
**错误类型**: 422 Unprocessable Entity  
**错误位置**: PUT /api/plan-tasks/{id}  
**状态**: 🔍 分析中

## 🐛 问题描述

用户在尝试更新计划任务时遇到422错误：

```
PUT http://localhost:3000/api/plan-tasks/1 422 (Unprocessable Entity)
```

错误发生在PlanTasks.tsx的handleRescheduleSubmit函数中，当用户尝试重新调度任务时间时。

## 🔍 错误分析

### 1. 422错误的可能原因

422 Unprocessable Entity错误通常表示：
- **权限不足**: 用户没有更新计划任务的权限
- **认证失败**: 用户未登录或token过期
- **数据验证失败**: 请求数据格式不正确
- **业务逻辑验证失败**: 违反了业务规则

### 2. 权限要求

根据后端代码分析，更新计划任务需要以下角色之一：
- `admin` - 管理员
- `process_engineer` - 工艺工程师  
- `planner` - 计划员

### 3. 数据库用户状态

通过数据库查询确认：
- ✅ 数据库中存在用户
- ✅ 用户有正确的角色分配
- ✅ 用户状态为活跃

### 4. 前端请求分析

前端发送的请求数据：
```typescript
{
  id: selectedTask.id,
  data: {
    skill_group_id: values.skill_group_id,
    machine_id: values.machine_id,  // 已修复：移除了 || null
    planned_start: startTime.format('YYYY-MM-DD HH:mm:00'),
    planned_end: endTime.format('YYYY-MM-DD HH:mm:00'),
  }
}
```

## 🛠️ 已实施的修复

### 1. 修复前端数据格式
- **问题**: `machine_id: values.machine_id || null` 可能导致类型不匹配
- **修复**: 改为 `machine_id: values.machine_id`
- **文件**: `frontend/src/pages/PlanTasks.tsx:340`

### 2. 增强后端错误信息
- **添加**: 权限检查的详细日志
- **改进**: 错误消息包含用户当前角色信息
- **文件**: `src/handlers/plan_tasks.rs:152-169`

## 🎯 可能的解决方案

### 方案1: 用户认证问题
如果是认证问题，用户需要：
1. **重新登录**: 确保有有效的认证token
2. **检查权限**: 确认用户有正确的角色
3. **刷新页面**: 重新加载认证状态

### 方案2: 前端认证状态管理
可能需要改进前端的认证状态管理：
```typescript
// 检查认证状态
const isAuthenticated = useAuthStore(state => state.isAuthenticated);
const userRoles = useAuthStore(state => state.user?.roles);

// 在发送请求前验证权限
if (!isAuthenticated || !hasRequiredRole(userRoles)) {
  // 处理权限不足的情况
}
```

### 方案3: 后端权限检查优化
可以改进后端的权限检查逻辑：
```rust
// 更详细的权限检查
if auth_user.roles.is_empty() {
    return Err((
        StatusCode::UNAUTHORIZED,
        Json(ErrorResponse {
            error: "no_roles".to_string(),
            message: "User has no assigned roles".to_string(),
        }),
    ));
}
```

## 🔧 调试步骤

### 1. 检查用户认证状态
```bash
# 检查当前用户信息
curl -X GET "http://localhost:9000/api/auth/me" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. 检查用户角色
```sql
-- 查询用户角色
SELECT u.username, array_agg(r.role_name) as roles 
FROM users u 
LEFT JOIN user_roles ur ON u.id = ur.user_id 
LEFT JOIN roles r ON ur.role_id = r.id 
WHERE u.username = 'YOUR_USERNAME'
GROUP BY u.username;
```

### 3. 测试API端点
```bash
# 测试计划任务更新
curl -X PUT "http://localhost:9000/api/plan-tasks/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "skill_group_id": 1,
    "machine_id": 1,
    "planned_start": "2025-07-20T02:00:00",
    "planned_end": "2025-07-20T04:00:00"
  }'
```

## 📊 系统状态检查

### 后端服务
- ✅ 服务正常运行在端口9000
- ✅ 数据库连接正常
- ✅ 权限检查逻辑已增强

### 前端服务  
- ✅ 服务正常运行在端口3000
- ✅ 数据格式已修复
- ❓ 认证状态需要验证

### 数据库状态
- ✅ 用户表正常
- ✅ 角色分配正常
- ✅ 计划任务表正常

## 🎓 下一步行动

### 立即行动
1. **用户重新登录**: 确保有有效的认证token
2. **验证权限**: 确认用户有admin/planner/process_engineer角色
3. **测试功能**: 重新尝试更新计划任务

### 长期改进
1. **认证状态管理**: 改进前端认证状态的持久化
2. **错误处理**: 增强前端的错误处理和用户反馈
3. **权限UI**: 在UI中显示用户权限状态
4. **自动刷新**: 实现token自动刷新机制

## 🚨 紧急解决方案

如果用户急需使用功能，可以：

1. **临时提升权限**:
```sql
-- 给用户添加admin角色（临时）
INSERT INTO user_roles (user_id, role_id) 
SELECT u.id, r.id 
FROM users u, roles r 
WHERE u.username = 'YOUR_USERNAME' 
AND r.role_name = 'admin'
AND NOT EXISTS (
  SELECT 1 FROM user_roles ur 
  WHERE ur.user_id = u.id AND ur.role_id = r.id
);
```

2. **重新登录**: 清除浏览器缓存并重新登录

3. **检查网络**: 确保前后端通信正常

## 📝 总结

422错误最可能的原因是用户认证或权限问题。建议用户：
1. 重新登录系统
2. 确认有正确的角色权限
3. 如果问题持续，联系管理员检查用户权限配置

系统的技术修复已经完成，现在需要解决用户认证和权限的问题。
