# MES系统 Windows脚本完整指南

## 📋 概述

为了支持Windows用户，我们为MES制造执行系统创建了完整的Windows批处理脚本集合。这些脚本提供了与Linux/Unix版本相同的功能，确保Windows用户能够轻松安装、配置和管理MES系统。

## 🎯 Windows脚本列表

### 核心脚本

| 脚本文件 | 对应Linux版本 | 主要功能 | 使用场景 |
|---------|--------------|---------|---------|
| `quick_start.bat` | `quick_start.sh` | 一键快速启动 | 首次安装或快速部署 |
| `install.bat` | `install.sh` | 安装系统依赖 | 环境准备 |
| `init-database.bat` | `init-database.sh` | 数据库初始化 | 数据库设置 |
| `build_production.bat` | `build_production.sh` | 生产构建 | 编译发布版本 |
| `start_all.bat` | `start_all.sh` | 启动所有服务 | 日常启动 |
| `stop_all.bat` | - | 停止所有服务 | 服务管理 |
| `check-system.bat` | `check-system.sh` | 系统状态检查 | 故障诊断 |
| `docker-install.bat` | `docker-install.sh` | Docker部署 | 容器化部署 |

### 文档文件

| 文件名 | 功能 |
|-------|------|
| `README_WINDOWS.md` | Windows用户完整指南 |
| `WINDOWS_SCRIPTS_SUMMARY.md` | 脚本功能总结 |

## 🔧 脚本功能详解

### 1. quick_start.bat - 一键快速启动

**功能特点:**
- 自动检测系统环境
- 缺少依赖时自动调用安装脚本
- 自动初始化数据库
- 自动构建项目
- 自动启动所有服务
- 提供详细的进度反馈

**使用方法:**
```batch
# 双击运行或命令行执行
quick_start.bat
```

**适用场景:**
- 首次安装MES系统
- 快速演示部署
- 新环境快速搭建

### 2. install.bat - 依赖安装

**功能特点:**
- 自动下载并安装Rust环境
- 自动下载并安装Node.js LTS版本
- 提供PostgreSQL安装指导
- 安装必要的Rust工具链
- 安装前端依赖包

**安装组件:**
- Rust (通过rustup-init.exe)
- Node.js LTS 20.x
- npm包管理器
- sqlx-cli工具
- 前端依赖包

### 3. init-database.bat - 数据库初始化

**功能特点:**
- 交互式数据库配置
- 自动创建数据库
- 运行所有数据库迁移
- 创建默认管理员用户
- 生成环境配置文件

**配置选项:**
- 数据库主机和端口
- 数据库名称和用户
- 密码安全输入
- 连接测试验证

### 4. build_production.bat - 生产构建

**功能特点:**
- 多种构建选项
- 优化编译设置
- 前后端分离构建
- 构建结果验证
- 清理重建选项

**构建选项:**
1. 完整构建 (后端 + 前端)
2. 仅构建后端
3. 仅构建前端
4. 清理并重新构建

### 5. start_all.bat - 服务启动

**功能特点:**
- 多种启动模式
- 服务状态检查
- 端口冲突处理
- 服务健康检查
- 独立窗口运行

**启动模式:**
1. 快速启动 (推荐)
2. 开发模式启动
3. 生产模式启动
4. 仅启动后端
5. 仅启动前端

### 6. stop_all.bat - 服务停止

**功能特点:**
- 智能进程识别
- 强制终止选项
- 端口释放
- 清理临时文件
- 状态确认

### 7. check-system.bat - 系统检查

**功能特点:**
- 全面的环境检查
- 依赖版本验证
- 服务状态监控
- 端口占用检查
- 数据库连接测试

**检查项目:**
- 操作系统信息
- Rust环境
- Node.js环境
- PostgreSQL状态
- 项目文件完整性
- 构建文件状态
- 端口占用情况
- 数据库连接

### 8. docker-install.bat - Docker部署

**功能特点:**
- 多种部署选项
- 自动生成Docker配置
- 容器编排管理
- 网络配置
- 数据持久化

**部署选项:**
1. 完整部署 (数据库 + 后端 + 前端)
2. 仅部署数据库
3. 仅部署应用
4. 开发模式部署

## 🎨 Windows特有优化

### 1. 编码支持
- 使用UTF-8编码 (`chcp 65001`)
- 支持中文字符显示
- 正确处理路径和文件名

### 2. 权限处理
- 检测管理员权限
- 提供权限提升建议
- 安全的文件操作

### 3. 服务管理
- Windows服务检查
- 进程管理优化
- 端口占用处理

### 4. 用户体验
- 彩色输出支持
- 进度条显示
- 详细错误信息
- 交互式配置

## 🔄 与Linux版本的对比

| 功能 | Linux脚本 | Windows脚本 | 差异说明 |
|------|----------|-------------|---------|
| 包管理 | apt/yum | 直接下载安装 | Windows无统一包管理器 |
| 服务管理 | systemctl | 进程管理 | 不同的服务管理方式 |
| 权限管理 | sudo | 管理员模式 | 不同的权限提升机制 |
| 路径分隔符 | / | \ | 操作系统差异 |
| 脚本语法 | bash | batch | 不同的脚本语言 |

## 📊 使用统计和建议

### 推荐使用顺序

1. **首次安装:**
   ```
   quick_start.bat (一键完成所有步骤)
   ```

2. **分步安装:**
   ```
   install.bat → init-database.bat → build_production.bat → start_all.bat
   ```

3. **日常使用:**
   ```
   start_all.bat (启动)
   stop_all.bat (停止)
   check-system.bat (检查)
   ```

4. **故障排除:**
   ```
   check-system.bat → 根据提示修复 → start_all.bat
   ```

### 性能优化建议

1. **首次安装时间:**
   - 完整安装: 15-30分钟
   - 依赖下载: 5-10分钟
   - 项目构建: 5-15分钟

2. **系统要求:**
   - 最低: 4GB RAM, 2GB存储
   - 推荐: 8GB RAM, 5GB存储

3. **网络要求:**
   - 安装阶段需要互联网连接
   - 运行阶段可离线使用

## 🛡️ 安全考虑

### 1. 密码处理
- 数据库密码安全输入
- 不在脚本中硬编码密码
- 环境变量保护

### 2. 权限控制
- 最小权限原则
- 管理员权限提示
- 文件权限检查

### 3. 网络安全
- 本地服务绑定
- 防火墙配置建议
- HTTPS配置选项

## 📝 维护和更新

### 版本同步
- 与Linux版本功能保持一致
- 定期更新依赖版本
- 兼容性测试

### 错误处理
- 详细的错误信息
- 自动恢复机制
- 日志记录功能

### 用户反馈
- 收集使用统计
- 优化用户体验
- 持续改进脚本

## 🎯 总结

Windows脚本集合为MES系统提供了完整的Windows平台支持，确保Windows用户能够享受与Linux用户相同的便捷体验。通过这些脚本，用户可以：

- **快速部署**: 一键安装和启动
- **灵活配置**: 多种部署选项
- **易于维护**: 完善的管理工具
- **故障诊断**: 全面的检查功能
- **跨平台**: 与Linux版本功能对等

这套脚本大大降低了Windows用户的使用门槛，提高了系统的可访问性和易用性。
