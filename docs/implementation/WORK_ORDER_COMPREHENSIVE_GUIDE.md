# MES系统工单功能详细指南

## 📋 工单包含的元素

### 基础字段
- **工单ID** (`id`): 系统自动生成的唯一标识符
- **项目BOM ID** (`project_bom_id`): 关联的项目BOM项目
- **数量** (`quantity`): 生产数量
- **状态** (`status`): 工单当前状态
- **交期** (`due_date`): 预期完成日期（可选）
- **创建时间** (`created_at`): 工单创建时间戳

### 关联信息（通过连表查询获得）
- **项目信息**:
  - 项目ID (`project_id`)
  - 项目名称 (`project_name`)
  - 客户名称 (`customer_name`)
- **零件信息**:
  - 零件ID (`part_id`)
  - 零件编号 (`part_number`)
  - 零件名称 (`part_name`)
  - 版本 (`version`)
  - 规格说明 (`specifications`)
- **BOM信息**:
  - BOM数量 (`bom_quantity`)

## 🔄 工单状态管理

### 可用状态
1. **待处理** (`pending`) - 默认状态
2. **已计划** (`planned`) - 已制定生产计划
3. **进行中** (`in_progress`) - 正在生产
4. **已完成** (`completed`) - 生产完成
5. **已取消** (`cancelled`) - 工单取消

### 状态流转
```
pending → planned → in_progress → completed
    ↓         ↓           ↓
cancelled ← cancelled ← cancelled
```

## 🚀 工单创建方式

### 1. 手动创建单个工单

#### API端点
```
POST /api/work-orders
```

#### 请求参数
```json
{
  "project_bom_id": 1,
  "quantity": 100,
  "due_date": "2025-01-15"  // 可选
}
```

#### 权限要求
- 管理员 (`admin`)
- 工艺工程师 (`process_engineer`)
- 生产计划员 (`planner`)

#### 前端操作
1. 进入工单管理页面
2. 点击"创建工单"按钮
3. 选择项目BOM项目
4. 输入生产数量
5. 设置交期（可选）
6. 提交创建

### 2. 从项目自动创建工单 ✅

#### API端点
```
POST /api/work-orders/from-project
```

#### 请求参数
```json
{
  "project_id": 1,
  "due_date": "2025-01-15",  // 可选
  "multiplier": 1.5          // 可选，数量倍数，默认1.0
}
```

#### 自动创建逻辑
1. **获取项目BOM**: 查询指定项目的所有BOM项目
2. **批量创建工单**: 为每个BOM项目创建一个工单
3. **数量计算**: `工单数量 = BOM数量 × 倍数`
4. **统一交期**: 所有工单使用相同的交期

#### 使用场景
- **项目启动**: 项目确认后一次性创建所有零件的工单
- **批量生产**: 需要按比例增减生产数量
- **重复订单**: 相同项目的重复生产

#### 前端操作
1. 进入项目详情页面
2. 点击"创建工单"按钮
3. 设置生产倍数（可选）
4. 设置统一交期（可选）
5. 确认创建

## 🔧 工单管理功能

### 查询功能
- **按项目查询**: 查看特定项目的所有工单
- **按状态筛选**: 筛选特定状态的工单
- **按零件查询**: 查看特定零件的工单
- **分页查询**: 支持大量数据的分页显示

### 更新功能
- **修改数量**: 调整生产数量
- **更新状态**: 变更工单状态
- **调整交期**: 修改预期完成日期
- **状态快速更新**: 单独的状态更新API

### 删除功能
- **单个删除**: 删除指定工单
- **权限控制**: 只有特定角色可以删除

## 📊 工单与生产计划的关系

### 计划任务生成
工单创建后，可以基于工单生成生产计划任务：

1. **工艺路线**: 根据零件的工艺路线
2. **计划任务**: 为每个工艺步骤创建计划任务
3. **资源分配**: 分配技能组或具体设备
4. **时间安排**: 设置计划开始和结束时间

### API端点
```
POST /api/work-orders/{work_order_id}/plan-tasks
```

## 🎯 自动化创建的优势

### 1. 效率提升
- **批量操作**: 一次创建多个工单
- **减少重复**: 避免逐个手动创建
- **快速启动**: 项目确认后快速进入生产

### 2. 数据一致性
- **统一标准**: 所有工单使用相同的参数
- **关联完整**: 自动建立项目-BOM-工单关系
- **错误减少**: 避免手动输入错误

### 3. 灵活性
- **比例调整**: 支持数量倍数调整
- **选择性创建**: 可以选择特定BOM项目
- **批量修改**: 支持批量更新操作

## 📋 使用流程示例

### 场景1：新项目启动
1. **项目创建**: 创建新项目
2. **BOM配置**: 添加项目所需的零件和数量
3. **工单生成**: 使用"从项目创建工单"功能
4. **计划制定**: 为工单创建生产计划任务
5. **生产执行**: 开始生产执行

### 场景2：重复订单
1. **选择项目**: 选择已有项目
2. **调整倍数**: 设置生产倍数（如1.5倍）
3. **批量创建**: 自动创建所有工单
4. **更新交期**: 根据新订单调整交期
5. **开始生产**: 进入生产流程

### 场景3：紧急订单
1. **手动创建**: 针对特定零件手动创建工单
2. **优先级设置**: 设置较早的交期
3. **快速计划**: 立即制定生产计划
4. **资源调配**: 分配优先资源
5. **跟踪执行**: 密切跟踪生产进度

## 🔍 前端界面功能

### 工单列表页面
- **表格显示**: 显示所有工单信息
- **状态标签**: 彩色标签显示工单状态
- **筛选功能**: 按项目、状态、零件筛选
- **操作按钮**: 查看、编辑、删除操作

### 工单详情页面
- **基本信息**: 显示工单基本信息
- **关联信息**: 显示项目、零件、BOM信息
- **计划任务**: 显示相关的生产计划任务
- **执行记录**: 显示生产执行记录

### 创建工单页面
- **单个创建**: 手动创建单个工单
- **批量创建**: 从项目批量创建工单
- **参数设置**: 数量、交期、倍数设置
- **预览确认**: 创建前预览工单信息

## ✅ 总结

MES系统的工单功能提供了完整的工单生命周期管理：

1. **灵活创建**: 支持手动和自动创建
2. **完整信息**: 包含项目、零件、BOM关联信息
3. **状态管理**: 完整的状态流转控制
4. **权限控制**: 基于角色的操作权限
5. **自动化**: 从项目批量创建工单的自动化功能

这个设计既满足了手动精确控制的需求，也提供了批量自动化的效率，是一个功能完整且实用的工单管理系统。
