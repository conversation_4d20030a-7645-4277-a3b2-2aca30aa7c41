# MES系统后台服务运行总结

## 🎯 任务完成状态

✅ **端口迁移完成**
- 后端端口：8080 → 9000
- 前端端口：4173 → 9999 (预览模式) / 自动分配 (开发模式)

✅ **IPv6双栈支持**
- 后端支持IPv4和IPv6双栈监听
- 前端支持IPv4和IPv6双栈监听

✅ **后台运行配置**
- 服务已配置为后台运行
- 使用nohup确保进程不会因终端关闭而终止

## 🚀 当前运行状态

### 后端服务
- **进程ID**: 48779
- **可执行文件**: ./target/release/mes-system
- **监听端口**: 9000 (双栈 *:9000)
- **IPv4访问**: http://localhost:9000
- **IPv6访问**: http://[::1]:9000
- **健康检查**: ✅ 正常 (返回 "OK")

### 前端服务
- **进程ID**: 48867
- **运行模式**: 开发模式 (vite dev)
- **监听端口**: 3003 (双栈 *:3003)
- **IPv4访问**: http://localhost:3003
- **IPv6访问**: http://[::1]:3003
- **服务状态**: ✅ 正常 (返回HTML页面)

## 📁 管理脚本

### 启动脚本
- **后台启动**: `./start_background.sh`
- **前台启动**: `./start.sh`

### 停止脚本
- **停止服务**: `./stop.sh`

### 状态检查
- **系统状态**: `./status.sh`
- **详细状态**: `./check_status.sh`

## 📊 技术实现

### IPv6双栈配置
```rust
// 后端 (src/main.rs)
// 优先尝试双栈绑定 [::]
let dual_stack_addr = SocketAddr::from(([0, 0, 0, 0, 0, 0, 0, 0], port));
```

```typescript
// 前端 (frontend/vite.config.ts)
server: {
  host: '::', // IPv6双栈监听
  port: 3000,
}
```

### 后台运行配置
```bash
# 使用nohup确保后台运行
nohup ./target/release/mes-system > backend.log 2>&1 &
nohup npm run dev > ../frontend.log 2>&1 &
```

## 🔧 环境变量

```bash
# .env 文件配置
SERVER_PORT=9000
ENABLE_IPV6=true
```

## 📝 日志文件

- **后端日志**: `backend.log`
- **前端日志**: `frontend.log`

### 查看实时日志
```bash
# 后端日志
tail -f backend.log

# 前端日志
tail -f frontend.log
```

## 🌐 访问地址

### 生产访问
- **前端**: http://localhost:3003 (IPv4) / http://[::1]:3003 (IPv6)
- **后端API**: http://localhost:9000 (IPv4) / http://[::1]:9000 (IPv6)

### 外部访问 (如果需要)
- **前端**: http://[服务器IP]:3003
- **后端API**: http://[服务器IP]:9000

## 🔍 验证命令

### 快速状态检查
```bash
# 检查进程
ps aux | grep -E "(mes-system|vite)" | grep -v grep

# 检查端口
ss -tlnp | grep -E ":(9000|3003)"

# 测试连接
curl -s http://localhost:9000/health
curl -s http://[::1]:9000/health
```

### 完整验证
```bash
# IPv4连接测试
curl -s http://localhost:9000/health && echo " - 后端IPv4正常"
curl -s http://localhost:3003 | head -1 && echo " - 前端IPv4正常"

# IPv6连接测试
curl -s http://[::1]:9000/health && echo " - 后端IPv6正常"
curl -s http://[::1]:3003 | head -1 && echo " - 前端IPv6正常"
```

## 🛡️ 安全注意事项

1. **防火墙配置**: 确保端口9000和3003在防火墙中正确配置
2. **外部访问**: 如需外部访问，请配置适当的安全措施
3. **日志轮转**: 定期清理或轮转日志文件以避免磁盘空间问题

## 🔄 重启和维护

### 重启服务
```bash
./stop.sh && ./start_background.sh
```

### 更新代码后重启
```bash
# 停止服务
./stop.sh

# 重新构建后端
cargo build --release

# 重新启动
./start_background.sh
```

## ✨ 特性总结

- ✅ 端口成功迁移到9000/9999
- ✅ IPv6双栈支持完全启用
- ✅ 后台运行配置完成
- ✅ 进程管理脚本完善
- ✅ 日志记录正常工作
- ✅ 健康检查通过
- ✅ 外部访问支持

## 📞 故障排除

如果服务无法启动：
1. 检查端口是否被占用：`lsof -i :9000`
2. 查看日志文件：`tail -20 backend.log`
3. 检查数据库连接：`psql $DATABASE_URL -c "SELECT 1;"`
4. 重新构建：`cargo build --release`

---

**系统已成功配置为后台运行，支持IPv6双栈，端口已迁移完成！**
