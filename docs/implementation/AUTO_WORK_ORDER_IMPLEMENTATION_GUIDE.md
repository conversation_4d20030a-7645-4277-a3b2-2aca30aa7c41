# 自动工单创建功能实现指南

## 🎯 功能概述

已成功实现自动工单创建功能，支持在添加零件、创建工艺或添加BOM时自动创建工单。系统提供灵活的配置选项，既支持自动化创建，也保留手动创建的完整功能。

## ✅ 实现的功能

### 1. 自动触发机制

#### 三种触发类型
- **零件创建** (`part_created`): 创建新零件时自动为包含该零件的BOM项目创建工单
- **工艺创建** (`routing_created`): 为零件创建工艺路线时自动创建工单
- **BOM添加** (`bom_added`): 在项目中添加BOM项目时自动创建工单

#### 触发时机
- **零件创建后**: 立即检查该零件是否在任何项目的BOM中，如果是则创建工单
- **工艺创建后**: 为该零件相关的BOM项目创建工单
- **BOM添加后**: 立即为新添加的BOM项目创建工单

### 2. 配置系统

#### 配置级别
- **全局配置**: 对所有项目生效的默认规则
- **项目特定配置**: 针对特定项目的专门规则（优先级更高）

#### 配置选项
- **启用状态**: 可以启用或禁用自动创建
- **默认数量**: 指定工单数量，留空则使用BOM数量
- **默认交期**: 设置从当前日期开始的天数
- **自动创建计划任务**: 是否同时创建生产计划任务

### 3. 数据库设计

#### 配置表 (`auto_work_order_configs`)
- 存储自动工单创建的配置规则
- 支持全局和项目特定配置
- 包含触发类型、默认参数等设置

#### 日志表 (`auto_work_order_logs`)
- 记录每次自动创建的详细信息
- 追踪触发事件和创建结果
- 便于审计和问题排查

### 4. 后端API

#### 配置管理API
- `GET /api/auto-work-orders/configs` - 获取配置列表
- `POST /api/auto-work-orders/configs` - 创建新配置
- `PUT /api/auto-work-orders/configs/:id` - 更新配置
- `DELETE /api/auto-work-orders/configs/:id` - 删除配置

#### 手动触发API
- `POST /api/auto-work-orders/trigger` - 手动触发自动创建（用于测试）

### 5. 前端界面

#### 自动工单配置页面
- **配置管理**: 创建、编辑、删除自动工单配置
- **状态控制**: 启用/禁用特定配置
- **参数设置**: 配置默认数量、交期等参数
- **范围选择**: 选择全局或特定项目

#### 集成到现有流程
- **零件创建**: 创建成功后显示自动创建的工单信息
- **BOM管理**: 添加BOM项目后显示自动创建结果
- **工艺管理**: 创建工艺后触发相关工单创建

## 🔧 技术实现

### 事件驱动架构
```rust
// 触发事件结构
pub struct AutoWorkOrderTriggerEvent {
    pub trigger_type: String,    // 触发类型
    pub project_id: Option<i32>, // 项目ID
    pub part_id: Option<i32>,    // 零件ID
    pub bom_id: Option<i32>,     // BOM项目ID
    pub routing_id: Option<i32>, // 工艺ID
    pub quantity: Option<i32>,   // 数量
    pub user_id: i32,           // 操作用户
}
```

### 配置匹配逻辑
1. **查找匹配配置**: 根据触发类型和项目ID查找配置
2. **优先级处理**: 项目特定配置优先于全局配置
3. **状态检查**: 只处理启用状态的配置
4. **参数应用**: 使用配置中的默认参数创建工单

### 工单创建流程
1. **事件触发**: 零件/工艺/BOM操作触发事件
2. **配置查找**: 查找匹配的自动创建配置
3. **BOM查询**: 根据事件类型查找相关BOM项目
4. **工单创建**: 为每个BOM项目创建工单
5. **日志记录**: 记录自动创建的详细信息
6. **计划任务**: 可选择自动创建计划任务

## 🎯 使用场景

### 场景1: 新零件开发
1. **工艺工程师创建零件**: 定义零件基本信息
2. **自动触发**: 系统检查该零件是否在BOM中
3. **工单创建**: 自动为相关项目创建工单
4. **通知反馈**: 显示创建的工单信息

### 场景2: 项目BOM管理
1. **计划员添加BOM项目**: 在项目中添加新的零件需求
2. **立即创建工单**: 系统自动创建对应的生产工单
3. **参数应用**: 使用配置的默认数量和交期
4. **计划生成**: 可选择自动生成生产计划任务

### 场景3: 工艺路线完善
1. **工艺工程师创建工艺**: 为零件定义生产工艺
2. **工单补充**: 为该零件的所有BOM项目创建工单
3. **计划任务**: 自动创建基于工艺路线的计划任务

## 📋 配置建议

### 默认配置
```sql
-- BOM添加时自动创建工单（推荐启用）
INSERT INTO auto_work_order_configs 
(trigger_type, is_enabled, default_due_days, auto_create_plan_tasks)
VALUES ('bom_added', true, 30, false);

-- 零件创建时自动创建工单（可选启用）
INSERT INTO auto_work_order_configs 
(trigger_type, is_enabled, default_quantity, default_due_days)
VALUES ('part_created', false, 1, 30);

-- 工艺创建时自动创建工单（推荐启用）
INSERT INTO auto_work_order_configs 
(trigger_type, is_enabled, default_due_days, auto_create_plan_tasks)
VALUES ('routing_created', true, 30, true);
```

### 项目特定配置
- **紧急项目**: 设置较短的默认交期（如7天）
- **批量生产项目**: 启用自动计划任务创建
- **研发项目**: 可能禁用自动创建，采用手动控制

## 🔒 权限控制

### 配置管理权限
- **管理员**: 完整的配置管理权限
- **工艺工程师**: 可以创建和修改配置
- **生产计划员**: 可以手动触发自动创建

### 操作权限
- **零件创建**: 工艺工程师、管理员
- **BOM管理**: 工艺工程师、计划员、管理员
- **工艺创建**: 工艺工程师、管理员

## 🚀 优势特点

### 1. 效率提升
- **减少重复操作**: 自动创建工单，避免手动逐个创建
- **快速响应**: 零件或BOM变更后立即创建工单
- **流程简化**: 一次操作完成多个步骤

### 2. 灵活配置
- **可选启用**: 可以选择性启用不同的触发类型
- **参数定制**: 自定义默认数量、交期等参数
- **范围控制**: 支持全局和项目特定配置

### 3. 数据一致性
- **自动关联**: 确保工单与零件、BOM的正确关联
- **参数统一**: 使用配置的默认参数保证一致性
- **审计跟踪**: 完整的操作日志记录

### 4. 兼容性
- **保留手动创建**: 完全保留原有的手动创建功能
- **渐进式采用**: 可以逐步启用自动创建功能
- **灵活切换**: 随时可以启用或禁用自动创建

## 📈 后续扩展

### 功能增强
1. **条件触发**: 基于零件类型、项目状态等条件触发
2. **批量操作**: 支持批量配置和批量触发
3. **通知系统**: 自动创建后发送通知给相关人员
4. **模板系统**: 预定义常用的配置模板

### 集成优化
1. **工作流集成**: 与审批流程集成
2. **ERP集成**: 与外部ERP系统同步
3. **报表分析**: 自动创建效果的统计分析
4. **性能优化**: 大批量数据的处理优化

## ✅ 验证清单

- [x] 数据库表结构创建完成
- [x] 后端服务和API实现完成
- [x] 前端配置界面实现完成
- [x] 零件创建触发集成完成
- [x] BOM添加触发集成完成
- [x] 权限控制实施完成
- [x] 默认配置数据插入完成

## 🎉 总结

自动工单创建功能已成功实现，提供了：

1. **完整的自动化**: 支持三种触发类型的自动工单创建
2. **灵活的配置**: 全局和项目特定的配置选项
3. **友好的界面**: 直观的配置管理界面
4. **完善的集成**: 与现有零件、BOM、工艺管理无缝集成
5. **保留手动**: 完全保留原有的手动创建功能

这个功能大大提高了工单创建的效率，减少了重复操作，同时保持了系统的灵活性和可控性。
