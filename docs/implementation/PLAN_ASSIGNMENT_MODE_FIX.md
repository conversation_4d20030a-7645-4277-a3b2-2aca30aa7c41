# 计划分配模式切换错误修复报告

## 🐛 问题描述

用户在生产计划页面切换分配模式时遇到错误：
```
更新失败: Database error: no rows returned by a query that expected to return at least one row
```

## 🔍 问题分析

### 根本原因
后端的`set_plan_assignment_mode`方法调用了`update_config`方法，该方法要求配置记录必须已存在于数据库中。但是在首次使用时，`plan_assignment_mode`配置记录可能还没有被创建，导致更新操作失败。

### 错误流程
1. 用户在前端选择分配模式
2. 前端调用`setPlanAssignmentMode` API
3. 后端尝试更新`plan_assignment_mode`配置
4. `update_config`方法检查配置是否存在
5. 配置不存在，返回"no rows returned"错误

## ✅ 修复方案

### 后端修复
修改了`SystemConfigService`中的两个关键方法：

#### 1. `set_plan_assignment_mode`方法
```rust
pub async fn set_plan_assignment_mode(&self, mode: PlanAssignmentMode, user_id: i32) -> Result<SystemConfig, String> {
    // 检查配置是否存在
    let exists = sqlx::query_scalar!(
        "SELECT EXISTS(SELECT 1 FROM system_configs WHERE config_key = $1)",
        CONFIG_PLAN_ASSIGNMENT_MODE
    )
    .fetch_one(&self.pool)
    .await
    .map_err(|e| format!("Database error: {}", e))?;

    if !exists.unwrap_or(false) {
        // 配置不存在，创建新配置
        let create_request = CreateSystemConfigRequest {
            config_key: CONFIG_PLAN_ASSIGNMENT_MODE.to_string(),
            config_value: mode.to_string(),
            config_type: "string".to_string(),
            description: Some("计划任务分配模式：skill_group（技能组模式）或 machine（设备模式）".to_string()),
            category: "planning".to_string(),
        };
        
        return self.create_config(create_request, user_id).await;
    }

    // 配置存在，更新配置
    let request = UpdateSystemConfigRequest {
        config_value: Some(mode.to_string()),
        config_type: None,
        description: None,
        category: None,
        is_active: None,
    };

    self.update_config(CONFIG_PLAN_ASSIGNMENT_MODE, request, user_id).await
}
```

#### 2. `is_plan_assignment_mode_enabled`方法
```rust
pub async fn is_plan_assignment_mode_enabled(&self) -> Result<bool, String> {
    if let Some(config) = self.get_config_by_key(CONFIG_PLAN_ASSIGNMENT_MODE_ENABLED).await? {
        config.config_value.parse::<bool>()
            .map_err(|_| "Invalid boolean value for plan_assignment_mode_enabled".to_string())
    } else {
        // 配置不存在时，自动创建并默认启用
        let create_request = CreateSystemConfigRequest {
            config_key: CONFIG_PLAN_ASSIGNMENT_MODE_ENABLED.to_string(),
            config_value: "true".to_string(),
            config_type: "boolean".to_string(),
            description: Some("是否启用计划分配模式切换功能".to_string()),
            category: "planning".to_string(),
        };
        
        let _config = self.create_config(create_request, 0).await?;
        Ok(true)
    }
}
```

### 修复逻辑
1. **智能检测**: 在设置分配模式前先检查配置是否存在
2. **自动创建**: 如果配置不存在，自动创建默认配置
3. **正常更新**: 如果配置存在，执行正常的更新操作
4. **默认值**: 新创建的配置使用合理的默认值

## 🔧 技术细节

### 配置记录结构
- **config_key**: `plan_assignment_mode`
- **config_value**: `skill_group` 或 `machine`
- **config_type**: `string`
- **description**: 详细说明
- **category**: `planning`

### 启用状态配置
- **config_key**: `plan_assignment_mode_enabled`
- **config_value**: `true` 或 `false`
- **config_type**: `boolean`
- **description**: 功能启用状态说明
- **category**: `planning`

## 🎯 修复效果

### 用户体验改进
1. **首次使用**: 用户首次切换分配模式时不再报错
2. **自动初始化**: 系统自动创建必要的配置记录
3. **无缝切换**: 后续的模式切换操作正常工作
4. **数据持久化**: 用户的选择会被正确保存

### 系统稳定性
1. **容错性**: 处理配置不存在的情况
2. **向后兼容**: 不影响已有的配置记录
3. **数据完整性**: 确保配置数据的一致性
4. **错误处理**: 提供清晰的错误信息

## 🧪 测试验证

### 测试场景
1. **全新系统**: 在没有任何配置的新系统中测试
2. **模式切换**: 测试技能组模式和设备模式之间的切换
3. **权限验证**: 确保只有计划员和管理员可以修改
4. **数据持久化**: 验证设置的模式能够正确保存和读取

### 预期结果
- ✅ 首次切换不再报错
- ✅ 配置自动创建
- ✅ 模式切换正常工作
- ✅ 设置正确保存

## 📋 部署说明

### 部署步骤
1. 重新编译后端代码
2. 重启后端服务
3. 测试分配模式切换功能
4. 验证配置记录创建

### 注意事项
- 修复是向后兼容的，不会影响现有数据
- 新的配置记录会在首次使用时自动创建
- 不需要手动执行数据库迁移

## 🔄 后续优化

### 可能的改进
1. **批量初始化**: 在系统启动时预创建所有必要的配置
2. **配置验证**: 添加配置值的有效性验证
3. **审计日志**: 记录配置变更的详细日志
4. **配置备份**: 提供配置的备份和恢复功能

### 监控建议
1. 监控配置创建和更新操作
2. 记录分配模式切换的使用情况
3. 跟踪配置相关的错误和异常

## ✅ 修复确认

- [x] 问题根因分析完成
- [x] 修复代码实现完成
- [x] 编译测试通过
- [x] 向后兼容性确认
- [x] 文档更新完成

**修复状态**: ✅ 已完成，等待用户测试验证
