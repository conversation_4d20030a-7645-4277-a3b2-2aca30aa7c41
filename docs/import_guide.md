# MES系统数据导入使用指南

## 概述

MES系统提供了便捷的数据导入功能，支持批量导入项目、零件、BOM、工艺流程、设备、用户等核心数据，帮助您快速接入现有生产数据。

## 支持的数据类型

### 1. 项目管理
- **模块**: 项目 (projects)
- **支持字段**: 项目名称（必填）、客户名称（可选）
- **模板文件**: projects_template.csv

### 2. 零件管理
- **模块**: 零件 (parts)
- **支持字段**: 零件编号（必填）、零件名称（可选）、版本（必填）、规格说明（可选）
- **模板文件**: parts_template.csv
- **唯一性约束**: 零件编号+版本组合必须唯一

### 3. BOM管理
- **模块**: BOM (bom)
- **支持字段**: 项目名称（必填）、零件编号（必填）、零件版本（必填）、数量（必填）
- **模板文件**: bom_template.csv
- **前置条件**: 项目和零件必须已存在

### 4. 工艺流程
- **模块**: 工艺流程 (routings)
- **支持字段**: 零件编号（必填）、零件版本（必填）、工序号（必填）、工艺名称（必填）、作业指导书（可选）、标准工时（可选）
- **模板文件**: routings_template.csv
- **前置条件**: 零件必须已存在
- **唯一性约束**: 同一零件的工序号不能重复

### 5. 设备管理
- **模块**: 设备 (machines)
- **支持字段**: 设备名称（必填）、技能组（必填）、状态（可选，默认available）
- **模板文件**: machines_template.csv
- **前置条件**: 技能组必须已存在

### 6. 用户管理
- **模块**: 用户 (users)
- **支持字段**: 用户名（必填）、全名（可选）、角色（必填）、技能组（可选）
- **模板文件**: users_template.csv
- **前置条件**: 角色和技能组必须已存在

### 7. 技能组管理
- **模块**: 技能组 (skill_groups)
- **支持字段**: 技能组名称（必填）
- **模板文件**: skill_groups_template.csv

## 文件格式要求

### CSV格式规范
- **编码**: UTF-8（推荐）
- **分隔符**: 逗号 (,)
- **第一行**: 必须是列标题（中文），不要修改标题名称
- **空值**: 可选字段可以留空
- **文件大小**: 不超过10MB
- **行数限制**: 不超过10000行

### Excel格式规范
- **格式**: .xlsx
- **工作表**: 使用第一个工作表
- **第一行**: 必须是列标题（中文）
- **空值**: 可选字段可以留空

## 导入流程

### 1. 下载模板
1. 在相应的管理页面点击"导入"按钮
2. 在导入弹窗中点击"下载模板"
3. 根据模板格式填写数据

### 2. 准备数据
1. 按照模板格式填写数据
2. 确保必填字段不为空
3. 检查数据格式和约束条件
4. 保存为CSV或Excel格式

### 3. 上传文件
1. 点击上传区域或拖拽文件到上传区域
2. 系统会自动验证文件格式和大小
3. 上传成功后进入预览步骤

### 4. 预览数据
1. 系统会显示前10行数据供预览
2. 检查列标题是否正确
3. 查看是否有验证错误
4. 确认无误后点击"开始导入"

### 5. 执行导入
1. 系统开始批量处理数据
2. 可以看到实时的处理进度
3. 导入完成后显示结果统计

### 6. 查看结果
1. 查看成功和失败的记录数量
2. 如有错误，可查看详细的错误信息
3. 根据错误信息修正数据后重新导入

## 数据验证规则

### 格式验证
- 文件大小不超过10MB
- 行数不超过10000行
- 必填字段不能为空
- 数据类型必须正确

### 业务验证
- 唯一性约束检查
- 外键关联检查（如项目、零件、技能组等必须存在）
- 数据范围检查
- 自定义业务规则

### 错误处理
- 详细的错误信息和行号定位
- 支持部分成功导入
- 重复数据自动跳过
- 错误数据不影响正确数据的导入

## 最佳实践

### 1. 数据准备
- 建议先导入基础数据（技能组、项目、零件）
- 再导入关联数据（BOM、工艺流程、设备、用户）
- 分批导入大量数据，每批不超过1000条

### 2. 错误处理
- 仔细阅读错误信息，根据提示修正数据
- 常见错误：重复数据、缺少关联数据、格式错误
- 建议先用少量数据测试导入流程

### 3. 数据备份
- 导入前建议备份现有数据
- 重要数据建议分批导入并验证结果
- 保留原始导入文件以备查验

## 常见问题

### Q: 导入时提示"缺少必填列"怎么办？
A: 检查CSV文件的第一行标题是否与模板完全一致，不要修改列标题名称。

### Q: 为什么有些数据导入失败？
A: 查看错误详情，常见原因包括：
- 重复数据（违反唯一性约束）
- 关联数据不存在（如BOM中的项目或零件不存在）
- 数据格式错误（如数字字段填入文本）

### Q: 可以更新已存在的数据吗？
A: 目前导入功能主要用于新增数据，重复数据会被跳过。如需更新数据，请使用编辑功能。

### Q: 导入的数据可以撤销吗？
A: 导入操作不可撤销，建议导入前备份数据，或先用少量数据测试。

### Q: 支持哪些文件格式？
A: 支持CSV（推荐）和Excel (.xlsx)格式。

## 技术支持

如果在使用过程中遇到问题，请联系系统管理员或查看系统日志获取更多信息。
