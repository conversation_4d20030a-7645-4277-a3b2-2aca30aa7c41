# MES系统数据导入功能设计

## 概述

为MES系统添加通用的数据导入功能，支持CSV和Excel格式，方便接入现有生产数据。

## 系统架构

### 1. 导入流程

```
文件上传 → 格式验证 → 数据解析 → 业务验证 → 批量插入 → 结果反馈
```

### 2. 核心组件

#### 2.1 后端组件
- **ImportService**: 通用导入服务
- **FileParser**: 文件解析器（CSV/Excel）
- **DataValidator**: 数据验证器
- **BatchProcessor**: 批量处理器
- **ImportLogger**: 导入日志记录

#### 2.2 前端组件
- **ImportModal**: 导入弹窗组件
- **FileUploader**: 文件上传组件
- **ImportProgress**: 导入进度组件
- **ImportResult**: 导入结果组件

## 支持的数据模块

### 1. 项目管理 (Projects)
- **字段**: 项目名称、客户名称
- **模板**: projects_template.csv
- **验证**: 项目名称唯一性

### 2. 零件管理 (Parts)
- **字段**: 零件编号、零件名称、版本、规格说明
- **模板**: parts_template.csv
- **验证**: 零件编号+版本唯一性

### 3. BOM管理 (Project BOMs)
- **字段**: 项目名称、零件编号、零件版本、数量
- **模板**: bom_template.csv
- **验证**: 项目和零件存在性

### 4. 工艺流程 (Routings)
- **字段**: 零件编号、零件版本、工序号、工艺名称、作业指导书、标准工时
- **模板**: routings_template.csv
- **验证**: 零件存在性、工序号唯一性

### 5. 设备管理 (Machines)
- **字段**: 设备名称、技能组、状态
- **模板**: machines_template.csv
- **验证**: 设备名称唯一性、技能组存在性

### 6. 用户管理 (Users)
- **字段**: 用户名、全名、角色、技能组
- **模板**: users_template.csv
- **验证**: 用户名唯一性、角色和技能组存在性

### 7. 技能组管理 (Skill Groups)
- **字段**: 技能组名称
- **模板**: skill_groups_template.csv
- **验证**: 技能组名称唯一性

## 文件格式要求

### CSV格式
- 编码: UTF-8
- 分隔符: 逗号(,)
- 第一行: 列标题（中文）
- 空值: 留空或使用"N/A"

### Excel格式
- 格式: .xlsx
- 工作表: 使用第一个工作表
- 第一行: 列标题（中文）
- 空值: 留空

## 数据验证规则

### 1. 格式验证
- 文件大小限制: 10MB
- 行数限制: 10000行
- 必填字段检查
- 数据类型检查

### 2. 业务验证
- 唯一性约束检查
- 外键关联检查
- 数据范围检查
- 自定义业务规则

### 3. 错误处理
- 详细错误信息
- 行号定位
- 批量错误收集
- 部分成功处理

## API设计

### 1. 上传文件
```
POST /api/import/upload
Content-Type: multipart/form-data
Body: file, module_type
```

### 2. 预览数据
```
POST /api/import/preview
Body: { file_id, module_type }
```

### 3. 执行导入
```
POST /api/import/execute
Body: { file_id, module_type, options }
```

### 4. 查询状态
```
GET /api/import/status/{import_id}
```

### 5. 下载模板
```
GET /api/import/template/{module_type}
```

## 数据库设计

### import_jobs 表
```sql
CREATE TABLE import_jobs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    module_type VARCHAR(50) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    total_rows INTEGER,
    processed_rows INTEGER DEFAULT 0,
    success_rows INTEGER DEFAULT 0,
    error_rows INTEGER DEFAULT 0,
    error_details JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ
);
```

### import_errors 表
```sql
CREATE TABLE import_errors (
    id SERIAL PRIMARY KEY,
    import_job_id INTEGER NOT NULL REFERENCES import_jobs(id),
    row_number INTEGER NOT NULL,
    column_name VARCHAR(100),
    error_type VARCHAR(50) NOT NULL,
    error_message TEXT NOT NULL,
    row_data JSONB
);
```

## 前端界面设计

### 1. 导入按钮
- 位置: 各模块管理页面的操作栏
- 样式: 次要按钮，带导入图标

### 2. 导入弹窗
- 步骤1: 选择文件和下载模板
- 步骤2: 数据预览和验证
- 步骤3: 执行导入和结果显示

### 3. 进度显示
- 上传进度条
- 处理进度条
- 实时状态更新

### 4. 结果展示
- 成功/失败统计
- 错误详情列表
- 下载错误报告

## 实施计划

1. **阶段1**: 创建基础架构和通用组件
2. **阶段2**: 实现项目和零件导入功能
3. **阶段3**: 实现BOM和工艺流程导入功能
4. **阶段4**: 实现设备和用户导入功能
5. **阶段5**: 优化和测试

## 注意事项

1. **性能考虑**: 大文件分批处理，避免内存溢出
2. **事务处理**: 使用数据库事务确保数据一致性
3. **权限控制**: 基于用户角色控制导入权限
4. **审计日志**: 记录所有导入操作和结果
5. **错误恢复**: 支持失败重试和数据回滚
