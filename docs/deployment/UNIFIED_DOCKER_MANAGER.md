# MES系统统一Docker管理器

## 概述

我们已经将所有Docker管理功能整合到一个交互式脚本中，提供了直观的用户界面和完整的管理功能。

## 🚀 快速开始

### 方式一：交互式界面（推荐）
```bash
# 启动交互式管理界面
./mes
# 或
./docker-manager.sh
```

### 方式二：命令行模式
```bash
# 快速启动服务
./mes start

# 停止服务
./mes stop

# 重启服务
./mes restart

# 查看状态
./mes status

# 查看日志
./mes logs [service]

# 完整部署
./mes deploy

# 备份数据
./mes backup
```

## 📋 功能特性

### 🎯 主要功能
- **🏗️ 完整部署系统** - 一键部署整个MES系统
- **▶️ 启动服务** - 启动所有Docker容器
- **⏹️ 停止服务** - 安全停止所有服务
- **🔄 重启服务** - 重启系统服务
- **📊 查看状态** - 实时监控系统状态
- **📋 查看日志** - 分服务查看日志
- **⚙️ 配置管理** - 环境配置管理
- **🛠️ 维护工具** - 系统维护功能

### 🎨 界面特色
- **彩色输出** - 清晰的状态指示
- **交互式菜单** - 直观的操作界面
- **实时状态** - 动态显示系统状态
- **错误处理** - 友好的错误提示
- **帮助系统** - 完整的使用指南

## 🖥️ 界面预览

### 主界面
```
╔══════════════════════════════════════════════════════════════╗
║                    MES系统 Docker管理器                      ║
║                     版本: 1.0.0                           ║
║              制造执行系统 - 容器化部署解决方案                ║
╚══════════════════════════════════════════════════════════════╝

📊 系统状态概览

✓ Docker: 27.5.1
✓ Docker Compose: 2.33.0
✓ Docker Compose配置: 存在
! 环境配置: 将自动生成

🚀 主菜单 - 请选择操作:

   1) 🏗️  完整部署系统
   2) ▶️  启动服务
   3) ⏹️  停止服务
   4) 🔄 重启服务
   5) 📊 查看状态
   6) 📋 查看日志
   7) ⚙️  配置管理
   8) 🛠️  维护工具
   9) 📚 帮助信息
   0) 🚪 退出

请输入选项 [0-9]:
```

### 配置管理界面
```
⚙️ 配置管理

   1) 🔧 生成环境配置
   2) 📝 编辑环境配置
   3) 🔍 查看当前配置
   4) 🔄 重置配置
   5) 📋 配置模板
   0) ⬅️  返回主菜单
```

### 维护工具界面
```
🛠️ 维护工具

   1) 💾 备份数据
   2) 📥 恢复数据
   3) 🧹 清理系统
   4) 🔍 健康检查
   5) 📈 性能监控
   6) 🗂️  镜像管理
   0) ⬅️  返回主菜单
```

## 🔧 详细功能说明

### 1. 完整部署系统
- 自动检查系统依赖
- 生成安全的环境配置
- 构建Docker镜像
- 启动所有服务
- 验证部署结果

### 2. 服务管理
- **启动**: 启动所有容器服务
- **停止**: 安全停止所有服务
- **重启**: 重启指定或所有服务
- **状态**: 实时显示容器状态和健康检查

### 3. 日志管理
- **所有服务日志**: 查看系统整体日志
- **分服务日志**: 
  - 数据库日志 (PostgreSQL)
  - 缓存日志 (Redis)
  - 后端日志 (Rust API)
  - 前端日志 (Nginx)

### 4. 配置管理
- **生成配置**: 自动生成安全的环境配置
- **编辑配置**: 使用系统编辑器修改配置
- **查看配置**: 显示当前环境配置
- **重置配置**: 重新生成默认配置
- **配置模板**: 查看配置模板和说明

### 5. 维护工具
- **数据备份**: 自动备份PostgreSQL数据库
- **数据恢复**: 从备份文件恢复数据
- **系统清理**: 清理容器、镜像和数据
- **健康检查**: 检查所有服务健康状态
- **性能监控**: 查看资源使用情况
- **镜像管理**: 管理Docker镜像

## 🛡️ 安全特性

### 自动密码生成
- 数据库密码自动生成（25位随机字符）
- Redis密码自动生成（25位随机字符）
- JWT密钥自动生成（50位随机字符）

### 安全配置
- 非root用户运行容器
- 网络隔离和安全组
- 资源限制和配额
- 安全头配置

## 🌐 网络支持

### IPv6双栈
- 完全支持IPv4和IPv6
- 双栈网络配置
- 容器间IPv6通信

### 移动设备优化
- 响应式设计
- 触摸手势支持
- 移动端缓存优化
- 图片懒加载

## 📊 监控和健康检查

### 自动健康检查
- PostgreSQL: `pg_isready`检查
- Redis: 连接测试
- Backend: `/api/health`端点
- Frontend: `/health`端点

### 状态监控
- 容器运行状态
- 服务健康状态
- 资源使用情况
- 网络连接状态

## 🚨 故障排除

### 常见问题解决
1. **服务启动失败**
   - 选择"查看日志"检查错误信息
   - 使用"健康检查"诊断问题
   - 尝试"重启服务"

2. **配置问题**
   - 使用"重置配置"重新生成
   - 检查"配置模板"确认格式
   - 手动"编辑配置"修正错误

3. **网络问题**
   - 检查端口占用情况
   - 验证防火墙设置
   - 确认Docker网络配置

### 日志分析
- 使用分服务日志查看具体问题
- 关注ERROR和WARN级别日志
- 检查容器启动顺序和依赖

## 📈 性能优化

### 资源配置
- PostgreSQL: 512MB内存限制
- Redis: 256MB内存限制
- Backend: 1GB内存限制
- Frontend: 256MB内存限制

### 优化建议
- 定期清理未使用的镜像
- 监控磁盘空间使用
- 定期备份重要数据
- 更新到最新版本

## 🔄 更新和维护

### 定期维护
```bash
# 备份数据
./mes backup

# 更新代码
git pull

# 重新部署
./mes deploy
```

### 版本升级
1. 备份当前数据
2. 停止现有服务
3. 更新代码和配置
4. 重新构建和部署
5. 验证服务正常

## 📞 技术支持

如果遇到问题：
1. 查看内置帮助系统
2. 检查日志输出
3. 使用健康检查诊断
4. 参考故障排除指南

---

**MES系统Docker管理器** - 让容器化部署变得简单高效！
