# MES系统安装指南

本指南将帮助您完成MES系统的完整安装和配置。

## 📋 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上（推荐8GB）
- **存储**: 20GB可用空间
- **网络**: 稳定的网络连接

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+), macOS, Windows 10+
- **数据库**: PostgreSQL 12+
- **运行时**: Rust 1.70+, Node.js 18+

## 🚀 快速安装

### 方式一：自动安装脚本（推荐）

```bash
# 下载并运行安装脚本
curl -fsSL https://raw.githubusercontent.com/your-repo/mes/main/install.sh | bash

# 或者如果已克隆仓库
./install.sh
```

### 方式二：Docker安装（推荐生产环境）

```bash
# 使用Docker Compose一键部署
docker-compose up -d

# 或使用安装脚本
./docker-install.sh
```

### 方式三：手动安装

请参考下面的详细安装步骤。

## 📦 详细安装步骤

### 1. 环境准备

#### 1.1 安装Rust
```bash
# 安装Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# 验证安装
rustc --version
cargo --version
```

#### 1.2 安装Node.js
```bash
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# macOS (使用Homebrew)
brew install node@18

# 验证安装
node --version
npm --version
```

#### 1.3 安装PostgreSQL
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# macOS (使用Homebrew)
brew install postgresql
brew services start postgresql

# 启动PostgreSQL服务
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### 2. 数据库配置

#### 2.1 创建数据库用户和数据库
```bash
# 切换到postgres用户
sudo -u postgres psql

# 在PostgreSQL命令行中执行
CREATE USER mes_user WITH PASSWORD 'mes_password';
CREATE DATABASE mes_db OWNER mes_user;
GRANT ALL PRIVILEGES ON DATABASE mes_db TO mes_user;
\q
```

#### 2.2 配置环境变量
```bash
# 创建.env文件
cat > .env << EOF
DATABASE_URL=postgresql://mes_user:mes_password@localhost:5432/mes_db
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
RUST_LOG=info
EOF
```

### 3. 后端安装

#### 3.1 克隆项目
```bash
git clone <repository-url>
cd mes-system
```

#### 3.2 构建后端
```bash
# 安装依赖并构建
cargo build --release

# 运行数据库迁移
cargo run --bin migrate
```

#### 3.3 启动后端服务
```bash
# 开发模式
cargo run

# 生产模式
./target/release/mes-system
```

### 4. 前端安装

#### 4.1 安装依赖
```bash
cd frontend
npm install
```

#### 4.2 启动前端
```bash
# 开发模式
npm run dev

# 构建生产版本
npm run build
```

### 5. 验证安装

#### 5.1 检查服务状态
```bash
# 检查后端API
curl http://localhost:8080/api/health

# 检查前端
curl http://localhost:3000
```

#### 5.2 登录测试
- 访问: http://localhost:3000
- 用户名: `admin`
- 密码: `admin123`

## 🐳 Docker安装

### 使用Docker Compose（推荐）

#### 1. 创建docker-compose.yml
```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: mes_db
      POSTGRES_USER: mes_user
      POSTGRES_PASSWORD: mes_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  backend:
    build: .
    environment:
      DATABASE_URL: ************************************************/mes_db
      JWT_SECRET: your-super-secret-jwt-key
    ports:
      - "8080:8080"
    depends_on:
      - postgres

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend

volumes:
  postgres_data:
```

#### 2. 启动服务
```bash
docker-compose up -d
```

### 使用单独的Docker容器

#### 1. 构建镜像
```bash
# 构建后端镜像
docker build -t mes-backend .

# 构建前端镜像
docker build -t mes-frontend ./frontend
```

#### 2. 运行容器
```bash
# 运行PostgreSQL
docker run -d --name mes-postgres \
  -e POSTGRES_DB=mes_db \
  -e POSTGRES_USER=mes_user \
  -e POSTGRES_PASSWORD=mes_password \
  -p 5432:5432 \
  postgres:15

# 运行后端
docker run -d --name mes-backend \
  -e DATABASE_URL=****************************************************/mes_db \
  -e JWT_SECRET=your-super-secret-jwt-key \
  -p 8080:8080 \
  --link mes-postgres \
  mes-backend

# 运行前端
docker run -d --name mes-frontend \
  -p 3000:3000 \
  --link mes-backend \
  mes-frontend
```

## 🔧 配置选项

### 环境变量配置
```bash
# 数据库配置
DATABASE_URL=postgresql://user:password@host:port/database

# JWT配置
JWT_SECRET=your-secret-key

# 日志级别
RUST_LOG=debug|info|warn|error

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# 前端配置
VITE_API_BASE_URL=http://localhost:8080/api
```

### 数据库配置优化
```sql
-- 在PostgreSQL中执行性能优化
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
SELECT pg_reload_conf();
```

## 🚨 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查PostgreSQL状态
sudo systemctl status postgresql

# 检查端口占用
netstat -tlnp | grep 5432

# 重启PostgreSQL
sudo systemctl restart postgresql
```

#### 2. 端口冲突
```bash
# 检查端口占用
lsof -i :8080
lsof -i :3000

# 修改配置文件中的端口
```

#### 3. 权限问题
```bash
# 修复文件权限
chmod +x install.sh
chmod +x docker-install.sh

# 修复数据库权限
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE mes_db TO mes_user;"
```

#### 4. 内存不足
```bash
# 检查内存使用
free -h

# 增加交换空间
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

## 📚 下一步

安装完成后，您可以：

1. **阅读用户手册**: 了解系统功能和操作
2. **配置用户角色**: 设置不同的用户权限
3. **导入数据**: 导入现有的生产数据
4. **自定义配置**: 根据需求调整系统设置
5. **备份设置**: 配置定期数据备份

## 🆘 获取帮助

如果遇到问题，请：

1. 查看日志文件: `tail -f /var/log/mes/mes.log`
2. 检查系统状态: `./check-status.sh`
3. 提交Issue: 在GitHub仓库中创建问题报告
4. 联系支持: 发送邮件至 <EMAIL>

---

**注意**: 请在生产环境中修改默认密码和密钥！
