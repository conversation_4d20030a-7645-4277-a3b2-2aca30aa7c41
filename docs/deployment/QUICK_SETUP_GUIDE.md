# MES系统快速安装指南

本指南提供MES系统的快速安装和启动方法。

## 🚀 一键安装（推荐）

### 方法1：自动安装脚本
```bash
# 下载项目后，在项目根目录运行
./install.sh
```

### 方法2：Docker一键部署
```bash
# 适合生产环境
./docker-install.sh
```

### 方法3：交互式启动
```bash
# 提供多种选项的交互式菜单
./start_all.sh
```

## 📋 安装脚本说明

| 脚本文件 | 功能说明 | 适用场景 |
|---------|---------|---------|
| `install.sh` | 自动检测系统并安装所有依赖 | 开发环境，首次安装 |
| `docker-install.sh` | 使用Docker容器化部署 | 生产环境，快速部署 |
| `init-database.sh` | 初始化PostgreSQL数据库 | 数据库配置，数据迁移 |
| `check-system.sh` | 检查系统状态和依赖 | 故障排除，环境验证 |
| `start_all.sh` | 交互式启动菜单 | 日常使用，多种启动方式 |

## ⚡ 快速启动流程

### 1. 克隆项目
```bash
git clone <repository-url>
cd mes-system
```

### 2. 选择安装方式

#### 选项A：自动安装（推荐新手）
```bash
./install.sh
```

#### 选项B：Docker部署（推荐生产）
```bash
./docker-install.sh
```

#### 选项C：手动安装
```bash
# 1. 检查系统环境
./check-system.sh

# 2. 初始化数据库
./init-database.sh

# 3. 构建和启动
cargo build --release
cd frontend && npm install && cd ..
./start_all.sh
```

### 3. 访问系统
- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8080
- **默认登录**: admin / admin123

## 🔧 系统要求

### 最低要求
- **CPU**: 2核心
- **内存**: 4GB
- **存储**: 20GB
- **操作系统**: Linux/macOS/Windows

### 软件依赖
- **Rust**: 1.70+
- **Node.js**: 18+
- **PostgreSQL**: 12+
- **Git**: 最新版本

## 🚨 常见问题

### 1. 安装失败
```bash
# 检查系统状态
./check-system.sh

# 查看详细日志
tail -f install.log
```

### 2. 数据库连接失败
```bash
# 重新初始化数据库
./init-database.sh

# 检查PostgreSQL状态
sudo systemctl status postgresql
```

### 3. 端口冲突
```bash
# 检查端口占用
lsof -i :8080
lsof -i :3000

# 修改配置文件
vim .env
```

### 4. 权限问题
```bash
# 添加执行权限
chmod +x *.sh

# 修复文件权限
sudo chown -R $USER:$USER .
```

## 📚 详细文档

- **[完整安装指南](INSTALLATION_GUIDE.md)** - 详细的安装步骤和配置
- **[API文档](API_DOCUMENTATION.md)** - 完整的API接口文档
- **[项目说明](README.md)** - 项目概述和功能介绍

## 🛠️ 开发模式

### 启动开发环境
```bash
# 后端开发
cargo run

# 前端开发
cd frontend
npm run dev
```

### 构建生产版本
```bash
# 后端构建
cargo build --release

# 前端构建
cd frontend
npm run build
```

## 🐳 Docker使用

### 基本命令
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 管理脚本
```bash
./docker-start.sh     # 启动服务
./docker-stop.sh      # 停止服务
./docker-restart.sh   # 重启服务
./docker-logs.sh      # 查看日志
./docker-backup.sh    # 备份数据
./docker-clean.sh     # 清理资源
```

## 🔍 系统监控

### 检查服务状态
```bash
# 全面系统检查
./check-system.sh

# 检查特定服务
curl http://localhost:8080/api/health
curl http://localhost:3000
```

### 查看日志
```bash
# 系统日志
journalctl -u mes-backend -f

# 应用日志
tail -f /var/log/mes/mes.log

# Docker日志
docker-compose logs -f [service-name]
```

## 📞 获取帮助

### 自助排查
1. 运行 `./check-system.sh` 检查系统状态
2. 查看 `INSTALLATION_GUIDE.md` 详细文档
3. 检查日志文件获取错误信息

### 联系支持
- **GitHub Issues**: 提交问题报告
- **邮件支持**: <EMAIL>
- **文档中心**: 查看在线文档

## ⚠️ 重要提醒

1. **生产环境**请修改默认密码和密钥
2. **定期备份**数据库数据
3. **监控系统**资源使用情况
4. **及时更新**系统和依赖

---

**快速开始**: 运行 `./install.sh` 或 `./start_all.sh` 即可开始使用MES系统！
