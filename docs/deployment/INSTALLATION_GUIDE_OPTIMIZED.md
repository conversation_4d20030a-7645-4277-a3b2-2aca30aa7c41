# MES系统 - 优化安装指南

## 📋 概述

本指南提供了MES制造执行系统的详细安装说明，包括系统要求、依赖安装、配置和故障排除。

## 🎯 推荐安装方式

### Windows 用户 - 优化部署脚本

**最简单、最可靠的安装方式：**

1. **下载项目**
   ```bash
   git clone <repository-url>
   cd mes-system
   ```

2. **运行优化部署脚本**
   ```bash
   # 双击运行或在命令行执行
   deploy_optimized.bat
   ```

3. **按照提示完成 PostgreSQL 安装**
   - 脚本会自动打开下载页面
   - 安装 PostgreSQL 15+ 版本
   - 记住设置的密码

4. **重新运行脚本完成部署**
   ```bash
   deploy_optimized.bat
   ```

5. **启动系统**
   ```bash
   quick_start.bat
   ```

## 📋 系统要求

### 硬件要求
- **CPU**: 双核 2.0GHz 或更高
- **内存**: 最少 4GB，推荐 8GB
- **磁盘**: 最少 2GB 可用空间
- **网络**: 互联网连接（用于下载依赖）

### 软件要求

#### 自动安装的组件
- ✅ **Rust** (最新稳定版)
  - 包含 Cargo 包管理器
  - 自动配置环境变量
- ✅ **Node.js** (LTS 版本)
  - 包含 npm 包管理器
  - 自动添加到 PATH

#### 需要手动安装的组件
- ⚠️ **PostgreSQL 15+** (必须手动安装)
  - 推荐版本: PostgreSQL 15 或 16
  - 必须添加到系统 PATH
  - 需要记住超级用户密码

#### 操作系统支持
- ✅ **Windows 10/11** (主要支持)
- ✅ **Linux** (Ubuntu 20.04+, CentOS 8+)
- ✅ **macOS** (10.15+)

## 🔧 详细安装步骤

### 步骤 1: PostgreSQL 安装

**Windows:**
1. 访问 https://www.postgresql.org/download/windows/
2. 下载 PostgreSQL 15+ 安装程序
3. 运行安装程序，注意以下设置：
   - ✅ 勾选 "Add to PATH"
   - ✅ 记住超级用户(postgres)密码
   - ✅ 保持默认端口 5432
   - ✅ 可选安装 pgAdmin (图形化管理工具)

**验证安装:**
```bash
psql --version
# 应该显示: psql (PostgreSQL) 15.x
```

### 步骤 2: 运行部署脚本

```bash
# Windows
deploy_optimized.bat

# Linux/macOS
./install.sh
```

### 步骤 3: 验证安装

```bash
# 检查系统状态
check-system.bat

# 启动系统
quick_start.bat
```

## 🌐 访问系统

安装完成后，您可以通过以下地址访问系统：

- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8080
- **API文档**: http://localhost:8080/docs (如果启用)

### 默认登录信息
- **用户名**: `admin`
- **密码**: `admin123`

## 🛠️ 管理命令

### 启动和停止
```bash
# 启动所有服务
start_all.bat

# 快速启动（推荐）
quick_start.bat

# 停止所有服务
stop_all.bat
```

### 系统管理
```bash
# 检查系统状态
check-system.bat

# 重新构建系统
build_production.bat

# 重新初始化数据库
init-database.bat
```

### 开发模式
```bash
# 启动开发模式（前端热重载）
start_dev.bat

# 仅启动后端
start_backend.bat

# 仅启动前端
cd frontend && npm run dev
```

## 🔍 故障排除

### 常见问题

#### 1. PostgreSQL 连接失败
**症状**: 数据库连接错误
**解决方案**:
```bash
# 检查 PostgreSQL 服务状态
sc query postgresql-x64-15

# 启动 PostgreSQL 服务
net start postgresql-x64-15

# 重新初始化数据库
init-database.bat
```

#### 2. 端口被占用
**症状**: 端口 8080 或 3000 被占用
**解决方案**:
```bash
# 检查端口占用
netstat -ano | findstr ":8080"
netstat -ano | findstr ":3000"

# 停止占用进程
stop_all.bat

# 或手动终止进程
taskkill /f /pid <PID>
```

#### 3. Rust 编译失败
**症状**: cargo build 失败
**解决方案**:
```bash
# 清理构建缓存
cargo clean

# 更新 Rust
rustup update

# 重新构建
cargo build --release
```

#### 4. 前端依赖安装失败
**症状**: npm install 失败
**解决方案**:
```bash
cd frontend

# 清理缓存
npm cache clean --force

# 删除 node_modules
rmdir /s node_modules

# 重新安装
npm install
```

### 日志查看

#### 后端日志
- 运行时日志显示在控制台
- 设置 `RUST_LOG=debug` 获取详细日志

#### 前端日志
- 浏览器开发者工具 Console 标签
- 网络请求在 Network 标签

#### 数据库日志
- PostgreSQL 日志位置: `C:\Program Files\PostgreSQL\15\data\log\`

## 📞 获取帮助

### 检查系统状态
```bash
check-system.bat
```

### 重置系统
```bash
# 完全重置（谨慎使用）
stop_all.bat
init-database.bat
build_production.bat
start_all.bat
```

### 联系支持
- 查看项目文档
- 提交 Issue 到项目仓库
- 查看 API 文档了解接口详情

## 🚀 生产环境部署

### Docker 部署（推荐）
```bash
# 使用 Docker Compose
docker-compose up -d

# 或使用单独的 Docker 命令
docker build -t mes-system .
docker run -d -p 3000:3000 -p 8080:8080 mes-system
```

### 手动生产部署
1. 构建生产版本
2. 配置反向代理 (Nginx/Apache)
3. 设置 HTTPS 证书
4. 配置数据库备份
5. 设置监控和日志

详细生产部署指南请参考项目文档。
