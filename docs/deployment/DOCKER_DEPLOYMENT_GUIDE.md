# MES系统Docker部署指南

## 概述

本指南提供了MES系统的完整Docker部署解决方案，包括自动化脚本、环境配置和维护工具。

## 系统要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少4GB可用内存
- 至少10GB可用磁盘空间

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd mes-1
```

### 2. 一键部署
```bash
# 使用快速启动脚本
./docker-start.sh
```

### 3. 访问系统
- 前端界面: http://localhost:3000
- 后端API: http://localhost:8080
- 默认登录: admin/admin123

## 详细部署步骤

### 1. 环境配置

生成Docker环境配置：
```bash
./scripts/generate-env.sh docker
```

手动配置（可选）：
```bash
cp .env.example .env.docker
# 编辑 .env.docker 文件
```

### 2. 构建和启动

使用完整部署脚本：
```bash
./scripts/docker-deploy.sh deploy
```

或分步执行：
```bash
# 构建镜像
docker compose build

# 启动服务
docker compose --env-file .env.docker up -d
```

### 3. 验证部署

检查服务状态：
```bash
docker compose ps
```

查看服务日志：
```bash
./docker-logs.sh
```

## 服务架构

### 容器服务

1. **postgres** - PostgreSQL数据库
   - 端口: 5432
   - 数据持久化: postgres_data卷

2. **redis** - Redis缓存
   - 端口: 6379
   - 数据持久化: redis_data卷

3. **backend** - Rust后端API
   - 端口: 8080
   - 健康检查: /api/health

4. **frontend** - React前端
   - 端口: 3000 (映射到容器80端口)
   - 健康检查: /health

### 网络配置

- 网络名称: mes-network
- IPv4子网: **********/16
- IPv6子网: 2001:db8::/32
- 支持IPv4/IPv6双栈

## 管理命令

### 基本操作
```bash
# 启动服务
./docker-start.sh

# 停止服务
./docker-stop.sh

# 重启服务
./docker-restart.sh

# 查看日志
./docker-logs.sh [service_name]
```

### 高级管理
```bash
# 完整部署管理
./scripts/docker-deploy.sh <command>

# 可用命令:
# deploy      - 完整部署系统
# start       - 启动服务
# stop        - 停止服务
# restart     - 重启服务
# status      - 显示系统状态
# logs [svc]  - 查看日志
# backup      - 备份数据
# restore <f> - 恢复数据
# cleanup     - 清理系统
# health      - 检查健康状态
```

### 数据管理
```bash
# 备份数据
./scripts/docker-deploy.sh backup

# 恢复数据
./scripts/docker-deploy.sh restore backup_file.sql

# 查看系统状态
./scripts/docker-deploy.sh status
```

## 配置说明

### 环境变量

主要配置项（.env.docker）：

```bash
# 数据库配置
POSTGRES_DB=mes_db
POSTGRES_USER=mes_user
POSTGRES_PASSWORD=<generated_password>

# Redis配置
REDIS_PASSWORD=<generated_password>

# 安全配置
JWT_SECRET=<generated_secret>

# 服务配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
RUST_LOG=info
```

### 端口映射

| 服务 | 容器端口 | 主机端口 | 协议 |
|------|----------|----------|------|
| Frontend | 80 | 3000 | HTTP |
| Backend | 8080 | 8080 | HTTP |
| PostgreSQL | 5432 | 5432 | TCP |
| Redis | 6379 | 6379 | TCP |

### 资源限制

| 服务 | 内存限制 | 内存预留 |
|------|----------|----------|
| Frontend | 256MB | 128MB |
| Backend | 1GB | 512MB |
| PostgreSQL | 512MB | 256MB |
| Redis | 256MB | 128MB |

## 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看详细日志
   docker compose logs <service_name>
   
   # 检查容器状态
   docker compose ps
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库容器
   docker compose exec postgres pg_isready -U mes_user
   
   # 查看数据库日志
   docker compose logs postgres
   ```

3. **前端无法访问后端**
   ```bash
   # 检查网络连接
   docker compose exec frontend curl http://backend:8080/api/health
   
   # 检查nginx配置
   docker compose exec frontend nginx -t
   ```

### 性能优化

1. **镜像优化**
   ```bash
   # 使用优化脚本
   ./scripts/docker-optimize.sh all
   ```

2. **清理资源**
   ```bash
   # 清理未使用的资源
   docker system prune -f
   
   # 清理构建缓存
   docker builder prune -f
   ```

## 生产环境部署

### 安全配置

1. 修改默认密码
2. 配置HTTPS
3. 设置防火墙规则
4. 启用日志监控

### 备份策略

1. 定期数据库备份
2. 配置文件备份
3. 日志文件轮转

### 监控配置

1. 健康检查
2. 资源监控
3. 日志聚合

## 移动设备支持

系统已针对移动设备进行优化：

- 响应式设计
- 触摸手势支持
- 移动端缓存优化
- 图片懒加载

## IPv6支持

系统完全支持IPv6：

- 双栈网络配置
- IPv6端口绑定
- 容器间IPv6通信

## 更新和维护

### 更新系统
```bash
# 拉取最新代码
git pull

# 重新构建和部署
./scripts/docker-deploy.sh deploy
```

### 定期维护
```bash
# 清理旧镜像
docker image prune -f

# 备份数据
./scripts/docker-deploy.sh backup

# 检查系统健康
./scripts/docker-deploy.sh health
```
