# 甘特图任务详情编辑功能

## 功能概述

在甘特图中点击任务条目时，会全屏弹出任务详情编辑弹窗，方便用户查看和编辑任务的详细信息。

## 功能特性

### 1. 点击交互
- **桌面端**: 点击任务条目即可打开详情弹窗
- **移动端**: 通过触摸手势点击任务条目打开详情弹窗
- **拖拽优先**: 如果正在拖拽任务，点击事件不会触发，确保拖拽功能正常

### 2. 全屏弹窗设计
- **大尺寸**: 弹窗宽度为 90vw，高度为 90vh，提供充足的编辑空间
- **响应式布局**: 使用 Ant Design 的栅格系统，适配不同屏幕尺寸
- **分区显示**: 信息按功能分组显示在不同的卡片中

### 3. 详细信息展示

#### 基本信息
- 任务ID
- 工序名称
- 工序步骤
- 标准工时
- 工作指导书

#### 项目信息
- 项目名称
- 客户名称
- 零件编号
- 零件名称
- 版本

#### 工单信息
- 工单ID
- 生产数量
- 工单状态
- 交期

#### 分配信息（可编辑）
- 技能组
- 设备
- 计划开始时间
- 计划结束时间
- 任务状态

### 4. 编辑功能

#### 编辑模式切换
- **查看模式**: 默认为只读模式，显示所有信息
- **编辑模式**: 点击"编辑"按钮进入编辑模式
- **保存/取消**: 编辑模式下可以保存或取消修改

#### 可编辑字段
- **技能组**: 下拉选择，必填
- **设备**: 下拉选择，可选，根据选择的技能组过滤
- **计划开始时间**: 日期时间选择器，必填
- **计划结束时间**: 日期时间选择器，必填
- **任务状态**: 下拉选择（待开始、进行中、已完成、已取消）

#### 数据验证
- 必填字段验证
- 时间逻辑验证（结束时间不能早于开始时间）
- 设备与技能组关联验证

### 5. 状态显示
- **状态标签**: 使用不同颜色的标签显示任务状态
- **实时更新**: 编辑后状态会实时反映在甘特图中

## 技术实现

### 前端组件

#### TaskDetailModal 组件
```typescript
interface TaskDetailModalProps {
  visible: boolean;
  taskId: number | null;
  onCancel: () => void;
  onUpdate?: (taskId: number, data: UpdatePlanTaskRequest) => void;
}
```

#### 主要功能
- 使用 React Query 获取任务详情
- 表单验证和提交
- 状态管理
- 响应式布局

#### GanttChart 组件修改
- 添加点击事件处理
- 集成 TaskDetailModal 组件
- 支持移动端和桌面端交互

### 后端支持

#### API 接口
- `GET /api/plan-tasks/:id` - 获取任务详情
- `PUT /api/plan-tasks/:id` - 更新任务信息
- `GET /api/skill-groups` - 获取技能组列表
- `GET /api/machines` - 获取设备列表

#### 数据模型
- PlanTaskWithDetails - 包含完整任务信息
- UpdatePlanTaskRequest - 更新请求模型

## 使用方法

### 1. 查看任务详情
1. 进入生产计划页面
2. 切换到甘特图标签页
3. 点击任意任务条目
4. 在弹出的详情窗口中查看完整信息

### 2. 编辑任务信息
1. 在任务详情弹窗中点击"编辑"按钮
2. 修改需要更改的字段
3. 点击"保存"按钮提交更改
4. 或点击"取消编辑"放弃更改

### 3. 关闭弹窗
- 点击"关闭"按钮
- 点击弹窗外部区域
- 按 ESC 键

## 移动设备适配

### 触摸交互
- 支持触摸点击打开详情
- 与拖拽手势兼容
- 防止误触发

### 响应式设计
- 弹窗在移动设备上自动调整大小
- 表单控件适配触摸操作
- 文字和按钮大小适合移动设备

## 注意事项

### 权限控制
- 只有有权限的用户才能编辑任务
- 编辑按钮根据权限显示/隐藏

### 数据一致性
- 编辑后自动刷新甘特图数据
- 与其他页面的数据保持同步

### 性能优化
- 使用 React Query 缓存数据
- 按需加载技能组和设备数据
- 防抖处理用户输入

## 扩展功能

### 未来可能的增强
- 添加任务历史记录查看
- 支持批量编辑多个任务
- 添加任务依赖关系编辑
- 集成质量检查信息
- 添加任务执行日志查看

### 自定义配置
- 可配置显示的字段
- 可配置编辑权限
- 可配置状态选项
