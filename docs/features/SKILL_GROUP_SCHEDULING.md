# 技能组任务调度功能

**功能版本**: v1.0  
**实现时间**: 2025-01-19  
**状态**: ✅ 已完成

## 🎯 功能概述

系统现在支持技能组级别的任务调度，当任务未指定具体设备时，可以通过技能组视图进行任务管理和时间调整。

## ✨ 主要特性

### 1. 智能模式切换
- **设备模式**: 当任务已分配给具体设备时，显示设备调度界面
- **技能组模式**: 当任务未分配设备但属于技能组时，显示技能组调度界面
- **自动识别**: 系统根据任务的分配状态自动选择合适的调度模式

### 2. 技能组调度视图
- **未分配任务**: 显示属于该技能组但未分配设备的任务
- **时间表视图**: 7×24小时或7×12小时的时间表格
- **拖拽调度**: 支持拖拽调整任务时间
- **任务列表**: 显示技能组内所有任务及其分配状态

### 3. 任务分配状态
- **已分配**: 显示具体的设备信息
- **未分配**: 显示为技能组任务，等待设备分配
- **状态标识**: 通过不同颜色的标签区分分配状态

## 🎮 使用方法

### 访问技能组调度
1. **进入计划任务页面**: 导航到"计划任务"模块
2. **查看甘特图**: 在甘特图中找到未分配设备的任务
3. **点击技能组**: 点击任务下方的"技能组: XXX"链接
4. **打开调度界面**: 系统自动打开技能组调度窗口

### 技能组调度操作
1. **查看任务分布**: 在时间表中查看技能组任务的时间分布
2. **拖拽调整时间**: 拖拽任务卡片到新的时间槽
3. **查看任务详情**: 在任务列表中查看完整的任务信息
4. **分配状态管理**: 查看哪些任务已分配设备，哪些还未分配

### 界面说明
- **标题栏**: 显示"技能组任务调度 - [技能组名称]"
- **绿色标签**: 技能组名称标识
- **日历图标**: 技能组模式的视觉标识
- **分配设备列**: 显示任务的设备分配状态

## 🔧 技术实现

### 组件架构
```typescript
interface MachineScheduleModalProps {
  visible: boolean;
  machineId: number | null;          // 设备ID（设备模式）
  machineName?: string;              // 设备名称
  skillGroupId?: number | null;      // 技能组ID（技能组模式）
  skillGroupName?: string;           // 技能组名称
  onCancel: () => void;
}
```

### 模式判断逻辑
```typescript
// 判断当前是设备模式还是技能组模式
const isSkillGroupMode = !machineId && skillGroupId;
const displayName = isSkillGroupMode ? skillGroupName : machineName;
const modalTitle = isSkillGroupMode ? 
  `技能组调度 - ${displayName}` : 
  `设备调度 - ${displayName}`;
```

### 数据获取策略
```typescript
const { data: tasks } = useQuery(
  [isSkillGroupMode ? 'skillGroupTasks' : 'machineTasks', machineId || skillGroupId],
  () => {
    if (isSkillGroupMode && skillGroupId) {
      // 获取技能组的计划任务
      return apiClient.getPlanTasks({
        skill_group_id: skillGroupId,
        start_date: startDate,
        end_date: endDate,
      });
    } else if (machineId) {
      // 获取设备任务
      return apiClient.getMachineTasksByDateRange(machineId, startDate, endDate);
    }
    return [];
  }
);
```

### 任务过滤逻辑
```typescript
// 技能组模式：只显示未分配设备的任务
if (isSkillGroupMode) {
  task = tasks?.find(task => {
    const taskStart = dayjs(task.planned_start);
    const taskEnd = dayjs(task.planned_end);
    return slotDateTime.isAfter(taskStart) && 
           slotDateTime.isBefore(taskEnd) && 
           !task.machine_id; // 关键：未分配设备
  });
}
```

## 📊 界面对比

### 设备调度模式
- **标题**: "设备任务调度 - [设备名称]"
- **图标**: 🔧 工具图标
- **标签颜色**: 蓝色
- **显示任务**: 分配给该设备的所有任务
- **任务列表**: 不显示设备分配列

### 技能组调度模式
- **标题**: "技能组任务调度 - [技能组名称]"
- **图标**: 📅 日历图标
- **标签颜色**: 绿色
- **显示任务**: 属于该技能组但未分配设备的任务
- **任务列表**: 显示"分配设备"列，标识任务分配状态

## 🎨 用户体验优化

### 视觉区分
1. **图标差异**: 设备模式使用工具图标，技能组模式使用日历图标
2. **颜色区分**: 设备标签为蓝色，技能组标签为绿色
3. **标题明确**: 清楚标识当前是设备调度还是技能组调度

### 操作一致性
1. **拖拽功能**: 两种模式都支持相同的拖拽操作
2. **时间调整**: 保持相同的时间计算和更新逻辑
3. **界面布局**: 使用相同的界面布局和交互方式

### 智能切换
1. **自动识别**: 根据任务状态自动选择合适的模式
2. **无缝切换**: 用户无需手动选择模式
3. **状态保持**: 切换模式时保持相关的状态信息

## 🔄 工作流程

### 典型使用场景

#### 场景1: 新任务规划
1. 创建新的计划任务，指定技能组但不指定设备
2. 在甘特图中点击"技能组: XXX"
3. 在技能组调度界面中调整任务时间
4. 后续可以将任务分配给具体设备

#### 场景2: 设备故障处理
1. 设备故障，需要将任务重新分配
2. 将任务的设备分配清空，回到技能组状态
3. 在技能组调度界面中重新安排时间
4. 分配给其他可用设备

#### 场景3: 产能规划
1. 查看技能组的整体任务负载
2. 在技能组调度界面中优化时间安排
3. 平衡不同时间段的任务分布
4. 为后续设备分配做准备

## 📈 业务价值

### 1. 灵活性提升
- **分层管理**: 支持技能组和设备两个层级的调度
- **渐进分配**: 可以先规划技能组任务，再分配具体设备
- **应急处理**: 设备故障时可以快速重新调度

### 2. 效率优化
- **统一界面**: 使用相同的界面处理不同层级的调度
- **智能识别**: 自动选择合适的调度模式
- **操作一致**: 保持一致的操作体验

### 3. 管理改进
- **可视化**: 清楚显示任务的分配状态
- **透明度**: 明确区分已分配和未分配的任务
- **决策支持**: 为管理决策提供更好的信息支持

## 🔮 未来扩展

### 计划功能
1. **批量分配**: 支持将技能组任务批量分配给设备
2. **智能推荐**: AI推荐最优的设备分配方案
3. **负载均衡**: 自动平衡不同设备的任务负载
4. **冲突检测**: 检测和解决任务时间冲突

### 界面增强
1. **拖拽分配**: 支持将任务从技能组拖拽到设备
2. **多选操作**: 支持多选任务进行批量操作
3. **过滤筛选**: 增加更多的任务过滤和筛选选项
4. **统计视图**: 显示技能组的任务统计信息

---

**功能状态**: 🟢 正常运行  
**最后更新**: 2025-01-19  
**维护者**: MES开发团队
