# 任务调度设备选择功能

**功能版本**: v4.0  
**实现时间**: 2025-01-19  
**状态**: ✅ 已完成

## 🎯 功能概述

为任务调度弹窗添加了完整的设备选择功能，用户现在可以在调度任务时同时指定技能组和具体设备，实现更精确的生产计划管理。

## ✨ 新增功能

### 1. 技能组选择
- **必填字段**: 每个任务必须分配给一个技能组
- **下拉选择**: 从系统中的所有技能组中选择
- **动态更新**: 选择技能组后自动更新可用设备列表

### 2. 设备选择
- **可选字段**: 可以选择具体设备，也可以只分配给技能组
- **智能过滤**: 只显示属于选定技能组的设备
- **清空功能**: 支持清空设备选择，回到技能组级别分配

### 3. 时间调度
- **开始时间**: 精确到分钟的计划开始时间
- **结束时间**: 精确到分钟的计划结束时间
- **时间验证**: 确保结束时间晚于开始时间至少30分钟

## 🎮 使用方法

### 打开任务调度弹窗
1. **从任务列表**: 点击任务行的"调度"按钮
2. **从甘特图**: 点击任务条目（如果使用简化调度）
3. **批量操作**: 选择多个任务后批量调度

### 设备分配流程
1. **选择技能组**: 
   - 从下拉列表中选择合适的技能组
   - 这是必填字段，必须选择

2. **选择设备（可选）**:
   - 如果需要指定具体设备，从过滤后的设备列表中选择
   - 如果不选择设备，任务将分配给整个技能组

3. **设置时间**:
   - 选择计划开始时间
   - 选择计划结束时间
   - 系统会验证时间的合理性

4. **确认保存**:
   - 点击"确定"按钮保存更改
   - 系统会更新任务的分配信息

## 🛠️ 技术实现

### 表单字段定义
```typescript
interface TaskScheduleForm {
  skill_group_id: number;        // 技能组ID（必填）
  machine_id?: number | null;    // 设备ID（可选）
  planned_start: Dayjs;          // 计划开始时间
  planned_end: Dayjs;            // 计划结束时间
}
```

### 设备过滤逻辑
```typescript
// 根据选择的技能组过滤设备
const filteredMachines = machines?.filter(machine => 
  machine.skill_group_id === form.getFieldValue('skill_group_id')
);
```

### 表单验证规则
```typescript
const validationRules = {
  skill_group_id: [
    { required: true, message: '请选择技能组' }
  ],
  planned_start: [
    { required: true, message: '请选择计划开始时间' }
  ],
  planned_end: [
    { required: true, message: '请选择计划结束时间' }
  ]
};

// 时间间隔验证
const diffMinutes = endTime.diff(startTime, 'minute');
if (diffMinutes < 30) {
  message.error('计划结束时间必须比开始时间至少晚30分钟');
}
```

### 数据更新
```typescript
const updateData = {
  skill_group_id: values.skill_group_id,
  machine_id: values.machine_id || null,  // 未选择时设为null
  planned_start: values.planned_start.format('YYYY-MM-DD HH:mm:00'),
  planned_end: values.planned_end.format('YYYY-MM-DD HH:mm:00'),
};
```

## 📊 界面设计

### 弹窗布局
```
┌─────────────────────────────────┐
│ 任务调度                        │
├─────────────────────────────────┤
│ 任务: Milling                   │
│ 项目: test01                    │
│ 零件: 11                        │
├─────────────────────────────────┤
│ 技能组: [下拉选择] *             │
│ 指定设备: [下拉选择]             │
│ 计划开始时间: [日期时间选择器] * │
│ 计划结束时间: [日期时间选择器] * │
├─────────────────────────────────┤
│           [取消] [确定]          │
└─────────────────────────────────┘
```

### 字段说明
- **技能组**: 红色星号标识必填字段
- **指定设备**: 带有提示信息"可选择具体设备，不选择则分配给技能组"
- **时间选择器**: 支持30分钟间隔选择

## 🔄 业务流程

### 分配策略
1. **技能组分配**: 
   - 只选择技能组，不选择设备
   - 任务分配给整个技能组，由调度员后续分配具体设备

2. **设备分配**:
   - 同时选择技能组和设备
   - 任务直接分配给指定设备

### 状态变化
```
未分配 → 技能组分配 → 设备分配
   ↓         ↓           ↓
 无归属   技能组负责   设备执行
```

### 权限控制
- **查看权限**: 所有用户都可以查看任务调度信息
- **编辑权限**: 只有有调度权限的用户可以修改分配
- **设备权限**: 根据用户权限过滤可选择的设备

## 📈 功能优势

### 1. 灵活性提升
- **分层分配**: 支持技能组和设备两个层级的分配
- **渐进细化**: 可以先分配技能组，后续再指定具体设备
- **快速调整**: 可以快速更改任务的分配状态

### 2. 操作便捷
- **一站式操作**: 在一个弹窗中完成所有调度操作
- **智能过滤**: 自动过滤相关的设备选项
- **表单验证**: 实时验证输入的合理性

### 3. 数据准确
- **关联验证**: 确保设备与技能组的关联关系正确
- **时间验证**: 防止不合理的时间安排
- **状态同步**: 修改后立即同步到系统各个模块

## 🎯 使用场景

### 场景1: 新任务分配
1. 创建新的生产任务
2. 打开任务调度弹窗
3. 选择合适的技能组
4. 根据需要选择具体设备
5. 设置计划时间并保存

### 场景2: 任务重新分配
1. 发现设备故障或其他问题
2. 打开现有任务的调度弹窗
3. 更改技能组或设备分配
4. 调整时间安排
5. 保存更改

### 场景3: 批量调度
1. 选择多个相关任务
2. 使用批量调度功能
3. 统一设置技能组和时间
4. 根据需要为不同任务指定不同设备

## ⚠️ 注意事项

### 操作限制
- **技能组必选**: 每个任务必须分配给一个技能组
- **设备关联**: 只能选择属于当前技能组的设备
- **时间合理性**: 结束时间必须晚于开始时间

### 数据一致性
- **实时更新**: 修改后立即更新相关视图
- **冲突检测**: 与现有的时间冲突检测功能集成
- **状态同步**: 确保甘特图和任务列表的状态同步

### 权限管理
- **分级权限**: 不同用户可能有不同的设备访问权限
- **操作记录**: 记录任务分配的变更历史
- **审批流程**: 重要变更可能需要审批

## 🔮 未来扩展

### 计划功能
1. **智能推荐**: 基于历史数据推荐最优的设备分配
2. **负载均衡**: 自动平衡不同设备的任务负载
3. **预测分析**: 预测任务完成时间和资源需求
4. **批量操作**: 更强大的批量分配和调度功能

### 界面改进
1. **拖拽分配**: 支持拖拽方式进行任务分配
2. **可视化**: 更直观的设备状态和负载显示
3. **快捷操作**: 提供更多的快捷操作选项
4. **移动优化**: 进一步优化移动设备的操作体验

---

**功能状态**: 🟢 正常运行  
**用户反馈**: 📝 等待收集  
**下一步**: 🔄 用户测试和反馈收集  
**最后更新**: 2025-01-19
