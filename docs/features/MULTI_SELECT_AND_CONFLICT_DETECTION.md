# 多选任务和时间冲突检测功能

**功能版本**: v2.0  
**实现时间**: 2025-01-19  
**状态**: ✅ 已完成

## 🎯 功能概述

在原有拖拽调度功能基础上，新增了多选任务和时间冲突检测功能，大幅提升了任务调度的效率和准确性。

## ✨ 新增特性

### 1. 多选任务功能
- **Ctrl+点击**: 多选/取消选择任务
- **Shift+点击**: 范围选择（基础实现）
- **Ctrl+A**: 全选所有任务
- **ESC键**: 清除所有选择
- **批量拖拽**: 同时移动多个选中的任务

### 2. 时间冲突检测
- **实时检测**: 拖拽时自动检测时间重叠
- **冲突提示**: 详细显示冲突的任务信息
- **批量检测**: 多选移动时检测所有任务的冲突
- **智能过滤**: 根据设备/技能组模式过滤检测范围

### 3. 视觉反馈增强
- **选择状态**: 绿色边框和勾选标记
- **冲突警告**: 红色边框和震动动画
- **批量操作**: 显示选中任务数量和操作提示
- **状态指示**: 实时显示操作状态和结果

## 🎮 使用方法

### 多选操作
1. **单选任务**: 直接点击任务卡片
2. **多选任务**: 
   - 按住Ctrl键点击多个任务
   - 选中的任务显示绿色边框和勾选标记
3. **全选任务**: 按Ctrl+A选择所有任务
4. **清除选择**: 按ESC键或点击"清除选择"按钮

### 批量移动
1. **选择多个任务**: 使用Ctrl+点击选择要移动的任务
2. **拖拽移动**: 拖拽任何一个选中的任务到目标位置
3. **系统处理**: 自动计算所有任务的新时间并检测冲突
4. **确认操作**: 无冲突时批量更新所有任务时间

### 冲突检测
1. **自动检测**: 拖拽任务时自动检测时间重叠
2. **冲突提示**: 显示具体冲突的任务名称
3. **操作阻止**: 有冲突时阻止移动操作
4. **解决建议**: 提供冲突解决的建议

## 🛠️ 技术实现

### 多选状态管理
```typescript
const [selectedTasks, setSelectedTasks] = useState<Set<number>>(new Set());
const [isMultiSelectMode, setIsMultiSelectMode] = useState(false);

// 任务选择处理
const handleTaskSelect = (taskId: number, event: React.MouseEvent) => {
  if (event.ctrlKey || event.metaKey) {
    // Ctrl+点击：切换选择状态
    const newSelected = new Set(selectedTasks);
    if (newSelected.has(taskId)) {
      newSelected.delete(taskId);
    } else {
      newSelected.add(taskId);
    }
    setSelectedTasks(newSelected);
    setIsMultiSelectMode(newSelected.size > 0);
  }
};
```

### 时间冲突检测算法
```typescript
const checkTimeOverlap = (
  newStartTime: dayjs.Dayjs, 
  newEndTime: dayjs.Dayjs, 
  excludeTaskId?: number
): { hasOverlap: boolean; conflictTasks: PlanTaskWithDetails[] } => {
  const conflictTasks: PlanTaskWithDetails[] = [];
  
  for (const task of tasks) {
    // 排除当前任务和指定排除的任务
    if (task.id === excludeTaskId || task.id === draggedTask?.id) continue;
    
    const taskStart = dayjs(task.planned_start);
    const taskEnd = dayjs(task.planned_end);
    
    // 检查时间重叠
    const hasOverlap = (
      (newStartTime.isBefore(taskEnd) && newStartTime.isAfter(taskStart)) ||
      (newEndTime.isBefore(taskEnd) && newEndTime.isAfter(taskStart)) ||
      (newStartTime.isBefore(taskStart) && newEndTime.isAfter(taskEnd)) ||
      (newStartTime.isSame(taskStart)) ||
      (newEndTime.isSame(taskEnd))
    );
    
    if (hasOverlap) {
      conflictTasks.push(task);
    }
  }
  
  return { hasOverlap: conflictTasks.length > 0, conflictTasks };
};
```

### 批量移动处理
```typescript
const handleBatchMove = (targetHour: number, targetDay: number) => {
  const selectedTaskList = tasks?.filter(task => selectedTasks.has(task.id)) || [];
  
  // 计算时间偏移量
  const firstTask = selectedTaskList[0];
  const originalStart = dayjs(firstTask.planned_start);
  const newStartTime = selectedDate
    .startOf('week')
    .add(targetDay, 'day')
    .hour(targetHour)
    .minute(0)
    .second(0);
  
  const timeOffset = newStartTime.diff(originalStart, 'minute');
  
  // 检查所有任务移动后是否有冲突
  const conflicts = [];
  for (const task of selectedTaskList) {
    const taskStart = dayjs(task.planned_start);
    const taskEnd = dayjs(task.planned_end);
    const newTaskStart = taskStart.add(timeOffset, 'minute');
    const newTaskEnd = taskEnd.add(timeOffset, 'minute');
    
    const { hasOverlap, conflictTasks } = checkTimeOverlap(newTaskStart, newTaskEnd, task.id);
    if (hasOverlap) {
      conflicts.push({ task, conflictTasks });
    }
  }
  
  if (conflicts.length > 0) {
    // 显示冲突错误
    return;
  }
  
  // 批量更新任务
  Promise.all(selectedTaskList.map(task => updateTask(task)));
};
```

## 🎨 界面设计

### 多选状态指示
- **选中任务**: 绿色边框 + 绿色勾选标记
- **选择计数**: "已选择 X 个任务"
- **操作按钮**: "清除选择"按钮
- **操作提示**: "Ctrl+点击可多选任务"

### 冲突警告样式
- **冲突任务**: 红色边框 + 震动动画
- **错误消息**: 详细的冲突信息提示
- **阻止操作**: 有冲突时阻止拖拽完成

### 批量操作反馈
- **拖拽提示**: "批量放置(X个任务)"
- **进度提示**: "正在批量移动 X 个任务..."
- **成功反馈**: "成功移动 X 个任务"

## 📊 功能对比

### 单任务操作 vs 批量操作

| 操作类型 | 单任务 | 批量任务 |
|----------|--------|----------|
| 选择方式 | 直接点击 | Ctrl+点击 |
| 拖拽方式 | 拖拽任务卡片 | 拖拽任何选中任务 |
| 冲突检测 | 单个任务检测 | 所有任务检测 |
| 时间计算 | 直接计算 | 基于偏移量计算 |
| 反馈信息 | 单任务信息 | 批量操作信息 |

### 冲突检测范围

| 模式 | 检测范围 | 过滤条件 |
|------|----------|----------|
| 设备模式 | 该设备的所有任务 | machine_id = 当前设备ID |
| 技能组模式 | 技能组未分配任务 | skill_group_id = 当前技能组ID 且 machine_id = null |

## 🔧 键盘快捷键

| 快捷键 | 功能 | 说明 |
|--------|------|------|
| Ctrl+点击 | 多选/取消选择 | 切换任务选择状态 |
| Shift+点击 | 范围选择 | 选择范围内的任务 |
| Ctrl+A | 全选 | 选择所有可见任务 |
| ESC | 清除选择 | 清除所有选中的任务 |
| Ctrl+点击空闲区域 | 批量移动 | 将选中任务移动到目标位置 |

## ⚠️ 使用注意事项

### 多选限制
- **权限要求**: 需要有任务编辑权限
- **任务状态**: 只能选择可编辑状态的任务
- **数量限制**: 建议一次不超过20个任务

### 冲突检测规则
- **时间精度**: 按分钟级别检测冲突
- **边界处理**: 任务开始/结束时间相同视为冲突
- **范围限制**: 只在相同设备/技能组内检测

### 批量操作约束
- **时间对齐**: 所有任务保持相对时间关系
- **跨天处理**: 支持跨天移动但需注意工作时间
- **网络要求**: 批量操作需要稳定的网络连接

## 🚀 性能优化

### 冲突检测优化
- **增量检测**: 只检测可能冲突的时间范围
- **缓存结果**: 缓存检测结果避免重复计算
- **异步处理**: 大量任务时使用异步检测

### 界面响应优化
- **虚拟滚动**: 大量任务时使用虚拟滚动
- **防抖处理**: 防止频繁的选择状态更新
- **内存管理**: 及时清理选择状态和临时数据

## 📈 未来改进

### 计划功能
1. **智能排序**: 自动优化任务时间安排
2. **冲突解决**: 自动建议冲突解决方案
3. **模板操作**: 保存和应用批量操作模板
4. **撤销重做**: 支持操作的撤销和重做

### 用户体验
1. **拖拽预览**: 显示拖拽后的时间安排预览
2. **快速选择**: 框选、按条件选择等功能
3. **操作历史**: 记录和查看操作历史
4. **协作功能**: 多用户协作时的冲突处理

---

**功能状态**: 🟢 正常运行  
**最后更新**: 2025-01-19  
**维护者**: MES开发团队
