# 设备任务拖拽调度功能

**功能版本**: v1.0  
**实现时间**: 2025-01-19  
**状态**: ✅ 已完成

## 🎯 功能概述

设备任务调度界面现在支持通过拖拽操作来调整任务的时间安排，提供直观的可视化调度体验。

## ✨ 主要特性

### 1. 拖拽操作
- **任务拖拽**: 点击并拖拽任务卡片到新的时间槽
- **视觉反馈**: 拖拽过程中提供清晰的视觉指示
- **放置预览**: 鼠标悬停时显示可放置区域
- **智能检测**: 自动检测是否拖拽到相同位置

### 2. 时间计算
- **持续时间保持**: 拖拽时保持任务的原始持续时间
- **精确定位**: 按小时精确定位任务开始时间
- **跨天支持**: 支持将任务拖拽到不同的日期
- **时间验证**: 自动验证新时间的合理性

### 3. 用户体验
- **即时更新**: 拖拽完成后立即更新数据库
- **状态反馈**: 显示更新进度和结果
- **错误处理**: 优雅处理拖拽失败的情况
- **撤销提示**: 提供操作确认信息

## 🎮 使用方法

### 基本操作
1. **打开设备调度**: 在甘特图中点击设备名称
2. **选择时间范围**: 使用周选择器选择要调度的周
3. **拖拽任务**: 
   - 鼠标悬停在任务卡片上，光标变为移动图标
   - 按住鼠标左键开始拖拽
   - 拖拽到目标时间槽
   - 释放鼠标完成操作

### 视觉指示
- **可拖拽任务**: 显示移动光标和拖拽指示器
- **拖拽中**: 任务卡片变为半透明并带有蓝色边框
- **放置目标**: 空闲时间槽高亮显示"放置到这里"
- **无效区域**: 已占用的时间槽不会响应拖拽

## 🛠️ 技术实现

### 核心组件
- **MachineScheduleModal.tsx**: 主要调度界面组件
- **MachineScheduleModal.css**: 拖拽相关样式
- **HTML5 Drag API**: 原生拖拽功能支持

### 关键功能

#### 1. 拖拽事件处理
```typescript
// 开始拖拽
const handleDragStart = (e: React.DragEvent, task: PlanTaskWithDetails) => {
  setDraggedTask(task);
  e.dataTransfer.effectAllowed = 'move';
  // 设置拖拽图像和数据
};

// 放置处理
const handleDrop = (e: React.DragEvent, targetHour: number, targetDay: number) => {
  // 计算新的时间
  // 更新任务数据
  // 刷新界面
};
```

#### 2. 时间计算逻辑
```typescript
// 计算新的开始时间
const newStartTime = selectedDate
  .startOf('week')
  .add(targetDay, 'day')
  .hour(targetHour)
  .minute(0)
  .second(0);

// 保持原始持续时间
const duration = originalEnd.diff(originalStart, 'minute');
const newEndTime = newStartTime.add(duration, 'minute');
```

#### 3. 数据更新
```typescript
// 使用React Query mutation更新数据
const updateTaskMutation = useMutation(
  ({ taskId, updateData }) => apiClient.updatePlanTask(taskId, updateData),
  {
    onSuccess: () => {
      // 刷新相关查询
      queryClient.invalidateQueries(['machineTasks', machineId]);
    }
  }
);
```

### 样式系统

#### 1. 拖拽状态样式
```css
.task-card-draggable {
  cursor: move;
  transition: all 0.2s ease;
}

.schedule-task-dragging {
  opacity: 0.7;
  border: 2px solid #1890ff;
  animation: dragPulse 0.5s ease-in-out infinite;
}
```

#### 2. 放置区域样式
```css
.drop-zone-highlight {
  background: linear-gradient(45deg, #e6f7ff, #bae7ff);
  border: 2px solid #1890ff;
  box-shadow: inset 0 0 10px rgba(24, 144, 255, 0.2);
}
```

## 📱 响应式支持

### 移动设备适配
- **触摸支持**: 支持触摸设备的拖拽操作
- **尺寸调整**: 在小屏幕上调整任务卡片大小
- **手势优化**: 优化触摸手势的响应

### 浏览器兼容性
- **现代浏览器**: 支持所有现代浏览器的HTML5拖拽API
- **降级处理**: 在不支持拖拽的环境中提供替代方案
- **性能优化**: 优化拖拽动画的性能

## 🔧 配置选项

### 拖拽行为配置
- **拖拽灵敏度**: 可调整拖拽开始的距离阈值
- **动画速度**: 可配置拖拽动画的持续时间
- **视觉效果**: 可自定义拖拽过程中的视觉效果

### 时间约束
- **最小时间单位**: 按小时对齐任务时间
- **工作时间限制**: 可配置允许调度的时间范围
- **冲突检测**: 检测时间冲突并提供警告

## 🚀 性能优化

### 渲染优化
- **虚拟化**: 大量任务时使用虚拟滚动
- **防抖处理**: 防止频繁的拖拽事件触发
- **内存管理**: 及时清理拖拽相关的临时数据

### 网络优化
- **批量更新**: 支持批量更新多个任务
- **乐观更新**: 先更新UI，后同步服务器
- **错误恢复**: 网络失败时自动恢复原始状态

## 🔍 故障排除

### 常见问题

#### 1. 拖拽不响应
- **检查浏览器**: 确保浏览器支持HTML5拖拽
- **检查权限**: 确保有任务编辑权限
- **清除缓存**: 清除浏览器缓存重试

#### 2. 时间计算错误
- **时区设置**: 检查系统时区配置
- **日期格式**: 确保日期格式正确
- **数据同步**: 检查前后端时间同步

#### 3. 性能问题
- **任务数量**: 减少同时显示的任务数量
- **动画效果**: 在低性能设备上禁用动画
- **内存清理**: 定期清理不必要的数据

## 📈 未来改进

### 计划功能
- **批量拖拽**: 支持同时拖拽多个任务
- **智能调度**: AI辅助的自动调度建议
- **冲突解决**: 自动解决时间冲突的算法
- **历史记录**: 调度操作的历史记录和撤销

### 用户体验
- **快捷键**: 键盘快捷键支持
- **手势操作**: 更丰富的触摸手势
- **语音控制**: 语音命令调度支持
- **协作功能**: 多用户实时协作调度

---

**功能状态**: 🟢 正常运行  
**最后更新**: 2025-01-19  
**维护者**: MES开发团队
