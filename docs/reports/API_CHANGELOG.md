# API Changelog

This document tracks changes to the MES System API endpoints.

## Current Version: v1.0.0 (2025-06-28)

### API Statistics
- **Total Active Endpoints**: 78
- **Total Disabled Endpoints**: 9 (routing-related)
- **HTTP Methods**: GET (46), POST (25), PUT (4), DELETE (3)
- **Access Levels**: Public (5), Protected (73), Admin-only (8)

### Active Endpoints by Category

#### Authentication (5 endpoints)
- ✅ `POST /api/auth/login` - User authentication
- ✅ `GET /api/auth/me` - Get current user info
- ✅ `POST /api/auth/users` - Create user (Admin only)
- ✅ `GET /api/auth/roles` - Get available roles (Public)
- ✅ `GET /api/auth/skill-groups` - Get skill groups (Public)

#### User Management (5 endpoints)
- ✅ `GET /api/users` - List all users
- ✅ `GET /api/users/{id}` - Get user details
- ✅ `POST /api/users/{id}/status` - Update user status
- ✅ `POST /api/users/{id}/roles` - Update user roles
- ✅ `POST /api/users/{id}/skills` - Update user skills
- ✅ `DELETE /api/users/{id}` - Delete user

#### Project Management (8 endpoints)
- ✅ `GET /api/projects` - List projects
- ✅ `POST /api/projects` - Create project
- ✅ `GET /api/projects/{id}` - Get project details
- ✅ `GET /api/projects/{id}/full` - Get project with BOM
- ✅ `PUT /api/projects/{id}` - Update project
- ✅ `DELETE /api/projects/{id}` - Delete project
- ✅ `GET /api/projects/{id}/bom` - Get project BOM
- ✅ `POST /api/projects/{id}/bom` - Add BOM item
- ✅ `GET /api/projects/{id}/work-orders` - Get project work orders
- ✅ `POST /api/projects/work-orders/create` - Create work orders from project

#### Parts Management (6 endpoints)
- ✅ `GET /api/parts` - List parts
- ✅ `POST /api/parts` - Create part
- ✅ `GET /api/parts/{id}` - Get part details
- ✅ `PUT /api/parts/{id}` - Update part
- ✅ `DELETE /api/parts/{id}` - Delete part
- ⚠️ `GET /api/parts/{id}/routing` - Get part routing (Disabled)
- ⚠️ `POST /api/parts/{id}/routing/reorder` - Reorder routing steps (Disabled)
- ⚠️ `POST /api/parts/{id}/routing/copy` - Copy routing (Disabled)

#### BOM Management (2 endpoints)
- ✅ `PUT /api/bom/{id}` - Update BOM item
- ✅ `DELETE /api/bom/{id}` - Remove BOM item

#### Machine Management (6 endpoints)
- ✅ `GET /api/machines` - List machines
- ✅ `POST /api/machines` - Create machine
- ✅ `GET /api/machines/{id}` - Get machine details
- ✅ `PUT /api/machines/{id}` - Update machine
- ✅ `DELETE /api/machines/{id}` - Delete machine
- ✅ `POST /api/machines/{id}/status` - Update machine status

#### Work Orders (6 endpoints)
- ✅ `GET /api/work-orders` - List work orders
- ✅ `POST /api/work-orders` - Create work order
- ✅ `GET /api/work-orders/{id}` - Get work order details
- ✅ `PUT /api/work-orders/{id}` - Update work order
- ✅ `DELETE /api/work-orders/{id}` - Delete work order
- ✅ `POST /api/work-orders/{id}/status` - Update work order status
- ✅ `POST /api/work-orders/{id}/plan-tasks` - Create plan tasks from work order

#### Production Planning (9 endpoints)
- ✅ `GET /api/plan-tasks` - List plan tasks
- ✅ `POST /api/plan-tasks` - Create plan task
- ✅ `GET /api/plan-tasks/{id}` - Get plan task details
- ✅ `PUT /api/plan-tasks/{id}` - Update plan task
- ✅ `DELETE /api/plan-tasks/{id}` - Delete plan task
- ✅ `POST /api/plan-tasks/{id}/status` - Update plan task status
- ✅ `POST /api/plan-tasks/{id}/reschedule` - Reschedule plan task
- ✅ `GET /api/planning/gantt` - Get Gantt chart data
- ✅ `GET /api/planning/plan-tasks` - Alternative plan tasks endpoint

#### Execution Tracking (9 endpoints)
- ✅ `GET /api/execution/logs` - Get execution logs
- ✅ `POST /api/execution/logs` - Create execution log
- ✅ `POST /api/execution/tasks/start` - Start task execution
- ✅ `POST /api/execution/tasks/complete` - Complete task execution
- ✅ `POST /api/execution/tasks/pause` - Pause task execution
- ✅ `POST /api/execution/tasks/resume` - Resume task execution
- ✅ `POST /api/execution/barcode/validate` - Validate barcode
- ✅ `GET /api/execution/tasks/active` - Get active tasks for user
- ✅ `GET /api/execution/dashboard` - Get shop floor dashboard

#### Dashboard & Reporting (11 endpoints)
- ✅ `GET /api/dashboard/overview` - Dashboard overview
- ✅ `GET /api/dashboard/production-summary` - Production summary
- ✅ `GET /api/dashboard/work-order-status` - Work order status
- ✅ `GET /api/dashboard/machine-utilization` - Machine utilization
- ✅ `GET /api/dashboard/skill-group-performance` - Skill group performance
- ✅ `GET /api/dashboard/recent-activities` - Recent activities
- ✅ `GET /api/dashboard/kpi-metrics` - KPI metrics
- ✅ `GET /api/dashboard/trends` - Trend data
- ✅ `POST /api/reports/production` - Generate production report
- ✅ `POST /api/quality/reports` - Generate quality report
- ✅ `POST /api/audit/reports` - Generate audit report

#### Quality Management (8 endpoints)
- ✅ `GET /api/quality/inspections` - List quality inspections
- ✅ `POST /api/quality/inspections` - Create quality inspection
- ✅ `GET /api/quality/inspections/{id}` - Get inspection details
- ✅ `PUT /api/quality/inspections/{id}` - Update quality inspection
- ✅ `POST /api/quality/checkpoints` - Create quality checkpoint
- ✅ `GET /api/quality/metrics` - Get quality metrics
- ✅ `GET /api/quality/pending` - Get pending inspections

#### Audit Logging (7 endpoints)
- ✅ `GET /api/audit/logs` - Get audit logs (Admin)
- ✅ `GET /api/audit/trail/{entity_type}/{entity_id}` - Get audit trail
- ✅ `GET /api/audit/summary` - Get audit summary (Admin)
- ✅ `GET /api/audit/statistics` - Get audit statistics (Admin)
- ✅ `GET /api/audit/export` - Export audit logs (Admin)
- ✅ `POST /api/audit/cleanup` - Cleanup old audit logs (Admin)

### Disabled Endpoints

#### Routing Management (9 endpoints - Disabled due to BigDecimal compatibility)
- ⚠️ `GET /api/routings` - List routing steps
- ⚠️ `POST /api/routings` - Create routing step
- ⚠️ `GET /api/routings/{id}` - Get routing step details
- ⚠️ `PUT /api/routings/{id}` - Update routing step
- ⚠️ `DELETE /api/routings/{id}` - Delete routing step

### Known Issues
- **Routing endpoints disabled**: BigDecimal compatibility issues prevent routing management functionality
- **WebSocket support**: Planned for future release for real-time updates

### Future Enhancements
- Re-enable routing management with BigDecimal fix
- Add WebSocket endpoints for real-time updates
- Expand reporting capabilities
- Add batch operation endpoints
- Implement API versioning

---

**Legend:**
- ✅ Active and functional
- ⚠️ Temporarily disabled
- ❌ Deprecated/removed
