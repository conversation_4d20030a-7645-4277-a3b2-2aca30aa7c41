# 角色和技能组管理安全设计方案

## 🎯 问题分析

### 当前风险
1. **级联删除风险**：删除角色/技能组会影响用户权限和系统功能
2. **数据完整性风险**：删除被引用的技能组会导致外键约束错误
3. **业务连续性风险**：误删核心角色可能导致系统无法管理
4. **审计追踪缺失**：无法追踪角色/技能组的变更历史

### 数据库依赖关系
```
roles (角色表)
├── user_roles (用户角色关联) - ON DELETE CASCADE ⚠️
└── 权限系统核心数据

skill_groups (技能组表)  
├── user_skills (用户技能关联) - ON DELETE CASCADE ⚠️
├── machines (设备表) - NO CASCADE ❌ 会阻止删除
├── plan_tasks (生产计划) - NO CASCADE ❌ 会阻止删除
└── 生产调度核心数据
```

## 🛡️ 安全解决方案

### 1. 软删除机制
- 添加 `is_active` 字段而不是物理删除
- 保持数据完整性，避免外键约束错误
- 支持恢复操作

### 2. 依赖检查系统
- 删除前检查所有依赖关系
- 提供详细的影响分析报告
- 强制确认机制

### 3. 分级权限保护
- **系统角色**：不可删除（admin, process_engineer等）
- **自定义角色**：可以安全删除
- **核心技能组**：需要特殊权限删除

### 4. 替换机制
- 删除前要求指定替换角色/技能组
- 自动迁移所有关联数据
- 确保业务连续性

## 📋 实现计划

### Phase 1: 数据库增强
```sql
-- 为角色表添加软删除和类型标识
ALTER TABLE roles ADD COLUMN is_active BOOLEAN DEFAULT true;
ALTER TABLE roles ADD COLUMN role_type VARCHAR(20) DEFAULT 'custom'; -- 'system' or 'custom'
ALTER TABLE roles ADD COLUMN created_at TIMESTAMPTZ DEFAULT NOW();

-- 为技能组表添加软删除和类型标识  
ALTER TABLE skill_groups ADD COLUMN is_active BOOLEAN DEFAULT true;
ALTER TABLE skill_groups ADD COLUMN group_type VARCHAR(20) DEFAULT 'custom'; -- 'system' or 'custom'
ALTER TABLE skill_groups ADD COLUMN created_at TIMESTAMPTZ DEFAULT NOW();

-- 更新现有数据为系统类型
UPDATE roles SET role_type = 'system' WHERE role_name IN ('admin', 'process_engineer', 'planner', 'operator', 'quality_inspector');
UPDATE skill_groups SET group_type = 'system' WHERE group_name IN ('CNC Machining', 'Milling', 'Turning', 'Grinding', 'Assembly', 'Quality Control');
```

### Phase 2: 后端API增强
- 创建角色/技能组管理API
- 实现依赖检查逻辑
- 添加软删除支持
- 实现替换迁移功能

### Phase 3: 前端界面
- 角色管理界面
- 技能组管理界面  
- 依赖关系可视化
- 安全删除向导

## 🔒 安全规则

### 角色管理规则
1. **系统角色不可删除**：admin, process_engineer等核心角色
2. **最后管理员保护**：不能删除最后一个admin角色
3. **依赖检查**：删除前检查用户关联
4. **替换要求**：删除角色时必须指定替换角色

### 技能组管理规则
1. **系统技能组保护**：核心技能组需要特殊权限
2. **设备依赖检查**：检查是否有设备关联
3. **计划任务检查**：检查是否有生产计划关联
4. **用户技能迁移**：删除前迁移用户技能

## 📊 用户界面设计

### 角色管理界面
```
┌─────────────────────────────────────────┐
│ 角色管理                                 │
├─────────────────────────────────────────┤
│ [新建角色] [批量操作]                    │
├─────────────────────────────────────────┤
│ 角色名称 │ 类型   │ 用户数 │ 状态 │ 操作 │
│ admin   │ 系统   │ 1     │ 活跃 │ 查看 │
│ 自定义1  │ 自定义 │ 3     │ 活跃 │ 编辑删除│
└─────────────────────────────────────────┘
```

### 安全删除向导
```
┌─────────────────────────────────────────┐
│ 删除角色确认                             │
├─────────────────────────────────────────┤
│ ⚠️ 警告：此操作将影响以下用户：           │
│ • 用户A (username1)                     │
│ • 用户B (username2)                     │
│                                         │
│ 请选择替换角色：                         │
│ [下拉选择框] ▼                          │
│                                         │
│ □ 我确认理解此操作的影响                 │
│                                         │
│ [取消] [确认删除]                       │
└─────────────────────────────────────────┘
```

## 🔄 迁移策略

### 现有数据保护
1. **备份现有数据**
2. **添加新字段**（不影响现有功能）
3. **逐步迁移**（保持系统可用）
4. **验证数据完整性**

### 向后兼容
- 现有API继续工作
- 软删除对现有查询透明
- 渐进式功能启用

## 📈 实施优先级

### 高优先级（立即实施）
1. ✅ 依赖检查API
2. ✅ 软删除机制
3. ✅ 系统角色保护

### 中优先级（后续实施）
1. 🔄 替换迁移功能
2. 🔄 批量操作
3. 🔄 高级权限控制

### 低优先级（未来考虑）
1. 📋 角色模板系统
2. 📋 权限继承
3. 📋 动态权限分配

## 🎯 预期效果

### 安全性提升
- ✅ 防止误删核心数据
- ✅ 保护业务连续性
- ✅ 完整的审计追踪

### 用户体验改善
- ✅ 直观的管理界面
- ✅ 清晰的影响提示
- ✅ 安全的操作流程

### 系统稳定性
- ✅ 数据完整性保护
- ✅ 外键约束兼容
- ✅ 业务逻辑一致性

这个设计方案既满足了管理需求，又最大化地保护了系统安全性和数据完整性。
