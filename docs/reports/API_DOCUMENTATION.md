# MES System API Documentation

## Overview
This document describes the REST API endpoints for the Manufacturing Execution System (MES).

## API Statistics
**Total Active API Endpoints: 78** (excluding disabled routing endpoints)

### Breakdown by Category:
- **Authentication**: 5 endpoints
- **User Management**: 5 endpoints
- **Project Management**: 8 endpoints
- **Parts Management**: 6 endpoints (including 3 disabled routing-related)
- **BOM Management**: 2 endpoints
- **Routings**: 9 endpoints (Currently disabled)
- **Machines**: 6 endpoints
- **Skill Groups**: 1 endpoint
- **Work Orders**: 6 endpoints
- **Production Planning**: 9 endpoints
- **Execution Tracking**: 9 endpoints
- **Dashboard and Reporting**: 11 endpoints
- **Quality Management**: 8 endpoints
- **Audit Logging**: 7 endpoints

### HTTP Methods Distribution:
- **GET**: 46 endpoints (59.0%)
- **POST**: 25 endpoints (32.1%)
- **PUT**: 4 endpoints (5.1%)
- **DELETE**: 3 endpoints (3.8%)

### Access Control:
- **Public Endpoints**: 5 (root, health, login, roles, skill-groups)
- **Protected Endpoints**: 73 (require authentication)
- **Admin-only Endpoints**: 8 (user creation, audit management)

## Base URL
```
http://localhost:8080/api
```

## Authentication
All endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## API Endpoints

### Authentication
- `POST /auth/login` - User login
- `GET /auth/me` - Get current authenticated user
- `POST /auth/users` - Create new user (Admin only)
- `GET /auth/roles` - Get all available roles (Public)
- `GET /auth/skill-groups` - Get all skill groups (Public)

### User Management
- `GET /users` - Get all users
- `GET /users/{id}` - Get user by ID
- `DELETE /users/{id}` - Delete user
- `POST /users/{id}/status` - Update user status
- `POST /users/{id}/roles` - Update user roles
- `POST /users/{id}/skills` - Update user skills

### Project Management
- `GET /projects` - Get all projects
- `GET /projects/{id}` - Get project by ID
- `GET /projects/{id}/full` - Get project with complete BOM
- `POST /projects` - Create new project
- `PUT /projects/{id}` - Update project
- `DELETE /projects/{id}` - Delete project
- `GET /projects/{id}/bom` - Get project BOM
- `POST /projects/{id}/bom` - Add BOM item to project
- `GET /projects/{id}/work-orders` - Get work orders by project
- `POST /projects/work-orders/create` - Create work orders from project

### Parts Management
- `GET /parts` - Get all parts
- `GET /parts/{id}` - Get part by ID
- `POST /parts` - Create new part
- `PUT /parts/{id}` - Update part
- `DELETE /parts/{id}` - Delete part

### BOM Management
- `PUT /bom/{id}` - Update BOM item
- `DELETE /bom/{id}` - Remove BOM item

### Routings
**Note: Routing endpoints are temporarily disabled due to BigDecimal compatibility issues**
- `GET /routings` - Get all routing steps (Disabled)
- `GET /routings/{id}` - Get routing step by ID (Disabled)
- `POST /routings` - Create new routing step (Disabled)
- `PUT /routings/{id}` - Update routing step (Disabled)
- `DELETE /routings/{id}` - Delete routing step (Disabled)
- `GET /parts/{id}/routing` - Get part routing (Disabled)
- `POST /parts/{id}/routing/reorder` - Reorder routing steps (Disabled)
- `POST /parts/{id}/routing/copy` - Copy routing (Disabled)

### Machines
- `GET /machines` - Get all machines
- `GET /machines/{id}` - Get machine by ID
- `POST /machines` - Create new machine
- `PUT /machines/{id}` - Update machine
- `DELETE /machines/{id}` - Delete machine
- `POST /machines/{id}/status` - Update machine status

### Skill Groups
- `GET /skill-groups` - Get all skill groups (Available via /auth/skill-groups)

### Work Orders
- `GET /work-orders` - Get all work orders
- `GET /work-orders/{id}` - Get work order by ID
- `POST /work-orders` - Create new work order
- `PUT /work-orders/{id}` - Update work order
- `DELETE /work-orders/{id}` - Delete work order
- `POST /work-orders/{id}/status` - Update work order status
- `POST /work-orders/{id}/plan-tasks` - Create plan tasks from work order

### Production Planning
- `GET /plan-tasks` - Get all plan tasks
- `GET /plan-tasks/{id}` - Get plan task by ID
- `POST /plan-tasks` - Create new plan task
- `PUT /plan-tasks/{id}` - Update plan task
- `DELETE /plan-tasks/{id}` - Delete plan task
- `POST /plan-tasks/{id}/status` - Update plan task status
- `POST /plan-tasks/{id}/reschedule` - Reschedule plan task
- `GET /planning/gantt` - Get Gantt chart data
- `GET /planning/plan-tasks` - Get all plan tasks (Alternative endpoint)

### Execution Tracking
- `GET /execution/logs` - Get execution logs
- `POST /execution/logs` - Create execution log
- `POST /execution/tasks/start` - Start task execution
- `POST /execution/tasks/complete` - Complete task execution
- `POST /execution/tasks/pause` - Pause task execution
- `POST /execution/tasks/resume` - Resume task execution
- `POST /execution/barcode/validate` - Validate barcode
- `GET /execution/tasks/active` - Get active tasks for user
- `GET /execution/dashboard` - Get shop floor dashboard

### Dashboard and Reporting
- `GET /dashboard/overview` - Get dashboard overview
- `GET /dashboard/production-summary` - Get production summary
- `GET /dashboard/work-order-status` - Get work order status summary
- `GET /dashboard/machine-utilization` - Get machine utilization data
- `GET /dashboard/skill-group-performance` - Get skill group performance
- `GET /dashboard/recent-activities` - Get recent activities
- `GET /dashboard/kpi-metrics` - Get KPI metrics
- `GET /dashboard/trends` - Get trend data
- `POST /reports/production` - Generate production report

### Quality Management
- `GET /quality/inspections` - Get all quality inspections
- `POST /quality/inspections` - Create quality inspection
- `GET /quality/inspections/{id}` - Get quality inspection by ID
- `PUT /quality/inspections/{id}` - Update quality inspection
- `POST /quality/checkpoints` - Create quality checkpoint
- `POST /quality/reports` - Generate quality report
- `GET /quality/metrics` - Get quality metrics
- `GET /quality/pending` - Get pending inspections

### Audit Logging
- `GET /audit/logs` - Get audit logs (Admin only)
- `GET /audit/trail/{entity_type}/{entity_id}` - Get audit trail
- `GET /audit/summary` - Get audit summary (Admin only)
- `GET /audit/statistics` - Get audit statistics (Admin only)
- `POST /audit/reports` - Generate audit report (Admin only)
- `GET /audit/export` - Export audit logs (Admin only)
- `POST /audit/cleanup` - Cleanup old audit logs (Admin only)

## Response Format
All API responses follow this format:
```json
{
  "data": {},
  "message": "Success message",
  "timestamp": "2025-06-28T10:45:00Z"
}
```

Error responses:
```json
{
  "error": "error_code",
  "message": "Error description",
  "timestamp": "2025-06-28T10:45:00Z"
}
```

## Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

## Role-Based Access Control
- **Admin**: Full access to all endpoints
- **Process Engineer**: Access to planning, quality, and reporting
- **Planner**: Access to planning and work orders
- **Operator**: Access to execution tracking and basic views
- **Quality Inspector**: Access to quality management
- **Viewer**: Read-only access to most endpoints

## Query Parameters
Many GET endpoints support query parameters for filtering:
- `limit` - Number of results to return (default: 50, max: 100)
- `offset` - Number of results to skip (default: 0)
- `start_date` - Filter by start date
- `end_date` - Filter by end date
- `status` - Filter by status
- `user_id` - Filter by user ID
- `project_id` - Filter by project ID

## Example Requests

### Login
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

### Create Work Order
```bash
curl -X POST http://localhost:8080/api/work-orders \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "project_bom_id": 1,
    "quantity": 100,
    "due_date": "2025-07-15",
    "priority": "high"
  }'
```

### Get Dashboard Overview
```bash
curl -X GET http://localhost:8080/api/dashboard/overview \
  -H "Authorization: Bearer <token>"
```

## Database Schema
The system uses PostgreSQL with the following main tables:
- `users` - User accounts and authentication
- `projects` - Manufacturing projects
- `parts` - Part definitions
- `project_boms` - Bill of materials
- `routings` - Manufacturing process steps
- `work_orders` - Production orders
- `plan_tasks` - Scheduled production tasks
- `execution_logs` - Task execution tracking
- `quality_inspections` - Quality control records
- `machines` - Manufacturing equipment
- `skill_groups` - Worker skill classifications

## Complete API Endpoint List

### Public Endpoints (No Authentication Required)
1. `GET /` - Root endpoint
2. `GET /health` - Health check
3. `POST /api/auth/login` - User login
4. `GET /api/auth/roles` - Get all roles
5. `GET /api/auth/skill-groups` - Get all skill groups

### Protected Endpoints (Authentication Required)

#### Authentication & User Info
6. `GET /api/auth/me` - Get current user
7. `POST /api/auth/users` - Create user (Admin only)

#### User Management
8. `GET /api/users` - Get all users
9. `GET /api/users/{id}` - Get user by ID
10. `POST /api/users/{id}/status` - Update user status
11. `POST /api/users/{id}/roles` - Update user roles
12. `POST /api/users/{id}/skills` - Update user skills
13. `DELETE /api/users/{id}` - Delete user

#### Machine Management
14. `GET /api/machines` - Get all machines
15. `POST /api/machines` - Create machine
16. `GET /api/machines/{id}` - Get machine by ID
17. `PUT /api/machines/{id}` - Update machine
18. `DELETE /api/machines/{id}` - Delete machine
19. `POST /api/machines/{id}/status` - Update machine status

#### Parts Management
20. `GET /api/parts` - Get all parts
21. `POST /api/parts` - Create part
22. `GET /api/parts/{id}` - Get part by ID
23. `PUT /api/parts/{id}` - Update part
24. `DELETE /api/parts/{id}` - Delete part

#### Project Management
25. `GET /api/projects` - Get all projects
26. `POST /api/projects` - Create project
27. `GET /api/projects/{id}` - Get project by ID
28. `GET /api/projects/{id}/full` - Get project with BOM
29. `PUT /api/projects/{id}` - Update project
30. `DELETE /api/projects/{id}` - Delete project
31. `GET /api/projects/{id}/bom` - Get project BOM
32. `POST /api/projects/{id}/bom` - Add BOM item
33. `GET /api/projects/{id}/work-orders` - Get work orders by project
34. `POST /api/projects/work-orders/create` - Create work orders from project

#### BOM Management
35. `PUT /api/bom/{id}` - Update BOM item
36. `DELETE /api/bom/{id}` - Remove BOM item

#### Work Order Management
37. `GET /api/work-orders` - Get all work orders
38. `POST /api/work-orders` - Create work order
39. `GET /api/work-orders/{id}` - Get work order by ID
40. `PUT /api/work-orders/{id}` - Update work order
41. `DELETE /api/work-orders/{id}` - Delete work order
42. `POST /api/work-orders/{id}/status` - Update work order status
43. `POST /api/work-orders/{id}/plan-tasks` - Create plan tasks from work order

#### Production Planning
44. `GET /api/plan-tasks` - Get all plan tasks
45. `POST /api/plan-tasks` - Create plan task
46. `GET /api/plan-tasks/{id}` - Get plan task by ID
47. `PUT /api/plan-tasks/{id}` - Update plan task
48. `DELETE /api/plan-tasks/{id}` - Delete plan task
49. `POST /api/plan-tasks/{id}/status` - Update plan task status
50. `POST /api/plan-tasks/{id}/reschedule` - Reschedule plan task
51. `GET /api/planning/gantt` - Get Gantt chart data
52. `GET /api/planning/plan-tasks` - Get all plan tasks (alternative)

#### Execution Tracking
53. `GET /api/execution/logs` - Get execution logs
54. `POST /api/execution/logs` - Create execution log
55. `POST /api/execution/tasks/start` - Start task execution
56. `POST /api/execution/tasks/complete` - Complete task execution
57. `POST /api/execution/tasks/pause` - Pause task execution
58. `POST /api/execution/tasks/resume` - Resume task execution
59. `POST /api/execution/barcode/validate` - Validate barcode
60. `GET /api/execution/tasks/active` - Get active tasks for user
61. `GET /api/execution/dashboard` - Get shop floor dashboard

#### Dashboard and Reporting
62. `GET /api/dashboard/overview` - Get dashboard overview
63. `GET /api/dashboard/production-summary` - Get production summary
64. `GET /api/dashboard/work-order-status` - Get work order status
65. `GET /api/dashboard/machine-utilization` - Get machine utilization
66. `GET /api/dashboard/skill-group-performance` - Get skill group performance
67. `GET /api/dashboard/recent-activities` - Get recent activities
68. `GET /api/dashboard/kpi-metrics` - Get KPI metrics
69. `GET /api/dashboard/trends` - Get trend data
70. `POST /api/reports/production` - Generate production report

#### Quality Management
71. `GET /api/quality/inspections` - Get all quality inspections
72. `POST /api/quality/inspections` - Create quality inspection
73. `GET /api/quality/inspections/{id}` - Get quality inspection by ID
74. `PUT /api/quality/inspections/{id}` - Update quality inspection
75. `POST /api/quality/checkpoints` - Create quality checkpoint
76. `POST /api/quality/reports` - Generate quality report
77. `GET /api/quality/metrics` - Get quality metrics
78. `GET /api/quality/pending` - Get pending inspections

#### Audit Logging (Admin Only)
79. `GET /api/audit/logs` - Get audit logs
80. `GET /api/audit/trail/{entity_type}/{entity_id}` - Get audit trail
81. `GET /api/audit/summary` - Get audit summary
82. `GET /api/audit/statistics` - Get audit statistics
83. `POST /api/audit/reports` - Generate audit report
84. `POST /api/audit/export` - Export audit logs
85. `POST /api/audit/cleanup` - Cleanup old audit logs

### Disabled Endpoints (Routing Management)
The following routing endpoints are temporarily disabled due to BigDecimal compatibility issues:
- `GET /api/routings` - Get all routing steps
- `POST /api/routings` - Create routing step
- `GET /api/routings/{id}` - Get routing step by ID
- `PUT /api/routings/{id}` - Update routing step
- `DELETE /api/routings/{id}` - Delete routing step
- `GET /api/parts/{id}/routing` - Get part routing
- `POST /api/parts/{id}/routing/reorder` - Reorder routing steps
- `POST /api/parts/{id}/routing/copy` - Copy routing

## Development Notes
- The system is built with Rust using Axum web framework
- Database migrations are handled automatically on startup
- All timestamps are in UTC
- The system supports real-time updates through WebSocket connections (planned)
- Comprehensive audit logging tracks all changes
- Role-based permissions are enforced at the API level
- JWT tokens are used for authentication with role-based access control
- All protected endpoints require valid JWT token in Authorization header
