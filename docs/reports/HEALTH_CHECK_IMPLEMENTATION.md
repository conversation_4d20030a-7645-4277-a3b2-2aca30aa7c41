# 健康检查端点实现指南

## 概述
为MES系统添加健康检查端点，用于监控系统状态和生产环境的可用性检测。

## 实现方案

### 1. 健康检查模型

在 `src/models/health.rs` 中添加：

```rust
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Serialize, Deserialize)]
pub struct HealthStatus {
    pub status: String,
    pub timestamp: DateTime<Utc>,
    pub version: String,
    pub uptime: u64,
    pub checks: HealthChecks,
}

#[derive(Serialize, Deserialize)]
pub struct HealthChecks {
    pub database: CheckResult,
    pub memory: CheckResult,
    pub disk: CheckResult,
}

#[derive(Serialize, Deserialize)]
pub struct CheckResult {
    pub status: String,
    pub message: String,
    pub response_time_ms: u64,
}
```

### 2. 健康检查服务

在 `src/services/health_service.rs` 中添加：

```rust
use crate::models::health::*;
use crate::utils::database::get_db_pool;
use sqlx::PgPool;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use sysinfo::{System, SystemExt, DiskExt};

pub struct HealthService;

impl HealthService {
    pub async fn get_health_status() -> HealthStatus {
        let start_time = Instant::now();
        
        HealthStatus {
            status: "healthy".to_string(),
            timestamp: Utc::now(),
            version: env!("CARGO_PKG_VERSION").to_string(),
            uptime: Self::get_uptime(),
            checks: Self::perform_health_checks().await,
        }
    }
    
    async fn perform_health_checks() -> HealthChecks {
        HealthChecks {
            database: Self::check_database().await,
            memory: Self::check_memory(),
            disk: Self::check_disk(),
        }
    }
    
    async fn check_database() -> CheckResult {
        let start = Instant::now();
        
        match get_db_pool().await {
            Ok(pool) => {
                match sqlx::query("SELECT 1").fetch_one(&pool).await {
                    Ok(_) => CheckResult {
                        status: "healthy".to_string(),
                        message: "Database connection successful".to_string(),
                        response_time_ms: start.elapsed().as_millis() as u64,
                    },
                    Err(e) => CheckResult {
                        status: "unhealthy".to_string(),
                        message: format!("Database query failed: {}", e),
                        response_time_ms: start.elapsed().as_millis() as u64,
                    }
                }
            },
            Err(e) => CheckResult {
                status: "unhealthy".to_string(),
                message: format!("Database connection failed: {}", e),
                response_time_ms: start.elapsed().as_millis() as u64,
            }
        }
    }
    
    fn check_memory() -> CheckResult {
        let start = Instant::now();
        let mut system = System::new_all();
        system.refresh_memory();
        
        let total_memory = system.total_memory();
        let used_memory = system.used_memory();
        let memory_usage = (used_memory as f64 / total_memory as f64) * 100.0;
        
        let status = if memory_usage < 80.0 { "healthy" } else { "warning" };
        
        CheckResult {
            status: status.to_string(),
            message: format!("Memory usage: {:.1}%", memory_usage),
            response_time_ms: start.elapsed().as_millis() as u64,
        }
    }
    
    fn check_disk() -> CheckResult {
        let start = Instant::now();
        let mut system = System::new_all();
        system.refresh_disks();
        
        if let Some(disk) = system.disks().first() {
            let total_space = disk.total_space();
            let available_space = disk.available_space();
            let used_percentage = ((total_space - available_space) as f64 / total_space as f64) * 100.0;
            
            let status = if used_percentage < 85.0 { "healthy" } else { "warning" };
            
            CheckResult {
                status: status.to_string(),
                message: format!("Disk usage: {:.1}%", used_percentage),
                response_time_ms: start.elapsed().as_millis() as u64,
            }
        } else {
            CheckResult {
                status: "unknown".to_string(),
                message: "Could not retrieve disk information".to_string(),
                response_time_ms: start.elapsed().as_millis() as u64,
            }
        }
    }
    
    fn get_uptime() -> u64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or(Duration::from_secs(0))
            .as_secs()
    }
}
```

### 3. 健康检查处理器

在 `src/handlers/health_handler.rs` 中添加：

```rust
use axum::{Json, response::Json as ResponseJson};
use crate::models::health::HealthStatus;
use crate::services::health_service::HealthService;

pub async fn health_check() -> ResponseJson<HealthStatus> {
    let health_status = HealthService::get_health_status().await;
    ResponseJson(health_status)
}

pub async fn readiness_check() -> ResponseJson<serde_json::Value> {
    // 简单的就绪检查
    ResponseJson(serde_json::json!({
        "status": "ready",
        "timestamp": chrono::Utc::now()
    }))
}

pub async fn liveness_check() -> ResponseJson<serde_json::Value> {
    // 简单的存活检查
    ResponseJson(serde_json::json!({
        "status": "alive",
        "timestamp": chrono::Utc::now()
    }))
}
```

### 4. 路由配置

在 `src/main.rs` 中添加路由：

```rust
use crate::handlers::health_handler;

// 在路由配置中添加
let app = Router::new()
    // ... 其他路由
    .route("/health", get(health_handler::health_check))
    .route("/health/ready", get(health_handler::readiness_check))
    .route("/health/live", get(health_handler::liveness_check))
    // ... 其他配置
```

### 5. 依赖添加

在 `Cargo.toml` 中添加：

```toml
[dependencies]
# ... 现有依赖
sysinfo = "0.29"
```

## 使用方式

### 基本健康检查
```bash
curl http://localhost:8080/health
```

响应示例：
```json
{
  "status": "healthy",
  "timestamp": "2025-07-09T10:30:00Z",
  "version": "0.1.0",
  "uptime": 3600,
  "checks": {
    "database": {
      "status": "healthy",
      "message": "Database connection successful",
      "response_time_ms": 15
    },
    "memory": {
      "status": "healthy",
      "message": "Memory usage: 45.2%",
      "response_time_ms": 2
    },
    "disk": {
      "status": "healthy",
      "message": "Disk usage: 68.5%",
      "response_time_ms": 1
    }
  }
}
```

### Kubernetes就绪检查
```bash
curl http://localhost:8080/health/ready
```

### Kubernetes存活检查
```bash
curl http://localhost:8080/health/live
```

## 监控集成

### Prometheus指标
可以扩展健康检查以暴露Prometheus指标：

```rust
// 在健康检查中添加指标收集
pub async fn metrics() -> String {
    format!(
        "# HELP mes_health_check_duration_seconds Time spent on health checks\n\
         # TYPE mes_health_check_duration_seconds gauge\n\
         mes_health_check_duration_seconds{{check=\"database\"}} {}\n\
         mes_health_check_duration_seconds{{check=\"memory\"}} {}\n\
         mes_health_check_duration_seconds{{check=\"disk\"}} {}\n",
        0.015, 0.002, 0.001
    )
}
```

### 告警规则
基于健康检查状态设置告警：

```yaml
# Prometheus告警规则示例
groups:
  - name: mes_health
    rules:
      - alert: MESServiceDown
        expr: up{job="mes-system"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "MES service is down"
          
      - alert: MESDatabaseUnhealthy
        expr: mes_database_health_status != 1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "MES database health check failed"
```

## 部署配置

### Docker健康检查
```dockerfile
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/health/live || exit 1
```

### Kubernetes配置
```yaml
apiVersion: v1
kind: Pod
spec:
  containers:
  - name: mes-system
    image: mes-system:latest
    livenessProbe:
      httpGet:
        path: /health/live
        port: 8080
      initialDelaySeconds: 30
      periodSeconds: 10
    readinessProbe:
      httpGet:
        path: /health/ready
        port: 8080
      initialDelaySeconds: 5
      periodSeconds: 5
```

## 实施步骤

1. **创建健康检查模型和服务**
2. **添加健康检查处理器**
3. **配置路由**
4. **添加依赖**
5. **测试健康检查端点**
6. **集成到监控系统**

## 预期效果

- **系统可观测性提升**: 实时了解系统状态
- **故障快速发现**: 自动检测系统异常
- **运维效率提升**: 减少手动检查工作
- **生产环境稳定性**: 及时发现和处理问题
